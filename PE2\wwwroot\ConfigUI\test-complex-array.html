<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复杂数组字段测试 - 组合条件</title>
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3.3.8/dist/vue.global.js"></script>
    
    <!-- Element Plus UI Framework -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    
    <!-- Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.iife.min.js"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei';
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 32px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 16px;
            color: #303133;
        }
        .result-display {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 16px;
            margin-top: 16px;
        }
        .json-display {
            background: #2d3748;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>复杂数组字段测试 - 组合特价条件</h1>
            
            <div class="test-section">
                <div class="test-title">测试组合条件配置</div>
                <complex-array-renderer
                    field-name="combinationConditions"
                    :field-config="fieldConfig"
                    :model-value="currentValue"
                    @update:model-value="updateValue">
                </complex-array-renderer>
                
                <div class="result-display">
                    <h3>当前值：</h3>
                    <div class="json-display">{{ JSON.stringify(currentValue, null, 2) }}</div>
                </div>
            </div>
            
            <div class="test-section">
                <div class="test-title">API元数据</div>
                <div class="result-display">
                    <div class="json-display">{{ JSON.stringify(fieldConfig, null, 2) }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载组件 -->
    <script src="./js/components/ComplexArrayRenderer.js"></script>
    
    <script>
        const { createApp } = Vue;
        
        const app = createApp({
            components: {
                'complex-array-renderer': ComplexArrayRenderer
            },
            setup() {
                const { ref, reactive } = Vue;
                
                // 模拟组合特价条件的字段配置
                const fieldConfig = reactive({
                    name: "combinationConditions",
                    label: "Combination Conditions",
                    type: "array-complex",
                    required: false,
                    group: "condition",
                    metadata: {
                        elementSchema: {
                            typeName: "CombinationSpecialPriceCondition",
                            displayName: "组合特价条件",
                            description: null,
                            fields: [
                                {
                                    name: "productId",
                                    label: "Product Id",
                                    type: {
                                        type: "input",
                                        isNullable: false
                                    },
                                    required: false,
                                    group: "condition",
                                    description: null,
                                    validation: null,
                                    default: null
                                },
                                {
                                    name: "requiredQuantity",
                                    label: "Required Quantity",
                                    type: {
                                        type: "number",
                                        isNullable: false
                                    },
                                    required: false,
                                    group: "condition",
                                    description: null,
                                    validation: null,
                                    default: null
                                },
                                {
                                    name: "requiredAmount",
                                    label: "Required Amount",
                                    type: {
                                        type: "number",
                                        isNullable: false
                                    },
                                    required: false,
                                    group: "condition",
                                    description: null,
                                    validation: null,
                                    default: null
                                }
                            ],
                            category: "condition"
                        },
                        defaultItem: {
                            productId: "",
                            requiredQuantity: 0,
                            requiredAmount: 0
                        },
                        operations: ["add", "remove", "edit", "reorder"]
                    },
                    isNullable: false,
                    elementType: "CombinationSpecialPriceCondition"
                });
                
                // 当前值
                const currentValue = ref([
                    {
                        productId: "PROD001",
                        requiredQuantity: 2,
                        requiredAmount: 100.00
                    },
                    {
                        productId: "PROD002",
                        requiredQuantity: 1,
                        requiredAmount: 50.00
                    }
                ]);
                
                // 更新值
                const updateValue = (newValue) => {
                    currentValue.value = newValue;
                    console.log('Value updated:', newValue);
                };
                
                return {
                    fieldConfig,
                    currentValue,
                    updateValue
                };
            }
        });
        
        // 注册Element Plus
        app.use(ElementPlus);
        
        // 注册Element Plus图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
        
        // 挂载应用
        app.mount('#app');
    </script>
</body>
</html>
