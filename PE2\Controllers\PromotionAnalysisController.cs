using Microsoft.AspNetCore.Mvc;
using PE2.Models;
using PE2.Services;

namespace PE2.Controllers;

/// <summary>
/// 促销分析控制器 - 展示折扣分摊和促销追踪功能
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class PromotionAnalysisController : ControllerBase
{
    private readonly PromotionEngineService _promotionEngineService;
    private readonly ILogger<PromotionAnalysisController> _logger;

    public PromotionAnalysisController(PromotionEngineService promotionEngineService, ILogger<PromotionAnalysisController> logger)
    {
        _promotionEngineService = promotionEngineService;
        _logger = logger;
    }

    /// <summary>
    /// 详细分析促销计算结果，展示折扣分摊详情
    /// </summary>
    [HttpPost("detailed-analysis")]
    public async Task<IActionResult> DetailedPromotionAnalysis([FromBody] ShoppingCart cart)
    {
        try
        {
            if (cart == null || !cart.Items.Any())
            {
                return BadRequest("购物车不能为空");
            }

            var result = await _promotionEngineService.CalculateOptimalPromotionsAsync(cart);

            var analysis = new
            {
                Summary = new
                {
                    OriginalAmount = result.OriginalAmount,
                    TotalDiscount = result.TotalDiscount,
                    FinalAmount = result.FinalAmount,
                    DiscountRate = result.DiscountRate,
                    CalculationTimeMs = result.CalculationTimeMs,
                    IsOptimal = result.IsOptimal
                },

                OriginalCart = new
                {
                    Items = result.OriginalCart.Items.Select(item => new
                    {
                        ProductId = item.Product.Id,
                        ProductName = item.Product.Name,
                        Quantity = item.Quantity,
                        UnitPrice = item.UnitPrice,
                        SubTotal = item.SubTotal
                    }),
                    TotalAmount = result.OriginalCart.TotalAmount
                },

                ProcessedCart = new
                {
                    Items = result.ProcessedCart.Items.Select(item => new
                    {
                        ProductId = item.Product.Id,
                        ProductName = item.Product.Name,
                        Quantity = item.Quantity,
                        OriginalUnitPrice = item.UnitPrice,
                        ActualUnitPrice = item.ActualUnitPrice,
                        OriginalSubTotal = item.SubTotal,
                        ActualSubTotal = item.ActualSubTotal,
                        IsGift = item.IsGift,
                        TotalDiscountAmount = item.TotalDiscountAmount,
                        AppliedPromotions = item.AppliedPromotionRuleIds,
                        PromotionDetails = item.PromotionDetails.Select(p => new
                        {
                            p.RuleId,
                            p.RuleName,
                            p.PromotionType,
                            p.DiscountAmount,
                            p.Description,
                            p.IsGiftRelated
                        })
                    }),
                    TotalAmount = result.ProcessedCart.TotalAmount,
                    ActualTotalAmount = result.ProcessedCart.ActualTotalAmount,
                    TotalDiscountAmount = result.ProcessedCart.TotalDiscountAmount
                },

                AppliedPromotions = result.AppliedPromotions.Select(p => new
                {
                    p.RuleId,
                    p.RuleName,
                    p.PromotionType,
                    p.DiscountAmount,
                    p.ApplicationCount,
                    p.Description,
                    ConsumedItems = p.ConsumedItems,
                    GiftItems = p.GiftItems
                }),

                IgnoredPromotions = result.IgnoredPromotions.Select(i => new
                {
                    i.RuleId,
                    i.RuleName,
                    i.Reason,
                    i.ReasonType
                }),

                DiscountAllocationDetails = AnalyzeDiscountAllocation(result.ProcessedCart),

                ExclusivityAnalysis = AnalyzePromotionExclusivity(result),

                ValidationInfo = ValidateDiscountAllocation(result)
            };

            return Ok(analysis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "详细促销分析时发生错误");
            return StatusCode(500, "促销分析时发生内部错误");
        }
    }

    /// <summary>
    /// 比较不同促销方案的效果
    /// </summary>
    [HttpPost("compare-scenarios")]
    public async Task<IActionResult> ComparePromotionScenarios([FromBody] ComparisonRequest request)
    {
        try
        {
            if (request?.Cart == null || !request.Cart.Items.Any())
            {
                return BadRequest("购物车不能为空");
            }

            var scenarios = new List<object>();

            // 场景1：最优促销组合
            var optimalResult = await _promotionEngineService.CalculateOptimalPromotionsAsync(request.Cart.Clone());
            scenarios.Add(new
            {
                ScenarioName = "最优促销组合",
                Result = CreateScenarioSummary(optimalResult),
                ItemDetails = optimalResult.ProcessedCart.Items.Select(CreateItemSummary)
            });

            // 场景2：不使用促销
            var noPromotionCart = request.Cart.Clone();
            noPromotionCart.InitializeActualPrices();
            scenarios.Add(new
            {
                ScenarioName = "不使用促销",
                Result = new
                {
                    OriginalAmount = noPromotionCart.TotalAmount,
                    TotalDiscount = 0m,
                    FinalAmount = noPromotionCart.TotalAmount,
                    DiscountRate = 0m,
                    AppliedPromotionsCount = 0
                },
                ItemDetails = noPromotionCart.Items.Select(CreateItemSummary)
            });

            // 场景3：单独应用每个促销规则（如果指定）
            if (request.SpecificRuleIds?.Any() == true)
            {
                foreach (var ruleId in request.SpecificRuleIds)
                {
                    var singleRuleCart = request.Cart.Clone();
                    var singleRuleResult = await _promotionEngineService.ApplySpecificPromotionAsync(singleRuleCart, ruleId);

                    if (singleRuleResult.IsSuccessful)
                    {
                        // 这里需要创建一个简化的结果对象
                        scenarios.Add(new
                        {
                            ScenarioName = $"仅使用 {singleRuleResult.RuleName}",
                            Result = new
                            {
                                OriginalAmount = request.Cart.TotalAmount,
                                TotalDiscount = singleRuleResult.DiscountAmount,
                                FinalAmount = request.Cart.TotalAmount - singleRuleResult.DiscountAmount,
                                DiscountRate = singleRuleResult.DiscountAmount / request.Cart.TotalAmount,
                                AppliedPromotionsCount = 1
                            },
                            ItemDetails = singleRuleCart.Items.Select(CreateItemSummary)
                        });
                    }
                }
            }

            var comparison = new
            {
                OriginalCart = new
                {
                    TotalAmount = request.Cart.TotalAmount,
                    TotalQuantity = request.Cart.TotalQuantity,
                    ItemCount = request.Cart.Items.Count
                },
                Scenarios = scenarios,
                BestScenario = scenarios.OrderByDescending(s =>
                    ((dynamic)s).Result.TotalDiscount).FirstOrDefault(),
                Recommendation = GenerateRecommendation(scenarios)
            };

            return Ok(comparison);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "比较促销方案时发生错误");
            return StatusCode(500, "比较促销方案时发生内部错误");
        }
    }

    /// <summary>
    /// 分析折扣分摊详情
    /// </summary>
    private object AnalyzeDiscountAllocation(ShoppingCart processedCart)
    {
        var regularItems = processedCart.Items.Where(x => !x.IsGift).ToList();
        var giftItems = processedCart.Items.Where(x => x.IsGift).ToList();

        return new
        {
            RegularItems = new
            {
                Count = regularItems.Count,
                TotalOriginalAmount = regularItems.Sum(x => x.SubTotal),
                TotalActualAmount = regularItems.Sum(x => x.ActualSubTotal),
                TotalDiscount = regularItems.Sum(x => x.TotalDiscountAmount),
                Items = regularItems.Select(x => new
                {
                    x.Product.Id,
                    x.Product.Name,
                    x.Quantity,
                    OriginalUnitPrice = x.UnitPrice,
                    ActualUnitPrice = x.ActualUnitPrice,
                    DiscountPerUnit = x.UnitPrice - x.ActualUnitPrice,
                    TotalDiscount = x.TotalDiscountAmount,
                    DiscountRate = x.UnitPrice > 0 ? (x.UnitPrice - x.ActualUnitPrice) / x.UnitPrice : 0
                })
            },
            GiftItems = new
            {
                Count = giftItems.Count,
                TotalValue = giftItems.Sum(x => x.SubTotal),
                Items = giftItems.Select(x => new
                {
                    x.Product.Id,
                    x.Product.Name,
                    x.Quantity,
                    OriginalValue = x.SubTotal,
                    RelatedPromotions = x.PromotionDetails.Where(p => p.IsGiftRelated).Select(p => p.RuleName)
                })
            }
        };
    }

    /// <summary>
    /// 分析促销互斥性
    /// </summary>
    private object AnalyzePromotionExclusivity(PE2.PromotionEngine.Models.PromotionResult result)
    {
        var appliedRules = result.AppliedPromotions;
        var ignoredRules = result.IgnoredPromotions;

        // 分析应用的促销规则的互斥性设置
        var exclusivityInfo = appliedRules.Select(p => new
        {
            p.RuleId,
            p.RuleName,
            p.PromotionType,
            // 这里需要从规则服务获取规则详情，暂时简化处理
            CanStackWithOthers = true, // 需要从实际规则获取
            ProductExclusivity = "None", // 需要从实际规则获取
            ConsumedProducts = p.ConsumedItems.Select(c => new
            {
                c.ProductId,
                c.ProductName,
                c.Quantity
            })
        }).ToList();

        // 分析因互斥而被忽略的促销
        var exclusivityConflicts = ignoredRules
            .Where(i => i.ReasonType == PE2.PromotionEngine.Models.IgnoreReason.Conflict)
            .Select(i => new
            {
                i.RuleId,
                i.RuleName,
                i.Reason
            }).ToList();

        // 分析商品占用情况
        var productOccupations = result.ProcessedCart.Items
            .Where(item => item.AppliedPromotionRuleIds.Any())
            .GroupBy(item => item.Product.Id)
            .Select(g => new
            {
                ProductId = g.Key,
                ProductName = g.First().Product.Name,
                TotalQuantity = g.Sum(item => item.Quantity),
                AppliedPromotions = g.SelectMany(item => item.AppliedPromotionRuleIds).Distinct().ToList(),
                PromotionCount = g.SelectMany(item => item.AppliedPromotionRuleIds).Distinct().Count(),
                HasMultiplePromotions = g.SelectMany(item => item.AppliedPromotionRuleIds).Distinct().Count() > 1
            }).ToList();

        return new
        {
            AppliedPromotionsExclusivity = exclusivityInfo,
            ExclusivityConflicts = exclusivityConflicts,
            ProductOccupations = productOccupations,
            Summary = new
            {
                TotalAppliedPromotions = appliedRules.Count,
                ConflictingPromotions = exclusivityConflicts.Count,
                OccupiedProducts = productOccupations.Count,
                ProductsWithMultiplePromotions = productOccupations.Count(p => p.HasMultiplePromotions)
            }
        };
    }

    /// <summary>
    /// 验证折扣分摊
    /// </summary>
    private object ValidateDiscountAllocation(PE2.PromotionEngine.Models.PromotionResult result)
    {
        var expectedTotal = result.OriginalAmount - result.TotalDiscount;
        var actualTotal = result.ProcessedCart.ActualTotalAmount;
        var difference = Math.Abs(expectedTotal - actualTotal);

        return new
        {
            IsValid = difference < 0.01m,
            ExpectedTotal = expectedTotal,
            ActualTotal = actualTotal,
            Difference = difference,
            Message = difference < 0.01m ? "折扣分摊正确" : $"存在 {difference:C} 的差异"
        };
    }

    /// <summary>
    /// 创建场景摘要
    /// </summary>
    private object CreateScenarioSummary(PE2.PromotionEngine.Models.PromotionResult result)
    {
        return new
        {
            OriginalAmount = result.OriginalAmount,
            TotalDiscount = result.TotalDiscount,
            FinalAmount = result.FinalAmount,
            DiscountRate = result.DiscountRate,
            AppliedPromotionsCount = result.AppliedPromotions.Count,
            CalculationTimeMs = result.CalculationTimeMs,
            IsOptimal = result.IsOptimal
        };
    }

    /// <summary>
    /// 创建商品摘要
    /// </summary>
    private object CreateItemSummary(CartItem item)
    {
        return new
        {
            ProductId = item.Product.Id,
            ProductName = item.Product.Name,
            Quantity = item.Quantity,
            OriginalUnitPrice = item.UnitPrice,
            ActualUnitPrice = item.ActualUnitPrice,
            IsGift = item.IsGift,
            TotalDiscount = item.TotalDiscountAmount,
            AppliedPromotions = item.AppliedPromotionRuleIds.Count
        };
    }

    /// <summary>
    /// 生成推荐
    /// </summary>
    private string GenerateRecommendation(List<object> scenarios)
    {
        if (scenarios.Count <= 1)
            return "建议使用当前促销方案";

        var bestDiscount = scenarios.Max(s => ((dynamic)s).Result.TotalDiscount);
        if (bestDiscount <= 0)
            return "当前购物车暂无可用的促销优惠";

        return $"推荐使用最优促销组合，可节省 {bestDiscount:C}";
    }
}

/// <summary>
/// 比较请求
/// </summary>
public class ComparisonRequest
{
    /// <summary>
    /// 购物车
    /// </summary>
    public ShoppingCart Cart { get; set; } = new();

    /// <summary>
    /// 指定要比较的促销规则ID列表
    /// </summary>
    public List<string>? SpecificRuleIds { get; set; }
}
