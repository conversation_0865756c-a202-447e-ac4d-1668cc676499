using PE2.Models;
using PE2.PromotionEngine.Models;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Rules.BuyFreeRules;

/// <summary>
/// 组合买免规则
/// 同时购买A+B+C，免去其中价格最低的一件
/// 场景：购买A商品2件、B商品1件，免费送其中价格最低的1件商品
/// </summary>
public class CombinationBuyFreeRule : BaseBuyFreeRule
{
    public override string RuleType => "CombinationBuyFree";

    /// <summary>
    /// 组合购买条件列表
    /// </summary>
    public List<CombinationBuyCondition> CombinationConditions { get; set; } = new();

    /// <summary>
    /// 免费商品数量
    /// </summary>
    public int FreeQuantity { get; set; } = 1;

    /// <summary>
    /// 是否只从参与组合的商品中选择免费商品
    /// </summary>
    public bool FreeFromCombinationOnly { get; set; } = true;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!CombinationConditions.Any())
            return false;

        // 验证组合商品是否在购物车中
        var allProductIds = CombinationConditions.Select(c => c.ProductId).ToList();
        if (!ValidateBuyFreeProductsInCart(cart, allProductIds))
            return false;

        // 检查组合购买条件
        return CheckCombinationConditions(cart);
    }

    /// <summary>
    /// 检查组合购买条件
    /// </summary>
    private bool CheckCombinationConditions(ShoppingCart cart)
    {
        foreach (var condition in CombinationConditions)
        {
            var availableQuantity = cart.GetAvailableProductQuantity(condition.ProductId);
            var totalAmount = cart.Items
                .Where(x => x.Product.Id == condition.ProductId)
                .Sum(x => x.SubTotal);

            if (condition.RequiredQuantity > 0 && availableQuantity < condition.RequiredQuantity)
                return false;

            if (condition.RequiredAmount > 0 && totalAmount < condition.RequiredAmount)
                return false;
        }

        return true;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = int.MaxValue;

        // 计算每个组合条件的最大应用次数
        foreach (var condition in CombinationConditions)
        {
            var availableQuantity = cart.GetAvailableProductQuantity(condition.ProductId);
            
            if (condition.RequiredQuantity > 0)
            {
                var maxByQuantity = IsRepeatable 
                    ? availableQuantity / condition.RequiredQuantity
                    : (availableQuantity >= condition.RequiredQuantity ? 1 : 0);
                
                maxApplications = Math.Min(maxApplications, maxByQuantity);
            }
        }

        if (maxApplications == int.MaxValue)
            maxApplications = 1;

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyCombinationBuyFree(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用组合买免促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用组合买免促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyCombinationBuyFree(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var freeItems = new List<GiftItem>(); // 免费商品记录为GiftItem

        for (int app = 0; app < applicationCount; app++)
        {
            // 消耗组合条件商品
            var combinationProducts = ConsumeCombinationProducts(cart, consumedItems);
            if (!combinationProducts.Any()) break;

            // 选择免费商品（从组合商品中选择价格最低的）
            var candidateProducts = FreeFromCombinationOnly 
                ? combinationProducts
                : CollectAllAvailableProducts(cart);

            var freeProducts = SelectFreeProducts(candidateProducts, FreeQuantity);
            if (!freeProducts.Any()) break;

            // 计算折扣金额
            var discountAmount = CalculateFreeDiscount(freeProducts);
            totalDiscountAmount += discountAmount;

            // 记录免费商品
            foreach (var freeProduct in freeProducts)
            {
                var strategyDescription = FreeItemSelectionStrategy == FreeItemSelectionStrategy.CustomerBenefit
                    ? "客户利益最大化"
                    : "商家利益最大化";

                var existingFree = freeItems.FirstOrDefault(x => x.ProductId == freeProduct.ProductId);
                if (existingFree != null)
                {
                    existingFree.Quantity += freeProduct.Quantity;
                    existingFree.Value += freeProduct.Quantity * freeProduct.UnitPrice;
                }
                else
                {
                    var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == freeProduct.ProductId);
                    freeItems.Add(new GiftItem
                    {
                        ProductId = freeProduct.ProductId,
                        ProductName = cartItem?.Product.Name ?? freeProduct.ProductId,
                        Quantity = freeProduct.Quantity,
                        Value = freeProduct.Quantity * freeProduct.UnitPrice,
                        Description = $"组合买免：免费{freeProduct.Quantity}件，节省{freeProduct.Quantity * freeProduct.UnitPrice:C}（{strategyDescription}）"
                    });
                }
            }

            // 应用免费效果到购物车
            var promotion = new AppliedPromotion
            {
                RuleId = Id,
                RuleName = Name,
                PromotionType = RuleType
            };
            ApplyFreeEffectToCart(cart, freeProducts, promotion);
        }

        // 清理数量为0的购物车项
        cart.Items.RemoveAll(x => x.Quantity <= 0);

        return (totalDiscountAmount, consumedItems, freeItems);
    }

    /// <summary>
    /// 消耗组合条件商品
    /// </summary>
    private List<(string ProductId, int Quantity, decimal UnitPrice)> ConsumeCombinationProducts(ShoppingCart cart, List<ConsumedItem> consumedItems)
    {
        var combinationProducts = new List<(string ProductId, int Quantity, decimal UnitPrice)>();

        foreach (var condition in CombinationConditions)
        {
            var remainingQuantity = condition.RequiredQuantity;

            var cartItems = cart.Items
                .Where(x => x.Product.Id == condition.ProductId && x.Quantity > 0)
                .ToList();

            foreach (var cartItem in cartItems)
            {
                if (remainingQuantity <= 0) break;

                var consumeQuantity = Math.Min(cartItem.Quantity, remainingQuantity);

                // 记录组合商品
                combinationProducts.Add((condition.ProductId, consumeQuantity, cartItem.UnitPrice));

                // 记录消耗的商品
                var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == condition.ProductId);
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += consumeQuantity;
                }
                else
                {
                    consumedItems.Add(new ConsumedItem
                    {
                        ProductId = condition.ProductId,
                        ProductName = cartItem.Product.Name,
                        Quantity = consumeQuantity,
                        UnitPrice = cartItem.UnitPrice
                    });
                }

                cartItem.Quantity -= consumeQuantity;
                remainingQuantity -= consumeQuantity;
            }

            if (remainingQuantity > 0)
            {
                // 无法满足某个组合条件，返回空列表
                return new List<(string, int, decimal)>();
            }
        }

        return combinationProducts;
    }

    /// <summary>
    /// 收集所有可用的商品（用于非组合限制的免费选择）
    /// </summary>
    private List<(string ProductId, int Quantity, decimal UnitPrice)> CollectAllAvailableProducts(ShoppingCart cart)
    {
        var products = new List<(string ProductId, int Quantity, decimal UnitPrice)>();

        foreach (var cartItem in cart.Items.Where(x => x.Quantity > 0))
        {
            products.Add((cartItem.Product.Id, cartItem.Quantity, cartItem.UnitPrice));
        }

        return products;
    }
}

/// <summary>
/// 组合购买条件
/// </summary>
public class CombinationBuyCondition
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 所需数量
    /// </summary>
    public int RequiredQuantity { get; set; }

    /// <summary>
    /// 所需金额
    /// </summary>
    public decimal RequiredAmount { get; set; }
}
