global using System.Text;
global using System.Xml.Linq;
using OfficeOpenXml;
using System.Text.Json;

namespace PE2.PromotionEngine.Observability;

/// <summary>
/// 可观测性引擎辅助方法
/// </summary>
public static class ObservabilityHelpers
{
    /// <summary>
    /// 映射步骤类型到决策节点类型
    /// </summary>
    public static DecisionNodeType MapStepTypeToNodeType(StepType stepType)
    {
        return stepType switch
        {
            StepType.RuleFiltering => DecisionNodeType.RuleFiltering,
            StepType.ConditionValidation => DecisionNodeType.ConditionValidation,
            StepType.PromotionApplication => DecisionNodeType.PromotionApplication,
            StepType.OptimizationSearch => DecisionNodeType.OptimizationSearch,
            StepType.AllocationCalculation => DecisionNodeType.AllocationCalculation,
            _ => DecisionNodeType.FinalDecision
        };
    }

    /// <summary>
    /// 获取步骤类型描述
    /// </summary>
    public static string GetStepTypeDescription(StepType stepType)
    {
        return stepType switch
        {
            StepType.RuleFiltering => "规则筛选阶段",
            StepType.ConditionValidation => "条件验证阶段",
            StepType.PromotionApplication => "促销应用阶段",
            StepType.OptimizationSearch => "优化搜索阶段",
            StepType.AllocationCalculation => "分摊计算阶段",
            StepType.ResultValidation => "结果验证阶段",
            StepType.InventoryReservation => "库存预占用阶段",
            StepType.InventoryRelease => "库存释放阶段",
            StepType.CacheOperation => "缓存操作阶段",
            StepType.ErrorHandling => "错误处理阶段",
            _ => "未知阶段"
        };
    }

    /// <summary>
    /// 计算步骤组的总耗时
    /// </summary>
    public static long CalculateGroupDuration(List<CalculationStep> steps)
    {
        if (!steps.Any())
            return 0;

        var firstStep = steps.MinBy(s => s.Timestamp);
        var lastStep = steps.MaxBy(s => s.Timestamp);
        
        if (firstStep == null || lastStep == null)
            return 0;

        return (long)(lastStep.Timestamp - firstStep.Timestamp).TotalMilliseconds;
    }

    /// <summary>
    /// 获取百分位数
    /// </summary>
    public static long GetPercentile(List<long> sortedValues, double percentile)
    {
        if (!sortedValues.Any())
            return 0;

        var index = (int)Math.Ceiling(sortedValues.Count * percentile) - 1;
        index = Math.Max(0, Math.Min(index, sortedValues.Count - 1));
        return sortedValues[index];
    }

    /// <summary>
    /// 生成优化建议
    /// </summary>
    public static List<string> GenerateOptimizationSuggestions(CalculationTrace trace, List<CalculationStep> steps)
    {
        var suggestions = new List<string>();

        // 检查总耗时
        if (trace.TotalDurationMs > 5000)
        {
            suggestions.Add("计算耗时过长，建议启用缓存或优化算法");
        }

        // 检查规则数量
        var ruleSteps = steps.Where(s => !string.IsNullOrEmpty(s.RuleId)).ToList();
        if (ruleSteps.Select(s => s.RuleId).Distinct().Count() > 50)
        {
            suggestions.Add("处理的规则数量过多，建议启用预筛选");
        }

        // 检查条件验证步骤
        var conditionSteps = steps.Where(s => s.StepType == StepType.ConditionValidation).ToList();
        if (conditionSteps.Count > 100)
        {
            suggestions.Add("条件验证次数过多，建议启用条件缓存");
        }

        // 检查优化搜索深度
        var searchSteps = steps.Where(s => s.StepType == StepType.OptimizationSearch).ToList();
        if (searchSteps.Count > 1000)
        {
            suggestions.Add("优化搜索深度过深，建议调整搜索策略或增加剪枝");
        }

        return suggestions;
    }

    /// <summary>
    /// 识别性能瓶颈
    /// </summary>
    public static List<PerformanceBottleneck> IdentifyPerformanceBottlenecks(List<CalculationTrace> traces)
    {
        var bottlenecks = new List<PerformanceBottleneck>();

        // 分析平均耗时
        var avgDuration = traces.Average(t => t.TotalDurationMs);
        if (avgDuration > 3000)
        {
            bottlenecks.Add(new PerformanceBottleneck
            {
                Type = "计算耗时",
                Description = $"平均计算时间过长: {avgDuration:F2}ms",
                Impact = avgDuration > 5000 ? 9 : 6,
                Recommendation = "考虑启用缓存、预筛选或优化算法"
            });
        }

        // 分析错误率
        var errorRate = traces.Count(t => t.Result?.IsSuccessful == false) / (double)traces.Count;
        if (errorRate > 0.05)
        {
            bottlenecks.Add(new PerformanceBottleneck
            {
                Type = "错误率",
                Description = $"计算错误率过高: {errorRate:P2}",
                Impact = errorRate > 0.1 ? 10 : 7,
                Recommendation = "检查规则配置和输入数据有效性"
            });
        }

        // 分析内存使用（如果有相关指标）
        var memoryTraces = traces.Where(t => t.Metrics.ContainsKey("peakMemoryMB")).ToList();
        if (memoryTraces.Any())
        {
            var avgMemory = memoryTraces.Average(t => Convert.ToDouble(t.Metrics["peakMemoryMB"]));
            if (avgMemory > 500)
            {
                bottlenecks.Add(new PerformanceBottleneck
                {
                    Type = "内存使用",
                    Description = $"平均内存使用过高: {avgMemory:F2}MB",
                    Impact = avgMemory > 1000 ? 8 : 5,
                    Recommendation = "优化数据结构或启用对象池"
                });
            }
        }

        return bottlenecks.OrderByDescending(b => b.Impact).ToList();
    }
}

/// <summary>
/// 可观测性引擎的导出方法扩展
/// </summary>
public static class ObservabilityExportExtensions
{
    /// <summary>
    /// 导出为JSON格式
    /// </summary>
    public static async Task<string> ExportToJsonAsync(CalculationTrace trace)
    {
        var exportData = new
        {
            traceId = trace.TraceId,
            startTime = trace.StartTime,
            endTime = trace.EndTime,
            totalDurationMs = trace.TotalDurationMs,
            algorithmType = trace.AlgorithmType,
            cartSnapshot = trace.CartSnapshot,
            result = trace.Result,
            steps = trace.Steps.ToList().OrderBy(s => s.Timestamp).Select(s => new
            {
                id = s.Id,
                stepType = s.StepType.ToString(),
                timestamp = s.Timestamp,
                description = s.Description,
                ruleId = s.RuleId,
                data = s.Data,
                durationMs = s.DurationMs,
                isSuccessful = s.IsSuccessful,
                errorMessage = s.ErrorMessage
            }),
            metrics = trace.Metrics.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
        };

        var options = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        return await Task.FromResult(JsonSerializer.Serialize(exportData, options)).ConfigureAwait(false);
    }

    /// <summary>
    /// 导出为XML格式
    /// </summary>
    public static async Task<string> ExportToXmlAsync(CalculationTrace trace)
    {
        var root = new XElement("CalculationTrace",
            new XAttribute("traceId", trace.TraceId),
            new XAttribute("startTime", trace.StartTime),
            new XAttribute("endTime", trace.EndTime),
            new XAttribute("totalDurationMs", trace.TotalDurationMs),
            new XAttribute("algorithmType", trace.AlgorithmType),
            
            new XElement("CartSnapshot",
                new XAttribute("totalAmount", trace.CartSnapshot.TotalAmount),
                new XAttribute("totalQuantity", trace.CartSnapshot.TotalQuantity),
                new XAttribute("itemCount", trace.CartSnapshot.ItemCount),
                trace.CartSnapshot.Items.Select(item =>
                    new XElement("Item",
                        new XAttribute("productId", item.ProductId),
                        new XAttribute("productName", item.ProductName),
                        new XAttribute("quantity", item.Quantity),
                        new XAttribute("unitPrice", item.UnitPrice),
                        new XAttribute("totalPrice", item.TotalPrice)
                    )
                )
            ),
            
            new XElement("Steps",
                trace.Steps.ToList().OrderBy(s => s.Timestamp).Select(step =>
                    new XElement("Step",
                        new XAttribute("id", step.Id),
                        new XAttribute("stepType", step.StepType),
                        new XAttribute("timestamp", step.Timestamp),
                        new XAttribute("description", step.Description),
                        new XAttribute("ruleId", step.RuleId ?? ""),
                        new XAttribute("isSuccessful", step.IsSuccessful),
                        step.Data.Any() ? new XElement("Data", 
                            step.Data.Select(kvp => new XElement("Item", 
                                new XAttribute("key", kvp.Key), 
                                new XAttribute("value", kvp.Value?.ToString() ?? "")
                            ))
                        ) : null
                    )
                )
            )
        );

        return await Task.FromResult(root.ToString()).ConfigureAwait(false);
    }

    /// <summary>
    /// 导出为CSV格式
    /// </summary>
    public static async Task<string> ExportToCsvAsync(CalculationTrace trace)
    {
        var csv = new StringBuilder();
        
        // CSV头部
        csv.AppendLine("StepId,StepType,Timestamp,Description,RuleId,IsSuccessful,ErrorMessage");
        
        // 步骤数据
        var steps = trace.Steps.ToList().OrderBy(s => s.Timestamp);
        foreach (var step in steps)
        {
            csv.AppendLine($"{step.Id},{step.StepType},{step.Timestamp:yyyy-MM-dd HH:mm:ss.fff}," +
                          $"\"{step.Description}\",{step.RuleId ?? ""},{step.IsSuccessful}," +
                          $"\"{step.ErrorMessage ?? ""}\"");
        }

        return await Task.FromResult(csv.ToString()).ConfigureAwait(false);
    }

    /// <summary>
    /// 导出为Excel格式
    /// </summary>
    public static async Task<string> ExportToExcelAsync(CalculationTrace trace)
    {
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        
        using var package = new ExcelPackage();
        
        // 概览工作表
        var summarySheet = package.Workbook.Worksheets.Add("概览");
        summarySheet.Cells[1, 1].Value = "追踪ID";
        summarySheet.Cells[1, 2].Value = trace.TraceId;
        summarySheet.Cells[2, 1].Value = "开始时间";
        summarySheet.Cells[2, 2].Value = trace.StartTime;
        summarySheet.Cells[3, 1].Value = "结束时间";
        summarySheet.Cells[3, 2].Value = trace.EndTime;
        summarySheet.Cells[4, 1].Value = "总耗时(ms)";
        summarySheet.Cells[4, 2].Value = trace.TotalDurationMs;
        summarySheet.Cells[5, 1].Value = "算法类型";
        summarySheet.Cells[5, 2].Value = trace.AlgorithmType;

        // 步骤详情工作表
        var stepsSheet = package.Workbook.Worksheets.Add("步骤详情");
        stepsSheet.Cells[1, 1].Value = "步骤ID";
        stepsSheet.Cells[1, 2].Value = "步骤类型";
        stepsSheet.Cells[1, 3].Value = "时间戳";
        stepsSheet.Cells[1, 4].Value = "描述";
        stepsSheet.Cells[1, 5].Value = "规则ID";
        stepsSheet.Cells[1, 6].Value = "是否成功";
        stepsSheet.Cells[1, 7].Value = "错误信息";

        var steps = trace.Steps.ToList().OrderBy(s => s.Timestamp).ToList();
        for (int i = 0; i < steps.Count; i++)
        {
            var step = steps[i];
            var row = i + 2;
            stepsSheet.Cells[row, 1].Value = step.Id;
            stepsSheet.Cells[row, 2].Value = step.StepType.ToString();
            stepsSheet.Cells[row, 3].Value = step.Timestamp;
            stepsSheet.Cells[row, 4].Value = step.Description;
            stepsSheet.Cells[row, 5].Value = step.RuleId ?? "";
            stepsSheet.Cells[row, 6].Value = step.IsSuccessful;
            stepsSheet.Cells[row, 7].Value = step.ErrorMessage ?? "";
        }

        // 自动调整列宽
        summarySheet.Cells.AutoFitColumns();
        stepsSheet.Cells.AutoFitColumns();

        var bytes = await package.GetAsByteArrayAsync().ConfigureAwait(false);
        return Convert.ToBase64String(bytes);
    }
}
