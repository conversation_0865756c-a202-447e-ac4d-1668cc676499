using PE2.Models;
using PE2.PromotionEngine.Models;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Rules.SpecialPriceRules;

/// <summary>
/// 梯度特价规则
/// 针对某一类商品，一件特价60元，两件特价110元，三件特价150元
/// 场景：当购买商品数量大于等于1件时，特价100元；当商品数量大于等于2件时，特价50元；当商品数量大于等于三件时，特价10元
/// </summary>
public class TieredSpecialPriceRule : BaseSpecialPriceRule
{
    public override string RuleType => "TieredSpecialPrice";

    /// <summary>
    /// 适用的商品ID列表
    /// </summary>
    public List<string> ApplicableProductIds { get; set; } = new();

    /// <summary>
    /// 特价梯度列表（按数量从低到高排序）
    /// </summary>
    public List<SpecialPriceTier> SpecialPriceTiers { get; set; } = new();

    /// <summary>
    /// 是否按金额计算（如果为true，则使用金额条件；否则使用数量条件）
    /// </summary>
    public bool IsByAmount { get; set; } = false;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!ApplicableProductIds.Any() || !SpecialPriceTiers.Any())
            return false;

        // 验证商品是否在购物车中
        if (!ValidateSpecialPriceProductsInCart(cart, ApplicableProductIds))
            return false;

        // 检查是否满足最低梯度条件
        return GetApplicableTier(cart) != null;
    }

    /// <summary>
    /// 获取适用的特价梯度
    /// </summary>
    private SpecialPriceTier? GetApplicableTier(ShoppingCart cart)
    {
        if (IsByAmount)
        {
            var totalAmount = CalculateTotalAmount(cart, ApplicableProductIds);
            return SpecialPriceTiers
                .Where(t => totalAmount >= t.MinAmount)
                .OrderByDescending(t => t.MinAmount)
                .FirstOrDefault();
        }
        else
        {
            var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);
            return SpecialPriceTiers
                .Where(t => totalQuantity >= t.MinQuantity)
                .OrderByDescending(t => t.MinQuantity)
                .FirstOrDefault();
        }
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var applicableTier = GetApplicableTier(cart);
        if (applicableTier == null)
            return 0;

        var maxApplications = 0;

        if (IsByAmount)
        {
            var totalAmount = CalculateTotalAmount(cart, ApplicableProductIds);
            
            maxApplications = IsRepeatable 
                ? (int)(totalAmount / applicableTier.MinAmount)
                : (totalAmount >= applicableTier.MinAmount ? 1 : 0);
        }
        else
        {
            var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);
            
            maxApplications = IsRepeatable 
                ? totalQuantity / applicableTier.MinQuantity
                : (totalQuantity >= applicableTier.MinQuantity ? 1 : 0);
        }

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyTieredSpecialPrice(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用梯度特价促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用梯度特价促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyTieredSpecialPrice(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var specialPriceRecords = new List<GiftItem>(); // 特价记录

        var applicableTier = GetApplicableTier(cart);
        if (applicableTier == null)
            return (0m, consumedItems, specialPriceRecords);

        // 获取所有适用的商品项
        var applicableItems = cart.Items
            .Where(x => ApplicableProductIds.Contains(x.Product.Id) && x.Quantity > 0)
            .ToList();

        if (!applicableItems.Any())
            return (0m, consumedItems, specialPriceRecords);

        var promotion = new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType
        };

        for (int app = 0; app < applicationCount; app++)
        {
            // 计算本次应用的特价总金额
            var currentSpecialPrice = applicableTier.SpecialPrice;

            // 根据策略排序商品
            var sortedItems = SortItemsByStrategy(applicableItems);

            // 处理翻倍情况下的特价分配
            if (IsRepeatable && app > 0)
            {
                // 翻倍时需要重新计算剩余商品的特价
                var remainingItems = GetRemainingItemsForApplication(sortedItems, app, applicableTier);
                if (!remainingItems.Any()) break;

                sortedItems = remainingItems;
            }

            // 按比例分摊特价到所有适用商品
            var strategyDescription = SpecialPriceSelectionStrategy == SpecialPriceSelectionStrategy.CustomerBenefit
                ? "客户利益最大化"
                : "商家利益最大化";

            var description = $"梯度特价：{applicableTier.Description}，特价{currentSpecialPrice:C}（第{app + 1}次应用，{strategyDescription}）";

            // 计算折扣前的总金额
            var totalOriginalAmount = sortedItems.Sum(x => x.SubTotal);
            var totalDiscountForThisApp = totalOriginalAmount - currentSpecialPrice;

            if (totalDiscountForThisApp > 0)
            {
                totalDiscountAmount += totalDiscountForThisApp;

                // 按比例分摊特价
                ApplyProportionalSpecialPrice(sortedItems, currentSpecialPrice, promotion, description);

                // 记录特价
                specialPriceRecords.Add(CreateSpecialPriceRecord(
                    string.Join(",", ApplicableProductIds),
                    "梯度特价商品",
                    sortedItems.Sum(x => x.Quantity),
                    totalDiscountForThisApp,
                    description
                ));

                // 记录消耗的商品
                foreach (var item in sortedItems)
                {
                    var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == item.Product.Id);
                    if (existingConsumed != null)
                    {
                        existingConsumed.Quantity += item.Quantity;
                    }
                    else
                    {
                        consumedItems.Add(new ConsumedItem
                        {
                            ProductId = item.Product.Id,
                            ProductName = item.Product.Name,
                            Quantity = item.Quantity,
                            UnitPrice = item.UnitPrice
                        });
                    }
                }
            }
        }

        return (totalDiscountAmount, consumedItems, specialPriceRecords);
    }

    /// <summary>
    /// 获取翻倍应用时的剩余商品
    /// </summary>
    private List<CartItem> GetRemainingItemsForApplication(List<CartItem> allItems, int applicationIndex, SpecialPriceTier tier)
    {
        var remainingItems = new List<CartItem>();
        var requiredQuantityPerApp = tier.MinQuantity;
        var startIndex = applicationIndex * requiredQuantityPerApp;

        var expandedItems = new List<CartItem>();
        foreach (var item in allItems)
        {
            for (int i = 0; i < item.Quantity; i++)
            {
                expandedItems.Add(new CartItem
                {
                    Product = item.Product,
                    Quantity = 1,
                    UnitPrice = item.UnitPrice,
                    ActualUnitPrice = item.ActualUnitPrice
                });
            }
        }

        // 取出本次应用需要的商品
        var itemsForThisApp = expandedItems.Skip(startIndex).Take(requiredQuantityPerApp).ToList();
        
        // 按商品ID分组并合并数量
        var groupedItems = itemsForThisApp
            .GroupBy(x => x.Product.Id)
            .Select(g => new CartItem
            {
                Product = g.First().Product,
                Quantity = g.Count(),
                UnitPrice = g.First().UnitPrice,
                ActualUnitPrice = g.First().ActualUnitPrice
            })
            .ToList();

        return groupedItems;
    }
}

/// <summary>
/// 特价梯度
/// </summary>
public class SpecialPriceTier
{
    /// <summary>
    /// 最小数量要求
    /// </summary>
    public int MinQuantity { get; set; }

    /// <summary>
    /// 最小金额要求
    /// </summary>
    public decimal MinAmount { get; set; }

    /// <summary>
    /// 特价金额
    /// </summary>
    public decimal SpecialPrice { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}
