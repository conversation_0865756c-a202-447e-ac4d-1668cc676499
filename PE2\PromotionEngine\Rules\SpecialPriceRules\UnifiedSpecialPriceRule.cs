using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.SpecialPriceRules;

/// <summary>
/// 统一特价规则 [OK]
/// 针对某一类商品，满X件，立享特价Y元
/// 场景：A商品满1件时，特价100元（不翻倍）；B商品满1件时，特价100元（最多翻2倍）
/// 场景案例：A商品吊牌价，零售价为1000，设置买商品A大于等于1件，特价100（不翻倍），
///           B商品吊牌价，零售价为1000, 设置买商品B等于1件，特价100（最多翻2倍）。
///A商品买1件（大于等于1件，不翻倍）：100 * 1=100
///A商品买5件（大于等于1件，不翻倍）：100 * 5 = 500
///B商品购买3件时，应收金额为1000 + 100 * 2=1200
///备注：特价 如果是大于等于 或者大于 都不考虑翻倍，只有等于时才考虑翻倍
/// </summary>
public class UnifiedSpecialPriceRule : BaseSpecialPriceRule
{
    public override string RuleType => "UnifiedSpecialPrice";

    /// <summary>
    /// 适用的商品ID列表
    /// </summary>
    public List<string> ApplicableProductIds { get; set; } = [];

    /// <summary>
    /// 最小数量要求
    /// </summary>
    public int MinQuantity { get; set; }

    /// <summary>
    /// 特价金额
    /// </summary>
    public decimal SpecialPrice { get; set; }

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!ApplicableProductIds.Any())
            return false;

        // 验证商品是否在购物车中
        if (!ValidateSpecialPriceProductsInCart(cart, ApplicableProductIds))
            return false;

        // 检查购买条件
        return CheckBuyConditions(cart);
    }

    /// <summary>
    /// 检查购买条件
    /// </summary>
    private bool CheckBuyConditions(ShoppingCart cart)
    {
        var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);
        return totalQuantity >= MinQuantity;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyUnifiedSpecialPrice(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用统一特价促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用统一特价促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyUnifiedSpecialPrice(
        ShoppingCart cart,
        int applicationCount
    )
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var specialPriceRecords = new List<GiftItem>(); // 特价记录

        // 获取所有适用的商品项
        var applicableItems = cart
            .Items.Where(x => ApplicableProductIds.Contains(x.Product.Id) && x.Quantity > 0)
            .ToList();

        if (!applicableItems.Any())
            return (0m, consumedItems, specialPriceRecords);

        var promotion = new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType
        };

        // 根据策略排序商品（决定哪些商品优先享受特价）
        var sortedItems = SortItemsByStrategy(applicableItems);

        for (int app = 0; app < applicationCount; app++)
        {
            // 计算本次应用能享受特价的商品数量
            var specialPriceQuantity = CalculateSpecialPriceQuantity(cart, app);
            if (specialPriceQuantity <= 0)
                break;

            var strategyDescription =
                SpecialPriceSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                    ? "客户利益最大化"
                    : "商家利益最大化";

            var processedQuantity = 0;

            // 按排序顺序为商品应用特价，直到达到特价数量限制
            foreach (var item in sortedItems)
            {
                if (processedQuantity >= specialPriceQuantity)
                    break;

                var remainingSpecialPriceQuantity = specialPriceQuantity - processedQuantity;
                var currentItemSpecialQuantity = Math.Min(
                    item.Quantity,
                    remainingSpecialPriceQuantity
                );

                if (currentItemSpecialQuantity > 0)
                {
                    var originalPrice = item.UnitPrice;
                    var specialPrice = SpecialPrice;
                    var discountPerItem = originalPrice - specialPrice;
                    var totalDiscountForThisItem = discountPerItem * currentItemSpecialQuantity;

                    if (discountPerItem > 0)
                    {
                        totalDiscountAmount += totalDiscountForThisItem;

                        // 修改商品的实际单价（混合定价：部分特价，部分原价）
                        ApplyMixedPricingToCartItem(
                            item,
                            currentItemSpecialQuantity,
                            specialPrice,
                            promotion,
                            $"统一特价：满{MinQuantity}件特价{specialPrice:C}（第{app + 1}次应用，{strategyDescription}）"
                        );

                        // 记录特价
                        specialPriceRecords.Add(
                            CreateSpecialPriceRecord(
                                item.Product.Id,
                                item.Product.Name,
                                currentItemSpecialQuantity,
                                totalDiscountForThisItem,
                                $"统一特价：{currentItemSpecialQuantity}件特价{specialPrice:C}，节省{totalDiscountForThisItem:C}"
                            )
                        );

                        // 记录消耗的商品
                        var existingConsumed = consumedItems.FirstOrDefault(x =>
                            x.ProductId == item.Product.Id
                        );
                        if (existingConsumed != null)
                        {
                            existingConsumed.Quantity += currentItemSpecialQuantity;
                        }
                        else
                        {
                            consumedItems.Add(
                                new ConsumedItem
                                {
                                    ProductId = item.Product.Id,
                                    ProductName = item.Product.Name,
                                    Quantity = currentItemSpecialQuantity,
                                    UnitPrice = originalPrice
                                }
                            );
                        }

                        processedQuantity += currentItemSpecialQuantity;
                    }
                }
            }
        }

        return (totalDiscountAmount, consumedItems, specialPriceRecords);
    }

    /// <summary>
    /// 计算本次应用能享受特价的商品数量
    /// </summary>
    private int CalculateSpecialPriceQuantity(ShoppingCart cart, int currentApplication)
    {
        if (!IsRepeatable && currentApplication > 0)
            return 0; // 不翻倍且已经应用过，不再享受特价

        // 如果是翻倍的，每次应用可以享受特价的数量就是最小要求数量
        // 如果不翻倍，只有第一次应用时才能享受特价
        return MinQuantity;
    }

    /// <summary>
    /// 为购物车商品应用混合定价（部分特价，部分原价）
    /// </summary>
    private void ApplyMixedPricingToCartItem(
        CartItem cartItem,
        int specialPriceQuantity,
        decimal specialPrice,
        AppliedPromotion promotion,
        string description
    )
    {
        var originalPrice = cartItem.UnitPrice;
        var totalQuantity = cartItem.Quantity;
        var regularQuantity = totalQuantity - specialPriceQuantity;

        // 计算加权平均价格：(特价数量 × 特价 + 正常数量 × 原价) / 总数量
        var weightedAveragePrice =
            (specialPriceQuantity * specialPrice + regularQuantity * originalPrice) / totalQuantity;

        // 更新商品的实际单价
        cartItem.ActualUnitPrice = weightedAveragePrice;

        // 记录促销详情
        var discountAmount = (originalPrice - specialPrice) * specialPriceQuantity;
        var promotionDetail = new ItemPromotionDetail
        {
            RuleId = promotion.RuleId,
            RuleName = promotion.RuleName,
            PromotionType = promotion.PromotionType,
            DiscountAmount = discountAmount,
            Description = $"{description}（{specialPriceQuantity}件特价，{regularQuantity}件原价）",
            IsGiftRelated = false
        };

        cartItem.AddPromotionDetail(promotionDetail);
    }

    /// <summary>
    /// 修正最大应用次数计算逻辑
    /// </summary>
    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = 0;

        {
            var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);

            if (IsRepeatable)
            {
                // 翻倍模式：每满足一次最小数量要求，就可以有MinQuantity件商品享受特价
                maxApplications = totalQuantity / MinQuantity;
            }
            else
            {
                // 不翻倍模式：只要满足最小数量要求，就可以有MinQuantity件商品享受特价（只应用一次）
                maxApplications = totalQuantity >= MinQuantity ? 1 : 0;
            }
        }

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }
}
