using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.DiscountRules;

/// <summary>
/// 自由组合折扣规则 [OK]
/// 针对多类商品，满X件或X元，享受对应的各自的折扣
/// 场景案例：A商品吊牌、零售价为1000元，买1件0.8折，2件0.7折，3件0.6折；
///           B商品吊牌、零售价为1000元，买1件0.9折 ，2件0.8折，3件0.7折；
///           C商品吊牌、零售价为99元，买1件0.9折，2件0.85折，3件0.8折。
///           上述三种商品自由组合，只要数量满足2件，就能各自享受满2件时对应的折扣；
///           只要满足数量为3件，则能各自享受满3件时对应的折扣。
///   购买1件A、1件B,1件C：1000*0.6+1000*0.7+99*0.8=1379.2元 （因为满足了3件的条件）
///   购买1件A、2件B：1000*0.6+1000*0.7+1000*0.7 = 2000元
///   购买1件A、1件C：1000*0.7+99*0.85=784.15元
///   购买1件A、1件B、2件C：1000*0.6+1000*0.7+99*2*0.8=1458.4元 （因为满足了大于等于3件的条件，总4件）
/// </summary>
public class FreeFormDiscountRule : BaseDiscountRule
{
    public override string RuleType => "FreeFormDiscount";

    /// <summary>
    /// 商品折扣配置列表
    /// </summary>
    public List<ProductDiscountConfig> ProductConfigs { get; set; } = [];

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!ProductConfigs.Any())
            return false;

        // 验证商品是否在购物车中
        var allProductIds = ProductConfigs.SelectMany(c => c.ProductIds).ToList();
        if (!ValidateDiscountProductsInCart(cart, allProductIds))
            return false;

        // 检查是否满足最低组合条件（支持按数量或按金额）
        var isByAmount = IsConfigurationByAmount();

        if (isByAmount)
        {
            // 按金额判断
            var totalAmount = CalculateTotalAmount(cart, allProductIds);
            var minRequiredAmount = ProductConfigs.SelectMany(c => c.DiscountTiers).Min(t => t.MinAmount);
            return totalAmount >= minRequiredAmount;
        }
        else
        {
            // 按数量判断
            var totalQuantity = CalculateTotalQuantity(cart, allProductIds);
            var minRequiredQuantity = ProductConfigs.SelectMany(c => c.DiscountTiers).Min(t => t.MinQuantity);
            return totalQuantity >= minRequiredQuantity;
        }
    }

    /// <summary>
    /// 判断配置是否按金额计算
    /// </summary>
    private bool IsConfigurationByAmount()
    {
        return ProductConfigs.Any() &&
               ProductConfigs.All(c => c.DiscountTiers.All(t => t.MinAmount > 0 && t.MinQuantity == 0));
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        // 自由组合折扣通常只应用一次，因为会根据总数量给每个商品应用对应折扣
        return 1;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyFreeFormDiscount(cart);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用自由组合折扣促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }


    /// <summary>
    /// 应用自由组合折扣促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyFreeFormDiscount(ShoppingCart cart)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var discountRecords = new List<GiftItem>(); // 折扣记录

        // 判断是按数量还是按金额计算
        var isByAmount = IsConfigurationByAmount();

        // 计算所有参与商品的总数量或总金额
        var allProductIds = ProductConfigs.SelectMany(c => c.ProductIds).ToList();
        var totalQuantity = isByAmount ? 0 : CalculateTotalQuantity(cart, allProductIds);
        var totalAmount = isByAmount ? CalculateTotalAmount(cart, allProductIds) : 0m;

        var promotion = new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType
        };

        // 为每个商品配置应用对应的折扣
        foreach (var config in ProductConfigs)
        {
            // 获取该商品的购物车项
            var productItems = cart.Items
                .Where(x => x.Quantity > 0 && config.ProductIds.Contains(x.Product.Id))
                .ToList();

            if (!productItems.Any()) continue;

            // 根据总数量或总金额确定该商品应享受的折扣梯度
            DiscountTier? applicableTier = null;

            if (isByAmount)
            {
                // 按金额判断梯度
                applicableTier = config.DiscountTiers
                    .Where(t => totalAmount >= t.MinAmount)
                    .OrderByDescending(t => t.MinAmount)
                    .FirstOrDefault();
            }
            else
            {
                // 按数量判断梯度
                applicableTier = config.DiscountTiers
                    .Where(t => totalQuantity >= t.MinQuantity)
                    .OrderByDescending(t => t.MinQuantity)
                    .FirstOrDefault();
            }

            if (applicableTier == null) continue;

            // 根据策略排序商品
            var sortedItems = SortItemsByStrategy(productItems);

            // 对该商品的所有项应用折扣
            foreach (var item in sortedItems)
            {
                var originalPrice = item.UnitPrice;
                var discountedPrice = originalPrice * applicableTier.DiscountRate;
                var discountAmount = (originalPrice - discountedPrice) * item.Quantity;

                if (discountAmount > 0)
                {
                    totalDiscountAmount += discountAmount;

                    var strategyDescription = DiscountSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                        ? "客户利益最大化"
                        : "商家利益最大化";

                    var conditionDescription = isByAmount
                        ? $"总金额满{totalAmount:C}"
                        : $"总{totalQuantity}件";

                    var description = $"自由组合折扣：{conditionDescription}享{applicableTier.Description}，{applicableTier.DiscountRate:P1}折，节省{discountAmount:C}（{strategyDescription}）";

                    // 应用折扣到商品项
                    ApplyDiscountToCartItem(item, applicableTier.DiscountRate, promotion, description);

                    // 记录折扣
                    discountRecords.Add(CreateDiscountRecord(
                        item.Product.Id,
                        item.Product.Name,
                        item.Quantity,
                        discountAmount,
                        description
                    ));

                    // 记录消耗的商品
                    var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == item.Product.Id);
                    if (existingConsumed != null)
                    {
                        existingConsumed.Quantity += item.Quantity;
                    }
                    else
                    {
                        consumedItems.Add(new ConsumedItem
                        {
                            ProductId = item.Product.Id,
                            ProductName = item.Product.Name,
                            Quantity = item.Quantity,
                            UnitPrice = originalPrice
                        });
                    }
                }
            }
        }

        return (totalDiscountAmount, consumedItems, discountRecords);
    }
}

/// <summary>
/// 商品折扣配置
/// </summary>
public class ProductDiscountConfig
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public List<string> ProductIds { get; set; } = [];

    /// <summary>
    /// 该商品的折扣梯度列表
    /// </summary>
    public List<DiscountTier> DiscountTiers { get; set; } = [];
}
