# PE2 促销引擎单元测试项目

## 项目概述

本项目为 POSPE2 促销引擎提供全面的单元测试和集成测试，确保促销规则计算的准确性、性能和可靠性。

## 项目结构

```
PE2.Tests/
├── PE2.Tests.csproj          # 测试项目配置文件
├── README.md                  # 项目说明文档
├── Infrastructure/            # 测试基础设施
│   ├── TestBase.cs           # 测试基类，提供通用断言和辅助方法
│   └── TestDataGenerator.cs  # 测试数据生成器
├── Unit/                      # 单元测试
│   └── PromotionCalculatorTests.cs  # 促销计算器核心算法测试
├── Integration/               # 集成测试
│   └── PromotionEngineServiceTests.cs  # 促销引擎服务集成测试
└── Rules/                     # 促销规则测试
    ├── UnifiedGiftRuleTests.cs      # 统一买赠规则测试
    ├── TieredGiftRuleTests.cs       # 阶梯买赠规则测试
    ├── UnifiedDiscountRuleTests.cs  # 统一打折规则测试
    ├── TieredDiscountRuleTests.cs   # 阶梯打折规则测试
    ├── SpecialPriceRuleTests.cs     # 特价规则测试
    ├── UnifiedCashOffRuleTests.cs   # 统一减现规则测试
    ├── TieredCashOffRuleTests.cs    # 阶梯减现规则测试
    └── BuyFreeRuleTests.cs          # 买免规则测试
```

## 测试覆盖范围

### 1. 促销规则测试 (Rules/)

#### 统一买赠规则 (UnifiedGiftRule)
- ✅ 基础买赠功能测试
- ✅ 多次应用场景
- ✅ 部分赠品可用处理
- ✅ 边界条件测试
- ✅ 复杂买赠条件
- ✅ 性能测试
- ✅ 规则配置验证

#### 阶梯买赠规则 (TieredGiftRule)
- ✅ 基础阶梯功能
- ✅ 阶梯优化选择
- ✅ 赠品不足回退
- ✅ 多次应用优化
- ✅ 混合阶梯应用
- ✅ 边界条件处理
- ✅ 性能和配置测试

#### 统一打折规则 (UnifiedDiscountRule)
- ✅ 基础折扣功能
- ✅ 多商品折扣条件
- ✅ 部分商品可用
- ✅ 边界条件测试
- ✅ 复杂折扣场景
- ✅ 性能测试

#### 阶梯打折规则 (TieredDiscountRule)
- ✅ 基础阶梯折扣
- ✅ 阶梯优化选择
- ✅ 多商品阶梯
- ✅ 边界条件处理
- ✅ 复杂阶梯场景
- ✅ 性能测试

#### 特价规则 (SpecialPriceRule)
- ✅ 基础特价功能
- ✅ 多商品特价
- ✅ 数量相关测试
- ✅ 边界条件处理
- ✅ 复杂特价场景
- ✅ 性能测试

#### 统一减现规则 (UnifiedCashOffRule)
- ✅ 基础减现功能
- ✅ 多阶梯减现
- ✅ 指定商品减现
- ✅ 边界条件处理
- ✅ 复杂减现场景
- ✅ 性能测试

#### 阶梯减现规则 (TieredCashOffRule)
- ✅ 基础阶梯减现
- ✅ 阶梯优化选择
- ✅ 多商品阶梯减现
- ✅ 边界条件处理
- ✅ 复杂阶梯场景
- ✅ 性能测试

#### 买免规则 (BuyFreeRule)
- ✅ 基础买免功能
- ✅ 多次应用
- ✅ 不同商品买免
- ✅ 复杂买免条件
- ✅ 边界条件处理
- ✅ 性能测试

### 2. 核心算法测试 (Unit/)

#### 促销计算器 (PromotionCalculator)
- ✅ 回溯算法测试
- ✅ 规则筛选逻辑
- ✅ 折扣分摊算法
- ✅ 最优组合选择
- ✅ 互斥规则处理
- ✅ 可叠加规则处理
- ✅ 性能优化测试
- ✅ 边界条件处理

### 3. 集成测试 (Integration/)

#### 促销引擎服务 (PromotionEngineService)
- ✅ 完整促销计算流程
- ✅ 多规则组合优化
- ✅ 促销预览功能
- ✅ 指定规则应用
- ✅ 复杂场景集成
- ✅ 冲突规则解决
- ✅ 可叠加规则处理
- ✅ 客户类型限制
- ✅ 并发安全测试
- ✅ 数据一致性验证

## 测试分类和优先级

### 测试分类 (Category)
- **Unit**: 单元测试，测试单个组件的功能
- **Integration**: 集成测试，测试组件间的协作
- **Rules**: 规则测试，测试具体促销规则的逻辑

### 测试优先级 (Priority)
- **High**: 核心功能测试，必须通过
- **Medium**: 重要功能测试，建议通过
- **Low**: 边界条件和性能测试，可选通过

## 运行测试

### 运行所有测试
```bash
dotnet test
```

### 按分类运行测试
```bash
# 运行单元测试
dotnet test --filter Category=Unit

# 运行集成测试
dotnet test --filter Category=Integration

# 运行规则测试
dotnet test --filter Category=Rules
```

### 按优先级运行测试
```bash
# 运行高优先级测试
dotnet test --filter Priority=High

# 运行中等优先级测试
dotnet test --filter Priority=Medium

# 运行低优先级测试
dotnet test --filter Priority=Low
```

### 运行特定规则测试
```bash
# 运行买赠规则测试
dotnet test --filter ClassName~GiftRule

# 运行打折规则测试
dotnet test --filter ClassName~DiscountRule

# 运行减现规则测试
dotnet test --filter ClassName~CashOffRule
```

### 生成测试报告
```bash
# 生成覆盖率报告
dotnet test --collect:"XPlat Code Coverage"

# 生成详细测试报告
dotnet test --logger:"trx;LogFileName=TestResults.trx"
```

## 测试数据

### 标准测试商品
- **商品A**: ID="A", 名称="商品A", 价格=30.00元
- **商品B**: ID="B", 名称="商品B", 价格=20.00元
- **商品C**: ID="C", 名称="商品C", 价格=15.00元
- **商品D**: ID="D", 名称="商品D", 价格=10.00元

### 标准测试规则
- **买2送1**: 买2件A送1件B
- **买3打8折**: 买3件A享受20%折扣
- **满100减20**: 购物满100元减20元
- **特价商品**: 商品A特价25元
- **买2免1**: 买2件A免1件A

### 测试购物车类型
- **空购物车**: 无商品的购物车
- **简单购物车**: 包含1-3种商品的购物车
- **复杂购物车**: 包含多种商品和数量的购物车
- **大型购物车**: 包含大量商品的性能测试购物车

## 断言和验证

### 通用断言方法
- `AssertPromotionResult()`: 验证促销结果的基本有效性
- `AssertRuleApplication()`: 验证规则应用的正确性
- `AssertIgnoredRule()`: 验证规则忽略的原因
- `AssertAmountEqual()`: 验证金额相等性（考虑精度）
- `AssertCartConsistency()`: 验证购物车数据一致性

### 性能断言
- `MeasureExecutionTime()`: 测量代码执行时间
- `AssertPerformance()`: 验证性能指标

### 数据验证
- `ValidateTestData()`: 验证测试数据有效性
- `ValidateRuleConfiguration()`: 验证规则配置正确性

## 日志和调试

### 测试输出
- 购物车详情输出
- 促销结果详情输出
- 规则应用过程输出
- 性能测试结果输出

### 调试辅助
- 详细的断言失败信息
- 测试数据状态输出
- 执行过程跟踪

## 最佳实践

### 测试编写原则
1. **单一职责**: 每个测试方法只测试一个功能点
2. **独立性**: 测试之间不应有依赖关系
3. **可重复**: 测试结果应该是确定和可重复的
4. **清晰命名**: 测试方法名应清楚表达测试意图
5. **完整覆盖**: 包含正常流程、边界条件和异常情况

### 测试数据管理
1. 使用 `TestDataGenerator` 生成标准化测试数据
2. 避免硬编码测试数据
3. 为不同测试场景准备专门的数据集
4. 确保测试数据的一致性和有效性

### 断言策略
1. 使用具体的断言方法而非通用断言
2. 提供清晰的断言失败消息
3. 验证关键业务逻辑和数据一致性
4. 包含性能和边界条件验证

## 持续集成

### CI/CD 集成
- 所有测试应在 CI 管道中自动运行
- 高优先级测试失败应阻止部署
- 定期运行完整测试套件
- 监控测试覆盖率和性能指标

### 测试维护
- 定期更新测试数据和场景
- 及时修复失败的测试
- 添加新功能的对应测试
- 重构时同步更新测试

## 故障排除

### 常见问题
1. **测试数据不一致**: 检查 `TestDataGenerator` 的数据生成逻辑
2. **金额计算误差**: 使用 `AssertAmountEqual` 方法处理精度问题
3. **性能测试失败**: 调整性能阈值或优化测试环境
4. **并发测试不稳定**: 检查线程安全和资源竞争问题

### 调试技巧
1. 使用测试输出查看详细信息
2. 单独运行失败的测试方法
3. 检查测试数据的有效性
4. 验证业务逻辑的正确性

## 贡献指南

### 添加新测试
1. 确定测试分类和优先级
2. 使用标准的测试数据和断言方法
3. 添加适当的测试特性标记
4. 编写清晰的测试文档

### 修改现有测试
1. 保持测试的向后兼容性
2. 更新相关的测试文档
3. 确保所有相关测试仍然通过
4. 考虑对其他测试的影响

---

**注意**: 本测试项目是 POSPE2 促销引擎质量保证的重要组成部分，请确保所有修改都经过充分测试和验证。