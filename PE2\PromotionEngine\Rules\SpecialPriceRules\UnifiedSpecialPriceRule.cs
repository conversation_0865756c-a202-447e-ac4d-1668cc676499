using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.SpecialPriceRules;

/// <summary>
/// 统一特价规则
/// 针对某一类商品，满X件，立享特价Y元
/// 场景：A商品满1件时，特价100元（不翻倍）；B商品满1件时，特价100元（最多翻2倍）
/// </summary>
public class UnifiedSpecialPriceRule : BaseSpecialPriceRule
{
    public override string RuleType => "UnifiedSpecialPrice";

    /// <summary>
    /// 适用的商品ID列表
    /// </summary>
    public List<string> ApplicableProductIds { get; set; } = new();

    /// <summary>
    /// 最小数量要求
    /// </summary>
    public int MinQuantity { get; set; }

    /// <summary>
    /// 最小金额要求
    /// </summary>
    public decimal MinAmount { get; set; }

    /// <summary>
    /// 特价金额
    /// </summary>
    public decimal SpecialPrice { get; set; }

    /// <summary>
    /// 是否按金额计算（如果为true，则使用MinAmount；否则使用MinQuantity）
    /// </summary>
    public bool IsByAmount { get; set; } = false;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!ApplicableProductIds.Any())
            return false;

        // 验证商品是否在购物车中
        if (!ValidateSpecialPriceProductsInCart(cart, ApplicableProductIds))
            return false;

        // 检查购买条件
        return CheckBuyConditions(cart);
    }

    /// <summary>
    /// 检查购买条件
    /// </summary>
    private bool CheckBuyConditions(ShoppingCart cart)
    {
        if (IsByAmount)
        {
            var totalAmount = CalculateTotalAmount(cart, ApplicableProductIds);
            return totalAmount >= MinAmount;
        }
        else
        {
            var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);
            return totalQuantity >= MinQuantity;
        }
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = 0;

        if (IsByAmount)
        {
            var totalAmount = CalculateTotalAmount(cart, ApplicableProductIds);

            maxApplications = IsRepeatable
                ? (int)(totalAmount / MinAmount)
                : (totalAmount >= MinAmount ? 1 : 0);
        }
        else
        {
            var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);

            maxApplications = IsRepeatable
                ? totalQuantity / MinQuantity
                : (totalQuantity >= MinQuantity ? 1 : 0);
        }

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyUnifiedSpecialPrice(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用统一特价促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用统一特价促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyUnifiedSpecialPrice(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var specialPriceRecords = new List<GiftItem>(); // 特价记录

        // 获取所有适用的商品项
        var applicableItems = cart.Items
            .Where(x => ApplicableProductIds.Contains(x.Product.Id) && x.Quantity > 0)
            .ToList();

        if (!applicableItems.Any())
            return (0m, consumedItems, specialPriceRecords);

        var promotion = new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType
        };

        for (int app = 0; app < applicationCount; app++)
        {
            // 计算本次应用的特价总金额
            var currentSpecialPrice = SpecialPrice;

            // 根据策略排序商品
            var sortedItems = SortItemsByStrategy(applicableItems);

            // 按比例分摊特价到所有适用商品
            var strategyDescription = SpecialPriceSelectionStrategy == SpecialPriceSelectionStrategy.CustomerBenefit
                ? "客户利益最大化"
                : "商家利益最大化";

            var description = $"统一特价：特价{currentSpecialPrice:C}（第{app + 1}次应用，{strategyDescription}）";

            // 计算折扣前的总金额
            var totalOriginalAmount = sortedItems.Sum(x => x.SubTotal);
            var totalDiscountForThisApp = totalOriginalAmount - currentSpecialPrice;

            if (totalDiscountForThisApp > 0)
            {
                totalDiscountAmount += totalDiscountForThisApp;

                // 按比例分摊特价
                ApplyProportionalSpecialPrice(sortedItems, currentSpecialPrice, promotion, description);

                // 记录特价
                specialPriceRecords.Add(CreateSpecialPriceRecord(
                    string.Join(",", ApplicableProductIds),
                    "统一特价商品",
                    sortedItems.Sum(x => x.Quantity),
                    totalDiscountForThisApp,
                    description
                ));

                // 记录消耗的商品
                foreach (var item in sortedItems)
                {
                    var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == item.Product.Id);
                    if (existingConsumed != null)
                    {
                        existingConsumed.Quantity += item.Quantity;
                    }
                    else
                    {
                        consumedItems.Add(new ConsumedItem
                        {
                            ProductId = item.Product.Id,
                            ProductName = item.Product.Name,
                            Quantity = item.Quantity,
                            UnitPrice = item.UnitPrice
                        });
                    }
                }
            }
        }

        return (totalDiscountAmount, consumedItems, specialPriceRecords);
    }
}
