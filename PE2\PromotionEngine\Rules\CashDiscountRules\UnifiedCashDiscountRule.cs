using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.CashDiscountRules;

/// <summary>
/// 统一减现规则 [OK]
/// 针对某一类商品，满X件或X元，立减Y元
/// 场景案例：A商品吊牌、零售价均为1000元;购买A商品，满足数量大于等于1件时，应收金额立减10元。
/// 允许翻倍则买A数量为2件时，优惠金额20元；买A数量3件时，优惠金额30元。
/// </summary>
public class UnifiedCashDiscountRule : BaseCashDiscountRule
{
    public override string RuleType => "UnifiedCashDiscount";

    /// <summary>
    /// 适用的商品ID列表
    /// </summary>
    public List<string> ApplicableProductIds { get; set; } = new();

    /// <summary>
    /// 最小数量要求
    /// </summary>
    public int MinQuantity { get; set; }

    /// <summary>
    /// 最小金额要求
    /// </summary>
    public decimal MinAmount { get; set; }

    /// <summary>
    /// 减现金额
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// 是否按金额计算（如果为true，则使用MinAmount；否则使用MinQuantity）
    /// </summary>
    public bool IsByAmount => MinAmount > 0;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!ApplicableProductIds.Any())
            return false;

        // 验证商品是否在购物车中
        if (!ValidateCashDiscountProductsInCart(cart, ApplicableProductIds))
            return false;

        // 检查购买条件
        return CheckBuyConditions(cart);
    }

    /// <summary>
    /// 检查购买条件
    /// </summary>
    private bool CheckBuyConditions(ShoppingCart cart)
    {
        if (IsByAmount)
        {
            var totalAmount = CalculateTotalAmount(cart, ApplicableProductIds);
            return totalAmount >= MinAmount;
        }
        else
        {
            var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);
            return totalQuantity >= MinQuantity;
        }
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = 0;

        if (IsByAmount)
        {
            var totalAmount = CalculateTotalAmount(cart, ApplicableProductIds);

            maxApplications = IsRepeatable
                ? (int)(totalAmount / MinAmount)
                : (totalAmount >= MinAmount ? 1 : 0);
        }
        else
        {
            var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);

            maxApplications = IsRepeatable
                ? totalQuantity / MinQuantity
                : (totalQuantity >= MinQuantity ? 1 : 0);
        }

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyUnifiedCashDiscount(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用统一减现促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用统一减现促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyUnifiedCashDiscount(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var discountRecords = new List<GiftItem>(); // 减现记录

        for (int app = 0; app < applicationCount; app++)
        {
            // 计算本次应用的减现金额
            var currentDiscountAmount = DiscountAmount;
            totalDiscountAmount += currentDiscountAmount;

            // 消耗购买条件商品（记录消耗但不修改购物车）
            var requiredQuantity = IsByAmount ? 0 : MinQuantity;
            if (requiredQuantity > 0)
            {
                var consumed = ConsumeConditionProducts(cart, ApplicableProductIds, requiredQuantity);
                foreach (var item in consumed)
                {
                    var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == item.ProductId);
                    if (existingConsumed != null)
                    {
                        existingConsumed.Quantity += item.Quantity;
                    }
                    else
                    {
                        consumedItems.Add(item);
                    }
                }
            }

            // 创建减现记录
            var description = IsByAmount
                ? $"统一减现：满{MinAmount:C}立减{currentDiscountAmount:C}（第{app + 1}次应用）"
                : $"统一减现：满{MinQuantity}件立减{currentDiscountAmount:C}（第{app + 1}次应用）";

            discountRecords.Add(CreateCashDiscountRecord(currentDiscountAmount, description));
        }

        // 应用减现到购物车
        var promotion = new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType
        };
        ApplyCashDiscountToCart(cart, totalDiscountAmount, promotion, ApplicableProductIds);

        return (totalDiscountAmount, consumedItems, discountRecords);
    }
}
