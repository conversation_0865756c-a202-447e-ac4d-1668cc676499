using PE2.Models;
using PE2.PromotionEngine.Models;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Rules.BuyFreeRules;

/// <summary>
/// 商品买免规则
/// 针对某一类商品，满X免Y
/// 场景：购买A商品3件时，免1件
/// </summary>
public class ProductBuyFreeRule : BaseBuyFreeRule
{
    public override string RuleType => "ProductBuyFree";

    /// <summary>
    /// 适用的商品ID列表
    /// </summary>
    public List<string> ApplicableProductIds { get; set; } = new();

    /// <summary>
    /// 购买数量要求
    /// </summary>
    public int BuyQuantity { get; set; }

    /// <summary>
    /// 免费数量
    /// </summary>
    public int FreeQuantity { get; set; }

    /// <summary>
    /// 是否按金额计算（如果为true，则BuyQuantity表示金额）
    /// </summary>
    public bool IsByAmount { get; set; } = false;

    /// <summary>
    /// 最小金额要求（当IsByAmount为true时使用）
    /// </summary>
    public decimal MinAmount { get; set; }

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!ApplicableProductIds.Any())
            return false;

        // 验证商品是否在购物车中
        if (!ValidateBuyFreeProductsInCart(cart, ApplicableProductIds))
            return false;

        // 检查购买条件
        return CheckBuyConditions(cart);
    }

    /// <summary>
    /// 检查购买条件
    /// </summary>
    private bool CheckBuyConditions(ShoppingCart cart)
    {
        if (IsByAmount)
        {
            var totalAmount = ApplicableProductIds.Sum(id =>
                cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));
            return totalAmount >= MinAmount;
        }
        else
        {
            var totalQuantity = ApplicableProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
            return totalQuantity >= BuyQuantity;
        }
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = 0;

        if (IsByAmount)
        {
            var totalAmount = ApplicableProductIds.Sum(id =>
                cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));
            
            maxApplications = IsRepeatable 
                ? (int)(totalAmount / MinAmount)
                : (totalAmount >= MinAmount ? 1 : 0);
        }
        else
        {
            var totalQuantity = ApplicableProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
            
            // 考虑买免比例：需要确保有足够的商品既满足购买条件又能提供免费商品
            var requiredTotalQuantity = BuyQuantity + FreeQuantity;
            
            maxApplications = IsRepeatable 
                ? totalQuantity / requiredTotalQuantity
                : (totalQuantity >= requiredTotalQuantity ? 1 : 0);
        }

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyProductBuyFree(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用买免促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用商品买免促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyProductBuyFree(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var freeItems = new List<GiftItem>(); // 免费商品记录为GiftItem

        for (int app = 0; app < applicationCount; app++)
        {
            // 收集所有适用的商品
            var availableProducts = CollectAvailableProducts(cart);
            if (!availableProducts.Any()) break;

            // 消耗购买条件商品
            var buyProducts = ConsumeBuyProducts(availableProducts, consumedItems);
            if (!buyProducts.Any()) break;

            // 选择免费商品
            var remainingProducts = availableProducts
                .Select(p => (p.ProductId, Math.Max(0, p.Quantity - buyProducts.Where(b => b.ProductId == p.ProductId).Sum(b => b.Quantity)), p.UnitPrice))
                .Where(p => p.Item2 > 0)
                .ToList();

            var freeProducts = SelectFreeProducts(remainingProducts, FreeQuantity);
            if (!freeProducts.Any()) break;

            // 计算折扣金额
            var discountAmount = CalculateFreeDiscount(freeProducts);
            totalDiscountAmount += discountAmount;

            // 记录免费商品
            foreach (var freeProduct in freeProducts)
            {
                var strategyDescription = FreeItemSelectionStrategy == FreeItemSelectionStrategy.CustomerBenefit
                    ? "客户利益最大化"
                    : "商家利益最大化";

                var existingFree = freeItems.FirstOrDefault(x => x.ProductId == freeProduct.ProductId);
                if (existingFree != null)
                {
                    existingFree.Quantity += freeProduct.Quantity;
                    existingFree.Value += freeProduct.Quantity * freeProduct.UnitPrice;
                }
                else
                {
                    var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == freeProduct.ProductId);
                    freeItems.Add(new GiftItem
                    {
                        ProductId = freeProduct.ProductId,
                        ProductName = cartItem?.Product.Name ?? freeProduct.ProductId,
                        Quantity = freeProduct.Quantity,
                        Value = freeProduct.Quantity * freeProduct.UnitPrice,
                        Description = $"买{BuyQuantity}免{FreeQuantity}：免费{freeProduct.Quantity}件，节省{freeProduct.Quantity * freeProduct.UnitPrice:C}（{strategyDescription}）"
                    });
                }
            }

            // 应用免费效果到购物车
            var promotion = new AppliedPromotion
            {
                RuleId = Id,
                RuleName = Name,
                PromotionType = RuleType
            };
            ApplyFreeEffectToCart(cart, freeProducts, promotion);

            // 从购物车中移除消耗的商品数量
            foreach (var buyProduct in buyProducts)
            {
                var cartItems = cart.Items.Where(x => x.Product.Id == buyProduct.ProductId).ToList();
                var remainingQuantity = buyProduct.Quantity;

                foreach (var cartItem in cartItems)
                {
                    if (remainingQuantity <= 0) break;

                    var deductQuantity = Math.Min(cartItem.Quantity, remainingQuantity);
                    cartItem.Quantity -= deductQuantity;
                    remainingQuantity -= deductQuantity;
                }
            }
        }

        // 清理数量为0的购物车项
        cart.Items.RemoveAll(x => x.Quantity <= 0);

        return (totalDiscountAmount, consumedItems, freeItems);
    }

    /// <summary>
    /// 收集可用的商品
    /// </summary>
    private List<(string ProductId, int Quantity, decimal UnitPrice)> CollectAvailableProducts(ShoppingCart cart)
    {
        var products = new List<(string ProductId, int Quantity, decimal UnitPrice)>();

        foreach (var productId in ApplicableProductIds)
        {
            var cartItems = cart.Items.Where(x => x.Product.Id == productId && x.Quantity > 0).ToList();
            foreach (var cartItem in cartItems)
            {
                products.Add((productId, cartItem.Quantity, cartItem.UnitPrice));
            }
        }

        return products;
    }

    /// <summary>
    /// 消耗购买条件商品
    /// </summary>
    private List<(string ProductId, int Quantity, decimal UnitPrice)> ConsumeBuyProducts(
        List<(string ProductId, int Quantity, decimal UnitPrice)> availableProducts, 
        List<ConsumedItem> consumedItems)
    {
        var buyProducts = new List<(string ProductId, int Quantity, decimal UnitPrice)>();
        var remainingBuyQuantity = BuyQuantity;

        // 按价格排序：商家利益最大化时优先消耗高价商品作为购买条件
        var sortedProducts = FreeItemSelectionStrategy == FreeItemSelectionStrategy.MerchantBenefit
            ? availableProducts.OrderByDescending(p => p.UnitPrice)
            : availableProducts.OrderBy(p => p.UnitPrice);

        foreach (var product in sortedProducts)
        {
            if (remainingBuyQuantity <= 0) break;

            var buyQuantity = Math.Min(product.Quantity, remainingBuyQuantity);
            if (buyQuantity > 0)
            {
                buyProducts.Add((product.ProductId, buyQuantity, product.UnitPrice));

                // 记录消耗的商品
                var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == product.ProductId);
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += buyQuantity;
                }
                else
                {
                    consumedItems.Add(new ConsumedItem
                    {
                        ProductId = product.ProductId,
                        ProductName = product.ProductId, // 这里可以从购物车获取实际名称
                        Quantity = buyQuantity,
                        UnitPrice = product.UnitPrice
                    });
                }

                remainingBuyQuantity -= buyQuantity;
            }
        }

        return buyProducts;
    }
}
