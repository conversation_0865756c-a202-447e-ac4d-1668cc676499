[{"$type": "BuyXGetY", "id": "CUSTOMER_BENEFIT_GIFT_001", "name": "客户利益最大化 - 购买A送高价值赠品", "description": "购买A商品1件，从B、C、D中选择价值最高的商品赠送（客户利益最大化）", "priority": 20, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftType": "UnifiedGift", "gradientStrategy": "ByGradient", "giftSelectionStrategy": "CustomerBenefit", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1}], "giftConditions": [{"productIds": ["B", "C", "D"], "giftQuantity": 1}], "combinationConditions": [], "gradientGiftConditions": []}, {"$type": "BuyXGetY", "id": "MERCHANT_BENEFIT_GIFT_001", "name": "商家利益最大化 - 购买A送低价值赠品", "description": "购买A商品1件，从B、C、D中选择价值最低的商品赠送（商家利益最大化）", "priority": 20, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftType": "UnifiedGift", "gradientStrategy": "ByGradient", "giftSelectionStrategy": "MerchantBenefit", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1}], "giftConditions": [{"productIds": ["B", "C", "D"], "giftQuantity": 1}], "combinationConditions": [], "gradientGiftConditions": []}, {"$type": "BuyXGetY", "id": "GRADIENT_CUSTOMER_BENEFIT_001", "name": "梯度赠品 - 客户利益最大化", "description": "购买A商品梯度赠送，每个梯度选择价值最高的赠品（客户利益最大化）", "priority": 25, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftType": "GradientGift", "gradientStrategy": "ByGradient", "giftSelectionStrategy": "CustomerBenefit", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1}], "giftConditions": [], "combinationConditions": [], "gradientGiftConditions": [{"gradientLevel": 1, "requiredQuantity": 1, "requiredAmount": 0, "giftProductIds": ["B", "C"], "giftQuantity": 1, "description": "购买1件A从B、C中选择价值最高的送1件"}, {"gradientLevel": 2, "requiredQuantity": 2, "requiredAmount": 0, "giftProductIds": ["D", "E", "F"], "giftQuantity": 1, "description": "购买2件A从D、E、F中选择价值最高的送1件"}]}, {"$type": "BuyXGetY", "id": "GRADIENT_MERCHANT_BENEFIT_001", "name": "梯度赠品 - 商家利益最大化", "description": "购买A商品梯度赠送，每个梯度选择价值最低的赠品（商家利益最大化）", "priority": 25, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftType": "GradientGift", "gradientStrategy": "ByGradient", "giftSelectionStrategy": "MerchantBenefit", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1}], "giftConditions": [], "combinationConditions": [], "gradientGiftConditions": [{"gradientLevel": 1, "requiredQuantity": 1, "requiredAmount": 0, "giftProductIds": ["B", "C"], "giftQuantity": 1, "description": "购买1件A从B、C中选择价值最低的送1件"}, {"gradientLevel": 2, "requiredQuantity": 2, "requiredAmount": 0, "giftProductIds": ["D", "E", "F"], "giftQuantity": 1, "description": "购买2件A从D、E、F中选择价值最低的送1件"}]}, {"$type": "BuyXGetY", "id": "COMBINATION_CUSTOMER_BENEFIT_001", "name": "组合赠品 - 客户利益最大化", "description": "购买A+B组合，从多个候选赠品中选择价值最高的赠送（客户利益最大化）", "priority": 30, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftType": "CombinationGift", "gradientStrategy": "ByGradient", "giftSelectionStrategy": "CustomerBenefit", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "buyConditions": [], "giftConditions": [{"productIds": ["C", "D", "E"], "giftQuantity": 1}], "combinationConditions": [{"productId": "A", "requiredQuantity": 1, "minAmount": 0}, {"productId": "B", "requiredQuantity": 1, "minAmount": 0}], "gradientGiftConditions": []}, {"$type": "BuyXGetY", "id": "COMBINATION_MERCHANT_BENEFIT_001", "name": "组合赠品 - 商家利益最大化", "description": "购买A+B组合，从多个候选赠品中选择价值最低的赠送（商家利益最大化）", "priority": 30, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftType": "CombinationGift", "gradientStrategy": "ByGradient", "giftSelectionStrategy": "MerchantBenefit", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "buyConditions": [], "giftConditions": [{"productIds": ["C", "D", "E"], "giftQuantity": 1}], "combinationConditions": [{"productId": "A", "requiredQuantity": 1, "minAmount": 0}, {"productId": "B", "requiredQuantity": 1, "minAmount": 0}], "gradientGiftConditions": []}, {"$type": "BuyXGetY", "id": "MULTI_GIFT_CUSTOMER_BENEFIT_001", "name": "多赠品选择 - 客户利益最大化", "description": "购买A商品2件，从多个候选赠品中选择价值最高的2件赠送", "priority": 20, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftType": "UnifiedGift", "gradientStrategy": "ByGradient", "giftSelectionStrategy": "CustomerBenefit", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "buyConditions": [{"productIds": ["A"], "requiredQuantity": 2}], "giftConditions": [{"productIds": ["B", "C", "D", "E"], "giftQuantity": 2}], "combinationConditions": [], "gradientGiftConditions": []}, {"$type": "BuyXGetY", "id": "MULTI_GIFT_MERCHANT_BENEFIT_001", "name": "多赠品选择 - 商家利益最大化", "description": "购买A商品2件，从多个候选赠品中选择价值最低的2件赠送", "priority": 20, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftType": "UnifiedGift", "gradientStrategy": "ByGradient", "giftSelectionStrategy": "MerchantBenefit", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "buyConditions": [{"productIds": ["A"], "requiredQuantity": 2}], "giftConditions": [{"productIds": ["B", "C", "D", "E"], "giftQuantity": 2}], "combinationConditions": [], "gradientGiftConditions": []}]