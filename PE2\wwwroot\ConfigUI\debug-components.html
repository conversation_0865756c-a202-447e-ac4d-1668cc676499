<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组件加载调试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
</head>
<body>
    <div id="app" style="padding: 20px;">
        <h1>🔍 组件加载状态检查</h1>
        
        <div style="margin: 20px 0;">
            <h3>📦 核心库检查</h3>
            <p>Vue: {{ typeof Vue !== 'undefined' ? '✅ 已加载' : '❌ 未加载' }}</p>
            <p>ElementPlus: {{ typeof ElementPlus !== 'undefined' ? '✅ 已加载' : '❌ 未加载' }}</p>
            <p>ElementPlusIconsVue: {{ typeof ElementPlusIconsVue !== 'undefined' ? '✅ 已加载' : '❌ 未加载' }}</p>
        </div>
        
        <div style="margin: 20px 0;">
            <h3>🧩 自定义组件检查</h3>
            <p>ProductSelector: {{ typeof ProductSelector !== 'undefined' ? '✅ 已加载' : '❌ 未加载' }}</p>
            <p>ComplexArrayRenderer: {{ typeof ComplexArrayRenderer !== 'undefined' ? '✅ 已加载' : '❌ 未加载' }}</p>
            <p>ConditionConfigRenderer: {{ typeof ConditionConfigRenderer !== 'undefined' ? '✅ 已加载' : '❌ 未加载' }}</p>
            <p>EnhancedConditionRenderer: {{ typeof EnhancedConditionRenderer !== 'undefined' ? '✅ 已加载' : '❌ 未加载' }}</p>
            <p>DynamicFormRenderer: {{ typeof DynamicFormRenderer !== 'undefined' ? '✅ 已加载' : '❌ 未加载' }}</p>
            <p>RuleManager: {{ typeof RuleManager !== 'undefined' ? '✅ 已加载' : '❌ 未加载' }}</p>
            <p>PromotionConfigMainComponentHybrid: {{ typeof PromotionConfigMainComponentHybrid !== 'undefined' ? '✅ 已加载' : '❌ 未加载' }}</p>
        </div>
        
        <div style="margin: 20px 0;">
            <h3>⚙️ 配置文件检查</h3>
            <p>PROMOTION_TYPES: {{ typeof PROMOTION_TYPES !== 'undefined' ? '✅ 已加载' : '❌ 未加载' }}</p>
        </div>
        
        <div style="margin: 20px 0;">
            <h3>🧪 ProductSelector 组件测试</h3>
            <div v-if="showTest">
                <product-selector
                    v-model="testValue"
                    placeholder="测试商品选择器"
                    style="width: 300px;"
                />
                <p>选中值: {{ testValue }}</p>
            </div>
            <el-button @click="testProductSelector" type="primary">测试 ProductSelector</el-button>
        </div>
        
        <div style="margin: 20px 0;">
            <h3>🌐 API 连接测试</h3>
            <el-button @click="testAPI" type="success">测试 API 连接</el-button>
            <p v-if="apiResult">API 测试结果: {{ apiResult }}</p>
        </div>
    </div>

    <!-- 按照主页面的顺序加载脚本 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.min.js"></script>
    
    <script src="./js/config/promotionTypes.js"></script>
    <script src="./js/services/apiService.js"></script>
    <script src="./js/services/metadataService.js"></script>
    <script src="./js/components/ProductSelector.js"></script>
    <script src="./js/components/ConditionConfigRenderer.js"></script>
    <script src="./js/components/ComplexArrayRenderer.js"></script>
    <script src="./js/components/EnhancedConditionRenderer.js"></script>
    <script src="./js/components/DynamicFormRenderer_template.js"></script>
    <script src="./js/components/RuleManager.js"></script>
    <script src="./js/components/PromotionConfigMainComponentHybrid.js"></script>

    <script>
        const { createApp, ref } = Vue;

        createApp({
            components: {
                ProductSelector: window.ProductSelector
            },
            setup() {
                const showTest = ref(false);
                const testValue = ref('');
                const apiResult = ref('');

                const testProductSelector = () => {
                    if (typeof ProductSelector !== 'undefined') {
                        showTest.value = true;
                        ElMessage.success('ProductSelector 组件可用');
                    } else {
                        ElMessage.error('ProductSelector 组件未定义');
                    }
                };

                const testAPI = async () => {
                    try {
                        const response = await fetch('/api/promotion/metadata/types');
                        if (response.ok) {
                            apiResult.value = '✅ API 连接正常';
                        } else {
                            apiResult.value = `❌ API 错误: ${response.status}`;
                        }
                    } catch (error) {
                        apiResult.value = `❌ 连接失败: ${error.message}`;
                    }
                };

                return {
                    showTest,
                    testValue,
                    apiResult,
                    testProductSelector,
                    testAPI,
                    // 暴露检查变量到模板
                    Vue,
                    ElementPlus,
                    ElementPlusIconsVue,
                    ProductSelector,
                    ComplexArrayRenderer,
                    ConditionConfigRenderer,
                    EnhancedConditionRenderer,
                    DynamicFormRenderer,
                    RuleManager,
                    PromotionConfigMainComponentHybrid,
                    PROMOTION_TYPES
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
