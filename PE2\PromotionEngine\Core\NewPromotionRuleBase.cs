global using Microsoft.Extensions.Logging;
global using Microsoft.Extensions.DependencyInjection;
using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Observability;
using PE2.PromotionEngine.Conditions;
using PE2.PromotionEngine.Inventory;
using PE2.PromotionEngine.Performance;
using PE2.PromotionEngine.Allocation;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Core;

/// <summary>
/// 新架构促销规则基类
/// 集成所有基础设施组件，支持.NET 9.0语法和异步模式
/// </summary>
public abstract class NewPromotionRuleBase : IDisposable
{
    private readonly ILogger _logger;
    private readonly IObservabilityEngine _observability;
    private readonly IConditionEngine _conditionEngine;
    private readonly IInventoryManager _inventoryManager;
    private readonly IPerformanceOptimizer _performanceOptimizer;
    private readonly IAllocationEngine _allocationEngine;
    private bool _disposed;

    /// <summary>
    /// 构造函数
    /// </summary>
    protected NewPromotionRuleBase(
        ILogger logger,
        IObservabilityEngine observability,
        IConditionEngine conditionEngine,
        IInventoryManager inventoryManager,
        IPerformanceOptimizer performanceOptimizer,
        IAllocationEngine allocationEngine)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _observability = observability ?? throw new ArgumentNullException(nameof(observability));
        _conditionEngine = conditionEngine ?? throw new ArgumentNullException(nameof(conditionEngine));
        _inventoryManager = inventoryManager ?? throw new ArgumentNullException(nameof(inventoryManager));
        _performanceOptimizer = performanceOptimizer ?? throw new ArgumentNullException(nameof(performanceOptimizer));
        _allocationEngine = allocationEngine ?? throw new ArgumentNullException(nameof(allocationEngine));
    }

    #region 基本属性

    /// <summary>
    /// 规则ID
    /// </summary>
    public required string Id { get; init; }

    /// <summary>
    /// 规则名称
    /// </summary>
    public required string Name { get; init; }

    /// <summary>
    /// 规则描述
    /// </summary>
    public string Description { get; init; } = string.Empty;

    /// <summary>
    /// 规则类型
    /// </summary>
    public abstract string RuleType { get; }

    /// <summary>
    /// 优先级（数值越大优先级越高）
    /// </summary>
    public int Priority { get; init; } = 0;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; init; } = true;

    /// <summary>
    /// 生效时间
    /// </summary>
    public DateTime? StartTime { get; init; }

    /// <summary>
    /// 失效时间
    /// </summary>
    public DateTime? EndTime { get; init; }

    /// <summary>
    /// 是否可重复应用
    /// </summary>
    public bool IsRepeatable { get; init; } = true;

    /// <summary>
    /// 最大应用次数（0表示无限制）
    /// </summary>
    public int MaxApplications { get; init; } = 1;

    /// <summary>
    /// 适用的客户类型
    /// </summary>
    public List<string> ApplicableCustomerTypes { get; init; } = [];

    /// <summary>
    /// 排斥的规则ID列表（互斥规则）
    /// </summary>
    public List<string> ExclusiveRuleIds { get; init; } = [];

    /// <summary>
    /// 是否可与其他促销叠加
    /// </summary>
    public bool CanStackWithOthers { get; init; } = true;

    /// <summary>
    /// 商品互斥级别
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public ProductExclusivityLevel ProductExclusivity { get; init; } = ProductExclusivityLevel.None;

    #endregion

    #region 核心方法

    /// <summary>
    /// 检查规则是否在有效期内
    /// </summary>
    public virtual bool IsInValidPeriod(DateTime checkTime)
    {
        if (StartTime.HasValue && checkTime < StartTime.Value)
            return false;

        if (EndTime.HasValue && checkTime > EndTime.Value)
            return false;

        return true;
    }

    /// <summary>
    /// 异步检查规则是否适用于指定购物车
    /// </summary>
    public virtual async Task<bool> IsApplicableAsync(ShoppingCart cart, DateTime checkTime, CancellationToken cancellationToken = default)
    {
        if (!IsEnabled)
            return false;

        if (!IsInValidPeriod(checkTime))
            return false;

        return await CheckConditionsAsync(cart, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 异步检查促销条件是否满足
    /// </summary>
    protected abstract Task<bool> CheckConditionsAsync(ShoppingCart cart, CancellationToken cancellationToken = default);

    /// <summary>
    /// 异步计算可应用的最大次数
    /// </summary>
    public abstract Task<int> CalculateMaxApplicationsAsync(ShoppingCart cart, CancellationToken cancellationToken = default);

    /// <summary>
    /// 异步应用促销规则
    /// </summary>
    public abstract Task<AppliedPromotion> ApplyPromotionAsync(
        ProcessedCart cart, 
        int applicationCount = 1, 
        string? traceId = null,
        CancellationToken cancellationToken = default);

    #endregion

    #region 基础设施组件访问

    /// <summary>
    /// 获取可观测性引擎
    /// </summary>
    protected IObservabilityEngine Observability => _observability;

    /// <summary>
    /// 获取条件引擎
    /// </summary>
    protected IConditionEngine ConditionEngine => _conditionEngine;

    /// <summary>
    /// 获取库存管理器
    /// </summary>
    protected IInventoryManager InventoryManager => _inventoryManager;

    /// <summary>
    /// 获取性能优化器
    /// </summary>
    protected IPerformanceOptimizer PerformanceOptimizer => _performanceOptimizer;

    /// <summary>
    /// 获取分配引擎
    /// </summary>
    protected IAllocationEngine AllocationEngine => _allocationEngine;

    /// <summary>
    /// 获取日志记录器
    /// </summary>
    protected ILogger Logger => _logger;

    #endregion

    #region 辅助方法

    /// <summary>
    /// 创建应用的促销记录
    /// </summary>
    protected AppliedPromotion CreateAppliedPromotion(
        decimal discountAmount,
        int applicationCount = 1,
        List<ConsumedItem>? consumedItems = null,
        List<GiftItem>? giftItems = null)
    {
        return new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType,
            DiscountAmount = discountAmount,
            ApplicationCount = applicationCount,
            ConsumedItems = consumedItems ?? [],
            GiftItems = giftItems ?? []
        };
    }

    /// <summary>
    /// 记录促销应用追踪
    /// </summary>
    protected void TrackPromotionApplication(string? traceId, AppliedPromotion appliedPromotion)
    {
        if (string.IsNullOrEmpty(traceId))
            return;

        try
        {
            var application = new PromotionApplication
            {
                RuleId = appliedPromotion.RuleId,
                RuleName = appliedPromotion.RuleName,
                DiscountAmount = appliedPromotion.DiscountAmount,
                ApplicationCount = appliedPromotion.ApplicationCount,
                ConsumedItems = appliedPromotion.ConsumedItems,
                GiftItems = appliedPromotion.GiftItems,
                IsSuccessful = true
            };

            _observability.TrackPromotionApplication(traceId, Id, application);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "记录促销应用追踪失败: {RuleId}", Id);
        }
    }

    /// <summary>
    /// 验证库存可用性
    /// </summary>
    protected async Task<bool> ValidateInventoryAsync(
        List<(string ProductId, int Quantity)> requiredProducts,
        string? reservationId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            foreach (var (productId, quantity) in requiredProducts)
            {
                var available = await _inventoryManager.GetAvailableQuantityAsync(productId, cancellationToken).ConfigureAwait(false);
                if (available < quantity)
                    return false;
            }
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证库存可用性失败: {RuleId}", Id);
            return false;
        }
    }

    #endregion

    #region IDisposable

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            // 清理资源
            _disposed = true;
        }
    }

    #endregion
}
