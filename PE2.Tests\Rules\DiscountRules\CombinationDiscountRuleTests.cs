using PE2.Models;
using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.DiscountRules;

/// <summary>
/// 组合折扣规则测试类
/// 测试 CombinationDiscountRule 的组合条件检查和折扣应用逻辑
/// </summary>
public class CombinationDiscountRuleTests(ITestOutputHelper output) : TestBase(output)
{
    #region 基础组合折扣功能测试

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_ValidCombinationDiscountScenario_ShouldApplyCorrectly()
    {
        // Arrange - 组合折扣：买A+B各1件，C商品享受8折
        var rule = TestDataGenerator.CreateCombinationDiscountRule_AB_DiscountC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            (TestDataGenerator.CreateProductA(), 1), // A: 1件，满足条件
            (TestDataGenerator.CreateProductB(), 1), // B: 1件，满足条件
            (TestDataGenerator.CreateProductC(), 2)  // C: 2件，享受折扣
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "组合折扣测试购物车");

        var originalPriceC = TestDataGenerator.CreateProductC().Price; // 20元
        var expectedDiscountedPriceC = originalPriceC * 0.8m; // 16元（8折）
        var expectedTotalDiscount = (originalPriceC - expectedDiscountedPriceC) * 2; // (20-16)*2 = 8元

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "组合折扣测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证商品C的实际单价被调整为9折
        var productCItem = result.ProcessedCart.Items.First(i => i.Product.Id == "C");
        //AssertAmountEqual(expectedDiscountedPriceC, productCItem.ActualUnitPrice, "商品C的实际单价应为9折");

        // 验证A和B的价格未变
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        //AssertAmountEqual(TestDataGenerator.CreateProductA().Price, productAItem.ActualUnitPrice, "商品A价格应保持不变");
        //AssertAmountEqual(TestDataGenerator.CreateProductB().Price, productBItem.ActualUnitPrice, "商品B价格应保持不变");

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为C商品的折扣金额");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_MissingConditionProductA_ShouldNotApply()
    {
        // Arrange - 缺少条件商品A
        var rule = TestDataGenerator.CreateCombinationDiscountRule_AB_DiscountC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_002",
            // 缺少A
            (TestDataGenerator.CreateProductB(), 1), // B: 1件
            (TestDataGenerator.CreateProductC(), 2)  // C: 2件
        );

        LogCartDetails(cart, "缺少条件商品A的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "缺少条件商品A场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "缺少条件商品时应无优惠");

        // 验证所有商品价格未变
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        var productCItem = result.ProcessedCart.Items.First(i => i.Product.Id == "C");
        AssertAmountEqual(TestDataGenerator.CreateProductB().Price, productBItem.ActualUnitPrice, "B价格应保持原价");
        AssertAmountEqual(TestDataGenerator.CreateProductC().Price, productCItem.ActualUnitPrice, "C价格应保持原价");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_MissingDiscountProductC_ShouldNotApply()
    {
        // Arrange - 缺少折扣商品C
        var rule = TestDataGenerator.CreateCombinationDiscountRule_AB_DiscountC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_003",
            (TestDataGenerator.CreateProductA(), 1), // A: 1件，满足条件
            (TestDataGenerator.CreateProductB(), 1)  // B: 1件，满足条件
                                                     // 缺少C
        );

        LogCartDetails(cart, "缺少折扣商品C的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "缺少折扣商品C场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "缺少折扣商品时应无优惠");
    }

    #endregion

    #region 数量条件测试

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Medium")]
    public void Apply_InsufficientQuantityA_ShouldNotApply()
    {
        // Arrange - A商品数量不足
        var rule = TestDataGenerator.CreateCombinationDiscountRule_AB_DiscountC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_004",
            // A数量不足（需要1件，但没有）
            (TestDataGenerator.CreateProductB(), 1), // B: 1件，满足条件
            (TestDataGenerator.CreateProductC(), 1)  // C: 1件
        );

        LogCartDetails(cart, "A商品数量不足的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "A商品数量不足场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertAmountEqual(0m, result.TotalDiscount, "数量不足时应无优惠");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Medium")]
    public void Apply_ExcessQuantity_ShouldApplyOnce()
    {
        // Arrange - 超量购买
        var rule = TestDataGenerator.CreateCombinationDiscountRule_AB_DiscountC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_005",
            (TestDataGenerator.CreateProductA(), 3), // A: 3件，超过需要的1件
            (TestDataGenerator.CreateProductB(), 2), // B: 2件，超过需要的1件
            (TestDataGenerator.CreateProductC(), 3)  // C: 3件，全部享受折扣
        );

        LogCartDetails(cart, "超量购买的购物车");

        var originalPriceC = TestDataGenerator.CreateProductC().Price;
        var expectedDiscountedPriceC = originalPriceC * 0.8m;
        var expectedTotalDiscount = (originalPriceC - expectedDiscountedPriceC) * 3; // 3件C都享受折扣

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "超量购买场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证所有C商品都享受折扣
        var productCItem = result.ProcessedCart.Items.First(i => i.Product.Id == "C");
        //AssertAmountEqual(expectedDiscountedPriceC, productCItem.ActualUnitPrice, "所有C商品都应享受9折");
        Assert.Equal(3, productCItem.Quantity);

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为所有C商品的折扣金额");
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Low")]
    public void Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateCombinationDiscountRule_AB_DiscountC();
        var cart = TestDataGenerator.CreateEmptyCart();

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "空购物车");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertAmountEqual(0m, result.TotalDiscount, "空购物车应无优惠");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Low")]
    public void Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateCombinationDiscountRule_AB_DiscountC();
        rule.IsEnabled = false; // 禁用规则

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_DISABLED",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1),
            (TestDataGenerator.CreateProductC(), 1)
        );

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "禁用规则场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");
        AssertAmountEqual(0m, result.TotalDiscount, "禁用规则应无优惠");
    }

    #endregion

    #region 复杂组合场景测试

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Medium")]
    public void Apply_MultipleDiscountProducts_ShouldApplyToAll()
    {
        // Arrange - 多种折扣商品
        var rule = TestDataGenerator.CreateCombinationDiscountRule_AB_DiscountBOrC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_MULTI",
            (TestDataGenerator.CreateProductA(), 1), // A: 1件，满足条件
            (TestDataGenerator.CreateProductB(), 2), // B: 2件，既是条件商品又是折扣商品
            (TestDataGenerator.CreateProductC(), 1)  // C: 1件，折扣商品
        );

        LogCartDetails(cart, "多种折扣商品的购物车");

        var originalPriceB = TestDataGenerator.CreateProductB().Price;
        var originalPriceC = TestDataGenerator.CreateProductC().Price;
        var expectedDiscountedPriceB = originalPriceB * 0.9m;
        var expectedDiscountedPriceC = originalPriceC * 0.9m;
        var expectedTotalDiscount =
            (originalPriceB - expectedDiscountedPriceB) * 1 + // B商品剩下1件
            (originalPriceC - expectedDiscountedPriceC) * 1;   // C商品1件

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "多种折扣商品场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证B和C都享受折扣，A保持原价
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        var productCItem = result.ProcessedCart.Items.First(i => i.Product.Id == "C");

        //AssertAmountEqual(TestDataGenerator.CreateProductA().Price, productAItem.ActualUnitPrice, "A应保持原价");
        //AssertAmountEqual(expectedDiscountedPriceB, productBItem.ActualUnitPrice, "B应享受9折");
        //AssertAmountEqual(expectedDiscountedPriceC, productCItem.ActualUnitPrice, "C应享受9折");

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为B和C的折扣金额之和");
    }

    #endregion

    #region 性能测试

    [Fact]
    [Trait("Category", "Performance")]
    [Trait("Priority", "Low")]
    public void Apply_LargeCart_ShouldPerformWell()
    {
        // Arrange
        var rule = TestDataGenerator.CreateCombinationDiscountRule_AB_DiscountC();
        var cart = TestDataGenerator.CreateLargeTestCart(100); // 100个商品项

        // 确保包含必要的条件和折扣商品
        cart.Items.Add(new CartItem
        {
            Product = TestDataGenerator.CreateProductA(),
            Quantity = 1,
            UnitPrice = TestDataGenerator.CreateProductA().Price
        });
        cart.Items.Add(new CartItem
        {
            Product = TestDataGenerator.CreateProductB(),
            Quantity = 1,
            UnitPrice = TestDataGenerator.CreateProductB().Price
        });
        cart.Items.Add(new CartItem
        {
            Product = TestDataGenerator.CreateProductC(),
            Quantity = 5,
            UnitPrice = TestDataGenerator.CreateProductC().Price
        });
        cart.InitializeActualPrices();

        Output.WriteLine($"大型购物车测试: {cart.Items.Count} 个商品项");

        TestPromotionRuleService.Rules = [rule];
        // Act & Assert
        var executionTime = MeasureExecutionTime(() =>
        {
            var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;
            AssertPromotionResult(result, "大型购物车性能测试");
        });

        // 性能断言：大型购物车处理应在合理时间内完成
        AssertPerformance(executionTime, TimeSpan.FromMilliseconds(500), "大型购物车组合折扣规则计算");
    }

    #endregion
}