using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Core;
using PE2.PromotionEngine.Observability;
using PE2.PromotionEngine.Conditions;
using PE2.PromotionEngine.Inventory;
using PE2.PromotionEngine.Performance;
using PE2.PromotionEngine.Allocation;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Rules.BuyFreeRules;

/// <summary>
/// 新架构组合买免规则
/// 同时购买A+B+C，免去其中价格最低的一件
/// 集成新架构的所有基础设施组件，支持.NET 9.0语法和异步模式
/// </summary>
public sealed class NewCombinationBuyFreeRule : NewBuyFreeRuleBase, INewPromotionRule
{
    /// <summary>
    /// 构造函数
    /// </summary>
    public NewCombinationBuyFreeRule(
        ILogger<NewCombinationBuyFreeRule> logger,
        IObservabilityEngine observability,
        IConditionEngine conditionEngine,
        IInventoryManager inventoryManager,
        IPerformanceOptimizer performanceOptimizer,
        IAllocationEngine allocationEngine,
        CombinationBuyFreeConfiguration configuration)
        : base(logger, observability, conditionEngine, inventoryManager, performanceOptimizer, allocationEngine)
    {
        ArgumentNullException.ThrowIfNull(configuration);
        
        Id = configuration.Id;
        Name = configuration.Name;
        Description = configuration.Description;
        Priority = configuration.Priority;
        IsEnabled = configuration.IsEnabled;
        StartTime = configuration.StartTime;
        EndTime = configuration.EndTime;
        IsRepeatable = configuration.IsRepeatable;
        MaxApplications = configuration.MaxApplications;
        ApplicableCustomerTypes = configuration.ApplicableCustomerTypes;
        ExclusiveRuleIds = configuration.ExclusiveRuleIds;
        CanStackWithOthers = configuration.CanStackWithOthers;
        ProductExclusivity = configuration.ProductExclusivity;
        FreeItemSelectionStrategy = configuration.FreeItemSelectionStrategy;
        CombinationConditions = configuration.CombinationConditions;
        FreeQuantity = configuration.FreeQuantity;
        FreeFromCombinationOnly = configuration.FreeFromCombinationOnly;
    }

    /// <summary>
    /// 规则类型
    /// </summary>
    public override string RuleType => "CombinationBuyFree";

    /// <summary>
    /// 组合购买条件列表
    /// </summary>
    public List<CombinationBuyCondition> CombinationConditions { get; init; } = [];

    /// <summary>
    /// 免费商品数量
    /// </summary>
    public int FreeQuantity { get; init; } = 1;

    /// <summary>
    /// 是否只从参与组合的商品中选择免费商品
    /// </summary>
    public bool FreeFromCombinationOnly { get; init; } = true;

    /// <summary>
    /// 异步检查促销条件是否满足
    /// </summary>
    protected override async Task<bool> CheckConditionsAsync(ShoppingCart cart, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!CombinationConditions.Any())
                return false;

            // 验证组合商品是否在购物车中
            var allProductIds = CombinationConditions.SelectMany(c => c.ProductId).Distinct().ToList();
            if (!await ValidateBuyFreeProductsInCartAsync(cart, allProductIds, cancellationToken).ConfigureAwait(false))
                return false;

            // 检查每个组合条件是否满足
            foreach (var condition in CombinationConditions)
            {
                var conditionResult = await ConditionEngine.ValidateConditionAsync(condition, cart, cancellationToken).ConfigureAwait(false);
                if (!conditionResult.IsValid)
                    return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "检查组合买免条件时发生异常: {RuleId}", Id);
            return false;
        }
    }

    /// <summary>
    /// 异步计算可应用的最大次数
    /// </summary>
    public override async Task<int> CalculateMaxApplicationsAsync(ShoppingCart cart, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!await CheckConditionsAsync(cart, cancellationToken).ConfigureAwait(false))
                return 0;

            // 使用性能优化器计算最大应用次数
            var optimizationContext = new OptimizationContext
            {
                Cart = cart,
                Rules = [this],
                Target = OptimizationTarget.MaximizeApplications
            };

            var result = await PerformanceOptimizer.OptimizeRuleCombinationAsync(optimizationContext, cancellationToken).ConfigureAwait(false);

            // 计算基于组合条件的最大应用次数
            var maxApplications = CombinationConditions
                .Where(c => c.RequiredQuantity > 0)
                .Select(c =>
                {
                    var totalQuantity = c.ProductId.Sum(productId => 
                        cart.Items.Where(item => item.ProductId == productId).Sum(item => item.Quantity));
                    return IsRepeatable
                        ? totalQuantity / c.RequiredQuantity
                        : (totalQuantity >= c.RequiredQuantity ? 1 : 0);
                })
                .DefaultIfEmpty(1)
                .Min();

            return ApplyApplicationLimits(maxApplications);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "计算组合买免最大应用次数时发生异常: {RuleId}", Id);
            return 0;
        }
    }

    /// <summary>
    /// 异步应用促销规则
    /// </summary>
    public override async Task<AppliedPromotion> ApplyPromotionAsync(
        ProcessedCart cart, 
        int applicationCount = 1, 
        string? traceId = null,
        CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        
        try
        {
            // 记录开始追踪
            if (!string.IsNullOrEmpty(traceId))
            {
                Observability.TrackCalculationStep(traceId, new CalculationStep
                {
                    Id = Guid.NewGuid().ToString("N"),
                    TraceId = traceId,
                    StepType = StepType.PromotionApplication,
                    Timestamp = startTime,
                    Description = $"开始应用组合买免规则: {Name}",
                    RuleId = Id,
                    Data = new Dictionary<string, object>
                    {
                        ["applicationCount"] = applicationCount,
                        ["cartItemCount"] = cart.Items.Count,
                        ["combinationConditionsCount"] = CombinationConditions.Count,
                        ["freeQuantity"] = FreeQuantity
                    }
                });
            }

            // 预留库存
            var reservationId = await ReserveInventoryAsync(cart, applicationCount, cancellationToken).ConfigureAwait(false);

            try
            {
                // 应用组合买免逻辑
                var (discountAmount, consumedItems, giftItems) = await ApplyCombinationBuyFreeAsync(
                    cart, applicationCount, traceId, cancellationToken).ConfigureAwait(false);

                var appliedPromotion = CreateAppliedPromotion(discountAmount, applicationCount, consumedItems, giftItems);

                // 记录促销应用追踪
                TrackPromotionApplication(traceId, appliedPromotion);

                // 使用分配引擎进行精确的折扣分配
                if (discountAmount > 0)
                {
                    var allocationRequest = new AllocationRequest
                    {
                        TotalDiscount = discountAmount,
                        Items = cart.Items.Where(item => giftItems.Any(gi => gi.ProductId == item.ProductId)).ToList(),
                        Strategy = AllocationStrategy.ProportionalByValue,
                        RoundingStrategy = RoundingStrategy.RoundToNearest
                    };

                    var allocationResult = await AllocationEngine.AllocateDiscountAsync(allocationRequest, cancellationToken).ConfigureAwait(false);
                    
                    // 应用分配结果到购物车
                    ApplyAllocationToCart(cart, allocationResult);
                }

                return appliedPromotion;
            }
            finally
            {
                // 释放库存预留
                if (!string.IsNullOrEmpty(reservationId))
                {
                    await InventoryManager.ReleaseReservationAsync(reservationId, cancellationToken).ConfigureAwait(false);
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "应用组合买免规则时发生异常: {RuleId}", Id);
            
            return CreateAppliedPromotion(0, 0, [], []);
        }
        finally
        {
            // 记录性能指标
            var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
            Logger.LogDebug("组合买免规则执行完成: {RuleId}, 耗时: {ExecutionTime}ms", Id, executionTime);
        }
    }

    /// <summary>
    /// 应用应用次数限制
    /// </summary>
    private int ApplyApplicationLimits(int maxApplications)
    {
        if (!IsRepeatable)
        {
            maxApplications = Math.Min(maxApplications, 1);
        }
        else if (MaxApplications > 0)
        {
            maxApplications = Math.Min(maxApplications, MaxApplications);
        }

        return maxApplications;
    }

    /// <summary>
    /// 验证买免商品是否在购物车中
    /// </summary>
    private async Task<bool> ValidateBuyFreeProductsInCartAsync(
        ShoppingCart cart, 
        List<string> productIds, 
        CancellationToken cancellationToken)
    {
        try
        {
            foreach (var productId in productIds)
            {
                var hasProduct = cart.Items.Any(item => item.ProductId == productId && item.Quantity > 0);
                if (!hasProduct)
                    return false;

                // 验证库存可用性
                var available = await InventoryManager.GetAvailableQuantityAsync(productId, cancellationToken).ConfigureAwait(false);
                if (available <= 0)
                    return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "验证买免商品时发生异常: {RuleId}", Id);
            return false;
        }
    }

    /// <summary>
    /// 预留库存
    /// </summary>
    private async Task<string?> ReserveInventoryAsync(ProcessedCart cart, int applicationCount, CancellationToken cancellationToken)
    {
        try
        {
            var requiredProducts = new List<(string ProductId, int Quantity)>();

            // 计算需要预留的商品数量
            foreach (var condition in CombinationConditions)
            {
                foreach (var productId in condition.ProductId)
                {
                    var requiredQuantity = condition.RequiredQuantity * applicationCount;
                    requiredProducts.Add((productId, requiredQuantity));
                }
            }

            if (!requiredProducts.Any())
                return null;

            var reservationRequest = new ReservationRequest
            {
                ReservationId = Guid.NewGuid().ToString("N"),
                ProductQuantities = requiredProducts.GroupBy(p => p.ProductId)
                    .ToDictionary(g => g.Key, g => g.Sum(p => p.Quantity)),
                Priority = ReservationPriority.High,
                ExpirationTime = DateTime.UtcNow.AddMinutes(5),
                Source = $"Rule_{Id}"
            };

            var result = await InventoryManager.ReserveProductsAsync(reservationRequest, cancellationToken).ConfigureAwait(false);
            return result.IsSuccessful ? reservationRequest.ReservationId : null;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "预留库存时发生异常: {RuleId}", Id);
            return null;
        }
    }

    /// <summary>
    /// 应用组合买免逻辑
    /// </summary>
    private async Task<(decimal totalDiscount, List<ConsumedItem> consumed, List<GiftItem> gifts)> ApplyCombinationBuyFreeAsync(
        ProcessedCart cart,
        int applicationCount,
        string? traceId,
        CancellationToken cancellationToken)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var giftItems = new List<GiftItem>();

        try
        {
            var processedByProduct = new Dictionary<string, int>();

            for (int application = 0; application < applicationCount; application++)
            {
                var (success, discount, gifts) = await TryApplySingleCombinationBuyFreeAsync(
                    cart, processedByProduct, application + 1, traceId, cancellationToken).ConfigureAwait(false);

                if (!success)
                    break; // 无法继续应用，停止

                totalDiscountAmount += discount;
                giftItems.AddRange(gifts);

                if (!IsRepeatable)
                    break;
            }

            return (totalDiscountAmount, consumedItems, giftItems);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "应用组合买免逻辑时发生异常: {RuleId}", Id);
            return (0, [], []);
        }
    }

    /// <summary>
    /// 尝试应用单次组合买免
    /// </summary>
    private async Task<(bool success, decimal discount, List<GiftItem> gifts)> TryApplySingleCombinationBuyFreeAsync(
        ProcessedCart cart,
        Dictionary<string, int> processedByProduct,
        int applicationIndex,
        string? traceId,
        CancellationToken cancellationToken)
    {
        try
        {
            // 验证组合条件
            if (!await CheckCombinationConditionsAsync(cart, processedByProduct, cancellationToken).ConfigureAwait(false))
                return (false, 0, []);

            // 消耗组合条件商品
            var consumedInThisApplication = await ConsumeCombinationConditionProductsAsync(cart, processedByProduct, cancellationToken).ConfigureAwait(false);

            // 选择免费商品
            var (freeDiscount, gifts) = await SelectAndProcessFreeItemsAsync(cart, processedByProduct, traceId, cancellationToken).ConfigureAwait(false);

            return (freeDiscount > 0, freeDiscount, gifts);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "尝试应用单次组合买免时发生异常: {RuleId}, ApplicationIndex: {ApplicationIndex}", Id, applicationIndex);
            return (false, 0, []);
        }
    }

    /// <summary>
    /// 检查组合条件
    /// </summary>
    private async Task<bool> CheckCombinationConditionsAsync(
        ProcessedCart cart,
        Dictionary<string, int> processedByProduct,
        CancellationToken cancellationToken)
    {
        try
        {
            foreach (var condition in CombinationConditions)
            {
                var availableQuantity = condition.ProductId.Sum(productId =>
                {
                    var cartQuantity = cart.Items.Where(item => item.ProductId == productId).Sum(item => item.Quantity);
                    var processedQuantity = processedByProduct.GetValueOrDefault(productId, 0);
                    return Math.Max(0, cartQuantity - processedQuantity);
                });

                if (availableQuantity < condition.RequiredQuantity)
                    return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "检查组合条件时发生异常: {RuleId}", Id);
            return false;
        }
    }

    /// <summary>
    /// 消耗组合条件商品
    /// </summary>
    private async Task<List<ConsumedItem>> ConsumeCombinationConditionProductsAsync(
        ProcessedCart cart,
        Dictionary<string, int> processedByProduct,
        CancellationToken cancellationToken)
    {
        var consumedItems = new List<ConsumedItem>();

        try
        {
            foreach (var condition in CombinationConditions)
            {
                var consumed = ConsumeSingleConditionProducts(cart, condition, processedByProduct);
                consumedItems.AddRange(consumed);
            }

            return consumedItems;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "消耗组合条件商品时发生异常: {RuleId}", Id);
            return [];
        }
    }

    /// <summary>
    /// 消耗单个条件的商品
    /// </summary>
    private List<ConsumedItem> ConsumeSingleConditionProducts(
        ProcessedCart cart,
        CombinationBuyCondition condition,
        Dictionary<string, int> processedByProduct)
    {
        var consumedItems = new List<ConsumedItem>();
        var remainingQuantity = condition.RequiredQuantity;

        foreach (var productId in condition.ProductId)
        {
            if (remainingQuantity <= 0) break;

            var cartItems = cart.Items.Where(item => item.ProductId == productId).ToList();
            foreach (var cartItem in cartItems)
            {
                if (remainingQuantity <= 0) break;

                var processedQuantity = processedByProduct.GetValueOrDefault(productId, 0);
                var availableQuantity = Math.Max(0, cartItem.Quantity - processedQuantity);

                if (availableQuantity > 0)
                {
                    var consumeQuantity = Math.Min(availableQuantity, remainingQuantity);

                    consumedItems.Add(new ConsumedItem
                    {
                        ProductId = productId,
                        ProductName = cartItem.ProductName,
                        Quantity = consumeQuantity,
                        UnitPrice = cartItem.OriginalUnitPrice
                    });

                    processedByProduct[productId] = processedByProduct.GetValueOrDefault(productId, 0) + consumeQuantity;
                    remainingQuantity -= consumeQuantity;
                }
            }
        }

        return consumedItems;
    }

    /// <summary>
    /// 选择并处理免费商品
    /// </summary>
    private async Task<(decimal discount, List<GiftItem> gifts)> SelectAndProcessFreeItemsAsync(
        ProcessedCart cart,
        Dictionary<string, int> processedByProduct,
        string? traceId,
        CancellationToken cancellationToken)
    {
        var totalDiscount = 0m;
        var gifts = new List<GiftItem>();

        try
        {
            // 获取候选免费商品
            var candidateItems = GetCandidateFreeItems(cart, processedByProduct);

            if (!candidateItems.Any())
                return (0, []);

            // 根据策略排序候选商品
            var sortedCandidates = FreeItemSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                ? candidateItems.OrderByDescending(x => x.Item.OriginalUnitPrice).ToList()
                : candidateItems.OrderBy(x => x.Item.OriginalUnitPrice).ToList();

            // 选择免费商品
            var remainingFreeQuantity = FreeQuantity;
            foreach (var (item, availableQuantity) in sortedCandidates)
            {
                if (remainingFreeQuantity <= 0) break;

                var freeQuantity = Math.Min(availableQuantity, remainingFreeQuantity);
                if (freeQuantity > 0)
                {
                    var freeDiscount = item.OriginalUnitPrice * freeQuantity;
                    totalDiscount += freeDiscount;

                    // 更新商品实际价格为0（免费）
                    item.ActualUnitPrice = 0;

                    gifts.Add(new GiftItem
                    {
                        ProductId = item.ProductId,
                        ProductName = item.ProductName,
                        Quantity = freeQuantity,
                        UnitPrice = 0,
                        OriginalUnitPrice = item.OriginalUnitPrice
                    });

                    // 更新已处理数量
                    processedByProduct[item.ProductId] = processedByProduct.GetValueOrDefault(item.ProductId, 0) + freeQuantity;
                    remainingFreeQuantity -= freeQuantity;
                }
            }

            return (totalDiscount, gifts);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "选择并处理免费商品时发生异常: {RuleId}", Id);
            return (0, []);
        }
    }

    /// <summary>
    /// 获取候选免费商品
    /// </summary>
    private List<(CartItem Item, int AvailableQuantity)> GetCandidateFreeItems(
        ProcessedCart cart,
        Dictionary<string, int> processedByProduct)
    {
        var candidateItems = new List<(CartItem Item, int AvailableQuantity)>();

        if (FreeFromCombinationOnly)
        {
            // 只从参与组合的商品中选择
            var combinationProductIds = CombinationConditions.SelectMany(c => c.ProductId).Distinct().ToList();

            foreach (var productId in combinationProductIds)
            {
                var cartItems = cart.Items.Where(item => item.ProductId == productId).ToList();
                foreach (var cartItem in cartItems)
                {
                    var processedQuantity = processedByProduct.GetValueOrDefault(productId, 0);
                    var availableQuantity = Math.Max(0, cartItem.Quantity - processedQuantity);

                    if (availableQuantity > 0)
                    {
                        candidateItems.Add((cartItem, availableQuantity));
                    }
                }
            }
        }
        else
        {
            // 从所有购物车商品中选择
            foreach (var cartItem in cart.Items)
            {
                var processedQuantity = processedByProduct.GetValueOrDefault(cartItem.ProductId, 0);
                var availableQuantity = Math.Max(0, cartItem.Quantity - processedQuantity);

                if (availableQuantity > 0)
                {
                    candidateItems.Add((cartItem, availableQuantity));
                }
            }
        }

        return candidateItems;
    }

    /// <summary>
    /// 应用分配结果到购物车
    /// </summary>
    private static void ApplyAllocationToCart(ProcessedCart cart, AllocationResult allocationResult)
    {
        foreach (var allocation in allocationResult.Allocations)
        {
            var cartItem = cart.Items.FirstOrDefault(item => item.ProductId == allocation.ProductId);
            if (cartItem != null)
            {
                cartItem.ActualUnitPrice = Math.Max(0, cartItem.ActualUnitPrice - allocation.AllocatedDiscount / cartItem.Quantity);
            }
        }
    }
}

/// <summary>
/// 组合买免规则配置
/// </summary>
public sealed class CombinationBuyFreeConfiguration
{
    public required string Id { get; init; }
    public required string Name { get; init; }
    public string Description { get; init; } = string.Empty;
    public int Priority { get; init; } = 0;
    public bool IsEnabled { get; init; } = true;
    public DateTime? StartTime { get; init; }
    public DateTime? EndTime { get; init; }
    public bool IsRepeatable { get; init; } = true;
    public int MaxApplications { get; init; } = 1;
    public List<string> ApplicableCustomerTypes { get; init; } = [];
    public List<string> ExclusiveRuleIds { get; init; } = [];
    public bool CanStackWithOthers { get; init; } = true;
    public ProductExclusivityLevel ProductExclusivity { get; init; } = ProductExclusivityLevel.None;
    public BenefitSelectionStrategy FreeItemSelectionStrategy { get; init; } = BenefitSelectionStrategy.CustomerBenefit;
    public List<CombinationBuyCondition> CombinationConditions { get; init; } = [];
    public int FreeQuantity { get; init; } = 1;
    public bool FreeFromCombinationOnly { get; init; } = true;
}
