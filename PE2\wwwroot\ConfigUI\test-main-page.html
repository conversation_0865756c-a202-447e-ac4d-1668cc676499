<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主页面促销配置测试</title>
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3.3.8/dist/vue.global.js"></script>
    
    <!-- Element Plus UI Framework -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    
    <!-- Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.iife.min.js"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei';
            padding: 20px;
            background-color: #f5f5f5;
            margin: 0;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .test-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e9ecef;
        }
        .test-section {
            margin-bottom: 32px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 16px;
            color: #303133;
        }
        .status-bar {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px 16px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        .status-indicator.success { background: #52c41a; }
        .status-indicator.error { background: #ff4d4f; }
        .status-indicator.loading { background: #1890ff; }
        .json-display {
            background: #2d3748;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
        .promotion-types-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        .promotion-type-card {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s;
            background: white;
        }
        .promotion-type-card:hover {
            border-color: #1890ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
        }
        .promotion-type-card.selected {
            border-color: #1890ff;
            background: #f0f9ff;
        }
        .card-title {
            font-weight: 500;
            margin-bottom: 8px;
            color: #303133;
        }
        .card-description {
            font-size: 14px;
            color: #606266;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <div class="test-header">
                <h1>主页面促销配置系统测试</h1>
                <p>模拟主页面的促销类型选择和动态表单生成过程</p>
            </div>
            
            <!-- 状态信息 -->
            <div class="status-bar">
                <div class="status-indicator" :class="statusClass"></div>
                <span>{{ statusText }}</span>
                <el-button 
                    v-if="!metadataLoaded"
                    @click="loadMetadata" 
                    type="primary" 
                    size="small"
                    :loading="loading">
                    加载元数据
                </el-button>
            </div>
            
            <!-- 促销类型选择 -->
            <div class="test-section" v-if="metadataLoaded">
                <div class="test-title">1. 选择促销类型</div>
                <div class="promotion-types-grid">
                    <div 
                        v-for="typeOption in typeOptions"
                        :key="typeOption.value"
                        class="promotion-type-card"
                        :class="{ selected: selectedType === typeOption.value }"
                        @click="selectPromotionType(typeOption.value)">
                        <div class="card-title">{{ typeOption.label }}</div>
                        <div class="card-description">{{ typeOption.description }}</div>
                    </div>
                </div>
            </div>
            
            <!-- 动态表单区域 -->
            <div class="test-section" v-if="selectedType && promotionTypes">
                <div class="test-title">2. 动态表单配置</div>
                <dynamic-form-renderer
                    v-model="formData"
                    :promotion-types="promotionTypes"
                    selected-category="specialPrice"
                    :selected-type="selectedType"
                    mode="create">
                </dynamic-form-renderer>
            </div>
            
            <!-- 表单数据预览 -->
            <div class="test-section" v-if="formData && Object.keys(formData).length > 0">
                <div class="test-title">3. 表单数据预览</div>
                <div class="json-display">{{ formatJson(formData) }}</div>
            </div>
            
            <!-- 调试信息 -->
            <div class="test-section" v-if="debugInfo">
                <div class="test-title">调试信息</div>
                <el-collapse>
                    <el-collapse-item title="元数据结构" name="metadata">
                        <div class="json-display">{{ formatJson(promotionTypes) }}</div>
                    </el-collapse-item>
                    <el-collapse-item title="API响应" name="apiResponse">
                        <div class="json-display">{{ formatJson(debugInfo) }}</div>
                    </el-collapse-item>
                </el-collapse>
            </div>
        </div>
    </div>

    <!-- 加载所有必要的组件 -->
    <script src="./js/config/promotionTypes.js"></script>
    <script src="./js/services/apiService.js"></script>
    <script src="./js/services/metadataService.js"></script>
    <script src="./js/components/ConditionConfigRenderer.js"></script>
    <script src="./js/components/ComplexArrayRenderer.js"></script>
    <script src="./js/components/EnhancedConditionRenderer.js"></script>
    <script src="./js/components/DynamicFormRenderer_template.js"></script>
    
    <script>
        const { createApp } = Vue;
        
        const app = createApp({
            components: {
                'dynamic-form-renderer': DynamicFormRenderer
            },
            setup() {
                const { ref, computed, watch } = Vue;
                const { ElMessage, ElLoading } = ElementPlus;
                
                // 状态管理
                const loading = ref(false);
                const metadataLoaded = ref(false);
                const selectedType = ref('');
                const promotionTypes = ref(null);
                const formData = ref({});
                const debugInfo = ref(null);
                
                // 促销类型选项
                const typeOptions = ref([
                    { 
                        value: 'CombinationSpecialPriceRule', 
                        label: '组合特价规则',
                        description: '支持多商品组合条件的特价规则'
                    },
                    { 
                        value: 'ProductSpecialPriceRule', 
                        label: '商品特价规则',
                        description: '单一商品的特价促销规则'
                    },
                    { 
                        value: 'ProductDiscountRule', 
                        label: '商品折扣规则',
                        description: '商品折扣优惠规则'
                    },
                    { 
                        value: 'ProductBuyGiftRule', 
                        label: '买赠规则',
                        description: '购买指定商品赠送其他商品'
                    }
                ]);
                
                // 状态计算
                const statusClass = computed(() => {
                    if (loading.value) return 'loading';
                    if (metadataLoaded.value) return 'success';
                    return 'error';
                });
                
                const statusText = computed(() => {
                    if (loading.value) return '正在加载元数据...';
                    if (metadataLoaded.value) return '元数据加载完成，可以选择促销类型';
                    return '点击"加载元数据"开始测试';
                });
                
                // 加载元数据
                const loadMetadata = async () => {
                    loading.value = true;
                    try {
                        // 初始化服务
                        if (!window.apiService) {
                            throw new Error('API服务未初始化');
                        }
                        
                        const metadataService = window.getMetadataService(window.apiService);
                        
                        // 获取基础元数据结构
                        const metadata = await metadataService.getPromotionTypes();
                        
                        // 创建促销类型结构
                        promotionTypes.value = {
                            specialPrice: {
                                name: '特价规则',
                                description: '商品特价销售的促销规则',
                                types: {}
                            }
                        };
                        
                        debugInfo.value = metadata;
                        metadataLoaded.value = true;
                        
                        ElMessage.success('元数据加载成功');
                    } catch (error) {
                        console.error('加载元数据失败:', error);
                        ElMessage.error('加载元数据失败：' + error.message);
                        debugInfo.value = { error: error.message };
                    } finally {
                        loading.value = false;
                    }
                };
                
                // 选择促销类型
                const selectPromotionType = async (typeKey) => {
                    selectedType.value = typeKey;
                    formData.value = {};
                    
                    try {
                        loading.value = true;
                        
                        // 获取具体类型的详细元数据
                        const metadataService = window.getMetadataService(window.apiService);
                        const typeDetail = await metadataService.getPromotionTypeDetail(typeKey);
                        
                        // 更新促销类型结构
                        if (typeDetail && typeDetail.fields) {
                            promotionTypes.value.specialPrice.types[typeKey] = {
                                name: typeDetail.name || typeKey,
                                description: typeDetail.description || '促销规则',
                                ruleType: typeDetail.ruleType || typeKey,
                                fields: typeDetail.fields
                            };
                            
                            // 初始化表单数据
                            const initialData = {};
                            typeDetail.fields.forEach(field => {
                                initialData[field.name] = getDefaultValue(field.type);
                            });
                            formData.value = initialData;
                        }
                        
                        ElMessage.success(`已加载${typeKey}的配置字段`);
                        console.log('促销类型详细信息:', typeDetail);
                        console.log('初始化表单数据:', formData.value);
                        
                    } catch (error) {
                        console.error('加载类型详细信息失败:', error);
                        ElMessage.error('加载类型详细信息失败：' + error.message);
                    } finally {
                        loading.value = false;
                    }
                };
                
                // 获取默认值
                const getDefaultValue = (type) => {
                    if (typeof type === 'string') {
                        switch (type) {
                            case 'number':
                            case 'decimal':
                            case 'integer':
                                return 0;
                            case 'boolean':
                            case 'switch':
                                return false;
                            case 'array':
                            case 'array-simple':
                            case 'array-complex':
                                return [];
                            case 'object':
                                return {};
                            default:
                                return '';
                        }
                    } else if (typeof type === 'object' && type !== null) {
                        const actualType = type.type || 'string';
                        return getDefaultValue(actualType);
                    }
                    return '';
                };
                
                // 格式化JSON
                const formatJson = (obj) => {
                    try {
                        return JSON.stringify(obj, null, 2);
                    } catch (error) {
                        return 'JSON解析错误: ' + error.message;
                    }
                };
                
                return {
                    loading,
                    metadataLoaded,
                    selectedType,
                    promotionTypes,
                    formData,
                    debugInfo,
                    typeOptions,
                    statusClass,
                    statusText,
                    loadMetadata,
                    selectPromotionType,
                    formatJson
                };
            }
        });
        
        // 注册Element Plus
        app.use(ElementPlus);
        
        // 注册Element Plus图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
        
        // 挂载应用
        app.mount('#app');
    </script>
</body>
</html>
