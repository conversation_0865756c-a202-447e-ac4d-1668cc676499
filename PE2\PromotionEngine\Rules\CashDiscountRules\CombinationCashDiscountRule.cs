using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.CashDiscountRules;

/// <summary>
/// 组合减现规则
/// 某A类商品满X件或Y元并且某B类商品满X件或Y元，立减N元
/// 场景：购买A、B商品，若数量各大于1件时，则可以优惠10元
/// </summary>
public class CombinationCashDiscountRule : BaseCashDiscountRule
{
    public override string RuleType => "CombinationCashDiscount";

    /// <summary>
    /// 组合购买条件列表
    /// </summary>
    public List<CombinationDiscountCondition> CombinationConditions { get; set; } = new();

    /// <summary>
    /// 减现金额
    /// </summary>
    public decimal DiscountAmount { get; set; }

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!CombinationConditions.Any())
            return false;

        // 验证组合商品是否在购物车中
        var allProductIds = CombinationConditions.Select(c => c.ProductId).ToList();
        if (!ValidateCashDiscountProductsInCart(cart, allProductIds))
            return false;

        // 检查组合购买条件
        return CheckCombinationConditions(cart);
    }

    /// <summary>
    /// 检查组合购买条件
    /// </summary>
    private bool CheckCombinationConditions(ShoppingCart cart)
    {
        foreach (var condition in CombinationConditions)
        {
            var availableQuantity = cart.GetAvailableProductQuantity(condition.ProductId);
            var totalAmount = cart.Items
                .Where(x => x.Product.Id == condition.ProductId)
                .Sum(x => x.SubTotal);

            if (condition.RequiredQuantity > 0 && availableQuantity < condition.RequiredQuantity)
                return false;

            if (condition.RequiredAmount > 0 && totalAmount < condition.RequiredAmount)
                return false;
        }

        return true;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = int.MaxValue;

        // 计算每个组合条件的最大应用次数
        foreach (var condition in CombinationConditions)
        {
            var conditionMaxApplications = 0;

            if (condition.RequiredQuantity > 0)
            {
                var availableQuantity = cart.GetAvailableProductQuantity(condition.ProductId);
                conditionMaxApplications = IsRepeatable
                    ? availableQuantity / condition.RequiredQuantity
                    : (availableQuantity >= condition.RequiredQuantity ? 1 : 0);
            }
            else if (condition.RequiredAmount > 0)
            {
                var totalAmount = cart.Items
                    .Where(x => x.Product.Id == condition.ProductId)
                    .Sum(x => x.SubTotal);
                conditionMaxApplications = IsRepeatable
                    ? (int)(totalAmount / condition.RequiredAmount)
                    : (totalAmount >= condition.RequiredAmount ? 1 : 0);
            }
            else
            {
                conditionMaxApplications = 1;
            }

            maxApplications = Math.Min(maxApplications, conditionMaxApplications);
        }

        if (maxApplications == int.MaxValue)
            maxApplications = 1;

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyCombinationCashDiscount(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用组合减现促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用组合减现促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyCombinationCashDiscount(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var discountRecords = new List<GiftItem>(); // 减现记录

        for (int app = 0; app < applicationCount; app++)
        {
            // 消耗组合条件商品（记录消耗但不修改购物车）
            var combinationConsumed = ConsumeCombinationProducts(cart);
            if (!combinationConsumed.Any()) break;

            // 计算本次应用的减现金额
            var currentDiscountAmount = DiscountAmount;
            totalDiscountAmount += currentDiscountAmount;

            // 合并消耗记录
            foreach (var item in combinationConsumed)
            {
                var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == item.ProductId);
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += item.Quantity;
                }
                else
                {
                    consumedItems.Add(item);
                }
            }

            // 创建减现记录
            var conditionDescription = string.Join("、", CombinationConditions.Select(c =>
                c.RequiredQuantity > 0 ? $"{c.ProductId}满{c.RequiredQuantity}件" : $"{c.ProductId}满{c.RequiredAmount:C}"));

            var description = $"组合减现：{conditionDescription}立减{currentDiscountAmount:C}（第{app + 1}次应用）";

            discountRecords.Add(CreateCashDiscountRecord(currentDiscountAmount, description));
        }

        // 应用减现到购物车
        var promotion = new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType
        };
        var allProductIds = CombinationConditions.Select(c => c.ProductId).ToList();
        ApplyCashDiscountToCart(cart, totalDiscountAmount, promotion, allProductIds);

        return (totalDiscountAmount, consumedItems, discountRecords);
    }

    /// <summary>
    /// 消耗组合条件商品
    /// </summary>
    private List<ConsumedItem> ConsumeCombinationProducts(ShoppingCart cart)
    {
        var consumedItems = new List<ConsumedItem>();

        foreach (var condition in CombinationConditions)
        {
            var requiredQuantity = condition.RequiredQuantity;
            if (requiredQuantity <= 0) continue;

            var consumed = ConsumeConditionProducts(cart, new List<string> { condition.ProductId }, requiredQuantity);
            consumedItems.AddRange(consumed);
        }

        return consumedItems;
    }
}

/// <summary>
/// 组合减现条件
/// </summary>
public class CombinationDiscountCondition
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 所需数量
    /// </summary>
    public int RequiredQuantity { get; set; }

    /// <summary>
    /// 所需金额
    /// </summary>
    public decimal RequiredAmount { get; set; }
}
