using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.new.Core.Models;

/// <summary>
/// 执行结果
/// </summary>
public sealed class ExecutionResult
{
    /// <summary>
    /// 是否执行成功
    /// </summary>
    public bool IsSuccessful { get; init; }
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; init; }
    
    /// <summary>
    /// 折扣金额
    /// </summary>
    public decimal DiscountAmount { get; init; }
    
    /// <summary>
    /// 消耗的商品项
    /// </summary>
    public List<ConsumedItem> ConsumedItems { get; init; } = new();
    
    /// <summary>
    /// 赠品项
    /// </summary>
    public List<GiftItem> GiftItems { get; init; } = new();
    
    /// <summary>
    /// 执行详情
    /// </summary>
    public List<ExecutionDetail> Details { get; init; } = new();
    
    /// <summary>
    /// 应用次数
    /// </summary>
    public int ApplicationCount { get; init; }
    
    /// <summary>
    /// 执行耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; init; }
    
    /// <summary>
    /// 执行时间
    /// </summary>
    public DateTime ExecutedAt { get; init; } = DateTime.UtcNow;
    
    /// <summary>
    /// 追踪信息
    /// </summary>
    public List<string> TraceMessages { get; init; } = new();
    
    /// <summary>
    /// 扩展属性
    /// </summary>
    public Dictionary<string, object> Properties { get; init; } = new();
    
    /// <summary>
    /// 创建成功结果
    /// </summary>
    /// <param name="discountAmount">折扣金额</param>
    /// <param name="consumedItems">消耗的商品项</param>
    /// <param name="giftItems">赠品项</param>
    /// <param name="applicationCount">应用次数</param>
    /// <returns>执行结果</returns>
    public static ExecutionResult Success(
        decimal discountAmount = 0,
        List<ConsumedItem>? consumedItems = null,
        List<GiftItem>? giftItems = null,
        int applicationCount = 1)
    {
        return new ExecutionResult
        {
            IsSuccessful = true,
            DiscountAmount = discountAmount,
            ConsumedItems = consumedItems ?? new(),
            GiftItems = giftItems ?? new(),
            ApplicationCount = applicationCount
        };
    }
    
    /// <summary>
    /// 创建失败结果
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>执行结果</returns>
    public static ExecutionResult Failure(string errorMessage)
    {
        return new ExecutionResult
        {
            IsSuccessful = false,
            ErrorMessage = errorMessage
        };
    }
    
    /// <summary>
    /// 转换为传统的促销应用结果（向后兼容）
    /// </summary>
    /// <param name="ruleId">规则ID</param>
    /// <param name="ruleName">规则名称</param>
    /// <returns>促销应用结果</returns>
    public PromotionApplication ToPromotionApplication(string ruleId, string ruleName)
    {
        return new PromotionApplication
        {
            RuleId = ruleId,
            RuleName = ruleName,
            IsSuccessful = IsSuccessful,
            ErrorMessage = ErrorMessage,
            DiscountAmount = DiscountAmount,
            ConsumedItems = ConsumedItems,
            GiftItems = GiftItems,
            ApplicationCount = ApplicationCount
        };
    }
}

/// <summary>
/// 执行详情
/// </summary>
public sealed class ExecutionDetail
{
    /// <summary>
    /// 步骤名称
    /// </summary>
    public required string StepName { get; init; }
    
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccessful { get; init; }
    
    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; init; } = string.Empty;
    
    /// <summary>
    /// 执行时间
    /// </summary>
    public DateTime ExecutedAt { get; init; } = DateTime.UtcNow;
    
    /// <summary>
    /// 耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; init; }
    
    /// <summary>
    /// 相关数据
    /// </summary>
    public Dictionary<string, object> Data { get; init; } = new();
}

/// <summary>
/// 批量执行结果
/// </summary>
public sealed class BatchExecutionResult
{
    /// <summary>
    /// 总体是否成功
    /// </summary>
    public bool IsSuccessful { get; init; }
    
    /// <summary>
    /// 成功的执行结果
    /// </summary>
    public List<ExecutionResult> SuccessfulResults { get; init; } = new();
    
    /// <summary>
    /// 失败的执行结果
    /// </summary>
    public List<ExecutionResult> FailedResults { get; init; } = new();
    
    /// <summary>
    /// 总折扣金额
    /// </summary>
    public decimal TotalDiscountAmount => SuccessfulResults.Sum(r => r.DiscountAmount);
    
    /// <summary>
    /// 总应用次数
    /// </summary>
    public int TotalApplicationCount => SuccessfulResults.Sum(r => r.ApplicationCount);
    
    /// <summary>
    /// 执行摘要
    /// </summary>
    public string Summary => $"成功: {SuccessfulResults.Count}, 失败: {FailedResults.Count}, 总折扣: {TotalDiscountAmount:C}";
    
    /// <summary>
    /// 创建批量结果
    /// </summary>
    /// <param name="results">执行结果列表</param>
    /// <returns>批量执行结果</returns>
    public static BatchExecutionResult Create(List<ExecutionResult> results)
    {
        var successful = results.Where(r => r.IsSuccessful).ToList();
        var failed = results.Where(r => !r.IsSuccessful).ToList();
        
        return new BatchExecutionResult
        {
            IsSuccessful = failed.Count == 0,
            SuccessfulResults = successful,
            FailedResults = failed
        };
    }
}
