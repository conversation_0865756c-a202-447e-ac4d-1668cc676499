<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>促销规则配置示例</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .example-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .example-section {
            margin-bottom: 32px;
        }
        .example-title {
            font-size: 18px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 16px;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 8px;
        }
        .example-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
        }
        .example-card {
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 16px;
            background: #fafafa;
        }
        .example-card h4 {
            margin: 0 0 8px 0;
            color: #1890ff;
        }
        .example-card p {
            margin: 4px 0;
            font-size: 14px;
            color: #595959;
        }
        .example-json {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 4px;
            padding: 12px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .link-button {
            display: inline-block;
            background: #1890ff;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 16px;
        }
        .link-button:hover {
            background: #40a9ff;
        }
    </style>
</head>
<body>
    <div class="example-container">
        <h1>促销规则配置系统</h1>
        <p>这是一个基于Vue.js的促销规则配置界面，支持6种类型的促销规则配置。</p>
        
        <a href="./index.html" class="link-button">打开配置界面</a>
        
        <div class="example-section">
            <div class="example-title">支持的促销类型</div>
            <div class="example-grid">
                <div class="example-card">
                    <h4>🎁 买免规则</h4>
                    <p><strong>商品买免:</strong> 购买指定商品满足条件时免费获得</p>
                    <p><strong>组合买免:</strong> 购买多种商品组合满足条件时免费获得</p>
                </div>
                
                <div class="example-card">
                    <h4>🎉 买赠规则</h4>
                    <p><strong>统一买赠:</strong> 购买指定商品满足条件时赠送商品</p>
                    <p><strong>阶梯买赠:</strong> 根据购买数量阶梯提供不同赠品</p>
                    <p><strong>组合买赠:</strong> 购买多种商品组合时提供赠品</p>
                </div>
                
                <div class="example-card">
                    <h4>💰 减现规则</h4>
                    <p><strong>统一减现:</strong> 满足条件时减免固定金额</p>
                    <p><strong>阶梯减现:</strong> 根据购买金额阶梯减免不同金额</p>
                    <p><strong>组合减现:</strong> 购买多种商品组合时减免金额</p>
                </div>
                
                <div class="example-card">
                    <h4>📊 打折规则</h4>
                    <p><strong>统一打折:</strong> 满足条件时统一打折</p>
                    <p><strong>阶梯打折:</strong> 根据购买数量阶梯打折</p>
                    <p><strong>组合打折:</strong> 购买多种商品组合时打折</p>
                </div>
                
                <div class="example-card">
                    <h4>🔄 换购规则</h4>
                    <p><strong>统一特价换购:</strong> 满足条件时以特价换购商品</p>
                    <p><strong>统一优惠换购:</strong> 满足条件时优惠一定金额换购</p>
                    <p><strong>组合特价换购:</strong> 购买组合商品时特价换购</p>
                </div>
                
                <div class="example-card">
                    <h4>💸 特价规则</h4>
                    <p><strong>统一特价:</strong> 指定商品统一特价销售</p>
                    <p><strong>阶梯特价:</strong> 根据购买数量阶梯特价</p>
                    <p><strong>组合特价:</strong> 购买组合商品时特价销售</p>
                </div>
            </div>
        </div>
        
        <div class="example-section">
            <div class="example-title">配置示例</div>
            
            <h4>统一优惠换购示例</h4>
            <div class="example-json">{
  "$type": "UnifiedDiscountAmountExchange",
  "id": "EXCHANGE_DISCOUNT_AMOUNT_001",
  "name": "统一优惠换购测试 - 买A享B商品优惠100元",
  "description": "购买A商品大于等于1件时，可以优惠100元购买B商品",
  "priority": 50,
  "isEnabled": true,
  "startTime": "2024-01-01T00:00:00",
  "endTime": "2025-12-31T23:59:59",
  "isRepeatable": false,
  "maxApplications": 1,
  "applicableCustomerTypes": [],
  "exclusiveRuleIds": [],
  "canStackWithOthers": false,
  "productExclusivity": "Exclusive",
  "buyConditions": [
    {
      "productIds": ["A"],
      "requiredQuantity": 1,
      "requiredAmount": 0
    }
  ],
  "exchangeConditions": [
    {
      "exchangeProductIds": ["B"],
      "exchangeQuantity": 1,
      "discountAmount": 100.00
    }
  ]
}</div>
            
            <h4>组合买赠示例</h4>
            <div class="example-json">{
  "$type": "CombinationGift",
  "id": "GIFT_COMBINATION_001",
  "name": "组合买赠测试 - 买A+B送C",
  "description": "购买A、B商品各大于等于1件时，赠送C商品1件",
  "priority": 40,
  "isEnabled": true,
  "startTime": "2024-01-01T00:00:00",
  "endTime": "2025-12-31T23:59:59",
  "isRepeatable": true,
  "maxApplications": 0,
  "applicableCustomerTypes": [],
  "exclusiveRuleIds": [],
  "canStackWithOthers": true,
  "productExclusivity": "Shared",
  "combinationConditions": [
    {
      "productId": ["A"],
      "requiredQuantity": 1,
      "requiredAmount": 0
    },
    {
      "productId": ["B"],
      "requiredQuantity": 1,
      "requiredAmount": 0
    }
  ],
  "giftConditions": [
    {
      "giftProductIds": ["C"],
      "giftQuantity": 1
    }
  ]
}</div>
        </div>
        
        <div class="example-section">
            <div class="example-title">功能特性</div>
            <div class="example-grid">
                <div class="example-card">
                    <h4>🎯 类型化配置</h4>
                    <p>根据不同促销类型动态生成对应的配置表单</p>
                    <p>支持数组字段、条件配置、标签输入等复杂表单组件</p>
                </div>
                
                <div class="example-card">
                    <h4>📝 实时预览</h4>
                    <p>实时生成JSON配置预览</p>
                    <p>支持JSON格式验证和下载导出</p>
                </div>
                
                <div class="example-card">
                    <h4>📥 导入导出</h4>
                    <p>支持从JSON文件导入配置</p>
                    <p>支持导出JSON配置文件</p>
                </div>
                
                <div class="example-card">
                    <h4>💾 本地存储</h4>
                    <p>自动保存配置到浏览器本地存储</p>
                    <p>刷新页面后数据不丢失</p>
                </div>
                
                <div class="example-card">
                    <h4>📱 响应式设计</h4>
                    <p>适配桌面和移动设备</p>
                    <p>流畅的用户交互体验</p>
                </div>
                
                <div class="example-card">
                    <h4>🔧 可扩展性</h4>
                    <p>基于配置驱动的表单生成</p>
                    <p>易于添加新的促销类型</p>
                </div>
            </div>
        </div>
        
        <div class="example-section">
            <div class="example-title">使用说明</div>
            <ol>
                <li><strong>选择促销类型:</strong> 从左侧面板选择需要配置的促销类型</li>
                <li><strong>填写基础信息:</strong> 填写规则ID、名称、描述等基础信息</li>
                <li><strong>配置条件:</strong> 根据促销类型配置相应的触发条件</li>
                <li><strong>设置高级选项:</strong> 配置客户类型、独占性等高级选项</li>
                <li><strong>预览和验证:</strong> 在右侧预览生成的JSON配置并进行验证</li>
                <li><strong>保存或导出:</strong> 保存到本地存储或导出为JSON文件</li>
            </ol>
        </div>
    </div>
</body>
</html>
