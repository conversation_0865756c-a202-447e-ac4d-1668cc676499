# POSPE2 促销引擎 API 测试脚本
# PowerShell 脚本用于测试促销引擎的各种功能

$baseUrl = "http://localhost:5213"

Write-Host "=== POSPE2 促销引擎 API 测试 ===" -ForegroundColor Green
Write-Host "基础URL: $baseUrl" -ForegroundColor Yellow
Write-Host ""

# 测试健康检查
Write-Host "1. 测试健康检查..." -ForegroundColor Cyan
try {
    $healthResponse = Invoke-RestMethod -Uri "$baseUrl/health" -Method Get
    Write-Host "✓ 健康检查通过: $($healthResponse.Status)" -ForegroundColor Green
} catch {
    Write-Host "✗ 健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 获取促销规则统计
Write-Host "`n2. 获取促销统计信息..." -ForegroundColor Cyan
try {
    $statsResponse = Invoke-RestMethod -Uri "$baseUrl/api/promotion/statistics" -Method Get
    Write-Host "✓ 促销统计获取成功:" -ForegroundColor Green
    Write-Host "  - 总规则数: $($statsResponse.totalRules)" -ForegroundColor White
    Write-Host "  - 启用规则数: $($statsResponse.enabledRules)" -ForegroundColor White
    Write-Host "  - 有效规则数: $($statsResponse.validRules)" -ForegroundColor White
} catch {
    Write-Host "✗ 获取促销统计失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 创建测试购物车
Write-Host "`n3. 创建测试购物车..." -ForegroundColor Cyan
$testCart = @{
    id = "TEST_CART_$(Get-Date -Format 'yyyyMMddHHmmss')"
    customerId = "TEST_CUSTOMER"
    items = @(
        @{
            product = @{
                id = "A"
                name = "商品A"
                price = 50.00
                category = "电子产品"
                barcode = "1234567890001"
                brand = "品牌A"
            }
            quantity = 1
            unitPrice = 50.00
        },
        @{
            product = @{
                id = "B"
                name = "商品B"
                price = 30.00
                category = "服装"
                barcode = "1234567890002"
                brand = "品牌B"
            }
            quantity = 2
            unitPrice = 30.00
        },
        @{
            product = @{
                id = "C"
                name = "商品C"
                price = 20.00
                category = "家居"
                barcode = "1234567890003"
                brand = "品牌C"
            }
            quantity = 5
            unitPrice = 20.00
        }
    )
}

$cartJson = $testCart | ConvertTo-Json -Depth 10
Write-Host "✓ 测试购物车创建完成 (A:1, B:2, C:5, 总价: 210元)" -ForegroundColor Green

# 获取促销预览
Write-Host "`n4. 获取促销预览..." -ForegroundColor Cyan
try {
    $previewResponse = Invoke-RestMethod -Uri "$baseUrl/api/promotion/preview" -Method Post -Body $cartJson -ContentType "application/json"
    Write-Host "✓ 促销预览获取成功:" -ForegroundColor Green
    foreach ($preview in $previewResponse) {
        $status = if ($preview.isApplicable) { "✓ 可用" } else { "✗ 不可用" }
        $color = if ($preview.isApplicable) { "Green" } else { "Yellow" }
        Write-Host "  - $($preview.ruleName): $status" -ForegroundColor $color
        if ($preview.isApplicable) {
            Write-Host "    预计优惠: $($preview.estimatedDiscount)" -ForegroundColor White
        } else {
            Write-Host "    原因: $($preview.reason)" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "✗ 获取促销预览失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 计算最优促销
Write-Host "`n5. 计算最优促销组合..." -ForegroundColor Cyan
try {
    $calculationResponse = Invoke-RestMethod -Uri "$baseUrl/api/promotion/calculate" -Method Post -Body $cartJson -ContentType "application/json"
    Write-Host "✓ 最优促销计算成功:" -ForegroundColor Green
    Write-Host "  - 原价: $($calculationResponse.originalAmount)" -ForegroundColor White
    Write-Host "  - 总优惠: $($calculationResponse.totalDiscount)" -ForegroundColor White
    Write-Host "  - 最终价格: $($calculationResponse.finalAmount)" -ForegroundColor White
    Write-Host "  - 优惠率: $([math]::Round($calculationResponse.discountRate * 100, 2))%" -ForegroundColor White
    Write-Host "  - 计算耗时: $($calculationResponse.calculationTimeMs)ms" -ForegroundColor White
    Write-Host "  - 是否最优: $($calculationResponse.isOptimal)" -ForegroundColor White
    
    if ($calculationResponse.appliedPromotions.Count -gt 0) {
        Write-Host "  - 应用的促销:" -ForegroundColor White
        foreach ($promotion in $calculationResponse.appliedPromotions) {
            Write-Host "    * $($promotion.ruleName): 优惠 $($promotion.discountAmount)" -ForegroundColor Cyan
        }
    }
    
    if ($calculationResponse.ignoredPromotions.Count -gt 0) {
        Write-Host "  - 忽略的促销:" -ForegroundColor White
        foreach ($ignored in $calculationResponse.ignoredPromotions) {
            Write-Host "    * $($ignored.ruleName): $($ignored.reason)" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "✗ 计算最优促销失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 运行性能基准测试（小规模）
Write-Host "`n6. 运行性能基准测试..." -ForegroundColor Cyan
try {
    $benchmarkResponse = Invoke-RestMethod -Uri "$baseUrl/api/performance/benchmark?iterations=10" -Method Post
    Write-Host "✓ 性能基准测试完成:" -ForegroundColor Green
    Write-Host "  - 测试场景数: $($benchmarkResponse.totalScenarios)" -ForegroundColor White
    Write-Host "  - 总迭代次数: $($benchmarkResponse.totalIterations)" -ForegroundColor White
    Write-Host "  - 平均响应时间: $([math]::Round($benchmarkResponse.overallStatistics.averageTime, 2))ms" -ForegroundColor White
    Write-Host "  - 最快响应时间: $($benchmarkResponse.overallStatistics.minTime)ms" -ForegroundColor White
    Write-Host "  - 最慢响应时间: $($benchmarkResponse.overallStatistics.maxTime)ms" -ForegroundColor White
    
    Write-Host "  - 各场景详情:" -ForegroundColor White
    foreach ($result in $benchmarkResponse.results) {
        Write-Host "    * $($result.scenarioName): 平均 $([math]::Round($result.averageTimeMs, 2))ms" -ForegroundColor Cyan
    }
} catch {
    Write-Host "✗ 性能基准测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 运行复杂度分析
Write-Host "`n7. 运行算法复杂度分析..." -ForegroundColor Cyan
try {
    $complexityResponse = Invoke-RestMethod -Uri "$baseUrl/api/performance/complexity-analysis" -Method Post
    Write-Host "✓ 复杂度分析完成:" -ForegroundColor Green
    Write-Host "  - 时间复杂度: $($complexityResponse.trends.timeComplexity)" -ForegroundColor White
    Write-Host "  - 空间复杂度: $($complexityResponse.trends.spaceComplexity)" -ForegroundColor White
    Write-Host "  - 可扩展性因子: $([math]::Round($complexityResponse.trends.scalabilityFactor, 2))" -ForegroundColor White
    
    Write-Host "  - 性能数据点:" -ForegroundColor White
    foreach ($result in $complexityResponse.results) {
        Write-Host "    * $($result.itemCount)个商品: $($result.calculationTimeMs)ms, $($result.stepsCount)步骤" -ForegroundColor Cyan
    }
} catch {
    Write-Host "✗ 复杂度分析失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "所有API测试已完成。如需查看详细的API文档，请访问: $baseUrl" -ForegroundColor Yellow
Write-Host "提示: 应用正在运行中，您可以通过浏览器访问Swagger UI进行交互式测试。" -ForegroundColor Cyan
