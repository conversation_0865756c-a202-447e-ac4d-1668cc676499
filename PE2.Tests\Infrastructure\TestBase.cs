using Microsoft.Extensions.Logging;
using Moq;
using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Rules;
using PE2.Services;
using Xunit;
using Xunit.Abstractions;
namespace PE2.Tests.Infrastructure;

/// <summary>
/// 测试基类
/// 提供通用的测试基础设施和辅助方法
/// </summary>
public abstract class TestBase
{
    protected readonly ITestOutputHelper Output;
    protected readonly Mock<ILogger<PromotionEngineService>> MockLogger;
    protected readonly PromotionEngineService PromotionEngine;

    protected TestBase(ITestOutputHelper output)
    {
        Output = output;
        MockLogger = new Mock<ILogger<PromotionEngineService>>();
        PromotionEngine = new PromotionEngineService(new TestPromotionRuleService(), MockLogger.Object);
    }

    #region 通用断言方法

    /// <summary>
    /// 断言促销计算结果的基本属性
    /// </summary>
    protected void AssertPromotionResult(PromotionResult result, string scenario = "")
    {
        var prefix = string.IsNullOrEmpty(scenario) ? "" : $"[{scenario}] ";

        Assert.NotNull(result);
        Output.WriteLine($"{prefix}促销计算结果验证:");
        Output.WriteLine($"  原始总额: {result.OriginalAmount:C}");
        Output.WriteLine($"  最终总额: {result.FinalAmount:C}");
        Output.WriteLine($"  总优惠: {result.TotalDiscount:C}");
        Output.WriteLine($"  应用规则数: {result.AppliedPromotions.Count}");
        Output.WriteLine($"  被忽略规则数: {result.IgnoredPromotions.Count}");

        // 基本数学关系验证
        Assert.True(result.OriginalAmount >= 0, "原始总额不能为负数");
        Assert.True(result.FinalAmount >= 0, "最终总额不能为负数");
        Assert.True(result.TotalDiscount >= 0, "总优惠不能为负数");
        Assert.Equal(result.OriginalAmount - result.TotalDiscount, result.FinalAmount);

        // 集合验证
        Assert.NotNull(result.AppliedPromotions);
        Assert.NotNull(result.IgnoredPromotions);

    }

    /// <summary>
    /// 断言购物车状态的一致性
    /// </summary>
    protected void AssertCartConsistency(ShoppingCart cart, string scenario = "")
    {
        var prefix = string.IsNullOrEmpty(scenario) ? "" : $"[{scenario}] ";

        Assert.NotNull(cart);
        Assert.NotNull(cart.Items);

        Output.WriteLine($"{prefix}购物车一致性验证:");

        foreach (var item in cart.Items)
        {
            Assert.NotNull(item.Product);
            Assert.True(item.Quantity > 0, $"商品 {item.Product.Id} 数量必须大于0");
            Assert.True(item.UnitPrice >= 0, $"商品 {item.Product.Id} 单价不能为负数");
            Assert.True(item.ActualUnitPrice >= 0, $"商品 {item.Product.Id} 实际单价不能为负数");

            Output.WriteLine($"  {item.Product.Id}: 数量={item.Quantity}, 原价={item.UnitPrice:C}, 实价={item.ActualUnitPrice:C}");
        }
    }

    /// <summary>
    /// 断言规则应用的正确性
    /// </summary>
    protected void AssertRuleApplication(AppliedPromotion appliedPromotion, string expectedRuleId = null)
    {
        Assert.NotNull(appliedPromotion);
        Assert.False(string.IsNullOrEmpty(appliedPromotion.RuleId));
        Assert.True(appliedPromotion.DiscountAmount >= 0, "折扣金额不能为负数");

        if (!string.IsNullOrEmpty(expectedRuleId))
        {
            Assert.Equal(expectedRuleId, appliedPromotion.RuleId);
        }

        Output.WriteLine($"应用规则: {appliedPromotion.RuleName} (ID: {appliedPromotion.RuleId})");
        Output.WriteLine($"  折扣金额: {appliedPromotion.DiscountAmount:C}");
        Output.WriteLine($"  应用详情: {appliedPromotion.Description}");
    }

    /// <summary>
    /// 断言忽略规则的原因
    /// </summary>
    protected void AssertIgnoredRule(IgnoredPromotion ignoredPromotion, string expectedReason = null)
    {
        Assert.NotNull(ignoredPromotion);
        Assert.False(string.IsNullOrEmpty(ignoredPromotion.RuleId));
        Assert.False(string.IsNullOrEmpty(ignoredPromotion.Reason));

        if (!string.IsNullOrEmpty(expectedReason))
        {
            Assert.Contains(expectedReason, ignoredPromotion.Reason);
        }

        Output.WriteLine($"忽略规则: {ignoredPromotion.RuleName} (ID: {ignoredPromotion.RuleId})");
        Output.WriteLine($"  忽略原因: {ignoredPromotion.Reason}");
    }

    /// <summary>
    /// 断言优惠金额在预期范围内
    /// </summary>
    protected void AssertDiscountInRange(decimal actualDiscount, decimal expectedMin, decimal expectedMax, string context = "")
    {
        var message = string.IsNullOrEmpty(context) ? "" : $" ({context})";
        Assert.True(actualDiscount >= expectedMin && actualDiscount <= expectedMax,
            $"优惠金额 {actualDiscount:C} 不在预期范围 [{expectedMin:C}, {expectedMax:C}] 内{message}");

        Output.WriteLine($"优惠金额验证{message}: {actualDiscount:C} ∈ [{expectedMin:C}, {expectedMax:C}] ✓");
    }

    /// <summary>
    /// 断言两个金额相等（考虑精度）
    /// </summary>
    protected void AssertAmountEqual(decimal expected, decimal actual, string context = "", decimal tolerance = 0.01m)
    {
        var difference = Math.Abs(expected - actual);
        var message = string.IsNullOrEmpty(context) ? "" : $" ({context})";

        Assert.True(difference <= tolerance,
            $"金额不匹配{message}: 期望 {expected:C}, 实际 {actual:C}, 差异 {difference:C}");

        Output.WriteLine($"金额验证{message}: {expected:C} = {actual:C} ✓");
    }

    #endregion

    #region 测试数据验证方法

    /// <summary>
    /// 验证测试数据的有效性
    /// </summary>
    protected void ValidateTestData(ShoppingCart cart, List<PromotionRuleBase> rules)
    {
        Assert.NotNull(cart);
        Assert.NotNull(rules);

        // 验证购物车
        AssertCartConsistency(cart, "测试数据验证");

        // 验证规则
        foreach (var rule in rules)
        {
            Assert.NotNull(rule);
            Assert.False(string.IsNullOrEmpty(rule.Id));
            Assert.False(string.IsNullOrEmpty(rule.Name));
            Assert.True(rule.IsEnabled, $"测试规则 {rule.Id} 应该是启用状态");
        }

        Output.WriteLine($"测试数据验证完成: 购物车包含 {cart.Items.Count} 个商品项, {rules.Count} 个促销规则");
    }

    /// <summary>
    /// 验证规则的基本属性
    /// </summary>
    protected void ValidateRuleBasicProperties(PromotionRuleBase rule)
    {
        Assert.NotNull(rule);
        Assert.False(string.IsNullOrEmpty(rule.Id), "规则ID不能为空");
        Assert.False(string.IsNullOrEmpty(rule.Name), "规则名称不能为空");
        Assert.True(rule.Priority >= 0, "规则优先级不能为负数");

        Output.WriteLine($"规则基本属性验证: {rule.Name} (ID: {rule.Id}, 优先级: {rule.Priority}) ✓");
    }

    #endregion

    #region 性能测试辅助方法

    /// <summary>
    /// 测量方法执行时间
    /// </summary>
    protected TimeSpan MeasureExecutionTime(Action action)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        action();
        stopwatch.Stop();
        return stopwatch.Elapsed;
    }

    /// <summary>
    /// 测量异步方法执行时间
    /// </summary>
    protected async Task<TimeSpan> MeasureExecutionTimeAsync(Func<Task> action)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        await action();
        stopwatch.Stop();
        return stopwatch.Elapsed;
    }

    /// <summary>
    /// 断言执行时间在可接受范围内
    /// </summary>
    protected void AssertPerformance(TimeSpan actualTime, TimeSpan maxExpectedTime, string operation = "")
    {
        var message = string.IsNullOrEmpty(operation) ? "操作" : operation;
        Assert.True(actualTime <= maxExpectedTime,
            $"{message}执行时间 {actualTime.TotalMilliseconds:F2}ms 超过预期的 {maxExpectedTime.TotalMilliseconds:F2}ms");

        Output.WriteLine($"性能验证: {message}执行时间 {actualTime.TotalMilliseconds:F2}ms ≤ {maxExpectedTime.TotalMilliseconds:F2}ms ✓");
    }

    #endregion

    #region 日志和调试辅助方法

    /// <summary>
    /// 输出详细的促销结果信息
    /// </summary>
    protected void LogPromotionResultDetails(PromotionResult result, string title = "促销计算结果")
    {
        Output.WriteLine($"\n=== {title} ===");
        Output.WriteLine($"原始总额: {result.OriginalAmount:C}");
        Output.WriteLine($"最终总额: {result.FinalAmount:C}");
        Output.WriteLine($"总优惠: {result.TotalDiscount:C}");
        Output.WriteLine($"优惠率: {(result.OriginalAmount > 0 ? result.TotalDiscount / result.OriginalAmount * 100 : 0):F2}%");

        if (result.AppliedPromotions.Any())
        {
            Output.WriteLine("\n应用的规则:");
            foreach (var appliedPromotion in result.AppliedPromotions)
            {
                Output.WriteLine($"  - {appliedPromotion.RuleName}: {appliedPromotion.DiscountAmount:C}");
                if (!string.IsNullOrEmpty(appliedPromotion.Description))
                {
                    Output.WriteLine($"    详情: {appliedPromotion.Description}");
                }
            }
        }

        if (result.IgnoredPromotions.Any())
        {
            Output.WriteLine("\n忽略的规则:");
            foreach (var ignoredPromotion in result.IgnoredPromotions)
            {
                Output.WriteLine($"  - {ignoredPromotion.RuleName}: {ignoredPromotion.Reason}");
            }
        }

        Output.WriteLine("\n购物车明细:");
        foreach (var item in result.ProcessedCart.Items)
        {
            var discount = (item.UnitPrice - item.ActualUnitPrice) * item.Quantity;
            Output.WriteLine($"  {item.Product.Name} x{item.Quantity}: {item.UnitPrice:C} → {item.ActualUnitPrice:C} (优惠: {discount:C})");
        }

        Output.WriteLine($"\n=== {title}结束 ===\n");
    }

    /// <summary>
    /// 输出购物车详细信息
    /// </summary>
    protected void LogCartDetails(ShoppingCart cart, string title = "购物车详情")
    {
        Output.WriteLine($"\n=== {title} ===");
        Output.WriteLine($"购物车ID: {cart.Id}");
        Output.WriteLine($"客户ID: {cart.CustomerId}");
        Output.WriteLine($"商品项数: {cart.Items.Count}");

        var totalOriginal = cart.Items.Sum(i => i.UnitPrice * i.Quantity);
        var totalActual = cart.Items.Sum(i => i.ActualUnitPrice * i.Quantity);

        Output.WriteLine($"原始总额: {totalOriginal:C}");
        Output.WriteLine($"实际总额: {totalActual:C}");

        Output.WriteLine("\n商品明细:");
        foreach (var item in cart.Items)
        {
            Output.WriteLine($"  {item.Product.Id} - {item.Product.Name}:");
            Output.WriteLine($"    数量: {item.Quantity}");
            Output.WriteLine($"    原价: {item.UnitPrice:C}");
            Output.WriteLine($"    实价: {item.ActualUnitPrice:C}");
            Output.WriteLine($"    小计: {item.ActualUnitPrice * item.Quantity:C}");
        }

        Output.WriteLine($"\n=== {title}结束 ===\n");
    }

    /// <summary>
    /// 输出规则列表信息
    /// </summary>
    protected void LogRulesInfo(List<PromotionRuleBase> rules, string title = "规则列表")
    {
        Output.WriteLine($"\n=== {title} ===");
        Output.WriteLine($"规则总数: {rules.Count}");

        var rulesByType = rules.GroupBy(r => r.GetType().Name).ToList();
        Output.WriteLine("\n按类型分组:");
        foreach (var group in rulesByType)
        {
            Output.WriteLine($"  {group.Key}: {group.Count()} 个");
        }

        Output.WriteLine("\n规则详情:");
        foreach (var rule in rules.OrderBy(r => r.Priority))
        {
            Output.WriteLine($"  [{rule.Priority}] {rule.Name} ({rule.GetType().Name})");
            Output.WriteLine($"      ID: {rule.Id}");
            Output.WriteLine($"      启用: {rule.IsEnabled}");
            if (!string.IsNullOrEmpty(rule.Description))
            {
                Output.WriteLine($"      描述: {rule.Description}");
            }
        }

        Output.WriteLine($"\n=== {title}结束 ===\n");
    }

    #endregion

    #region 比较和验证方法

    /// <summary>
    /// 比较两个促销结果
    /// </summary>
    protected void ComparePromotionResults(PromotionResult expected, PromotionResult actual, string context = "")
    {
        var prefix = string.IsNullOrEmpty(context) ? "" : $"[{context}] ";

        AssertAmountEqual(expected.OriginalAmount, actual.OriginalAmount, $"{prefix}原始总额");
        AssertAmountEqual(expected.FinalAmount, actual.FinalAmount, $"{prefix}最终总额");
        AssertAmountEqual(expected.TotalDiscount, actual.TotalDiscount, $"{prefix}总优惠");

        Assert.Equal(expected.AppliedPromotions.Count, actual.AppliedPromotions.Count);

        Output.WriteLine($"{prefix}促销结果比较完成 ✓");
    }

    /// <summary>
    /// 验证规则互斥性
    /// </summary>
    protected void ValidateRuleExclusivity(PromotionResult result, List<string> mutuallyExclusiveRuleIds)
    {
        var appliedRuleIds = result.AppliedPromotions.Select(ap => ap.RuleId).ToList();
        var appliedExclusiveRules = appliedRuleIds.Intersect(mutuallyExclusiveRuleIds).ToList();

        Assert.True(appliedExclusiveRules.Count <= 1,
            $"互斥规则不应同时应用: {string.Join(", ", appliedExclusiveRules)}");

        if (appliedExclusiveRules.Count == 1)
        {
            Output.WriteLine($"互斥规则验证: 仅应用了 {appliedExclusiveRules[0]} ✓");
        }
        else
        {
            Output.WriteLine("互斥规则验证: 无互斥规则应用 ✓");
        }
    }

    #endregion
}

//构建一个测试用的 IPromotionRuleService 并实现必要的模拟方法
public class TestPromotionRuleService : IPromotionRuleService
{
    public static List<PromotionRuleBase> Rules;
    public TestPromotionRuleService()
    {

    }
    public Task<List<PromotionRuleBase>> GetAllRulesAsync()
    {
        return Task.FromResult(Rules);
    }
    public Task<PromotionRuleBase> GetRuleByIdAsync(string ruleId)
    {
        var rule = Rules.FirstOrDefault(r => r.Id == ruleId);
        return Task.FromResult(rule);
    }
    public Task<bool> AddRuleAsync(PromotionRuleBase rule)
    {
        if (Rules.Any(r => r.Id == rule.Id))
            return Task.FromResult(false);
        Rules.Add(rule);
        return Task.FromResult(true);
    }
    public Task<bool> UpdateRuleAsync(PromotionRuleBase rule)
    {
        var existing = Rules.FirstOrDefault(r => r.Id == rule.Id);
        if (existing == null)
            return Task.FromResult(false);
        existing.Name = rule.Name;
        existing.Description = rule.Description;
        existing.Priority = rule.Priority;
        existing.IsEnabled = rule.IsEnabled;
        existing.StartTime = rule.StartTime;
        existing.EndTime = rule.EndTime;
        existing.IsRepeatable = rule.IsRepeatable;
        existing.MaxApplications = rule.MaxApplications;
        existing.ApplicableCustomerTypes = rule.ApplicableCustomerTypes;
        return Task.FromResult(true);
    }
    public Task<bool> DeleteRuleAsync(string ruleId)
    {
        var rule = Rules.FirstOrDefault(r => r.Id == ruleId);
        if (rule == null)
            return Task.FromResult(false);
        Rules.Remove(rule);
        return Task.FromResult(true);
    }

    public Task<List<PromotionRuleBase>> GetEnabledRulesAsync()
    {
        throw new NotImplementedException();
    }

    public async Task<List<PromotionRuleBase>> GetValidRulesAsync(DateTime? checkTime = null)
    {
        return await Task.FromResult(Rules);
    }

    public Task ReloadRulesAsync()
    {
        throw new NotImplementedException();
    }

    public Task<bool> SetRuleEnabledAsync(string ruleId, bool enabled)
    {
        throw new NotImplementedException();
    }

    public Task<(bool IsValid, string ErrorMessage)> ValidateRulesFileAsync()
    {
        throw new NotImplementedException();
    }
}