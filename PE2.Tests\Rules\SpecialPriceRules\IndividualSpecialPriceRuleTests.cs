using PE2.PromotionEngine.Rules.SpecialPriceRules;
using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.SpecialPriceRules;

/// <summary>
/// 单品特价规则测试类
/// 测试 IndividualSpecialPriceRule 的单品特价计算和应用逻辑
/// </summary>
public class IndividualSpecialPriceRuleTests(ITestOutputHelper output) : TestBase(output)
{
    #region 基础单品特价功能测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_SingleProductSpecialPrice_ShouldApplyCorrectly()
    {
        // Arrange - A商品特价600元
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_001",
            Name = "单品特价测试 - A商品特价600元",
            Description = "A商品特价600元",
            Priority = 60,
            IsEnabled = true,
            ProductConfigs =
            [
                new ProductSpecialPriceConfig
                {
                    ProductId = "A",
                    SpecialPrice = 600.00m,
                    Description = "A商品特价600元"
                }
            ]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            (TestDataGenerator.CreateProductA(), 2) // 2件A商品
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "应用前购物车");

        var originalPrice = TestDataGenerator.CreateProductA().Price; // 200元
        var expectedSpecialPrice = 600m; // 特价600元
        var expectedTotalDiscount = (originalPrice - expectedSpecialPrice) * 2; // (200-600)*2 = -800元（实际是增加价格）

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "A商品特价600元");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证商品A的实际单价被调整为特价
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(expectedSpecialPrice, productAItem.ActualUnitPrice, "商品A的实际单价应为特价600元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_MultipleProductsSpecialPrice_ShouldApplyCorrectly()
    {
        // Arrange - A商品特价600元，B商品特价500元
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_001",
            Name = "单品特价测试 - A、B商品独立特价",
            Description = "A商品特价600元，B商品特价500元",
            IsEnabled = true,
            ProductConfigs =
            [
                new ProductSpecialPriceConfig
                {
                    ProductId = "A",
                    SpecialPrice = 600.00m,
                    Description = "A商品特价600元"
                },
                new ProductSpecialPriceConfig
                {
                    ProductId = "B",
                    SpecialPrice = 500.00m,
                    Description = "B商品特价500元"
                }
            ]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_002",
            (TestDataGenerator.CreateProductA(), 1), // 1件A商品
            (TestDataGenerator.CreateProductB(), 2) // 2件B商品
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "多商品特价购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "A、B商品独立特价");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证商品A的实际单价被调整为特价
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(600m, productAItem.ActualUnitPrice, "商品A的实际单价应为特价600元");

        // 验证商品B的实际单价被调整为特价
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        AssertAmountEqual(500m, productBItem.ActualUnitPrice, "商品B的实际单价应为特价500元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_PartialProductsInCart_ShouldApplyOnlyAvailable()
    {
        // Arrange - 配置A、B商品特价，但购物车只有A商品
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_001",
            Name = "单品特价测试 - A、B商品独立特价",
            IsEnabled = true,
            ProductConfigs =
            [
                new ProductSpecialPriceConfig { ProductId = "A", SpecialPrice = 600.00m },
                new ProductSpecialPriceConfig { ProductId = "B", SpecialPrice = 500.00m }
            ]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_003",
            (TestDataGenerator.CreateProductA(), 2), // 只有A商品，没有B商品
            (TestDataGenerator.CreateProductC(), 1) // 其他商品
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "部分商品在购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "部分商品特价场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证只有A商品的实际单价被调整为特价
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(600m, productAItem.ActualUnitPrice, "商品A的实际单价应为特价600元");

        // 验证C商品价格未变
        var productCItem = result.ProcessedCart.Items.First(i => i.Product.Id == "C");
        AssertAmountEqual(
            TestDataGenerator.CreateProductC().Price,
            productCItem.ActualUnitPrice,
            "商品C价格应保持原价"
        );

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_NoConfiguredProductsInCart_ShouldNotApply()
    {
        // Arrange - 购物车中没有配置特价的商品
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_001",
            Name = "单品特价测试 - A、B商品独立特价",
            IsEnabled = true,
            ProductConfigs =
            [
                new ProductSpecialPriceConfig { ProductId = "A", SpecialPrice = 600.00m },
                new ProductSpecialPriceConfig { ProductId = "B", SpecialPrice = 500.00m }
            ]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_004",
            (TestDataGenerator.CreateProductC(), 3), // 只有C商品，没有A、B
            (TestDataGenerator.CreateProductD(), 1)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "无配置商品的购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "无配置商品场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "无配置商品时应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 特价优惠测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_SpecialPriceLowerThanOriginal_ShouldProvideDiscount()
    {
        // Arrange - B商品特价20元（原价30元，有优惠）
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_DISCOUNT_001",
            Name = "单品特价测试 - B商品优惠特价",
            IsEnabled = true,
            ProductConfigs =
            [
                new ProductSpecialPriceConfig
                {
                    ProductId = "B",
                    SpecialPrice = 20.00m, // 特价20元，原价30元
                    Description = "B商品特价20元"
                }
            ]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_005",
            (TestDataGenerator.CreateProductB(), 3) // 3件B商品
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "优惠特价购物车");

        var originalPrice = TestDataGenerator.CreateProductB().Price; // 30元
        var expectedSpecialPrice = 20m; // 特价20元
        var expectedTotalDiscount = (originalPrice - expectedSpecialPrice) * 3; // (30-20)*3 = 30元

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "B商品优惠特价20元");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证商品B的实际单价被调整为特价
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        AssertAmountEqual(expectedSpecialPrice, productBItem.ActualUnitPrice, "商品B的实际单价应为特价20元");

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为3件商品的优惠金额");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_SpecialPriceHigherThanOriginal_ShouldIncreasePrice()
    {
        // Arrange - B商品特价50元（原价30元，价格上涨）
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_INCREASE_001",
            Name = "单品特价测试 - B商品涨价特价",
            IsEnabled = true,
            ProductConfigs =
            [
                new ProductSpecialPriceConfig
                {
                    ProductId = "B",
                    SpecialPrice = 50.00m, // 特价50元，原价30元
                    Description = "B商品特价50元"
                }
            ]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_006",
            (TestDataGenerator.CreateProductB(), 2) // 2件B商品
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "涨价特价购物车");

        var originalPrice = TestDataGenerator.CreateProductB().Price; // 30元
        var expectedSpecialPrice = 50m; // 特价50元
        var expectedTotalDiscount = (originalPrice - expectedSpecialPrice) * 2; // (30-50)*2 = -40元（负优惠，实际是增加价格）

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "B商品涨价特价50元");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证商品B的实际单价被调整为特价
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        AssertAmountEqual(expectedSpecialPrice, productBItem.ActualUnitPrice, "商品B的实际单价应为特价50元");

        // 验证总优惠金额（负值表示价格增加）
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为负值，表示价格增加");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public async Task Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange - 空购物车
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_001",
            Name = "单品特价测试 - A商品特价600元",
            IsEnabled = true,
            ProductConfigs =
            [
                new ProductSpecialPriceConfig { ProductId = "A", SpecialPrice = 600.00m }
            ]
        };

        var cart = TestDataGenerator.CreateEmptyCart();

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "空购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "空购物车场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "空购物车应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange - 禁用的规则
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_001",
            Name = "单品特价测试 - A商品特价600元",
            IsEnabled = false, // 禁用规则
            ProductConfigs =
            [
                new ProductSpecialPriceConfig { ProductId = "A", SpecialPrice = 600.00m }
            ]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_008",
            (TestDataGenerator.CreateProductA(), 2)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "禁用规则测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "禁用规则场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "规则已禁用");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "禁用规则应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_ZeroSpecialPrice_ShouldApplyCorrectly()
    {
        // Arrange - 零特价（免费）
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_FREE_001",
            Name = "单品特价测试 - B商品免费",
            IsEnabled = true,
            ProductConfigs =
            [
                new ProductSpecialPriceConfig
                {
                    ProductId = "B",
                    SpecialPrice = 0.00m, // 免费
                    Description = "B商品免费"
                }
            ]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_010",
            (TestDataGenerator.CreateProductB(), 2) // 2件B商品
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "免费特价购物车");

        var originalPrice = TestDataGenerator.CreateProductB().Price; // 30元
        var expectedSpecialPrice = 0m; // 免费
        var expectedTotalDiscount = originalPrice * 2; // 30*2 = 60元

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "B商品免费特价");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证商品B的实际单价被调整为免费
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        AssertAmountEqual(expectedSpecialPrice, productBItem.ActualUnitPrice, "商品B的实际单价应为免费");

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为2件商品的原价");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_ExactQuantityMatch_ShouldApplyCorrectly()
    {
        // Arrange - 恰好满足数量要求
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_BOUNDARY_001",
            Name = "单品特价测试 - 边界条件",
            ProductConfigs = [new() { ProductId = "A", SpecialPrice = 150.00m }],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_008",
            (TestDataGenerator.CreateProductA(), 1) // 恰好1个
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "单品边界测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "单品边界场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证单品特价被正确应用
        var expectedDiscount = 200m - 150m; // 原价200 - 特价150 = 50优惠
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应应用单品特价优惠50元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_VeryHighSpecialPrice_ShouldHandleCorrectly()
    {
        // Arrange - 极高特价（高于原价）
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_HIGH_001",
            Name = "单品特价测试 - 极高特价",
            ProductConfigs =
            [
                new() { ProductId = "A", SpecialPrice = 1000.00m } // 远高于原价200
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_010",
            (TestDataGenerator.CreateProductA(), 1)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "极高特价测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "极高特价场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确处理（可能不应用或按原价处理）
        // 具体行为取决于业务逻辑实现

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 可重复性测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_MultipleApplications_ShouldBeConsistent()
    {
        // Arrange - 多次应用同一规则应该得到一致结果
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_REPEAT_001",
            Name = "单品特价测试 - 可重复性",
            ProductConfigs =
            [
                new() { ProductId = "A", SpecialPrice = 150.00m },
                new() { ProductId = "B", SpecialPrice = 100.00m }
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_011",
            (TestDataGenerator.CreateProductA(), 3),
            (TestDataGenerator.CreateProductB(), 2)
        );

        ValidateTestData(cart, [rule]);
        TestPromotionRuleService.Rules = [rule];

        // Act - 多次执行
        var result1 = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;
        var result2 = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;
        var result3 = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert - 结果应该完全一致
        AssertAmountEqual(result1.TotalDiscount, result2.TotalDiscount, "第一次和第二次结果应一致");
        AssertAmountEqual(result2.TotalDiscount, result3.TotalDiscount, "第二次和第三次结果应一致");

        Assert.Equal(result1.AppliedPromotions.Count, result2.AppliedPromotions.Count);
        Assert.Equal(result2.AppliedPromotions.Count, result3.AppliedPromotions.Count);

        // 验证购物车一致性
        AssertCartConsistency(result1.ProcessedCart);
        AssertCartConsistency(result2.ProcessedCart);
        AssertCartConsistency(result3.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_SameRuleMultipleProducts_ShouldApplyToAll()
    {
        // Arrange - 同一规则应用于多个商品
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_MULTI_001",
            Name = "单品特价测试 - 多商品应用",
            ProductConfigs =
            [
                new() { ProductId = "A", SpecialPrice = 150.00m },
                new() { ProductId = "B", SpecialPrice = 100.00m },
                new() { ProductId = "C", SpecialPrice = 80.00m }
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_012",
            (TestDataGenerator.CreateProductA(), 2),
            (TestDataGenerator.CreateProductB(), 3),
            (TestDataGenerator.CreateProductC(), 1)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "多商品单品特价测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "多商品单品特价场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证所有商品的单品特价
        var expectedDiscount = 2 * (200m - 150m) + 3 * (150m - 100m) + 1 * (100m - 80m);
        // A: 2*(200-150) = 100, B: 3*(150-100) = 150, C: 1*(100-80) = 20, 总计270
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应应用所有商品的单品特价优惠270元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 复杂场景测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_MultipleRulesConflict_ShouldApplyOptimal()
    {
        // Arrange - 多个单品规则冲突，应选择最优的
        var rule1 = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_CONFLICT_001",
            Name = "单品特价测试 - 规则1",
            ProductConfigs = [new() { ProductId = "A", SpecialPrice = 180.00m }],
            Priority = 50,
            IsEnabled = true
        };

        var rule2 = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_CONFLICT_002",
            Name = "单品特价测试 - 规则2",
            ProductConfigs =
            [
                new() { ProductId = "A", SpecialPrice = 120.00m } // 更优
            ],
            Priority = 60,
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_013",
            (TestDataGenerator.CreateProductA(), 2)
        );

        ValidateTestData(cart, [rule1, rule2]);
        LogCartDetails(cart, "多单品规则冲突测试购物车");

        TestPromotionRuleService.Rules = [rule1, rule2];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "多单品规则冲突场景");
        LogPromotionResultDetails(result);

        // 验证应用了更优的规则
        Assert.Single(result.AppliedPromotions);

        // 验证更优的单品特价（120元）
        var expectedDiscount = 2 * (200m - 120m); // 2 * 80 = 160元优惠
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应应用更优的单品特价优惠160元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_MixedProductTypes_ShouldHandleCorrectly()
    {
        // Arrange - 混合商品类型测试
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_MIXED_001",
            Name = "单品特价测试 - 混合商品类型",
            ProductConfigs =
            [
                new() { ProductId = "A", SpecialPrice = 150.00m },
                new() { ProductId = "B", SpecialPrice = 100.00m }
                // 注意：没有配置商品C
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_014",
            (TestDataGenerator.CreateProductA(), 2), // 有特价
            (TestDataGenerator.CreateProductB(), 1), // 有特价
            (TestDataGenerator.CreateProductC(), 3) // 无特价
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "混合商品类型测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "混合商品类型场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证只有配置的商品享受特价
        var expectedDiscount = 2 * (200m - 150m) + 1 * (150m - 100m); // A: 100, B: 50, C: 0, 总计150
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应只对配置的商品应用特价优惠150元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_LargeQuantityDifferentProducts_ShouldApplyCorrectly()
    {
        // Arrange - 大数量不同商品测试
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_LARGE_001",
            Name = "单品特价测试 - 大数量不同商品",
            ProductConfigs =
            [
                new() { ProductId = "A", SpecialPrice = 180.00m },
                new() { ProductId = "B", SpecialPrice = 120.00m },
                new() { ProductId = "C", SpecialPrice = 90.00m }
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_015",
            (TestDataGenerator.CreateProductA(), 10),
            (TestDataGenerator.CreateProductB(), 15),
            (TestDataGenerator.CreateProductC(), 20)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "大数量不同商品测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "大数量不同商品场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证大数量商品的单品特价
        var expectedDiscount = 10 * (200m - 180m) + 15 * (150m - 120m) + 20 * (100m - 90m);
        // A: 10*20=200, B: 15*30=450, C: 20*10=200, 总计850
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应应用大数量商品的单品特价优惠850元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 配置验证测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_NullProductConfigs_ShouldNotApply()
    {
        // Arrange - 空商品配置
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_NULL_001",
            Name = "单品特价测试 - 空商品配置",
            ProductConfigs = null, // 空配置
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_016",
            (TestDataGenerator.CreateProductA(), 2)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "空商品配置测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "空商品配置场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_EmptyProductConfigs_ShouldNotApply()
    {
        // Arrange - 空商品配置列表
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_EMPTY_001",
            Name = "单品特价测试 - 空商品配置列表",
            ProductConfigs = [], // 空列表
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_017",
            (TestDataGenerator.CreateProductA(), 2)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "空商品配置列表测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "空商品配置列表场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_NegativeSpecialPrice_ShouldHandleCorrectly()
    {
        // Arrange - 负数特价
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_NEGATIVE_001",
            Name = "单品特价测试 - 负数特价",
            ProductConfigs =
            [
                new() { ProductId = "A", SpecialPrice = -50.00m } // 负数价格
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_018",
            (TestDataGenerator.CreateProductA(), 2)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "负数特价测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "负数特价场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确处理（可能忽略负数价格或应用有效价格）
        // 具体行为取决于业务逻辑实现

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_InvalidProductId_ShouldHandleCorrectly()
    {
        // Arrange - 无效商品ID
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_INVALID_001",
            Name = "单品特价测试 - 无效商品ID",
            ProductConfigs =
            [
                new() { ProductId = "INVALID_PRODUCT", SpecialPrice = 100.00m }, // 无效ID
                new() { ProductId = "A", SpecialPrice = 150.00m } // 有效ID
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_019",
            (TestDataGenerator.CreateProductA(), 2)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "无效商品ID测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "无效商品ID场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用（只对有效商品）
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证只有有效商品享受特价
        var expectedDiscount = 2 * (200m - 150m); // 只有商品A享受特价
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应只对有效商品应用特价优惠100元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 性能测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_LargeQuantityCart_ShouldPerformWell()
    {
        // Arrange - 大数量购物车性能测试
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_PERFORMANCE_001",
            Name = "单品特价测试 - 性能测试",
            ProductConfigs =
            [
                new() { ProductId = "A", SpecialPrice = 180.00m },
                new() { ProductId = "B", SpecialPrice = 120.00m }
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_020",
            (TestDataGenerator.CreateProductA(), 5000), // 大数量
            (TestDataGenerator.CreateProductB(), 3000) // 大数量
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "大数量单品性能测试购物车");

        TestPromotionRuleService.Rules = [rule];

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        stopwatch.Stop();

        // Assert
        AssertPromotionResult(result, "大数量单品性能测试场景");
        LogPromotionResultDetails(result);

        // 验证性能（应在合理时间内完成，如1秒）
        Assert.True(
            stopwatch.ElapsedMilliseconds < 1000,
            $"性能测试超时：{stopwatch.ElapsedMilliseconds}ms"
        );

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public async Task Apply_ManyProductConfigs_ShouldPerformWell()
    {
        // Arrange - 大量商品配置性能测试


        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_MANY_CONFIGS_001",
            Name = "单品特价测试 - 大量配置",
            ProductConfigs = [new() { ProductId = "A", SpecialPrice = 5 }],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateLargeTestCart(100);

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "大量商品配置性能测试购物车");

        TestPromotionRuleService.Rules = [rule];

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        stopwatch.Stop();

        // Assert
        AssertPromotionResult(result, "大量商品配置性能测试场景");
        LogPromotionResultDetails(result);

        // 验证性能（应在合理时间内完成，如1秒）
        Assert.True(
            stopwatch.ElapsedMilliseconds < 1000,
            $"大量配置性能测试超时：{stopwatch.ElapsedMilliseconds}ms"
        );

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 利益最大化策略测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_CustomerBenefitMaximization_ShouldChooseOptimal()
    {
        // Arrange - 客户利益最大化：选择对客户最有利的单品规则
        var rule1 = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_BENEFIT_001",
            Name = "单品特价测试 - 利益最大化1",
            ProductConfigs = [new() { ProductId = "A", SpecialPrice = 170.00m }],
            Priority = 70,
            IsEnabled = true
        };

        var rule2 = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_BENEFIT_002",
            Name = "单品特价测试 - 利益最大化2",
            ProductConfigs =
            [
                new() { ProductId = "A", SpecialPrice = 130.00m } // 更优
            ],
            Priority = 60,
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_022",
            (TestDataGenerator.CreateProductA(), 3)
        );

        ValidateTestData(cart, [rule1, rule2]);
        LogCartDetails(cart, "单品利益最大化测试购物车");

        TestPromotionRuleService.Rules = [rule1, rule2];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "单品利益最大化场景");
        LogPromotionResultDetails(result);

        // 验证选择了对客户最有利的规则（更低的特价）
        Assert.Single(result.AppliedPromotions);

        // 验证总优惠最大化
        var expectedDiscount = 3 * (200m - 130m); // 3 * 70 = 210元
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应实现总优惠最大化210元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_PriceOptimization_ShouldMaximizeDiscount()
    {
        // Arrange - 价格优化：最大化单品优惠
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_OPTIMIZATION_001",
            Name = "单品特价测试 - 价格优化",
            ProductConfigs =
            [
                new() { ProductId = "A", SpecialPrice = 100.00m }, // 50%折扣
                new() { ProductId = "B", SpecialPrice = 75.00m }, // 50%折扣
                new() { ProductId = "C", SpecialPrice = 50.00m } // 50%折扣
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_023",
            (TestDataGenerator.CreateProductA(), 5),
            (TestDataGenerator.CreateProductB(), 4),
            (TestDataGenerator.CreateProductC(), 6)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "单品价格优化测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "单品价格优化场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证最大化单品优惠
        var expectedDiscount = 5 * (200m - 100m) + 4 * (150m - 75m) + 6 * (100m - 50m);
        // A: 5*100=500, B: 4*75=300, C: 6*50=300, 总计1100
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应实现单品优惠最大化1100元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 数据完整性测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_PriceCalculationAccuracy_ShouldMaintainPrecision()
    {
        // Arrange - 价格计算精度测试
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_PRECISION_001",
            Name = "单品特价测试 - 计算精度",
            ProductConfigs =
            [
                new() { ProductId = "A", SpecialPrice = 166.66m }, // 精确价格
                new() { ProductId = "B", SpecialPrice = 133.33m } // 精确价格
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_024",
            (TestDataGenerator.CreateProductA(), 3),
            (TestDataGenerator.CreateProductB(), 6)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "单品计算精度测试购物车");

        var originalTotal = cart.Items.Sum(i => i.Product.Price * i.Quantity);

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "单品计算精度场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证精确的价格计算
        var expectedDiscount = 3 * (200m - 166.66m) + 6 * (150m - 133.33m);
        // A: 3*33.34=100.02, B: 6*16.67=100.02, 总计200.04
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应实现精确的价格计算");

        // 验证购物车总额计算精度
        var processedTotal = result.ProcessedCart.Items.Sum(i => i.ActualUnitPrice * i.Quantity);
        var expectedTotal = originalTotal - result.TotalDiscount;

        AssertAmountEqual(expectedTotal, processedTotal, "处理后购物车总额应精确");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_CartTotalConsistency_ShouldMaintainIntegrity()
    {
        // Arrange - 购物车总额一致性测试
        var rule = new IndividualSpecialPriceRule
        {
            Id = "INDIVIDUAL_SPECIAL_PRICE_CONSISTENCY_001",
            Name = "单品特价测试 - 总额一致性",
            ProductConfigs =
            [
                new() { ProductId = "A", SpecialPrice = 150.00m },
                new() { ProductId = "B", SpecialPrice = 100.00m },
                new() { ProductId = "C", SpecialPrice = 80.00m }
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_025",
            (TestDataGenerator.CreateProductA(), 4),
            (TestDataGenerator.CreateProductB(), 3),
            (TestDataGenerator.CreateProductC(), 5)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "单品总额一致性测试购物车");

        var originalTotal = cart.Items.Sum(i => i.Product.Price * i.Quantity);
        var originalItemCount = cart.Items.Sum(i => i.Quantity);

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "单品总额一致性场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证购物车商品数量一致性
        var processedItemCount = result.ProcessedCart.Items.Sum(i => i.Quantity);
        Assert.Equal(originalItemCount, processedItemCount);

        // 验证购物车总额一致性
        var processedTotal = result.ProcessedCart.Items.Sum(i => i.ActualUnitPrice * i.Quantity);
        var expectedTotal = originalTotal - result.TotalDiscount;

        AssertAmountEqual(expectedTotal, processedTotal, "处理后购物车总额应与原总额减去优惠一致");

        // 验证每个商品的价格变化
        foreach (var item in result.ProcessedCart.Items)
        {
            var originalItem = cart.Items.First(i => i.Product.Id == item.Product.Id);
            var config = rule.ProductConfigs.FirstOrDefault(c => c.ProductId == item.Product.Id);

            if (config != null)
            {
                AssertAmountEqual(
                    config.SpecialPrice,
                    item.ActualUnitPrice,
                    $"商品{item.Product.Id}应应用特价"
                );
            }
            else
            {
                AssertAmountEqual(
                    originalItem.Product.Price,
                    item.ActualUnitPrice,
                    $"商品{item.Product.Id}应保持原价"
                );
            }
        }

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion
}
