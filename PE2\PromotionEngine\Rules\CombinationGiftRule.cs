using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules;

/// <summary>
/// 组合送赠品促销规则
/// 针对某些组合商品，必须购买A+B+C等商品且满足数量或金额条件才能赠送
/// 专为POS系统设计，考虑扫码商品的各种组合可能性和翻倍场景
/// </summary>
public class CombinationGiftRule : PromotionRuleBase
{
    public override string RuleType => "CombinationGift";

    /// <summary>
    /// 组合购买条件列表
    /// </summary>
    public List<CombinationCondition> CombinationConditions { get; set; } = new();

    /// <summary>
    /// 赠品条件列表
    /// </summary>
    public List<GiftCondition> GiftConditions { get; set; } = new();

    /// <summary>
    /// 赠品选择策略
    /// </summary>
    public GiftSelectionStrategy GiftSelectionStrategy { get; set; } = GiftSelectionStrategy.CustomerBenefit;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!CombinationConditions.Any() || !GiftConditions.Any())
            return false;

        // 使用择优算法检查是否能满足组合条件
        var optimalPlan = FindOptimalCombinationPlan(cart);
        return optimalPlan != null && optimalPlan.MaxApplications > 0;
    }

    /// <summary>
    /// 组合促销计划
    /// </summary>
    private class CombinationPromotionPlan
    {
        public int MaxApplications { get; set; }
        public List<ProductAllocation> ConsumedAllocations { get; set; } = new();
        public List<ProductAllocation> GiftAllocations { get; set; } = new();
        public decimal TotalGiftValue { get; set; }
    }

    /// <summary>
    /// 商品分配
    /// </summary>
    private class ProductAllocation
    {
        public string ProductId { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalValue => Quantity * UnitPrice;
    }

    /// <summary>
    /// 找到最优的组合促销计划
    /// 这是POS系统的核心择优算法，考虑所有可能的商品组合
    /// </summary>
    private CombinationPromotionPlan? FindOptimalCombinationPlan(ShoppingCart cart)
    {
        var allProducts = GetAllRelevantProducts(cart);
        if (!allProducts.Any()) return null;

        var bestPlan = new CombinationPromotionPlan();
        var maxApplications = IsRepeatable ? (MaxApplications > 0 ? MaxApplications : 10) : 1;

        // 尝试不同的应用次数，找到最优方案
        for (int appCount = 1; appCount <= maxApplications; appCount++)
        {
            var plan = TryCreateCombinationPlan(cart, allProducts, appCount);
            if (plan != null && plan.MaxApplications > bestPlan.MaxApplications)
            {
                bestPlan = plan;
            }
        }

        return bestPlan.MaxApplications > 0 ? bestPlan : null;
    }

    /// <summary>
    /// 获取所有相关的商品
    /// </summary>
    private List<ProductAllocation> GetAllRelevantProducts(ShoppingCart cart)
    {
        var allProductIds = new HashSet<string>();

        // 添加组合条件中的商品
        foreach (var condition in CombinationConditions)
        {
            allProductIds.Add(condition.ProductId);
        }

        // 添加赠品条件中的商品
        foreach (var condition in GiftConditions)
        {
            foreach (var productId in condition.ProductIds)
            {
                allProductIds.Add(productId);
            }
        }

        var products = new List<ProductAllocation>();
        foreach (var productId in allProductIds)
        {
            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
            if (cartItem != null && cartItem.AvailableQuantity > 0)
            {
                products.Add(new ProductAllocation
                {
                    ProductId = productId,
                    Quantity = cartItem.AvailableQuantity,
                    UnitPrice = cartItem.UnitPrice
                });
            }
        }

        return products;
    }

    /// <summary>
    /// 尝试创建指定应用次数的组合促销计划
    /// </summary>
    private CombinationPromotionPlan? TryCreateCombinationPlan(ShoppingCart cart, List<ProductAllocation> allProducts, int targetApplications)
    {
        var plan = new CombinationPromotionPlan();
        var remainingProducts = allProducts.Select(p => new ProductAllocation
        {
            ProductId = p.ProductId,
            Quantity = p.Quantity,
            UnitPrice = p.UnitPrice
        }).ToList();

        int actualApplications = 0;

        for (int app = 0; app < targetApplications; app++)
        {
            // 检查是否还能满足组合条件
            if (!CanSatisfyCombinationConditions(remainingProducts))
                break;

            // 消耗组合条件中的商品
            if (!TryConsumeCombinationConditions(remainingProducts, plan))
                break;

            // 分配赠品
            if (!TryAllocateCombinationGifts(remainingProducts, plan))
                break;

            actualApplications++;
        }

        plan.MaxApplications = actualApplications;
        return plan.MaxApplications > 0 ? plan : null;
    }

    /// <summary>
    /// 检查是否还能满足组合条件
    /// </summary>
    private bool CanSatisfyCombinationConditions(List<ProductAllocation> remainingProducts)
    {
        foreach (var condition in CombinationConditions)
        {
            var availableQuantity = remainingProducts
                .Where(p => p.ProductId == condition.ProductId)
                .Sum(p => p.Quantity);

            if (availableQuantity < condition.RequiredQuantity)
                return false;

            // 检查金额条件（如果有）
            if (condition.MinAmount > 0)
            {
                var totalAmount = remainingProducts
                    .Where(p => p.ProductId == condition.ProductId)
                    .Sum(p => p.TotalValue);
                if (totalAmount < condition.MinAmount)
                    return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 消耗组合条件中的商品
    /// </summary>
    private bool TryConsumeCombinationConditions(List<ProductAllocation> remainingProducts, CombinationPromotionPlan plan)
    {
        foreach (var condition in CombinationConditions)
        {
            var requiredQuantity = condition.RequiredQuantity;
            var consumedThisCondition = new List<ProductAllocation>();

            // 按价格排序：商家利益最大化时优先消耗高价商品作为购买条件
            var candidateProducts = remainingProducts
                .Where(p => p.ProductId == condition.ProductId && p.Quantity > 0)
                .OrderBy(p => GiftSelectionStrategy == GiftSelectionStrategy.MerchantBenefit ? -p.UnitPrice : p.UnitPrice)
                .ToList();

            foreach (var product in candidateProducts)
            {
                if (requiredQuantity <= 0) break;

                var consumeQuantity = Math.Min(product.Quantity, requiredQuantity);

                consumedThisCondition.Add(new ProductAllocation
                {
                    ProductId = product.ProductId,
                    Quantity = consumeQuantity,
                    UnitPrice = product.UnitPrice
                });

                product.Quantity -= consumeQuantity;
                requiredQuantity -= consumeQuantity;
            }

            if (requiredQuantity > 0)
                return false;

            plan.ConsumedAllocations.AddRange(consumedThisCondition);
        }

        return true;
    }

    /// <summary>
    /// 分配组合赠品
    /// </summary>
    private bool TryAllocateCombinationGifts(List<ProductAllocation> remainingProducts, CombinationPromotionPlan plan)
    {
        foreach (var giftCondition in GiftConditions)
        {
            var requiredGiftQuantity = giftCondition.GiftQuantity;
            var giftAllocations = new List<ProductAllocation>();

            // 根据策略排序候选赠品
            var candidateGifts = remainingProducts
                .Where(p => giftCondition.ProductIds.Contains(p.ProductId) && p.Quantity > 0)
                .OrderBy(p => GiftSelectionStrategy == GiftSelectionStrategy.CustomerBenefit ? -p.UnitPrice : p.UnitPrice)
                .ToList();

            foreach (var product in candidateGifts)
            {
                if (requiredGiftQuantity <= 0) break;

                var giftQuantity = Math.Min(product.Quantity, requiredGiftQuantity);

                giftAllocations.Add(new ProductAllocation
                {
                    ProductId = product.ProductId,
                    Quantity = giftQuantity,
                    UnitPrice = product.UnitPrice
                });

                product.Quantity -= giftQuantity;
                requiredGiftQuantity -= giftQuantity;
                plan.TotalGiftValue += giftQuantity * product.UnitPrice;
            }

            if (requiredGiftQuantity > 0)
                return false;

            plan.GiftAllocations.AddRange(giftAllocations);
        }

        return true;
    }

    /// <summary>
    /// 检查组合购买条件（已废弃，由择优算法替代）
    /// 考虑POS系统中商品扫码的各种可能性
    /// </summary>
    private bool CheckCombinationConditions(ShoppingCart cart)
    {
        // 计算可能的组合数量（考虑翻倍场景）
        var maxCombinations = CalculateMaxCombinations(cart);
        return maxCombinations > 0;
    }

    /// <summary>
    /// 计算最大可能的组合数量
    /// 这是POS系统的核心算法，需要考虑所有可能的商品组合
    /// </summary>
    private int CalculateMaxCombinations(ShoppingCart cart)
    {
        var minCombinations = int.MaxValue;

        foreach (var condition in CombinationConditions)
        {
            var availableQuantity = cart.GetAvailableProductQuantity(condition.ProductId);
            
            // 检查数量条件
            if (availableQuantity < condition.RequiredQuantity)
                return 0;

            // 检查金额条件（如果有）
            if (condition.MinAmount > 0)
            {
                var totalAmount = cart.Items
                    .Where(x => x.Product.Id == condition.ProductId)
                    .Sum(x => x.SubTotal);
                if (totalAmount < condition.MinAmount)
                    return 0;
            }

            // 计算该商品能参与多少次组合
            var maxForThisProduct = availableQuantity / condition.RequiredQuantity;
            minCombinations = Math.Min(minCombinations, maxForThisProduct);
        }

        return minCombinations == int.MaxValue ? 0 : minCombinations;
    }

    /// <summary>
    /// 检查赠品库存是否充足
    /// </summary>
    private bool CheckGiftStock(ShoppingCart cart)
    {
        foreach (var giftCondition in GiftConditions)
        {
            if (giftCondition.ProductIds.Count > 1)
            {
                // 多选一：只要有一个有库存即可
                var hasStock = giftCondition.ProductIds.Any(id => cart.GetAvailableProductQuantity(id) > 0);
                if (!hasStock) return false;
            }
            else
            {
                // 单一赠品：必须有足够库存
                var availableQuantity = giftCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                if (availableQuantity < giftCondition.GiftQuantity)
                    return false;
            }
        }

        return true;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        var optimalPlan = FindOptimalCombinationPlan(cart);
        return optimalPlan?.MaxApplications ?? 0;
    }

    /// <summary>
    /// 根据赠品库存计算最大应用次数
    /// </summary>
    private int CalculateMaxApplicationsByGiftStock(ShoppingCart cart)
    {
        var minApplications = int.MaxValue;

        foreach (var giftCondition in GiftConditions)
        {
            if (giftCondition.ProductIds.Count > 1)
            {
                // 多选一：通常不受库存限制
                continue;
            }
            else
            {
                // 单一赠品：根据库存计算
                var availableQuantity = giftCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                var maxByThisGift = availableQuantity / giftCondition.GiftQuantity;
                minApplications = Math.Min(minApplications, maxByThisGift);
            }
        }

        return minApplications == int.MaxValue ? int.MaxValue : minApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyCombinationGiftPromotion(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用组合送赠品促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = $"应用组合送赠品促销时发生错误: {ex.Message}";
        }

        return application;
    }

    /// <summary>
    /// 应用组合送赠品促销
    /// 优化的POS系统算法，考虑各种扫码组合可能性
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyCombinationGiftPromotion(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var giftItems = new List<GiftItem>();

        for (int app = 0; app < applicationCount; app++)
        {
            // 验证当前是否还能应用组合条件
            if (!CheckCombinationConditions(cart))
                break;

            // 消耗组合条件中的商品
            foreach (var combination in CombinationConditions)
            {
                var remainingQuantity = combination.RequiredQuantity;

                var availableItems = cart.Items
                    .Where(x => x.Product.Id == combination.ProductId && x.AvailableQuantity > 0)
                    .ToList();

                foreach (var item in availableItems)
                {
                    if (remainingQuantity <= 0) break;

                    var consumeQuantity = Math.Min(item.AvailableQuantity, remainingQuantity);

                    // 记录消耗的商品
                    var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == combination.ProductId);
                    if (existingConsumed != null)
                    {
                        existingConsumed.Quantity += consumeQuantity;
                    }
                    else
                    {
                        consumedItems.Add(new ConsumedItem
                        {
                            ProductId = combination.ProductId,
                            ProductName = item.Product.Name,
                            Quantity = consumeQuantity,
                            UnitPrice = item.UnitPrice
                        });
                    }

                    item.Quantity -= consumeQuantity;
                    remainingQuantity -= consumeQuantity;
                }
            }

            // 处理赠品
            foreach (var giftCondition in GiftConditions)
            {
                var selectedProductIds = giftCondition.ProductIds.Count > 1
                    ? SelectOptimalGiftProducts(giftCondition.ProductIds, cart, giftCondition.GiftQuantity)
                    : giftCondition.ProductIds;

                var distributedQuantity = 0;
                foreach (var productId in selectedProductIds)
                {
                    if (distributedQuantity >= giftCondition.GiftQuantity) break;

                    var productItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
                    if (productItem != null)
                    {
                        var giftQuantity = Math.Min(giftCondition.GiftQuantity - distributedQuantity, 1);
                        var giftValue = giftQuantity * productItem.UnitPrice;

                        totalDiscountAmount += giftValue;

                        var existingGift = giftItems.FirstOrDefault(x => x.ProductId == productId);
                        if (existingGift != null)
                        {
                            existingGift.Quantity += giftQuantity;
                            existingGift.Value += giftValue;
                        }
                        else
                        {
                            var strategyDescription = GiftSelectionStrategy == GiftSelectionStrategy.CustomerBenefit
                                ? "客户利益最大化选择"
                                : "商家利益最大化选择";

                            giftItems.Add(new GiftItem
                            {
                                ProductId = productId,
                                ProductName = productItem.Product.Name,
                                Quantity = giftQuantity,
                                Value = giftValue,
                                Description = $"组合赠品 - {strategyDescription}"
                            });
                        }

                        distributedQuantity += giftQuantity;
                    }
                }
            }
        }

        cart.Items.RemoveAll(x => x.Quantity <= 0);
        return (totalDiscountAmount, consumedItems, giftItems);
    }

    /// <summary>
    /// 根据策略选择最优的赠品商品
    /// </summary>
    private List<string> SelectOptimalGiftProducts(List<string> candidateProductIds, ShoppingCart cart, int requiredQuantity)
    {
        if (!candidateProductIds.Any())
            return new List<string>();

        var productPrices = new List<(string ProductId, decimal Price)>();

        foreach (var productId in candidateProductIds)
        {
            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
            if (cartItem != null)
            {
                productPrices.Add((productId, cartItem.Product.Price));
            }
        }

        if (!productPrices.Any())
            return new List<string>();

        var sortedProducts = GiftSelectionStrategy switch
        {
            GiftSelectionStrategy.CustomerBenefit => productPrices.OrderByDescending(p => p.Price),
            GiftSelectionStrategy.MerchantBenefit => productPrices.OrderBy(p => p.Price),
            _ => productPrices.OrderByDescending(p => p.Price)
        };

        var selectedProducts = new List<string>();
        var remainingQuantity = requiredQuantity;

        foreach (var (productId, _) in sortedProducts)
        {
            if (remainingQuantity <= 0) break;

            var availableQuantity = cart.Items
                .Where(x => x.Product.Id == productId)
                .Sum(x => x.Quantity);

            if (availableQuantity > 0)
            {
                selectedProducts.Add(productId);
                remainingQuantity--;
            }
        }

        return selectedProducts;
    }
}
