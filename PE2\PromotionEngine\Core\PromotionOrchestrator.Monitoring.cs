global using Microsoft.Extensions.Logging;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Core;

/// <summary>
/// 促销编排器监控和清理部分
/// </summary>
public sealed partial class PromotionOrchestrator
{
    #region 性能监控和清理

    /// <summary>
    /// 记录编排性能指标
    /// </summary>
    private void RecordOrchestrationMetric(string operation, long elapsedMs, int ruleCount)
    {
        var metric = new OrchestrationMetrics
        {
            Operation = operation,
            ElapsedMs = elapsedMs,
            RuleCount = ruleCount,
            Timestamp = DateTime.Now,
            ThreadId = Thread.CurrentThread.ManagedThreadId
        };

        var key = $"{operation}_{DateTime.Now:yyyyMMddHH}";
        _performanceMetrics.AddOrUpdate(key, metric, (k, existing) =>
        {
            existing.TotalCalls++;
            existing.TotalElapsedMs += elapsedMs;
            existing.AverageElapsedMs = existing.TotalElapsedMs / existing.TotalCalls;
            if (elapsedMs > existing.MaxElapsedMs)
            {
                existing.MaxElapsedMs = elapsedMs;
            }
            if (elapsedMs < existing.MinElapsedMs || existing.MinElapsedMs == 0)
            {
                existing.MinElapsedMs = elapsedMs;
            }
            return existing;
        });

        // 记录到日志
        if (elapsedMs > 1000) // 超过1秒的操作记录警告
        {
            _logger.LogWarning("促销编排操作耗时较长: {Operation}, 耗时{ElapsedMs}ms, 规则数{RuleCount}",
                operation, elapsedMs, ruleCount);
        }
        else
        {
            _logger.LogDebug("促销编排操作完成: {Operation}, 耗时{ElapsedMs}ms, 规则数{RuleCount}",
                operation, elapsedMs, ruleCount);
        }
    }

    /// <summary>
    /// 清理过期数据
    /// </summary>
    private void CleanupExpiredData(object? state)
    {
        try
        {
            var cutoffTime = DateTime.Now.Subtract(_resultRetentionTime);
            var expiredCalculations = new List<string>();
            var expiredMetrics = new List<string>();

            // 清理过期的计算结果
            foreach (var kvp in _calculationStatuses)
            {
                if (kvp.Value.EndTime.HasValue && kvp.Value.EndTime.Value < cutoffTime)
                {
                    expiredCalculations.Add(kvp.Key);
                }
            }

            foreach (var calculationId in expiredCalculations)
            {
                _calculationStatuses.TryRemove(calculationId, out _);
                _calculationResults.TryRemove(calculationId, out _);
            }

            // 清理过期的性能指标
            foreach (var kvp in _performanceMetrics)
            {
                if (kvp.Value.Timestamp < cutoffTime)
                {
                    expiredMetrics.Add(kvp.Key);
                }
            }

            foreach (var metricKey in expiredMetrics)
            {
                _performanceMetrics.TryRemove(metricKey, out _);
            }

            if (expiredCalculations.Count > 0 || expiredMetrics.Count > 0)
            {
                _logger.LogInformation("清理过期数据完成: 计算结果{CalculationCount}个, 性能指标{MetricCount}个",
                    expiredCalculations.Count, expiredMetrics.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期数据时发生异常");
        }
    }

    /// <summary>
    /// 获取性能统计信息
    /// </summary>
    public OrchestrationPerformanceStats GetPerformanceStats()
    {
        var stats = new OrchestrationPerformanceStats
        {
            TotalCalculations = _calculationStatuses.Count,
            ActiveCalculations = _calculationStatuses.Values.Count(s => s.Status != "Completed" && s.Status != "Failed"),
            AverageCalculationTime = 0,
            MaxCalculationTime = 0,
            MinCalculationTime = 0,
            SuccessRate = 0,
            CacheHitRate = 0,
            MemoryUsageMB = GC.GetTotalMemory(false) / 1024 / 1024
        };

        var completedCalculations = _calculationStatuses.Values
            .Where(s => s.EndTime.HasValue && s.StartTime.HasValue)
            .ToList();

        if (completedCalculations.Any())
        {
            var calculationTimes = completedCalculations
                .Select(s => (s.EndTime!.Value - s.StartTime!.Value).TotalMilliseconds)
                .ToList();

            stats.AverageCalculationTime = calculationTimes.Average();
            stats.MaxCalculationTime = calculationTimes.Max();
            stats.MinCalculationTime = calculationTimes.Min();
            stats.SuccessRate = (double)completedCalculations.Count(s => s.IsSuccessful) / completedCalculations.Count * 100;
        }

        return stats;
    }

    /// <summary>
    /// 获取详细的性能指标
    /// </summary>
    public Dictionary<string, OrchestrationMetrics> GetDetailedMetrics()
    {
        return new Dictionary<string, OrchestrationMetrics>(_performanceMetrics);
    }

    /// <summary>
    /// 重置性能统计
    /// </summary>
    public void ResetPerformanceStats()
    {
        _performanceMetrics.Clear();
        _logger.LogInformation("性能统计已重置");
    }

    #endregion

    #region IDisposable 实现

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
        {
            return;
        }

        try
        {
            // 停止定时器
            _metricsCleanupTimer?.Dispose();

            // 释放信号量
            _calculationSemaphore?.Dispose();

            // 清理缓存
            _calculationStatuses.Clear();
            _calculationResults.Clear();
            _performanceMetrics.Clear();

            _logger.LogInformation("促销编排器已释放资源");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "释放促销编排器资源时发生异常");
        }
        finally
        {
            _disposed = true;
        }
    }

    #endregion
}

/// <summary>
/// 编排性能指标
/// </summary>
public sealed class OrchestrationMetrics
{
    /// <summary>
    /// 操作名称
    /// </summary>
    public required string Operation { get; init; }

    /// <summary>
    /// 耗时（毫秒）
    /// </summary>
    public long ElapsedMs { get; set; }

    /// <summary>
    /// 规则数量
    /// </summary>
    public int RuleCount { get; init; }

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; init; }

    /// <summary>
    /// 线程ID
    /// </summary>
    public int ThreadId { get; init; }

    /// <summary>
    /// 总调用次数
    /// </summary>
    public long TotalCalls { get; set; } = 1;

    /// <summary>
    /// 总耗时
    /// </summary>
    public long TotalElapsedMs { get; set; }

    /// <summary>
    /// 平均耗时
    /// </summary>
    public double AverageElapsedMs { get; set; }

    /// <summary>
    /// 最大耗时
    /// </summary>
    public long MaxElapsedMs { get; set; }

    /// <summary>
    /// 最小耗时
    /// </summary>
    public long MinElapsedMs { get; set; }
}

/// <summary>
/// 编排性能统计
/// </summary>
public sealed class OrchestrationPerformanceStats
{
    /// <summary>
    /// 总计算次数
    /// </summary>
    public int TotalCalculations { get; init; }

    /// <summary>
    /// 活跃计算数
    /// </summary>
    public int ActiveCalculations { get; init; }

    /// <summary>
    /// 平均计算时间（毫秒）
    /// </summary>
    public double AverageCalculationTime { get; init; }

    /// <summary>
    /// 最大计算时间（毫秒）
    /// </summary>
    public double MaxCalculationTime { get; init; }

    /// <summary>
    /// 最小计算时间（毫秒）
    /// </summary>
    public double MinCalculationTime { get; init; }

    /// <summary>
    /// 成功率（百分比）
    /// </summary>
    public double SuccessRate { get; init; }

    /// <summary>
    /// 缓存命中率（百分比）
    /// </summary>
    public double CacheHitRate { get; init; }

    /// <summary>
    /// 内存使用量（MB）
    /// </summary>
    public long MemoryUsageMB { get; init; }
}

/// <summary>
/// 购物车特征
/// </summary>
public sealed class CartCharacteristics
{
    /// <summary>
    /// 总商品数量
    /// </summary>
    public int TotalItems { get; init; }

    /// <summary>
    /// 总金额
    /// </summary>
    public decimal TotalAmount { get; init; }

    /// <summary>
    /// 唯一商品数
    /// </summary>
    public int UniqueProducts { get; init; }

    /// <summary>
    /// 平均商品价格
    /// </summary>
    public decimal AverageItemPrice { get; init; }

    /// <summary>
    /// 最高商品价格
    /// </summary>
    public decimal MaxItemPrice { get; init; }

    /// <summary>
    /// 最低商品价格
    /// </summary>
    public decimal MinItemPrice { get; init; }

    /// <summary>
    /// 商品类别列表
    /// </summary>
    public required List<string> Categories { get; init; }
}

/// <summary>
/// 规则适用性信息
/// </summary>
public sealed class RuleApplicabilityInfo
{
    /// <summary>
    /// 规则ID
    /// </summary>
    public required string RuleId { get; init; }

    /// <summary>
    /// 规则名称
    /// </summary>
    public required string RuleName { get; init; }

    /// <summary>
    /// 是否适用
    /// </summary>
    public bool IsApplicable { get; init; }

    /// <summary>
    /// 最大应用次数
    /// </summary>
    public int MaxApplications { get; init; }

    /// <summary>
    /// 预估折扣
    /// </summary>
    public decimal EstimatedDiscount { get; init; }
}

/// <summary>
/// 优化潜力信息
/// </summary>
public sealed class OptimizationPotentialInfo
{
    /// <summary>
    /// 适用规则数量
    /// </summary>
    public int ApplicableRulesCount { get; init; }

    /// <summary>
    /// 总潜在折扣
    /// </summary>
    public decimal TotalPotentialDiscount { get; init; }

    /// <summary>
    /// 优化复杂度
    /// </summary>
    public required string OptimizationComplexity { get; init; }

    /// <summary>
    /// 预估计算时间（毫秒）
    /// </summary>
    public long EstimatedCalculationTime { get; init; }
}
