/**
 * 动态表单渲染器组件（模板版本）
 * 基于后端反射API返回的元数据动态生成促销规则配置表单
 * 使用template语法，更容易维护和理解
 */

const DynamicFormRenderer = {
    name: 'DynamicFormRenderer',
    components: {
        'product-selector': typeof ProductSelector !== 'undefined' ? ProductSelector : {},
        'complex-array-renderer': typeof ComplexArrayRenderer !== 'undefined' ? ComplexArrayRenderer : {},
        'condition-config-renderer': typeof ConditionConfigRenderer !== 'undefined' ? ConditionConfigRenderer : {},
        'enhanced-condition-renderer': typeof EnhancedConditionRenderer !== 'undefined' ? EnhancedConditionRenderer : {}
    },
    props: {
        modelValue: {
            type: Object,
            default: () => ({})
        },
        promotionTypes: {
            type: Object,
            default: () => ({})
        },
        selectedCategory: String,
        selectedType: String,
        mode: {
            type: String,
            default: 'create', // create, edit, view
            validator: (value) => ['create', 'edit', 'view'].includes(value)
        }
    },
    emits: ['update:modelValue', 'submit', 'cancel'],
    setup(props, { emit }) {
        const { ref, computed, watch, nextTick } = Vue;
        const { ElMessage } = ElementPlus;

        // 表单数据
        const formData = ref({});
        const formRef = ref(null);
        const loading = ref(false);
        const validationRules = ref({});

        // 当前选中的促销类型详细信息
        const currentTypeInfo = computed(() => {
            if (!props.selectedCategory || !props.selectedType) return null;
            
            const category = props.promotionTypes[props.selectedCategory];
            if (!category || !category.types) return null;
            
            return category.types[props.selectedType];
        });        // 表单字段配置
        const formFields = computed(() => {
            if (!currentTypeInfo.value) return [];
            return currentTypeInfo.value.fields || [];
        });

        // 当前促销类型
        const promotionType = computed(() => {
            return props.selectedType || 'general';
        });

        // 中文标签映射
        const chineseLabels = {
            // 基本字段
            'id': '规则ID',
            'name': '规则名称', 
            'description': '规则描述',
            'priority': '优先级',
            'isEnabled': '是否启用',
            'startTime': '开始时间',
            'endTime': '结束时间',
            'isRepeatable': '是否可重复',
            'maxApplications': '最大应用次数',
            'canStackWithOthers': '是否可与其他规则叠加',
            'productExclusivity': '商品排他性',
            'applicableCustomerTypes': '适用客户类型',
            'exclusiveRuleIds': '排他规则ID',
            
            // 条件字段
            'applicableProductIds': '适用商品ID',
            'minQuantity': '最小数量',
            'minAmount': '最小金额',
            'buyQuantity': '购买数量',
            'buyConditions': '购买条件',
            'combinationConditions': '组合条件',
            'exchangeConditions': '兑换条件',
            'isByAmount': '按金额计算',
            
            // 优惠字段
            'discountRate': '折扣率',
            'discountAmount': '折扣金额',
            'specialPrice': '特价',
            'freeQuantity': '免费数量',
            'giftQuantity': '赠品数量',
            'giftProductIds': '赠品商品ID',
            
            // 策略字段
            'discountSelectionStrategy': '折扣选择策略',
            'specialPriceSelectionStrategy': '特价选择策略',
            'freeItemSelectionStrategy': '免费商品选择策略',
            'giftSelectionStrategy': '赠品选择策略',
            'exchangeStrategy': '兑换策略',
            'exchangeSelectionStrategy': '兑换选择策略',
            'giftStrategy': '赠品策略',
            
            // 高级字段
            'discountTiers': '折扣层级',
            'specialPriceTiers': '特价层级',
            'giftTiers': '赠品层级',
            'cyclicTiers': '循环层级',
            'progressiveTiers': '递进层级',
            'productConfigs': '商品配置',
            'couponGifts': '优惠券赠品',
            'memberOnly': '仅限会员',
            'freeFromCombinationOnly': '仅从组合中免费'
        };

        // 分组名称映射
        const groupNameMap = {
            'basic': '基本信息',
            'time': '时间设置', 
            'condition': '条件配置',
            'benefit': '优惠配置',
            'advanced': '高级设置'
        };        // 获取字段的中文标签
        const getFieldLabel = (field) => {
            return chineseLabels[field.name] || field.label || field.displayName || field.name;
        };

        // 选项标签映射
        const optionLabels = {
            // 产品排他性
            'None': '无',
            'SameType': '同类型',
            'All': '全部',
            
            // 策略选择
            'CustomerBenefit': '客户优先',
            'MerchantBenefit': '商家优先',
            
            // 兑换策略
            'ByGradient': '按梯度',
            'AllExchange': '全部兑换',
            
            // 赠品策略
            'ByTier': '按层级',
            'AllTiers': '所有层级'
        };

        // 获取选项的中文标签
        const getOptionLabel = (option) => {
            return optionLabels[option.value] || option.label || option.value;
        };

        // 分组字段
        const groupedFields = computed(() => {
            const groups = {};
            formFields.value.forEach(field => {
                const groupKey = field.group || 'basic';
                const groupName = groupNameMap[groupKey] || groupKey || '基本信息';
                if (!groups[groupName]) {
                    groups[groupName] = [];
                }
                groups[groupName].push(field);
            });
            return groups;
        });

        // 初始化表单数据
        const initFormData = () => {
            const newFormData = { ...props.modelValue };
            
            // 设置默认值
            formFields.value.forEach(field => {
                if (newFormData[field.name] === undefined) {
                    newFormData[field.name] = getFieldDefaultValue(field);
                }
            });

            // 确保必要的基础字段
            if (!newFormData.ruleId) {
                newFormData.ruleId = generateRuleId();
            }
            if (!newFormData.ruleName) {
                newFormData.ruleName = generateRuleName();
            }
            if (!newFormData.ruleType && currentTypeInfo.value) {
                newFormData.ruleType = currentTypeInfo.value.ruleType;
            }

            formData.value = newFormData;
            emit('update:modelValue', newFormData);
        };

        // 获取字段默认值
        const getFieldDefaultValue = (field) => {
            if (field.defaultValue !== undefined) {
                return field.defaultValue;
            }            switch (field.type) {
                case 'string':
                case 'text':
                    return '';
                case 'number':
                case 'decimal':
                case 'integer':
                    return field.min || 0;
                case 'boolean':
                    return false;
                case 'array':
                case 'array-simple':
                case 'array-complex':
                    return [];
                case 'object':
                    return {};
                case 'date':
                case 'datetime':
                    return null;
                case 'select':
                case 'enum':
                    return field.options && field.options.length > 0 ? field.options[0].value : '';
                default:
                    return null;
            }
        };

        // 生成规则ID
        const generateRuleId = () => {
            const timestamp = Date.now();
            const random = Math.floor(Math.random() * 1000);
            return `rule_${timestamp}_${random}`;
        };

        // 生成规则名称
        const generateRuleName = () => {
            if (!currentTypeInfo.value) return '新促销规则';
            const typeName = currentTypeInfo.value.name;
            const timestamp = new Date().toLocaleString('zh-CN', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            }).replace(/[\/\s:]/g, '');
            return `${typeName}_${timestamp}`;
        };

        // 更新字段值
        const updateFieldValue = (fieldName, value) => {
            formData.value[fieldName] = value;
            emit('update:modelValue', { ...formData.value });
        };

        // 表单提交
        const handleSubmit = async () => {
            if (!formRef.value) return;
            
            try {
                await formRef.value.validate();
                emit('submit', { ...formData.value });
            } catch (error) {
                ElMessage.error('表单验证失败，请检查输入内容');
            }
        };

        // 表单重置
        const handleReset = () => {
            if (formRef.value) {
                formRef.value.resetFields();
            }
            initFormData();
        };

        // 格式化JSON显示
        const formatJsonValue = (value) => {
            if (value === null || value === undefined) return '';
            if (typeof value === 'string') return value;
            return JSON.stringify(value, null, 2);
        };

        // 解析JSON输入
        const parseJsonValue = (jsonString, fieldName) => {
            try {
                const parsed = JSON.parse(jsonString);
                updateFieldValue(fieldName, parsed);
            } catch (error) {
                // JSON解析错误，保持原值
                console.warn(`JSON解析错误 (${fieldName}):`, error.message);
            }
        };

        // 数组操作方法
        const addArrayItem = (fieldName, field) => {
            const items = [...(formData.value[fieldName] || [])];
            items.push(getFieldDefaultValue({ type: field.itemType || 'string' }));
            updateFieldValue(fieldName, items);
        };

        const removeArrayItem = (fieldName, index) => {
            const items = [...(formData.value[fieldName] || [])];
            items.splice(index, 1);
            updateFieldValue(fieldName, items);
        };        const updateArrayItem = (fieldName, index, value) => {
            const items = [...(formData.value[fieldName] || [])];
            items[index] = value;
            updateFieldValue(fieldName, items);
        };

        // 商品选择器相关
        const newProductId = ref('');
        
        const addProductId = (fieldName) => {
            if (!newProductId.value.trim()) return;
            
            const items = [...(formData.value[fieldName] || [])];
            if (!items.includes(newProductId.value.trim())) {
                items.push(newProductId.value.trim());
                updateFieldValue(fieldName, items);
            }
            newProductId.value = '';
        };
          const removeProductId = (fieldName, index) => {
            const items = [...(formData.value[fieldName] || [])];
            items.splice(index, 1);
            updateFieldValue(fieldName, items);
        };

        // JSON编辑器辅助方法
        const getJsonPlaceholder = (field) => {
            const fieldName = field.name.toLowerCase();
            if (fieldName.includes('combination')) {
                return '示例: {"requiredProducts": [{"productId": "P001", "quantity": 2}], "totalMinAmount": 100}';
            } else if (fieldName.includes('exchange')) {
                return '示例: {"exchangeItems": [{"productId": "P002", "quantity": 1}], "exchangeValue": 50}';
            } else if (fieldName.includes('conditions')) {
                return '示例: {"minQuantity": 1, "minAmount": 0, "applicableProducts": ["P001", "P002"]}';
            }
            return `请输入${getFieldLabel(field)}的JSON配置`;
        };

        const showJsonHelper = (field) => {
            ElMessage.info('JSON配置助手功能正在开发中，请参考占位符示例');
        };

        // 监听属性变化
        watch(() => props.modelValue, (newValue) => {
            formData.value = { ...newValue };
        }, { deep: true });

        watch(() => [props.selectedCategory, props.selectedType], () => {
            nextTick(() => {
                initFormData();
            });
        });

        // 初始化
        Vue.onMounted(() => {
            initFormData();
        });        return {
            formData,
            formRef,
            loading,
            validationRules,
            currentTypeInfo,
            promotionType,
            groupedFields,
            getFieldLabel,
            getOptionLabel,
            handleSubmit,
            handleReset,
            updateFieldValue,
            getFieldValue: (fieldName) => formData.value[fieldName],
            formatJsonValue,
            parseJsonValue,
            addArrayItem,
            removeArrayItem,
            updateArrayItem,            newProductId,
            addProductId,
            removeProductId,
            getJsonPlaceholder,
            showJsonHelper
        };
    },    template: `
        <div class="dynamic-form-renderer">
            <el-form
                ref="formRef"
                :model="formData"
                label-width="120px"
                label-position="right"
                :disabled="mode === 'view'"
                v-loading="loading"
            >
                <!-- 表单标题 -->
                <div v-if="currentTypeInfo" class="form-header">
                    <h3>{{ currentTypeInfo.name }}</h3>
                    <p class="form-description">{{ currentTypeInfo.description }}</p>
                </div>

                <!-- 分组表单项 -->
                <template v-for="(fields, groupName) in groupedFields" :key="groupName">
                    <el-divider content-position="left">
                        <span class="group-title">{{ groupName }}</span>
                    </el-divider>
                    
                    <el-row :gutter="16">
                        <template v-for="field in fields" :key="field.name">
                            <el-col :span="field.span || 24">                                <el-form-item
                                    :label="getFieldLabel(field)"
                                    :prop="field.name"
                                    :required="field.required"
                                >
                                    <template #label>
                                        <span>{{ getFieldLabel(field) }}</span>
                                        <el-tooltip
                                            v-if="field.description"
                                            :content="field.description"
                                            placement="top"
                                        >
                                            <el-icon style="margin-left: 4px; color: #999;">
                                                <QuestionFilled />
                                            </el-icon>
                                        </el-tooltip>
                                    </template>                                    <!-- 商品选择器 (单选) -->
                                    <product-selector
                                        v-if="field.type === 'product-selector-single' || 
                                             (field.name === 'productId' || field.name.toLowerCase().includes('productid'))"
                                        :model-value="getFieldValue(field.name)"
                                        @update:model-value="value => updateFieldValue(field.name, value)"
                                        :placeholder="'请选择' + getFieldLabel(field)"
                                        :disabled="mode === 'view' || field.readonly"
                                    />
                                    
                                    <!-- 商品选择器 (多选) -->
                                    <product-selector
                                        v-else-if="field.type === 'product-selector-multiple' || 
                                                  (field.name === 'productIds' || field.name.toLowerCase().includes('productids'))"
                                        :model-value="getFieldValue(field.name) || []"
                                        @update:model-value="value => updateFieldValue(field.name, value)"
                                        :placeholder="'请选择' + getFieldLabel(field)"
                                        :multiple="true"
                                        :disabled="mode === 'view' || field.readonly"
                                    />
                                    
                                    <!-- 字符串输入 -->
                                    <el-input 
                                        v-else-if="field.type === 'input' || field.type === 'string' || field.type === 'text'"
                                        v-model="formData[field.name]"
                                        :placeholder="field.placeholder || \`请输入\${getFieldLabel(field)}\`"
                                        :maxlength="field.maxLength"
                                        :show-word-limit="!!field.maxLength"
                                        :disabled="mode === 'view' || field.readonly"
                                    />
                                    
                                    <!-- 文本域输入 -->
                                    <el-input 
                                        v-else-if="field.type === 'textarea'"
                                        v-model="formData[field.name]"
                                        type="textarea"
                                        :placeholder="field.placeholder || \`请输入\${getFieldLabel(field)}\`"
                                        :rows="field.rows || 3"
                                        :maxlength="field.maxLength"
                                        :show-word-limit="!!field.maxLength"
                                        :disabled="mode === 'view' || field.readonly"
                                    />
                                    
                                    <!-- 数字输入 -->
                                    <el-input-number 
                                        v-else-if="field.type === 'number' || field.type === 'decimal' || field.type === 'integer'"
                                        v-model="formData[field.name]"
                                        :min="field.min"
                                        :max="field.max"
                                        :step="field.step || (field.type === 'integer' ? 1 : 0.01)"
                                        :precision="field.precision"
                                        controls-position="right"
                                        style="width: 100%"
                                        :disabled="mode === 'view' || field.readonly"
                                    />
                                    
                                    <!-- 布尔值开关 -->
                                    <el-switch 
                                        v-else-if="field.type === 'switch' || field.type === 'boolean'"
                                        v-model="formData[field.name]"
                                        :active-text="field.activeText || '是'"
                                        :inactive-text="field.inactiveText || '否'"
                                        :disabled="mode === 'view' || field.readonly"
                                    />                                    <!-- 选择器 -->
                                    <el-select 
                                        v-else-if="field.type === 'select' || field.type === 'enum'"
                                        v-model="formData[field.name]"
                                        :placeholder="field.placeholder || \`请选择\${getFieldLabel(field)}\`"
                                        :clearable="!field.required"
                                        :filterable="field.filterable !== false"
                                        :multiple="field.multiple"
                                        style="width: 100%"
                                        :disabled="mode === 'view' || field.readonly"
                                    >
                                        <el-option 
                                            v-for="option in field.options || []" 
                                            :key="option.value"
                                            :label="getOptionLabel(option)"
                                            :value="option.value"
                                            :disabled="option.disabled"
                                        />
                                    </el-select>
                                    
                                    <!-- 日期选择器 -->
                                    <el-date-picker 
                                        v-else-if="field.type === 'date'"
                                        v-model="formData[field.name]"
                                        type="date"
                                        :placeholder="field.placeholder || \`请选择\${field.displayName || field.label || field.name}\`"
                                        format="YYYY-MM-DD"
                                        value-format="YYYY-MM-DD"
                                        style="width: 100%"
                                        :disabled="mode === 'view' || field.readonly"
                                    />
                                      <!-- 日期时间选择器 -->
                                    <el-date-picker 
                                        v-else-if="field.type === 'datetime'"
                                        v-model="formData[field.name]"
                                        type="datetime"
                                        :placeholder="field.placeholder || \`请选择\${field.displayName || field.label || field.name}\`"
                                        format="YYYY-MM-DD HH:mm:ss"
                                        value-format="YYYY-MM-DD HH:mm:ss"
                                        style="width: 100%"
                                        :disabled="mode === 'view' || field.readonly"
                                    />
                                    
                                    <!-- 简单数组字段 -->
                                    <div v-else-if="field.type === 'array-simple' || field.type === 'array'" class="array-field">
                                        <!-- 商品选择器 (多选) -->
                                        <product-selector
                                            v-if="field.name.includes('ProductIds') || field.name.includes('productIds') ||
                                                  field.name.includes('ProductId') || field.name.includes('productId')"
                                            :model-value="formData[field.name] || []"
                                            @update:model-value="value => updateFieldValue(field.name, value)"
                                            :placeholder="'请选择' + getFieldLabel(field)"
                                            :multiple="true"
                                            :disabled="mode === 'view' || field.readonly"
                                        />
                                        
                                        <!-- 普通数组字段 -->
                                        <div v-else class="normal-array-field">
                                        <div v-for="(item, index) in formData[field.name] || []" :key="index" class="array-item">
                                            <div class="array-item-content">
                                                <el-input 
                                                    v-if="(field.itemType || 'string') === 'string'"
                                                    :model-value="item"
                                                    @update:model-value="value => updateArrayItem(field.name, index, value)"
                                                    :placeholder="\`\${field.displayName || field.name} \${index + 1}\`"
                                                />
                                                <el-input-number 
                                                    v-else-if="(field.itemType || 'string') === 'number'"
                                                    :model-value="item"
                                                    @update:model-value="value => updateArrayItem(field.name, index, value)"
                                                    style="width: 100%"
                                                />
                                                <el-input 
                                                    v-else
                                                    :model-value="typeof item === 'string' ? item : JSON.stringify(item)"
                                                    @update:model-value="value => updateArrayItem(field.name, index, value)"
                                                    :placeholder="\`\${field.displayName || field.name} \${index + 1}\`"
                                                />
                                            </div>
                                            <el-button 
                                                @click="removeArrayItem(field.name, index)" 
                                                type="danger" 
                                                size="small" 
                                                icon="Delete"
                                                circle
                                                :disabled="mode === 'view'"
                                            />
                                        </div>                                        <el-button 
                                            @click="addArrayItem(field.name, field)" 
                                            type="primary" 
                                            plain
                                            icon="Plus"
                                            style="width: 100%; margin-top: 8px;"
                                            :disabled="mode === 'view'"
                                        >
                                            添加{{ field.displayName || field.name }}
                                        </el-button>
                                        </div>
                                    </div>
                                    
                                    <!-- 复杂数组类型使用复杂数组渲染器 -->
                                    <div v-else-if="field.type === 'array-complex'" class="array-complex-field">
                                        <complex-array-renderer
                                            :field-name="field.name"
                                            :field-config="field"
                                            :model-value="getFieldValue(field.name) || []"
                                            :disabled="mode === 'view' || field.readonly"
                                            @update:model-value="value => updateFieldValue(field.name, value)">
                                        </complex-array-renderer>
                                    </div>

                                    <!-- 对象字段（条件配置或JSON编辑器） -->
                                    <div v-else-if="field.type === 'object' || field.name.includes('Conditions') || field.name.includes('conditions')" class="object-field">
                                        <!-- 使用智能条件配置组件 -->
                                        <condition-config-renderer
                                            v-if="field.name.includes('Conditions') || field.name.includes('conditions')"
                                            :field-name="field.name"
                                            :field-config="field"
                                            :model-value="formData[field.name]"
                                            :rule-type="promotionType || 'general'"
                                            :disabled="mode === 'view' || field.readonly"
                                            @update:model-value="value => updateFieldValue(field.name, value)"
                                        />
                                        
                                        <!-- 普通JSON编辑器 -->
                                        <div v-else class="json-editor-field">
                                            <div class="json-editor-header">
                                                <span>{{ getFieldLabel(field) }}配置</span>
                                                <el-button 
                                                    size="small" 
                                                    type="primary" 
                                                    link
                                                    @click="showJsonHelper(field)"
                                                >
                                                    配置助手
                                                </el-button>
                                            </div>
                                            <el-input 
                                                :model-value="formatJsonValue(formData[field.name])"
                                                @update:model-value="value => parseJsonValue(value, field.name)"
                                                type="textarea"
                                                :rows="field.rows || 6"
                                                :placeholder="getJsonPlaceholder(field)"
                                                :disabled="mode === 'view' || field.readonly"
                                                style="font-family: 'Courier New', monospace;"
                                            />
                                            <div class="json-hint">
                                                <el-text size="small" type="info">
                                                    提示：请输入有效的JSON格式，可点击"配置助手"获取帮助
                                                </el-text>
                                            </div>
                                        </div>
                                    </div>
                                      <!-- 默认文本输入 -->
                                    <el-input 
                                        v-else
                                        v-model="formData[field.name]"
                                        :placeholder="field.placeholder || \`请输入\${field.displayName || field.label || field.name}\`"
                                        :disabled="mode === 'view' || field.readonly"
                                    />
                                </el-form-item>
                            </el-col>
                        </template>
                    </el-row>
                </template>

                <!-- 表单操作 -->
                <el-form-item v-if="mode !== 'view'" style="margin-top: 32px;">
                    <el-button type="primary" @click="handleSubmit" :loading="loading">
                        {{ mode === 'edit' ? '更新规则' : '创建规则' }}
                    </el-button>
                    <el-button @click="handleReset">重置</el-button>
                    <el-button @click="$emit('cancel')">取消</el-button>
                </el-form-item>
            </el-form>
        </div>
    `
};

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DynamicFormRenderer;
} else if (typeof window !== 'undefined') {
    window.DynamicFormRenderer = DynamicFormRenderer;
    window.DynamicFormRenderer_template = DynamicFormRenderer; // 向后兼容
}
