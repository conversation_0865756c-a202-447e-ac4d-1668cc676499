using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Inventory;

/// <summary>
/// 库存管理器接口 - 商品数量占用管理
/// </summary>
public interface IInventoryManager
{
    /// <summary>
    /// 预占用商品数量
    /// </summary>
    Task<ReservationResult> ReserveQuantityAsync(string productId, int quantity, string promotionId, int priority);

    /// <summary>
    /// 批量预占用商品数量
    /// </summary>
    Task<Dictionary<string, ReservationResult>> ReserveBatchAsync(
        Dictionary<string, ReservationRequest> requests);

    /// <summary>
    /// 释放预占用的数量
    /// </summary>
    Task<bool> ReleaseReservationAsync(string reservationId);

    /// <summary>
    /// 批量释放预占用
    /// </summary>
    Task<Dictionary<string, bool>> ReleaseBatchAsync(List<string> reservationIds);

    /// <summary>
    /// 确认预占用（转为实际消耗）
    /// </summary>
    Task<bool> ConfirmReservationAsync(string reservationId);

    /// <summary>
    /// 获取商品可用数量
    /// </summary>
    Task<int> GetAvailableQuantityAsync(string productId);

    /// <summary>
    /// 获取商品预占用详情
    /// </summary>
    Task<List<QuantityReservation>> GetReservationsAsync(string productId);

    /// <summary>
    /// 回滚所有预占用（用于促销计算失败时）
    /// </summary>
    Task<RollbackResult> RollbackAllReservationsAsync(string sessionId);

    /// <summary>
    /// 创建库存快照
    /// </summary>
    Task<InventorySnapshot> CreateSnapshotAsync(ShoppingCart cart);

    /// <summary>
    /// 恢复库存快照
    /// </summary>
    Task<bool> RestoreSnapshotAsync(InventorySnapshot snapshot);
}

/// <summary>
/// 预占用请求
/// </summary>
public class ReservationRequest
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 预占用数量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 促销ID
    /// </summary>
    public string PromotionId { get; set; } = string.Empty;

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// 会话ID
    /// </summary>
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// 预占用类型
    /// </summary>
    public ReservationType Type { get; set; } = ReservationType.Consumption;
}

/// <summary>
/// 预占用结果
/// </summary>
public class ReservationResult
{
    /// <summary>
    /// 预占用ID
    /// </summary>
    public string ReservationId { get; set; } = string.Empty;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// 实际预占用数量
    /// </summary>
    public int ReservedQuantity { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 冲突的预占用列表
    /// </summary>
    public List<QuantityReservation> ConflictingReservations { get; set; } = new();
}

/// <summary>
/// 数量预占用记录
/// </summary>
public class QuantityReservation
{
    /// <summary>
    /// 预占用ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 预占用数量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 促销ID
    /// </summary>
    public string PromotionId { get; set; } = string.Empty;

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// 预占用类型
    /// </summary>
    public ReservationType Type { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ReservationStatus Status { get; set; } = ReservationStatus.Reserved;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime ExpiresAt { get; set; }

    /// <summary>
    /// 会话ID
    /// </summary>
    public string SessionId { get; set; } = string.Empty;
}

/// <summary>
/// 预占用类型
/// </summary>
public enum ReservationType
{
    /// <summary>消耗（用于促销条件）</summary>
    Consumption,
    /// <summary>赠送（用于赠品）</summary>
    Gift,
    /// <summary>换购（用于换购商品）</summary>
    Exchange
}

/// <summary>
/// 预占用状态
/// </summary>
public enum ReservationStatus
{
    /// <summary>已预占用</summary>
    Reserved,
    /// <summary>已确认</summary>
    Confirmed,
    /// <summary>已释放</summary>
    Released,
    /// <summary>已过期</summary>
    Expired
}

/// <summary>
/// 回滚结果
/// </summary>
public class RollbackResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// 回滚的预占用数量
    /// </summary>
    public int RolledBackCount { get; set; }

    /// <summary>
    /// 失败的预占用列表
    /// </summary>
    public List<string> FailedReservations { get; set; } = new();

    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;
}

/// <summary>
/// 库存快照
/// </summary>
public class InventorySnapshot
{
    /// <summary>
    /// 快照ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 商品库存状态
    /// </summary>
    public Dictionary<string, ProductInventoryState> ProductStates { get; set; } = new();

    /// <summary>
    /// 预占用记录
    /// </summary>
    public List<QuantityReservation> Reservations { get; set; } = new();
}

/// <summary>
/// 商品库存状态
/// </summary>
public class ProductInventoryState
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalQuantity { get; set; }

    /// <summary>
    /// 可用数量
    /// </summary>
    public int AvailableQuantity { get; set; }

    /// <summary>
    /// 预占用数量
    /// </summary>
    public int ReservedQuantity { get; set; }

    /// <summary>
    /// 已确认数量
    /// </summary>
    public int ConfirmedQuantity { get; set; }
}
