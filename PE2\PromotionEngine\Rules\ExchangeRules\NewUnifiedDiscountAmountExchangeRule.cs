using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Core;
using PE2.PromotionEngine.Observability;
using PE2.PromotionEngine.Conditions;
using PE2.PromotionEngine.Inventory;
using PE2.PromotionEngine.Performance;
using PE2.PromotionEngine.Allocation;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Rules.ExchangeRules;

/// <summary>
/// 新架构统一减现换购规则
/// 针对某类商品，满X件或X元，优惠Y元，换购一件C类商品
/// 集成新架构的所有基础设施组件，支持.NET 9.0语法和异步模式
/// </summary>
public sealed class NewUnifiedDiscountAmountExchangeRule : NewExchangeRuleBase, INewPromotionRule
{
    /// <summary>
    /// 构造函数
    /// </summary>
    public NewUnifiedDiscountAmountExchangeRule(
        ILogger<NewUnifiedDiscountAmountExchangeRule> logger,
        IObservabilityEngine observability,
        IConditionEngine conditionEngine,
        IInventoryManager inventoryManager,
        IPerformanceOptimizer performanceOptimizer,
        IAllocationEngine allocationEngine,
        UnifiedDiscountAmountExchangeConfiguration configuration)
        : base(logger, observability, conditionEngine, inventoryManager, performanceOptimizer, allocationEngine)
    {
        ArgumentNullException.ThrowIfNull(configuration);
        
        Id = configuration.Id;
        Name = configuration.Name;
        Description = configuration.Description;
        Priority = configuration.Priority;
        IsEnabled = configuration.IsEnabled;
        StartTime = configuration.StartTime;
        EndTime = configuration.EndTime;
        IsRepeatable = configuration.IsRepeatable;
        MaxApplications = configuration.MaxApplications;
        ApplicableCustomerTypes = configuration.ApplicableCustomerTypes;
        ExclusiveRuleIds = configuration.ExclusiveRuleIds;
        CanStackWithOthers = configuration.CanStackWithOthers;
        ProductExclusivity = configuration.ProductExclusivity;
        Strategy = configuration.Strategy;
        BenefitSelection = configuration.BenefitSelection;
        BuyCondition = configuration.BuyCondition;
        ExchangeCondition = configuration.ExchangeCondition;
        ExchangeConditions = configuration.ExchangeConditions;
    }

    /// <summary>
    /// 规则类型
    /// </summary>
    public override string RuleType => "UnifiedDiscountAmountExchange";

    /// <summary>
    /// 减现换购条件列表
    /// </summary>
    public List<DiscountAmountExchangeCondition> ExchangeConditions { get; init; } = [];

    /// <summary>
    /// 异步检查促销条件是否满足
    /// </summary>
    protected override async Task<bool> CheckConditionsAsync(ShoppingCart cart, CancellationToken cancellationToken = default)
    {
        try
        {
            // 使用条件引擎验证购买条件
            var buyConditionResult = await ConditionEngine.ValidateConditionAsync(
                BuyCondition, 
                cart, 
                cancellationToken).ConfigureAwait(false);

            if (!buyConditionResult.IsValid)
                return false;

            // 验证换购商品是否在购物车中
            var allExchangeProductIds = ExchangeConditions
                .SelectMany(c => c.ExchangeProductIds)
                .Distinct()
                .ToList();

            return await ValidateExchangeProductsInCartAsync(cart, allExchangeProductIds, cancellationToken).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "检查统一减现换购条件时发生异常: {RuleId}", Id);
            return false;
        }
    }

    /// <summary>
    /// 异步计算可应用的最大次数
    /// </summary>
    public override async Task<int> CalculateMaxApplicationsAsync(ShoppingCart cart, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!await CheckConditionsAsync(cart, cancellationToken).ConfigureAwait(false))
                return 0;

            // 使用性能优化器计算最大应用次数
            var optimizationContext = new OptimizationContext
            {
                Cart = cart,
                Rules = [this],
                Target = OptimizationTarget.MaximizeApplications
            };

            var result = await PerformanceOptimizer.OptimizeRuleCombinationAsync(optimizationContext, cancellationToken).ConfigureAwait(false);
            
            // 计算基于购买条件的最大应用次数
            var maxByBuyConditions = await CalculateMaxApplicationsByBuyConditionsAsync(cart, cancellationToken).ConfigureAwait(false);

            // 计算基于换购商品的最大应用次数
            var maxByExchangeProducts = await CalculateMaxApplicationsByExchangeProductsAsync(cart, cancellationToken).ConfigureAwait(false);

            // 取两者最小值，确保换购逻辑正确
            var maxApplications = Math.Min(maxByBuyConditions, maxByExchangeProducts);

            return ApplyApplicationLimits(maxApplications);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "计算统一减现换购最大应用次数时发生异常: {RuleId}", Id);
            return 0;
        }
    }

    /// <summary>
    /// 异步应用促销规则
    /// </summary>
    public override async Task<AppliedPromotion> ApplyPromotionAsync(
        ProcessedCart cart, 
        int applicationCount = 1, 
        string? traceId = null,
        CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        
        try
        {
            // 记录开始追踪
            if (!string.IsNullOrEmpty(traceId))
            {
                Observability.TrackCalculationStep(traceId, new CalculationStep
                {
                    Id = Guid.NewGuid().ToString("N"),
                    TraceId = traceId,
                    StepType = StepType.PromotionApplication,
                    Timestamp = startTime,
                    Description = $"开始应用统一减现换购规则: {Name}",
                    RuleId = Id,
                    Data = new Dictionary<string, object>
                    {
                        ["applicationCount"] = applicationCount,
                        ["cartItemCount"] = cart.Items.Count
                    }
                });
            }

            // 预留库存
            var reservationId = await ReserveInventoryAsync(cart, applicationCount, cancellationToken).ConfigureAwait(false);

            try
            {
                // 应用减现换购逻辑
                var (discountAmount, consumedItems, exchangeItems) = await ApplyDiscountAmountExchangeAsync(
                    cart, applicationCount, traceId, cancellationToken).ConfigureAwait(false);

                var appliedPromotion = CreateAppliedPromotion(discountAmount, applicationCount, consumedItems, exchangeItems);

                // 记录促销应用追踪
                TrackPromotionApplication(traceId, appliedPromotion);

                // 使用分配引擎进行精确的折扣分配
                if (discountAmount > 0)
                {
                    var allocationRequest = new AllocationRequest
                    {
                        TotalDiscount = discountAmount,
                        Items = cart.Items.Where(item => exchangeItems.Any(ei => ei.ProductId == item.ProductId)).ToList(),
                        Strategy = AllocationStrategy.ProportionalByValue,
                        RoundingStrategy = RoundingStrategy.RoundToNearest
                    };

                    var allocationResult = await AllocationEngine.AllocateDiscountAsync(allocationRequest, cancellationToken).ConfigureAwait(false);
                    
                    // 应用分配结果到购物车
                    ApplyAllocationToCart(cart, allocationResult);
                }

                return appliedPromotion;
            }
            finally
            {
                // 释放库存预留
                if (!string.IsNullOrEmpty(reservationId))
                {
                    await InventoryManager.ReleaseReservationAsync(reservationId, cancellationToken).ConfigureAwait(false);
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "应用统一减现换购规则时发生异常: {RuleId}", Id);
            
            return CreateAppliedPromotion(0, 0, [], []);
        }
        finally
        {
            // 记录性能指标
            var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
            Logger.LogDebug("统一减现换购规则执行完成: {RuleId}, 耗时: {ExecutionTime}ms", Id, executionTime);
        }
    }

    /// <summary>
    /// 根据购买条件计算最大应用次数
    /// </summary>
    private async Task<int> CalculateMaxApplicationsByBuyConditionsAsync(ShoppingCart cart, CancellationToken cancellationToken)
    {
        try
        {
            var conditionResult = await ConditionEngine.ValidateConditionAsync(BuyCondition, cart, cancellationToken).ConfigureAwait(false);
            
            if (!conditionResult.IsValid)
                return 0;

            var availableQuantity = BuyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
            var totalAmount = BuyCondition.ProductIds.Sum(id =>
                cart.Items.Where(x => x.ProductId == id).Sum(x => x.SubTotal));

            var maxByQuantity = int.MaxValue;
            var maxByAmount = int.MaxValue;

            // 按数量计算最大应用次数
            if (BuyCondition.RequiredQuantity > 0)
            {
                maxByQuantity = IsRepeatable
                    ? availableQuantity / BuyCondition.RequiredQuantity
                    : (availableQuantity >= BuyCondition.RequiredQuantity ? 1 : 0);
            }

            // 按金额计算最大应用次数
            if (BuyCondition.RequiredAmount > 0)
            {
                maxByAmount = IsRepeatable
                    ? (int)(totalAmount / BuyCondition.RequiredAmount)
                    : (totalAmount >= BuyCondition.RequiredAmount ? 1 : 0);
            }

            // 根据条件类型返回结果
            if (BuyCondition.RequiredQuantity <= 0 && BuyCondition.RequiredAmount > 0)
                return maxByAmount;
            else if (BuyCondition.RequiredQuantity > 0 && BuyCondition.RequiredAmount <= 0)
                return maxByQuantity;
            else if (BuyCondition.RequiredQuantity > 0 && BuyCondition.RequiredAmount > 0)
                return Math.Min(maxByQuantity, maxByAmount);

            return 1;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "计算购买条件最大应用次数时发生异常: {RuleId}", Id);
            return 0;
        }
    }

    /// <summary>
    /// 根据换购商品可用性计算最大应用次数
    /// </summary>
    private async Task<int> CalculateMaxApplicationsByExchangeProductsAsync(ShoppingCart cart, CancellationToken cancellationToken)
    {
        try
        {
            var applicableExchanges = GetApplicableExchangeConditions();
            if (!applicableExchanges.Any())
                return 0;

            var maxApplications = int.MaxValue;

            foreach (var exchange in applicableExchanges)
            {
                var totalAvailableQuantity = 0;
                
                foreach (var productId in exchange.ExchangeProductIds)
                {
                    var available = await InventoryManager.GetAvailableQuantityAsync(productId, cancellationToken).ConfigureAwait(false);
                    totalAvailableQuantity += available;
                }

                var requiredQuantity = Math.Max(exchange.ExchangeQuantity, 1);
                var maxByThisExchange = totalAvailableQuantity / requiredQuantity;
                
                maxApplications = Math.Min(maxApplications, maxByThisExchange);
            }

            return maxApplications == int.MaxValue ? 0 : maxApplications;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "计算换购商品最大应用次数时发生异常: {RuleId}", Id);
            return 0;
        }
    }

    /// <summary>
    /// 获取适用的换购条件
    /// </summary>
    private List<DiscountAmountExchangeCondition> GetApplicableExchangeConditions()
    {
        return ExchangeConditions.Where(c => c.IsEnabled).ToList();
    }

    /// <summary>
    /// 应用应用次数限制
    /// </summary>
    private int ApplyApplicationLimits(int maxApplications)
    {
        if (!IsRepeatable)
        {
            maxApplications = Math.Min(maxApplications, 1);
        }
        else if (MaxApplications > 0)
        {
            maxApplications = Math.Min(maxApplications, MaxApplications);
        }

        return maxApplications;
    }

    /// <summary>
    /// 验证换购商品是否在购物车中
    /// </summary>
    private async Task<bool> ValidateExchangeProductsInCartAsync(
        ShoppingCart cart,
        List<string> exchangeProductIds,
        CancellationToken cancellationToken)
    {
        try
        {
            foreach (var productId in exchangeProductIds)
            {
                var hasProduct = cart.Items.Any(item => item.ProductId == productId);
                if (!hasProduct)
                    return false;

                // 验证库存可用性
                var available = await InventoryManager.GetAvailableQuantityAsync(productId, cancellationToken).ConfigureAwait(false);
                if (available <= 0)
                    return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "验证换购商品时发生异常: {RuleId}", Id);
            return false;
        }
    }

    /// <summary>
    /// 预留库存
    /// </summary>
    private async Task<string?> ReserveInventoryAsync(ProcessedCart cart, int applicationCount, CancellationToken cancellationToken)
    {
        try
        {
            var requiredProducts = new List<(string ProductId, int Quantity)>();

            // 计算需要预留的商品数量
            foreach (var exchange in GetApplicableExchangeConditions())
            {
                foreach (var productId in exchange.ExchangeProductIds)
                {
                    var requiredQuantity = exchange.ExchangeQuantity * applicationCount;
                    requiredProducts.Add((productId, requiredQuantity));
                }
            }

            if (!requiredProducts.Any())
                return null;

            var reservationRequest = new ReservationRequest
            {
                ReservationId = Guid.NewGuid().ToString("N"),
                ProductQuantities = requiredProducts.ToDictionary(p => p.ProductId, p => p.Quantity),
                Priority = ReservationPriority.High,
                ExpirationTime = DateTime.UtcNow.AddMinutes(5),
                Source = $"Rule_{Id}"
            };

            var result = await InventoryManager.ReserveProductsAsync(reservationRequest, cancellationToken).ConfigureAwait(false);
            return result.IsSuccessful ? reservationRequest.ReservationId : null;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "预留库存时发生异常: {RuleId}", Id);
            return null;
        }
    }

    /// <summary>
    /// 应用减现换购逻辑
    /// </summary>
    private async Task<(decimal totalDiscount, List<ConsumedItem> consumed, List<GiftItem> exchanged)> ApplyDiscountAmountExchangeAsync(
        ProcessedCart cart,
        int applicationCount,
        string? traceId,
        CancellationToken cancellationToken)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var exchangeItems = new List<GiftItem>();

        try
        {
            for (int application = 0; application < applicationCount; application++)
            {
                var (success, discount, exchanges) = await TryApplySingleDiscountAmountExchangeAsync(
                    cart, consumedItems, application + 1, traceId, cancellationToken).ConfigureAwait(false);

                if (!success)
                    break; // 无法继续应用，停止

                totalDiscountAmount += discount;
                exchangeItems.AddRange(exchanges);
            }

            return (totalDiscountAmount, consumedItems, exchangeItems);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "应用减现换购逻辑时发生异常: {RuleId}", Id);
            return (0, [], []);
        }
    }

    /// <summary>
    /// 尝试应用单次减现换购
    /// </summary>
    private async Task<(bool success, decimal discount, List<GiftItem> exchanges)> TryApplySingleDiscountAmountExchangeAsync(
        ProcessedCart cart,
        List<ConsumedItem> consumedItems,
        int applicationIndex,
        string? traceId,
        CancellationToken cancellationToken)
    {
        try
        {
            // 验证购买条件
            var conditionResult = await ConditionEngine.ValidateConditionAsync(BuyCondition, cart, cancellationToken).ConfigureAwait(false);
            if (!conditionResult.IsValid)
                return (false, 0, []);

            // 消耗购买条件商品
            var consumedInThisApplication = ConsumeBuyConditionProducts(cart, BuyCondition);
            consumedItems.AddRange(consumedInThisApplication);

            // 应用减现换购逻辑
            var (discountAmount, exchangeItems) = await ApplyDiscountAmountExchangeLogicAsync(cart, traceId, cancellationToken).ConfigureAwait(false);

            return (discountAmount > 0, discountAmount, exchangeItems);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "尝试应用单次减现换购时发生异常: {RuleId}, ApplicationIndex: {ApplicationIndex}", Id, applicationIndex);
            return (false, 0, []);
        }
    }

    /// <summary>
    /// 消耗购买条件商品
    /// </summary>
    private List<ConsumedItem> ConsumeBuyConditionProducts(ProcessedCart cart, ExchangeBuyCondition buyCondition)
    {
        var consumedItems = new List<ConsumedItem>();

        if (buyCondition.RequiredQuantity > 0)
        {
            // 按数量消耗
            var remainingQuantity = buyCondition.RequiredQuantity;
            foreach (var productId in buyCondition.ProductIds)
            {
                if (remainingQuantity <= 0) break;

                var cartItem = cart.Items.FirstOrDefault(item => item.ProductId == productId && item.Quantity > 0);
                if (cartItem != null)
                {
                    var consumeQuantity = Math.Min(cartItem.Quantity, remainingQuantity);

                    consumedItems.Add(new ConsumedItem
                    {
                        ProductId = productId,
                        ProductName = cartItem.ProductName,
                        Quantity = consumeQuantity,
                        UnitPrice = cartItem.OriginalUnitPrice
                    });

                    cartItem.Quantity -= consumeQuantity;
                    remainingQuantity -= consumeQuantity;
                }
            }
        }
        else if (buyCondition.RequiredAmount > 0)
        {
            // 按金额消耗
            var remainingAmount = buyCondition.RequiredAmount;
            foreach (var productId in buyCondition.ProductIds)
            {
                if (remainingAmount <= 0) break;

                var cartItem = cart.Items.FirstOrDefault(item => item.ProductId == productId && item.Quantity > 0);
                if (cartItem != null)
                {
                    var maxAffordableQuantity = (int)Math.Floor(remainingAmount / cartItem.OriginalUnitPrice);
                    var consumeQuantity = Math.Min(cartItem.Quantity, maxAffordableQuantity);

                    if (consumeQuantity > 0)
                    {
                        consumedItems.Add(new ConsumedItem
                        {
                            ProductId = productId,
                            ProductName = cartItem.ProductName,
                            Quantity = consumeQuantity,
                            UnitPrice = cartItem.OriginalUnitPrice
                        });

                        cartItem.Quantity -= consumeQuantity;
                        remainingAmount -= consumeQuantity * cartItem.OriginalUnitPrice;
                    }
                }
            }
        }

        return consumedItems;
    }

    /// <summary>
    /// 应用减现换购逻辑
    /// </summary>
    private async Task<(decimal discountAmount, List<GiftItem> exchangeItems)> ApplyDiscountAmountExchangeLogicAsync(
        ProcessedCart cart,
        string? traceId,
        CancellationToken cancellationToken)
    {
        var totalDiscountAmount = 0m;
        var exchangeItems = new List<GiftItem>();

        try
        {
            var applicableExchanges = GetApplicableExchangeConditions();

            foreach (var exchange in applicableExchanges)
            {
                var (discount, items) = await ProcessSingleDiscountAmountExchangeConditionAsync(cart, exchange, traceId, cancellationToken).ConfigureAwait(false);
                totalDiscountAmount += discount;
                exchangeItems.AddRange(items);
            }

            return (totalDiscountAmount, exchangeItems);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "应用减现换购逻辑时发生异常: {RuleId}", Id);
            return (0, []);
        }
    }

    /// <summary>
    /// 处理单个减现换购条件
    /// </summary>
    private async Task<(decimal discountAmount, List<GiftItem> exchangeItems)> ProcessSingleDiscountAmountExchangeConditionAsync(
        ProcessedCart cart,
        DiscountAmountExchangeCondition exchange,
        string? traceId,
        CancellationToken cancellationToken)
    {
        var discountAmount = 0m;
        var exchangeItems = new List<GiftItem>();

        try
        {
            foreach (var productId in exchange.ExchangeProductIds)
            {
                var cartItem = cart.Items.FirstOrDefault(item => item.ProductId == productId);
                if (cartItem == null) continue;

                var exchangeQuantity = Math.Min(cartItem.Quantity, exchange.ExchangeQuantity);
                if (exchangeQuantity <= 0) continue;

                // 计算减现换购优惠
                var originalPrice = cartItem.OriginalUnitPrice * exchangeQuantity;
                var discountedPrice = Math.Max(0, originalPrice - exchange.DiscountAmount);
                var itemDiscount = originalPrice - discountedPrice;

                if (itemDiscount > 0)
                {
                    discountAmount += itemDiscount;

                    // 更新商品实际价格
                    cartItem.ActualUnitPrice = Math.Max(0, cartItem.OriginalUnitPrice - (exchange.DiscountAmount / exchangeQuantity));

                    exchangeItems.Add(new GiftItem
                    {
                        ProductId = productId,
                        ProductName = cartItem.ProductName,
                        Quantity = exchangeQuantity,
                        UnitPrice = cartItem.ActualUnitPrice,
                        OriginalUnitPrice = cartItem.OriginalUnitPrice
                    });
                }
            }

            return (discountAmount, exchangeItems);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "处理单个减现换购条件时发生异常: {RuleId}", Id);
            return (0, []);
        }
    }

    /// <summary>
    /// 应用分配结果到购物车
    /// </summary>
    private static void ApplyAllocationToCart(ProcessedCart cart, AllocationResult allocationResult)
    {
        foreach (var allocation in allocationResult.Allocations)
        {
            var cartItem = cart.Items.FirstOrDefault(item => item.ProductId == allocation.ProductId);
            if (cartItem != null)
            {
                cartItem.ActualUnitPrice = Math.Max(0, cartItem.ActualUnitPrice - allocation.AllocatedDiscount / cartItem.Quantity);
            }
        }
    }
}

/// <summary>
/// 统一减现换购规则配置
/// </summary>
public sealed class UnifiedDiscountAmountExchangeConfiguration
{
    public required string Id { get; init; }
    public required string Name { get; init; }
    public string Description { get; init; } = string.Empty;
    public int Priority { get; init; } = 0;
    public bool IsEnabled { get; init; } = true;
    public DateTime? StartTime { get; init; }
    public DateTime? EndTime { get; init; }
    public bool IsRepeatable { get; init; } = true;
    public int MaxApplications { get; init; } = 1;
    public List<string> ApplicableCustomerTypes { get; init; } = [];
    public List<string> ExclusiveRuleIds { get; init; } = [];
    public bool CanStackWithOthers { get; init; } = true;
    public ProductExclusivityLevel ProductExclusivity { get; init; } = ProductExclusivityLevel.None;
    public ExchangeStrategy Strategy { get; init; } = ExchangeStrategy.DiscountAmount;
    public BenefitSelectionStrategy BenefitSelection { get; init; } = BenefitSelectionStrategy.CustomerBenefit;
    public required ExchangeBuyCondition BuyCondition { get; init; }
    public required ExchangeCondition ExchangeCondition { get; init; }
    public List<DiscountAmountExchangeCondition> ExchangeConditions { get; init; } = [];
}
