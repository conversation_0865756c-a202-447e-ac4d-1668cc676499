<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>促销元数据API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #40a9ff;
        }
        .result {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #ffeaea;
            border-color: #ff4d4f;
            color: #a8071a;
        }
        .success {
            background: #f6ffed;
            border-color: #52c41a;
            color: #389e0d;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            font-size: 12px;
            line-height: 1.4;
        }
        .loading {
            color: #1890ff;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>促销元数据API测试页面</h1>
        <p>测试基于反射的动态促销配置系统的后端API</p>

        <div class="test-section">
            <h2>API连接测试</h2>
            <button class="btn" onclick="testConnection()">测试连接</button>
            <div id="connectionResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>获取所有促销类型元数据</h2>
            <button class="btn" onclick="getPromotionTypes()">获取促销类型</button>
            <div id="typesResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>获取具体促销类型详情</h2>
            <input type="text" id="ruleTypeInput" placeholder="输入规则类型名称，如: ProductDiscountRule" style="padding: 8px; width: 300px; margin-right: 10px;">
            <button class="btn" onclick="getTypeDetail()">获取详情</button>
            <div id="detailResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>测试促销规则CRUD操作</h2>
            <button class="btn" onclick="testCRUD()">测试CRUD</button>
            <div id="crudResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>API端点清单</h2>
            <ul>
                <li><strong>GET</strong> /api/promotion/metadata/types - 获取所有促销类型元数据</li>
                <li><strong>GET</strong> /api/promotion/metadata/types/{ruleType} - 获取指定促销类型详情</li>
                <li><strong>GET</strong> /api/PromotionRule - 获取所有促销规则</li>
                <li><strong>POST</strong> /api/PromotionRule - 创建促销规则</li>
                <li><strong>PUT</strong> /api/PromotionRule/{id} - 更新促销规则</li>
                <li><strong>DELETE</strong> /api/PromotionRule/{id} - 删除促销规则</li>
                <li><strong>GET</strong> /api/Product - 获取商品列表</li>
            </ul>
        </div>
    </div>

    <script>
        const baseUrl = 'http://localhost:5213/api';

        async function request(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    ...options
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                return await response.json();
            } catch (error) {
                throw error;
            }
        }

        function showResult(elementId, content, isError = false, isSuccess = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : isSuccess ? 'success' : ''}`;
            element.innerHTML = content;
        }

        function showLoading(elementId) {
            showResult(elementId, '<div class="loading">加载中...</div>');
        }

        async function testConnection() {
            showLoading('connectionResult');
            
            try {
                const response = await fetch(`${baseUrl}/promotion/metadata/types`);
                if (response.ok) {
                    showResult('connectionResult', 
                        '<strong>✓ 连接成功！</strong><br>后端API服务正常运行', 
                        false, true
                    );
                } else {
                    showResult('connectionResult', 
                        `<strong>✗ 连接失败</strong><br>HTTP状态: ${response.status}`, 
                        true
                    );
                }
            } catch (error) {
                showResult('connectionResult', 
                    `<strong>✗ 连接失败</strong><br>错误: ${error.message}`, 
                    true
                );
            }
        }

        async function getPromotionTypes() {
            showLoading('typesResult');
            
            try {
                const data = await request(`${baseUrl}/promotion/metadata/types`);
                
                let html = '<strong>✓ 促销类型元数据获取成功</strong><br><br>';
                html += `<strong>找到 ${Object.keys(data).length} 个促销分类：</strong><br><br>`;
                
                Object.keys(data).forEach(categoryKey => {
                    const category = data[categoryKey];
                    html += `<strong>${categoryKey}:</strong> ${category.name}<br>`;
                    html += `描述: ${category.description}<br>`;
                    html += `类型数量: ${Object.keys(category.types || {}).length}<br>`;
                    
                    if (category.types) {
                        html += '包含类型:<br>';
                        Object.keys(category.types).forEach(typeKey => {
                            const type = category.types[typeKey];
                            html += `  - ${typeKey}: ${type.name} (${type.ruleType})<br>`;
                        });
                    }
                    html += '<br>';
                });
                
                html += '<hr><strong>原始JSON数据:</strong><br>';
                html += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                
                showResult('typesResult', html, false, true);
                
            } catch (error) {
                showResult('typesResult', 
                    `<strong>✗ 获取促销类型失败</strong><br>错误: ${error.message}`, 
                    true
                );
            }
        }

        async function getTypeDetail() {
            const ruleType = document.getElementById('ruleTypeInput').value.trim();
            if (!ruleType) {
                showResult('detailResult', 
                    '<strong>请输入规则类型名称</strong><br>例如: ProductDiscountRule', 
                    true
                );
                return;
            }

            showLoading('detailResult');
            
            try {
                const data = await request(`${baseUrl}/promotion/metadata/types/${encodeURIComponent(ruleType)}`);
                
                let html = `<strong>✓ 促销类型 "${ruleType}" 详情获取成功</strong><br><br>`;
                html += `<strong>名称:</strong> ${data.name}<br>`;
                html += `<strong>描述:</strong> ${data.description}<br>`;
                html += `<strong>规则类型:</strong> ${data.ruleType}<br>`;
                html += `<strong>字段数量:</strong> ${(data.fields || []).length}<br><br>`;
                
                if (data.fields && data.fields.length > 0) {
                    html += '<strong>字段配置:</strong><br>';
                    data.fields.forEach((field, index) => {
                        html += `${index + 1}. <strong>${field.displayName || field.name}</strong> (${field.type})<br>`;
                        html += `   - 必填: ${field.required ? '是' : '否'}<br>`;
                        if (field.description) {
                            html += `   - 描述: ${field.description}<br>`;
                        }
                        if (field.group) {
                            html += `   - 分组: ${field.group}<br>`;
                        }
                        html += '<br>';
                    });
                }
                
                html += '<hr><strong>原始JSON数据:</strong><br>';
                html += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                
                showResult('detailResult', html, false, true);
                
            } catch (error) {
                showResult('detailResult', 
                    `<strong>✗ 获取类型详情失败</strong><br>错误: ${error.message}`, 
                    true
                );
            }
        }

        async function testCRUD() {
            showLoading('crudResult');
            
            try {
                let html = '<strong>开始CRUD操作测试...</strong><br><br>';
                
                // 1. 获取所有规则
                html += '1. 获取现有规则...<br>';
                const existingRules = await request(`${baseUrl}/PromotionRule`);
                html += `   ✓ 找到 ${existingRules.length} 个现有规则<br><br>`;
                
                // 2. 创建测试规则
                html += '2. 创建测试规则...<br>';
                const testRule = {
                    ruleId: `test_rule_${Date.now()}`,
                    ruleName: '动态API测试规则',
                    ruleType: 'ProductDiscountRule',
                    description: '这是一个API测试规则',
                    priority: 1,
                    isEnabled: true,
                    startTime: new Date().toISOString(),
                    endTime: null,
                    // 这里可以添加更多字段...
                };
                
                const createdRule = await request(`${baseUrl}/PromotionRule`, {
                    method: 'POST',
                    body: JSON.stringify(testRule)
                });
                html += `   ✓ 规则创建成功: ${createdRule.ruleId}<br><br>`;
                
                // 3. 更新规则
                html += '3. 更新测试规则...<br>';
                createdRule.description = '已更新的测试规则';
                const updatedRule = await request(`${baseUrl}/PromotionRule/${createdRule.ruleId}`, {
                    method: 'PUT',
                    body: JSON.stringify(createdRule)
                });
                html += `   ✓ 规则更新成功<br><br>`;
                
                // 4. 删除规则
                html += '4. 删除测试规则...<br>';
                await request(`${baseUrl}/PromotionRule/${createdRule.ruleId}`, {
                    method: 'DELETE'
                });
                html += `   ✓ 规则删除成功<br><br>`;
                
                html += '<strong>✓ CRUD操作测试完成！所有操作正常。</strong>';
                
                showResult('crudResult', html, false, true);
                
            } catch (error) {
                showResult('crudResult', 
                    `<strong>✗ CRUD操作测试失败</strong><br>错误: ${error.message}`, 
                    true
                );
            }
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            testConnection();
        };
    </script>
</body>
</html>
