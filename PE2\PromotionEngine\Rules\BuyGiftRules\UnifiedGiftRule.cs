using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.BuyGiftRules;

/// <summary>
/// 统一送赠品规则 [OK]
/// 满X件或X元，赠送某类商品Z件
/// 场景案例：购买A商品1件时，免费送1件B商品
/// 活动设置为不翻倍：A、B商品吊牌价和零售价均为1000元；购买A商品1件时，应收金额为1000元（免费送B商品）
/// 活动设置为不翻倍：A、B商品吊牌价和零售价均为1000元；购买A商品2件时，应收金额为2000元（免费送1件B商品）
/// 活动设置为翻2倍：A、B商品吊牌价和零售价均为1000元；购买A商品2件时，应收金额为2000元（免费送2件B商品）
/// 活动设置为翻2倍：A、B商品吊牌价和零售价均为1000元；购买A商品3件时+3件B商品，应收金额为3000 + 1000 = 4000元（免费送2件B商品，因为翻两倍）
/// 备注：1.赠送商品需要在购物车中存在，且赠送商品的数量或价值需满足条件才触发发促销规则执行
///       2.赠送商品可以是实物商品或券商品（券商品需要满足会员身份要求）
///       3.赠送商品策略：客户利益最大化 vs 商家利益最大化 需要根据配置来决定
/// </summary>
public class UnifiedGiftRule : BaseBuyGiftRule
{
    public override string RuleType => "UnifiedGift";

    /// <summary>
    /// 适用的商品ID列表（购买条件商品）
    /// </summary>
    public List<string> ApplicableProductIds { get; set; } = [];

    /// <summary>
    /// 赠品商品ID列表
    /// </summary>
    public List<string> GiftProductIds { get; set; } = [];

    /// <summary>
    /// 最小数量要求
    /// </summary>
    public int MinQuantity { get; set; }

    /// <summary>
    /// 最小金额要求
    /// </summary>
    public decimal MinAmount { get; set; }

    /// <summary>
    /// 赠品数量
    /// </summary>
    public int GiftQuantity { get; set; }

    /// <summary>
    /// 是否按金额计算（如果为true，则使用MinAmount；否则使用MinQuantity）
    /// </summary>
    public bool IsByAmount => MinAmount > 0;

    /// <summary>
    /// 券赠品列表（送券场景）
    /// </summary>
    public List<CouponGift> CouponGifts { get; set; } = new();

    /// <summary>
    /// 是否仅限会员（送券时必须为true）
    /// </summary>
    public bool MemberOnly { get; set; } = false;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!ApplicableProductIds.Any())
            return false;

        // 验证购买条件商品是否在购物车中
        if (!ValidateBuyGiftProductsInCart(cart, ApplicableProductIds))
            return false;

        // 如果有实物赠品，验证赠品是否在购物车中
        if (GiftProductIds.Any() && !ValidateGiftProductsInCart(cart, GiftProductIds))
            return false;

        // 如果有券赠品且仅限会员，验证会员身份
        if (CouponGifts.Any() && MemberOnly && string.IsNullOrEmpty(cart.MemberId))
            return false;

        // 检查购买条件
        return CheckBuyConditions(cart);
    }

    /// <summary>
    /// 检查购买条件
    /// </summary>
    private bool CheckBuyConditions(ShoppingCart cart)
    {
        if (IsByAmount)
        {
            var totalAmount = CalculateTotalAmount(cart, ApplicableProductIds);
            return totalAmount >= MinAmount;
        }
        else
        {
            var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);
            return totalQuantity >= MinQuantity;
        }
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = 0;

        if (IsByAmount)
        {
            var totalAmount = CalculateTotalAmount(cart, ApplicableProductIds);

            maxApplications = IsRepeatable
                ? (int)(totalAmount / MinAmount)
                : (totalAmount >= MinAmount ? 1 : 0);
        }
        else
        {
            var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);

            maxApplications = IsRepeatable
                ? totalQuantity / MinQuantity
                : (totalQuantity >= MinQuantity ? 1 : 0);
        }

        // 如果有实物赠品，还需要考虑赠品的可用数量
        if (GiftProductIds.Any())
        {
            var availableGiftQuantity = CalculateTotalQuantity(cart, GiftProductIds);
            var maxByGiftQuantity = availableGiftQuantity / GiftQuantity;
            maxApplications = Math.Min(maxApplications, maxByGiftQuantity);
        }

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyUnifiedGift(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0 || result.Item3.Any();

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用统一送赠品促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用统一送赠品促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyUnifiedGift(ShoppingCart cart, int applicationCount)
    {
        var totalGiftValue = 0m;
        var consumedItems = new List<ConsumedItem>();
        var giftItems = new List<GiftItem>(); // 赠品记录

        for (int app = 0; app < applicationCount; app++)
        {
            // 消耗购买条件商品（记录消耗但不修改购物车）
            var requiredQuantity = IsByAmount ? 0 : MinQuantity;
            if (requiredQuantity > 0)
            {
                var consumed = ConsumeConditionProducts(cart, ApplicableProductIds, requiredQuantity);
                foreach (var item in consumed)
                {
                    var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == item.ProductId);
                    if (existingConsumed != null)
                    {
                        existingConsumed.Quantity += item.Quantity;
                    }
                    else
                    {
                        consumedItems.Add(item);
                    }
                }
            }

            // 处理实物赠品
            if (GiftProductIds.Any())
            {
                var giftProducts = ProcessPhysicalGifts(cart, app);
                totalGiftValue += giftProducts.Sum(g => g.Value);
                giftItems.AddRange(giftProducts);
            }

            // 处理券赠品
            if (CouponGifts.Any() && !string.IsNullOrEmpty(cart.MemberId))
            {
                var couponGiftItems = ProcessCouponGifts(cart, app);
                giftItems.AddRange(couponGiftItems);
            }
        }

        return (totalGiftValue, consumedItems, giftItems);
    }

    /// <summary>
    /// 处理实物赠品
    /// </summary>
    private List<GiftItem> ProcessPhysicalGifts(ShoppingCart cart, int applicationIndex)
    {
        var giftItems = new List<GiftItem>();

        // 收集可用的赠品
        var availableGifts = new List<(string ProductId, int Quantity, decimal UnitPrice)>();
        foreach (var giftProductId in GiftProductIds)
        {
            var cartItems = cart.Items.Where(x => x.Product.Id == giftProductId && x.Quantity > 0).ToList();
            foreach (var cartItem in cartItems)
            {
                availableGifts.Add((giftProductId, cartItem.Quantity, cartItem.UnitPrice));
            }
        }

        // 选择赠品
        var selectedGifts = SelectGiftProducts(availableGifts, GiftQuantity);
        if (!selectedGifts.Any()) return giftItems;

        // 应用赠品效果到购物车
        var promotion = new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType
        };
        ApplyGiftEffectToCart(cart, selectedGifts, promotion);

        // 记录赠品
        foreach (var gift in selectedGifts)
        {
            var strategyDescription = GiftSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                ? "客户利益最大化"
                : "商家利益最大化";

            var existingGift = giftItems.FirstOrDefault(x => x.ProductId == gift.ProductId);
            if (existingGift != null)
            {
                existingGift.Quantity += gift.Quantity;
                existingGift.Value += gift.Quantity * gift.UnitPrice;
            }
            else
            {
                var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == gift.ProductId);
                giftItems.Add(new GiftItem
                {
                    ProductId = gift.ProductId,
                    ProductName = cartItem?.Product.Name ?? gift.ProductId,
                    Quantity = gift.Quantity,
                    Value = gift.Quantity * gift.UnitPrice,
                    Description = $"统一送赠品：赠送{gift.Quantity}件，价值{gift.Quantity * gift.UnitPrice:C}（第{applicationIndex + 1}次应用，{strategyDescription}）"
                });
            }
        }

        return giftItems;
    }

    /// <summary>
    /// 处理券赠品
    /// </summary>
    private List<GiftItem> ProcessCouponGifts(ShoppingCart cart, int applicationIndex)
    {
        var giftItems = new List<GiftItem>();

        foreach (var couponGift in CouponGifts)
        {
            // 发送券到会员账户（异步处理）
            _ = Task.Run(async () => await SendCouponToMemberAsync(cart.MemberId, couponGift));

            // 记录券赠品
            giftItems.Add(new GiftItem
            {
                ProductId = couponGift.CouponId,
                ProductName = couponGift.CouponName,
                Quantity = couponGift.Quantity,
                Value = couponGift.CouponValue * couponGift.Quantity,
                Description = $"统一送券：{couponGift.Description}，价值{couponGift.CouponValue * couponGift.Quantity:C}（第{applicationIndex + 1}次应用）"
            });
        }

        return giftItems;
    }
}
