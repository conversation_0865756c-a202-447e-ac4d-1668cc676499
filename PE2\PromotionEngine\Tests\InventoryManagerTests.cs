global using Microsoft.Extensions.Logging;
global using Microsoft.Extensions.DependencyInjection;
using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Inventory;
using PE2.PromotionEngine.Observability;

namespace PE2.PromotionEngine.Tests;

/// <summary>
/// 库存管理器测试类
/// </summary>
public class InventoryManagerTests
{
    private readonly IServiceProvider _serviceProvider;
    private readonly InventoryManager _inventoryManager;
    private readonly ObservabilityEngine _observabilityEngine;

    public InventoryManagerTests()
    {
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole());
        
        _serviceProvider = services.BuildServiceProvider();
        
        var logger = _serviceProvider.GetRequiredService<ILogger<InventoryManager>>();
        var obsLogger = _serviceProvider.GetRequiredService<ILogger<ObservabilityEngine>>();
        
        _observabilityEngine = new ObservabilityEngine(obsLogger);
        _inventoryManager = new InventoryManager(logger, _observabilityEngine);
    }

    /// <summary>
    /// 测试基本预占用功能
    /// </summary>
    public async Task TestBasicReservation()
    {
        Console.WriteLine("=== 测试基本预占用功能 ===");
        
        var cart = CreateTestCart();
        var request = new ReservationRequest
        {
            ProductId = "P001",
            Quantity = 2,
            Priority = ReservationPriority.High,
            RuleId = "RULE001",
            Cart = cart,
            ExpirationTime = TimeSpan.FromMinutes(10)
        };

        var result = await _inventoryManager.ReserveQuantityAsync(request);
        
        Console.WriteLine($"预占用结果: {result.IsSuccessful}");
        Console.WriteLine($"预占用ID: {result.ReservationId}");
        Console.WriteLine($"预占用数量: {result.ReservedQuantity}");
        Console.WriteLine($"过期时间: {result.ExpiresAt}");
        
        if (!result.IsSuccessful)
        {
            Console.WriteLine($"错误信息: {result.ErrorMessage}");
        }

        // 确认预占用
        if (result.IsSuccessful)
        {
            var confirmed = await _inventoryManager.ConfirmReservationAsync(result.ReservationId);
            Console.WriteLine($"确认预占用: {confirmed}");
        }
    }

    /// <summary>
    /// 测试预占用冲突解决
    /// </summary>
    public async Task TestReservationConflictResolution()
    {
        Console.WriteLine("\n=== 测试预占用冲突解决 ===");
        
        var cart = CreateTestCart();
        
        // 创建低优先级预占用
        var lowPriorityRequest = new ReservationRequest
        {
            ProductId = "P001",
            Quantity = 2,
            Priority = ReservationPriority.Low,
            RuleId = "RULE_LOW",
            Cart = cart,
            ExpirationTime = TimeSpan.FromMinutes(10)
        };

        var lowResult = await _inventoryManager.ReserveQuantityAsync(lowPriorityRequest);
        Console.WriteLine($"低优先级预占用: {lowResult.IsSuccessful} - {lowResult.ReservationId}");

        // 创建高优先级预占用（应该会抢占低优先级的）
        var highPriorityRequest = new ReservationRequest
        {
            ProductId = "P001",
            Quantity = 3, // 需要更多数量，会触发冲突解决
            Priority = ReservationPriority.High,
            RuleId = "RULE_HIGH",
            Cart = cart,
            ExpirationTime = TimeSpan.FromMinutes(10)
        };

        var highResult = await _inventoryManager.ReserveQuantityAsync(highPriorityRequest);
        Console.WriteLine($"高优先级预占用: {highResult.IsSuccessful} - {highResult.ReservationId}");
        
        if (highResult.ConflictingReservations?.Any() == true)
        {
            Console.WriteLine($"解决的冲突预占用: {string.Join(", ", highResult.ConflictingReservations)}");
        }

        // 查看当前活跃预占用
        var activeReservations = _inventoryManager.GetActiveReservations("P001");
        Console.WriteLine($"P001的活跃预占用数量: {activeReservations.Count}");
        foreach (var reservation in activeReservations)
        {
            Console.WriteLine($"  - {reservation.Id}: {reservation.Quantity}件, 优先级: {reservation.Priority}");
        }
    }

    /// <summary>
    /// 测试库存快照功能
    /// </summary>
    public async Task TestInventorySnapshot()
    {
        Console.WriteLine("\n=== 测试库存快照功能 ===");
        
        var cart = CreateTestCart();
        
        // 创建初始快照
        var snapshot1 = _inventoryManager.CreateSnapshot(cart);
        Console.WriteLine($"创建快照1: {snapshot1.Id}");
        Console.WriteLine($"快照商品数量: {snapshot1.CartItems.Count}");
        Console.WriteLine($"快照预占用数量: {snapshot1.ActiveReservations.Count}");

        // 进行一些预占用操作
        var request = new ReservationRequest
        {
            ProductId = "P001",
            Quantity = 1,
            Priority = ReservationPriority.Medium,
            RuleId = "RULE_SNAPSHOT",
            Cart = cart,
            ExpirationTime = TimeSpan.FromMinutes(5)
        };

        await _inventoryManager.ReserveQuantityAsync(request);

        // 修改购物车状态
        cart.Items.First(x => x.Product.Id == "P001").AvailableQuantity = 1;

        // 创建第二个快照
        var snapshot2 = _inventoryManager.CreateSnapshot(cart);
        Console.WriteLine($"创建快照2: {snapshot2.Id}");
        Console.WriteLine($"快照2预占用数量: {snapshot2.ActiveReservations.Count}");

        // 恢复到快照1
        var restored = _inventoryManager.RestoreSnapshot(snapshot1.Id, cart);
        Console.WriteLine($"恢复快照1: {restored}");
        
        var p001Item = cart.Items.First(x => x.Product.Id == "P001");
        Console.WriteLine($"恢复后P001可用数量: {p001Item.AvailableQuantity}");
    }

    /// <summary>
    /// 测试批量回滚功能
    /// </summary>
    public async Task TestBatchRollback()
    {
        Console.WriteLine("\n=== 测试批量回滚功能 ===");
        
        var cart = CreateTestCart();
        var reservationIds = new List<string>();

        // 创建多个预占用
        for (int i = 0; i < 3; i++)
        {
            var request = new ReservationRequest
            {
                ProductId = $"P00{i + 1}",
                Quantity = 1,
                Priority = ReservationPriority.Medium,
                RuleId = $"RULE_BATCH_{i}",
                Cart = cart,
                ExpirationTime = TimeSpan.FromMinutes(10)
            };

            var result = await _inventoryManager.ReserveQuantityAsync(request);
            if (result.IsSuccessful)
            {
                reservationIds.Add(result.ReservationId);
                Console.WriteLine($"创建预占用 {i + 1}: {result.ReservationId}");
            }
        }

        Console.WriteLine($"总共创建了 {reservationIds.Count} 个预占用");

        // 批量回滚
        var rollbackResult = await _inventoryManager.RollbackReservationsAsync(reservationIds);
        
        Console.WriteLine($"回滚结果: {rollbackResult.IsSuccessful}");
        Console.WriteLine($"成功回滚: {rollbackResult.SuccessfulRollbacks.Count}");
        Console.WriteLine($"失败回滚: {rollbackResult.FailedRollbacks.Count}");
        Console.WriteLine($"总处理数: {rollbackResult.TotalProcessed}");

        // 验证所有预占用都已释放
        var remainingReservations = _inventoryManager.GetActiveReservations();
        Console.WriteLine($"剩余活跃预占用: {remainingReservations.Count}");
    }

    /// <summary>
    /// 测试预占用过期清理
    /// </summary>
    public async Task TestReservationExpiration()
    {
        Console.WriteLine("\n=== 测试预占用过期清理 ===");
        
        var cart = CreateTestCart();
        
        // 创建一个很快过期的预占用
        var request = new ReservationRequest
        {
            ProductId = "P001",
            Quantity = 1,
            Priority = ReservationPriority.Medium,
            RuleId = "RULE_EXPIRE",
            Cart = cart,
            ExpirationTime = TimeSpan.FromSeconds(2) // 2秒后过期
        };

        var result = await _inventoryManager.ReserveQuantityAsync(request);
        Console.WriteLine($"创建短期预占用: {result.IsSuccessful} - {result.ReservationId}");

        // 等待过期
        Console.WriteLine("等待预占用过期...");
        await Task.Delay(3000);

        // 尝试确认已过期的预占用
        var confirmed = await _inventoryManager.ConfirmReservationAsync(result.ReservationId);
        Console.WriteLine($"确认过期预占用: {confirmed}");

        // 检查活跃预占用（应该为空或不包含过期的）
        var activeReservations = _inventoryManager.GetActiveReservations("P001");
        Console.WriteLine($"P001的活跃预占用数量: {activeReservations.Count}");
    }

    /// <summary>
    /// 运行所有测试
    /// </summary>
    public async Task RunAllTests()
    {
        Console.WriteLine("=== 库存管理器测试开始 ===\n");

        try
        {
            await TestBasicReservation();
            await TestReservationConflictResolution();
            await TestInventorySnapshot();
            await TestBatchRollback();
            await TestReservationExpiration();

            Console.WriteLine("\n=== 所有测试完成 ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"测试执行失败: {ex.Message}");
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        }
        finally
        {
            _inventoryManager.Dispose();
            _observabilityEngine.Dispose();
        }
    }

    /// <summary>
    /// 创建测试购物车
    /// </summary>
    private static ShoppingCart CreateTestCart()
    {
        var cart = new ShoppingCart
        {
            Id = "test-cart-inventory",
            MemberId = "M001",
            MemberLevel = "Gold",
            Items = new List<CartItem>
            {
                new CartItem
                {
                    Product = new Product { Id = "P001", Name = "商品1", CategoryId = "CAT001" },
                    Quantity = 5,
                    AvailableQuantity = 5,
                    UnitPrice = 50m
                },
                new CartItem
                {
                    Product = new Product { Id = "P002", Name = "商品2", CategoryId = "CAT002" },
                    Quantity = 3,
                    AvailableQuantity = 3,
                    UnitPrice = 30m
                },
                new CartItem
                {
                    Product = new Product { Id = "P003", Name = "商品3", CategoryId = "CAT001" },
                    Quantity = 2,
                    AvailableQuantity = 2,
                    UnitPrice = 80m
                }
            }
        };

        return cart;
    }
}

/// <summary>
/// 库存管理器测试程序入口
/// </summary>
public class InventoryTestProgram
{
    public static async Task Main(string[] args)
    {
        var tests = new InventoryManagerTests();
        await tests.RunAllTests();
        
        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
}
