using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.BuyFreeRules;

/// <summary>
/// 组合买免规则 [OK]
/// 同时购买A+B+C，免去其中价格最低的一件
/// 场景案例：购买A商品2件、B商品1件，免费送其中价格最低的1件商品。
/// 促销设置为不翻倍：A商品吊牌价、零售价为1000元，B商品吊牌价、零售价为99元；购买A商品2件、B商品1件时，应收金额为1000*2=2000元（免费送B商品）
/// 促销设置为翻2倍：A商品吊牌价、零售价为1000元，B商品吊牌价、零售价为99元，购买A商品6件、B商品3件时，应收金额为1000*6 + 99=6099元（免费送2件B商品）
/// 备注：组合买免策略需要考虑设置中的 客户利益最大化还是商户利益最大化来决定免费商品的选择策略
/// </summary>
public class CombinationBuyFreeRule : BaseBuyFreeRule
{
    public override string RuleType => "CombinationBuyFree";

    /// <summary>
    /// 组合购买条件列表
    /// </summary>
    public List<CombinationBuyCondition> CombinationConditions { get; set; } = new();

    /// <summary>
    /// 免费商品数量
    /// </summary>
    public int FreeQuantity { get; set; } = 1;

    /// <summary>
    /// 是否只从参与组合的商品中选择免费商品
    /// </summary>
    public bool FreeFromCombinationOnly { get; set; } = true;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!CombinationConditions.Any())
            return false;

        var allProductIds = CombinationConditions.SelectMany(c => c.ProductId).ToList();
        return ValidateBuyFreeProductsInCart(cart, allProductIds)
            && CombinationConditions.All(condition => CheckSingleCondition(cart, condition));
    }

    private bool CheckSingleCondition(ShoppingCart cart, CombinationBuyCondition condition)
    {
        var totalQuantity = GetConditionTotals(cart, condition.ProductId);

        return totalQuantity >= condition.RequiredQuantity;
    }

    private int GetConditionTotals(ShoppingCart cart, List<string> productIds)
    {
        return cart
            .Items.Where(x => productIds.Contains(x.Product.Id))
            .Sum(x => x.AvailableQuantity);
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = CombinationConditions
            .Where(c => c.RequiredQuantity > 0)
            .Select(c =>
            {
                var totalQuantity = c.ProductId.Sum(cart.GetAvailableProductQuantity);
                return IsRepeatable
                    ? totalQuantity / c.RequiredQuantity
                    : (totalQuantity >= c.RequiredQuantity ? 1 : 0);
            })
            .DefaultIfEmpty(1)
            .Min();

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);
        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var (discountAmount, consumedItems, giftItems) = ApplyCombinationBuyFree(
                cart,
                applicationCount
            );
            application.DiscountAmount = discountAmount;
            application.ConsumedItems = consumedItems;
            application.GiftItems = giftItems;
            application.IsSuccessful = discountAmount > 0;

            if (!application.IsSuccessful)
                application.ErrorMessage = "没有符合条件的商品可以应用组合买免促销";
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    private (
        decimal discount,
        List<ConsumedItem> consumed,
        List<GiftItem> gifts
    ) ApplyCombinationBuyFree(ShoppingCart cart, int applicationCount)
    {
        var totalDiscount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var freeItems = new List<GiftItem>();

        for (int app = 0; app < applicationCount; app++)
        {
            // 首先消耗组合条件商品
            if (!ConsumeCombinationConditionProducts(cart, consumedItems))
                break;

            // 修复：获取免费商品候选范围 - 应该从所有可用商品中选择，而不仅仅是组合商品
            var candidateProducts = GetFreeCandidateProducts(cart);
            if (!candidateProducts.Any())
                break;

            // 修复：按策略选择免费商品
            var freeProducts = SelectFreeProductsByStrategy(candidateProducts);
            if (!freeProducts.Any())
                break;

            // 应用免费效果
            var discount = ApplyFreeEffect(cart, freeProducts, freeItems);
            totalDiscount += discount;
        }

        cart.Items.RemoveAll(x => x.Quantity <= 0);
        return (totalDiscount, consumedItems, freeItems);
    }

    /// <summary>
    /// 修复：消耗组合条件商品（只消耗，不返回消耗的商品列表）
    /// </summary>
    private bool ConsumeCombinationConditionProducts(
        ShoppingCart cart,
        List<ConsumedItem> consumedItems
    )
    {
        foreach (var condition in CombinationConditions)
        {
            var remainingQuantity = condition.RequiredQuantity;
            var availableItems = cart
                .Items.Where(x =>
                    condition.ProductId.Contains(x.Product.Id) && x.AvailableQuantity > 0
                )
                .OrderBy(x => x.UnitPrice) // 优先消耗便宜的
                .ToList();

            foreach (var cartItem in availableItems)
            {
                if (remainingQuantity <= 0)
                    break;

                var consumeQuantity = Math.Min(cartItem.AvailableQuantity, remainingQuantity);

                // 记录消耗
                var existingConsumed = consumedItems.FirstOrDefault(x =>
                    x.ProductId == cartItem.Product.Id
                );
                if (existingConsumed != null)
                    existingConsumed.Quantity += consumeQuantity;
                else
                    consumedItems.Add(
                        new ConsumedItem
                        {
                            ProductId = cartItem.Product.Id,
                            ProductName = cartItem.Product.Name,
                            Quantity = consumeQuantity,
                            UnitPrice = cartItem.UnitPrice
                        }
                    );

                //cartItem.ConsumeQuantity(consumeQuantity);
                remainingQuantity -= consumeQuantity;
            }

            if (remainingQuantity > 0)
                return false; // 无法满足条件
        }

        return true;
    }

    /// <summary>
    /// 修复：获取免费商品候选范围
    /// </summary>
    private List<(string ProductId, int Quantity, decimal UnitPrice)> GetFreeCandidateProducts(
        ShoppingCart cart
    )
    {
        var candidates = new List<(string ProductId, int Quantity, decimal UnitPrice)>();

        if (FreeFromCombinationOnly)
        {
            // 只从组合商品中选择
            var combinationProductIds = CombinationConditions
                .SelectMany(c => c.ProductId)
                .Distinct()
                .ToList();
            foreach (
                var cartItem in cart.Items.Where(x =>
                    combinationProductIds.Contains(x.Product.Id) && x.AvailableQuantity > 0
                )
            )
            {
                candidates.Add(
                    (cartItem.Product.Id, cartItem.AvailableQuantity, cartItem.UnitPrice)
                );
            }
        }
        else
        {
            // 从所有商品中选择
            foreach (var cartItem in cart.Items.Where(x => x.AvailableQuantity > 0))
            {
                candidates.Add(
                    (cartItem.Product.Id, cartItem.AvailableQuantity, cartItem.UnitPrice)
                );
            }
        }

        return candidates;
    }

    /// <summary>
    /// 修复：按策略选择免费商品
    /// </summary>
    private List<(string ProductId, int Quantity, decimal UnitPrice)> SelectFreeProductsByStrategy(
        List<(string ProductId, int Quantity, decimal UnitPrice)> candidateProducts
    )
    {
        var freeProducts = new List<(string ProductId, int Quantity, decimal UnitPrice)>();
        var remainingFreeQuantity = FreeQuantity;

        // 修复：按策略正确排序
        var sortedProducts =
            FreeItemSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                ? candidateProducts.OrderByDescending(x => x.UnitPrice).ToList() // 客户利益：选择最贵的免费
                : candidateProducts.OrderBy(x => x.UnitPrice).ToList(); // 商家利益：选择最便宜的免费

        foreach (var product in sortedProducts)
        {
            if (remainingFreeQuantity <= 0)
                break;

            var freeQuantity = Math.Min(product.Quantity, remainingFreeQuantity);
            if (freeQuantity > 0)
            {
                freeProducts.Add((product.ProductId, freeQuantity, product.UnitPrice));
                remainingFreeQuantity -= freeQuantity;
            }
        }

        return freeProducts;
    }

    /// <summary>
    /// 应用免费效果
    /// </summary>
    private decimal ApplyFreeEffect(
        ShoppingCart cart,
        List<(string ProductId, int Quantity, decimal UnitPrice)> freeProducts,
        List<GiftItem> freeItems
    )
    {
        var totalDiscount = 0m;
        var strategyDescription =
            FreeItemSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                ? "客户利益最大化"
                : "商家利益最大化";

        foreach (var freeProduct in freeProducts)
        {
            var discount = freeProduct.Quantity * freeProduct.UnitPrice;
            totalDiscount += discount;

            // 从购物车中消耗免费商品
            var cartItems = cart
                .Items.Where(x => x.Product.Id == freeProduct.ProductId && x.AvailableQuantity > 0)
                .OrderBy(x => x.UnitPrice)
                .ToList();

            var remainingToConsume = freeProduct.Quantity;
            foreach (var cartItem in cartItems)
            {
                if (remainingToConsume <= 0)
                    break;

                var consumeQty = Math.Min(cartItem.AvailableQuantity, remainingToConsume);
                //cartItem.ConsumeQuantity(consumeQty);
                remainingToConsume -= consumeQty;
            }

            // 记录免费商品
            var existingFree = freeItems.FirstOrDefault(x => x.ProductId == freeProduct.ProductId);
            if (existingFree != null)
            {
                existingFree.Quantity += freeProduct.Quantity;
                existingFree.Value += discount;
            }
            else
            {
                var cartItem = cart.Items.FirstOrDefault(x =>
                    x.Product.Id == freeProduct.ProductId
                );
                freeItems.Add(
                    new GiftItem
                    {
                        ProductId = freeProduct.ProductId,
                        ProductName = cartItem?.Product.Name ?? freeProduct.ProductId,
                        Quantity = freeProduct.Quantity,
                        Value = discount,
                        Description =
                            $"组合买免：免费{freeProduct.Quantity}件，节省{discount:C}（{strategyDescription}）"
                    }
                );
            }

            // 应用促销到购物车
            var promotion = new AppliedPromotion
            {
                RuleId = Id,
                RuleName = Name,
                PromotionType = RuleType
            };

            var effectProducts = new List<(string ProductId, int Quantity, decimal UnitPrice)>
            {
                freeProduct
            };
            ApplyFreeEffectToCart(cart, effectProducts, promotion);
        }

        return totalDiscount;
    }
}

/// <summary>
/// 组合购买条件
/// </summary>
public class CombinationBuyCondition
{
    /// <summary>
    /// 商品ID列表（该条件范围内的商品ID，计算总和）
    /// </summary>
    public List<string> ProductId { get; set; } = [];

    /// <summary>
    /// 所需数量（该条件范围内商品的总数量）
    /// </summary>
    public int RequiredQuantity { get; set; }
}
