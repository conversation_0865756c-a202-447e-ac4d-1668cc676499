<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整工作流程测试</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="css/styles.css">
    
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #1890ff;
        }
        .test-description {
            margin-bottom: 15px;
            color: #666;
        }
        .test-buttons {
            margin: 10px 0;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            background: #fff;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>促销配置系统 - 完整工作流程测试</h1>
            
            <!-- 测试1: API连通性 -->
            <div class="test-section">
                <div class="test-title">测试1: API连通性检查</div>
                <div class="test-description">验证所有相关API是否正常工作</div>
                <div class="test-buttons">
                    <el-button @click="testAPIs" type="primary">测试API</el-button>
                </div>
                <div class="test-result" v-if="apiTestResult">
                    <pre>{{ apiTestResult }}</pre>
                </div>
            </div>
            
            <!-- 测试2: 元数据加载 -->
            <div class="test-section">
                <div class="test-title">测试2: 元数据加载测试</div>
                <div class="test-description">验证促销类型元数据是否能正确加载</div>
                <div class="test-buttons">
                    <el-button @click="testMetadata" type="primary">加载元数据</el-button>
                </div>
                <div class="test-result" v-if="metadataResult">
                    <pre>{{ metadataResult }}</pre>
                </div>
            </div>
            
            <!-- 测试3: 主页面集成 -->
            <div class="test-section">
                <div class="test-title">测试3: 主页面完整流程</div>
                <div class="test-description">在主页面中选择分类和类型，验证是否生成条件配置表单</div>
                <div class="test-buttons">
                    <el-button @click="openMainPage" type="primary">打开主页面</el-button>
                    <el-button @click="simulateUserFlow" type="success">模拟用户操作</el-button>
                </div>
                <div class="test-result" v-if="flowResult">
                    <pre>{{ flowResult }}</pre>
                </div>
            </div>
            
            <!-- 测试4: 组合条件渲染 -->
            <div class="test-section">
                <div class="test-title">测试4: 复杂组合条件渲染</div>
                <div class="test-description">验证array-complex类型字段的渲染和交互</div>
                <div class="test-buttons">
                    <el-button @click="testComplexConditions" type="primary">测试复杂条件</el-button>
                    <el-button @click="openConditionTest" type="success">打开条件测试页</el-parameter>
                </div>
                <div class="test-result" v-if="conditionResult">
                    <pre>{{ conditionResult }}</pre>
                </div>
            </div>
            
            <!-- 实时结果展示 -->
            <div class="test-section">
                <div class="test-title">实时结果监控</div>
                <div class="test-description">显示测试过程中的实时状态和错误信息</div>
                <el-card>
                    <template #header>
                        <span>控制台日志</span>
                        <el-button style="float: right; padding: 3px 0" type="text" @click="clearLogs">清空</el-button>
                    </template>
                    <div class="log-container" style="height: 200px; overflow-y: auto; background: #000; color: #0f0; padding: 10px; font-family: monospace;">
                        <div v-for="log in logs" :key="log.id" :class="'log-' + log.level">
                            [{{ log.time }}] {{ log.level.toUpperCase() }}: {{ log.message }}
                        </div>
                    </div>
                </el-card>
            </div>
        </div>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    
    <!-- 图标 -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.min.js"></script>

    <script>
        const { createApp, ref, reactive, onMounted } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            setup() {
                const apiTestResult = ref('');
                const metadataResult = ref('');
                const flowResult = ref('');
                const conditionResult = ref('');
                const logs = ref([]);
                let logId = 0;

                const addLog = (level, message) => {
                    logs.value.push({
                        id: logId++,
                        level,
                        message,
                        time: new Date().toLocaleTimeString()
                    });
                    // 保持最新的50条日志
                    if (logs.value.length > 50) {
                        logs.value.shift();
                    }
                };

                const clearLogs = () => {
                    logs.value = [];
                };

                const testAPIs = async () => {
                    addLog('info', '开始API连通性测试...');
                    
                    try {
                        // 测试基础API
                        const response1 = await fetch('/api/promotion/types');
                        const data1 = await response1.json();
                        addLog('success', `基础API测试成功，返回${Object.keys(data1).length}个分类`);
                          // 测试反射API
                        const response2 = await fetch('/api/promotion/metadata/types');
                        const data2 = await response2.json();
                        addLog('success', `反射API测试成功，返回${Object.keys(data2).length}个类型`);
                        
                        // 测试详细信息API
                        const response3 = await fetch('/api/promotion/metadata/types/BuyXGetYDiscount');
                        const data3 = await response3.json();
                        addLog('success', `详细信息API测试成功，返回${data3.fields?.length || 0}个字段`);
                        
                        apiTestResult.value = JSON.stringify({
                            basicAPI: data1,
                            reflectionAPI: data2,
                            detailAPI: data3
                        }, null, 2);
                        
                        ElMessage.success('所有API测试通过！');
                        
                    } catch (error) {
                        addLog('error', `API测试失败: ${error.message}`);
                        apiTestResult.value = `错误: ${error.message}`;
                        ElMessage.error('API测试失败！');
                    }
                };

                const testMetadata = async () => {
                    addLog('info', '开始元数据加载测试...');
                    
                    try {                        // 模拟元数据服务加载
                        const response = await fetch('/api/promotion/metadata/enhanced-types');
                        const data = await response.json();
                        
                        addLog('success', `元数据加载成功，包含以下分类:`);
                        Object.keys(data).forEach(category => {
                            const categoryData = data[category];
                            addLog('info', `- ${category}: ${categoryData.name} (${Object.keys(categoryData.types || {}).length}个类型)`);
                        });
                        
                        metadataResult.value = JSON.stringify(data, null, 2);
                        ElMessage.success('元数据加载成功！');
                        
                    } catch (error) {
                        addLog('error', `元数据加载失败: ${error.message}`);
                        metadataResult.value = `错误: ${error.message}`;
                        ElMessage.error('元数据加载失败！');
                    }
                };

                const openMainPage = () => {
                    addLog('info', '打开主页面...');
                    window.open('/ConfigUI/index_new.html', '_blank');
                };

                const simulateUserFlow = async () => {
                    addLog('info', '开始模拟用户操作流程...');
                    flowResult.value = '模拟流程:\n1. 选择促销分类\n2. 选择促销类型\n3. 检查是否生成条件配置表单\n4. 验证array-complex字段渲染';
                    
                    // 这里可以添加自动化测试逻辑
                    addLog('info', '请手动在主页面中执行以下操作:');
                    addLog('info', '1. 选择任意促销分类');
                    addLog('info', '2. 选择任意促销类型');
                    addLog('info', '3. 观察是否切换到"条件配置"步骤');
                    addLog('info', '4. 检查复杂字段是否正确渲染');
                };

                const testComplexConditions = async () => {
                    addLog('info', '测试复杂条件渲染...');
                    
                    try {                        // 获取包含复杂条件的类型详情
                        const response = await fetch('/api/promotion/metadata/types/BuyXGetYGift');
                        const data = await response.json();
                        
                        const complexFields = data.fields?.filter(field => 
                            field.type === 'array-complex' || 
                            field.fieldType?.includes('Complex') ||
                            field.fieldType?.includes('[]')
                        ) || [];
                        
                        addLog('success', `找到${complexFields.length}个复杂字段:`);
                        complexFields.forEach(field => {
                            addLog('info', `- ${field.name}: ${field.type} (${field.description || '无描述'})`);
                        });
                        
                        conditionResult.value = JSON.stringify(complexFields, null, 2);
                        ElMessage.success('复杂条件分析完成！');
                        
                    } catch (error) {
                        addLog('error', `复杂条件测试失败: ${error.message}`);
                        conditionResult.value = `错误: ${error.message}`;
                        ElMessage.error('复杂条件测试失败！');
                    }
                };

                const openConditionTest = () => {
                    addLog('info', '打开条件测试页...');
                    window.open('/ConfigUI/test-combination-conditions.html', '_blank');
                };

                onMounted(() => {
                    addLog('info', '测试页面已加载，可以开始测试');
                });

                return {
                    apiTestResult,
                    metadataResult,
                    flowResult,
                    conditionResult,
                    logs,
                    clearLogs,
                    testAPIs,
                    testMetadata,
                    openMainPage,
                    simulateUserFlow,
                    testComplexConditions,
                    openConditionTest
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
