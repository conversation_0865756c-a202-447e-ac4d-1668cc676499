# 商品占用和条件筛选架构设计方案

## 🎯 核心问题解决

### 1. 商品占用冲突
- **问题**：不同促销规则需要"占用"特定商品，被占用的商品不能重复参与其他促销
- **解决方案**：`ProductOccupationEngine` + 事务性会话管理

### 2. 条件与执行分离
- **问题**：某些促销（如买2送1）需要3件商品才能执行，不能在只有2件时就将其中1件价格变为0
- **解决方案**：`ConditionExecutionSeparator` + 两阶段提交模式

### 3. 排他性管理
- **问题**：需要考虑促销规则的排他设置，确保商品不会被多个冲突的促销同时应用
- **解决方案**：`ExclusivityManager` + 智能冲突检测与优化

## 🏗️ 整体架构设计

### 核心组件架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    促销引擎架构                              │
├─────────────────────────────────────────────────────────────┤
│  Controller Layer                                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  ProductOccupationTestController                        │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Business Logic Layer                                       │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ ExclusivityMgr  │ │ ConditionExecSep│ │ ProductOccupEng │ │
│  │                 │ │                 │ │                 │ │
│  │ • 冲突分析      │ │ • 条件验证      │ │ • 会话管理      │ │
│  │ • 最优选择      │ │ • 执行预留      │ │ • 状态追踪      │ │
│  │ • 兼容性矩阵    │ │ • 效果应用      │ │ • 事务控制      │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Data Layer                                                 │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ OccupationState │ │ ReservationData │ │ ConflictInfo    │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 设计原则

1. **混合式商品表示**：保持"A商品5件"的业务语义，同时支持精确的占用管理
2. **三阶段处理**：条件验证 → 执行预留 → 效果应用
3. **事务性操作**：支持提交和回滚，确保数据一致性
4. **层级排他**：支持None/Partial/Exclusive三级排他性
5. **智能优化**：根据不同策略选择最优促销组合

## 🔧 商品占用机制实现

### 1. ProductOccupationEngine

```csharp
// 核心功能
public sealed class ProductOccupationEngine : IDisposable
{
    // 创建事务性会话
    Task<IOccupationSession> CreateSessionAsync(ProcessedCart cart);
    
    // 获取占用状态
    ProductOccupationState GetOccupationState(string productId);
    
    // 清理会话
    void CleanupSession(string sessionId);
}
```

**特性**：
- ✅ 线程安全的并发操作
- ✅ 自动会话清理机制
- ✅ 状态持久化支持
- ✅ 内存优化管理

### 2. IOccupationSession

```csharp
// 会话接口
public interface IOccupationSession : IDisposable
{
    // 条件预留
    Task<ReservationResult> TryReserveForConditionAsync(...);
    
    // 执行预留
    Task<ReservationResult> TryReserveForExecutionAsync(...);
    
    // 释放预留
    Task ReleaseReservationsAsync(string ruleId);
    
    // 提交/回滚
    Task<CommitResult> CommitAsync();
    Task RollbackAsync();
}
```

**优势**：
- 🎯 事务性操作保证
- 🎯 自动资源管理
- 🎯 冲突检测机制
- 🎯 状态可观测性

## 🔄 条件与执行分离设计

### 1. ConditionExecutionSeparator

```csharp
// 分离器核心方法
public async Task<SeparatedPromotionResult> ApplyPromotionWithSeparationAsync(
    ProcessedCart cart,
    PromotionRuleBase rule)
{
    // 阶段1：条件验证和预留
    var conditionResult = await ValidateAndReserveConditionsAsync(...);
    
    // 阶段2：执行验证和预留
    var executionResult = await ValidateAndReserveExecutionAsync(...);
    
    // 阶段3：应用促销效果
    var applicationResult = await ApplyPromotionEffectsAsync(...);
    
    // 阶段4：提交会话
    var commitResult = await session.CommitAsync();
}
```

### 2. 处理流程

```
买2送1促销示例：
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ 条件验证    │ -> │ 执行验证    │ -> │ 效果应用    │ -> │ 提交会话    │
│ 预留2件买   │    │ 预留1件送   │    │ 设置0价格   │    │ 确认占用    │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

**核心优势**：
- ✨ 清晰的阶段分离
- ✨ 完整的回滚机制
- ✨ 精确的错误定位
- ✨ 可观测的执行过程

## ⚖️ 排他性管理策略

### 1. ExclusivityManager

```csharp
// 核心功能
public sealed class ExclusivityManager
{
    // 分析排他性冲突
    Task<ExclusivityAnalysisResult> AnalyzeExclusivityAsync(...);
    
    // 检查规则兼容性
    bool CanApplyTogether(PromotionRuleBase rule1, PromotionRuleBase rule2);
    
    // 选择最优组合
    Task<OptimalSelectionResult> SelectOptimalCombinationAsync(...);
}
```

### 2. 排他性级别

| 级别 | 描述 | 行为 |
|------|------|------|
| `None` | 无限制 | 可与任何促销叠加 |
| `Partial` | 部分互斥 | 只能与同级或更低级叠加 |
| `Exclusive` | 完全互斥 | 不能与任何其他促销叠加 |

### 3. 优化策略

```csharp
public enum OptimizationStrategy
{
    MaxCustomerBenefit,  // 客户利益最大化
    MaxMerchantBenefit,  // 商家利益最大化
    MaxRuleCount,        // 规则数量最大化
    Balanced            // 平衡策略
}
```

## 📊 购物车结构分析

### 混合式 vs 单件列表对比

| 维度 | 混合式（推荐） | 单件列表 |
|------|----------------|----------|
| **内存使用** | ✅ 优秀 | ❌ 大量开销 |
| **业务语义** | ✅ 清晰 | ❌ 复杂 |
| **计算性能** | ✅ 高效 | ❌ 低效 |
| **占用管理** | ✅ 精确 | ✅ 简单 |
| **状态追踪** | ⚠️ 复杂 | ✅ 直观 |
| **存储优化** | ✅ 紧凑 | ❌ 冗余 |

### 推荐方案：混合式商品表示

```csharp
// 购物车层面：保持聚合
CartItem: { ProductId: "A", Quantity: 5, UnitPrice: 10.00 }

// 占用层面：虚拟展开
VirtualInstances: [
    { ProductId: "A", InstanceId: "A1", State: Available },
    { ProductId: "A", InstanceId: "A2", State: ConditionUsed },
    { ProductId: "A", InstanceId: "A3", State: ExecutionUsed },
    { ProductId: "A", InstanceId: "A4", State: Available },
    { ProductId: "A", InstanceId: "A5", State: Available }
]
```

## 🚀 使用示例

### 1. 基本占用测试

```csharp
// 创建会话
using var session = await occupationEngine.CreateSessionAsync(cart);

// 条件预留
var conditionResult = await session.TryReserveForConditionAsync(
    "RULE_001", "买2送1", ["A"], 2);

// 执行预留
var executionResult = await session.TryReserveForExecutionAsync(
    "RULE_001", "买2送1", ["A"], 1);

// 提交会话
await session.CommitAsync();
```

### 2. 完整促销流程

```csharp
// 排他性分析
var analysis = await exclusivityManager.AnalyzeExclusivityAsync(rules, cart);

// 选择最优组合
var selection = await exclusivityManager.SelectOptimalCombinationAsync(
    rules, cart, OptimizationStrategy.MaxCustomerBenefit);

// 应用促销
foreach (var rule in selection.SelectedRules)
{
    var result = await separator.ApplyPromotionWithSeparationAsync(cart, rule);
}
```

## 📈 性能优化

### 1. 内存管理
- 使用 `ObjectPool<T>` 复用对象
- 及时释放会话资源
- 优化状态存储结构

### 2. 并发控制
- `ConcurrentDictionary` 线程安全
- `SemaphoreSlim` 控制并发
- 原子操作避免锁竞争

### 3. 计算优化
- 延迟计算策略
- 缓存中间结果
- 批量操作优化

## 🔍 可观测性

### 1. 状态报告
```csharp
var report = session.GetStateReport();
// 包含：会话ID、商品状态、预留详情、冲突信息
```

### 2. 冲突分析
```csharp
var conflicts = analysisResult.Conflicts;
// 包含：冲突类型、涉及规则、解决建议
```

### 3. 性能指标
- 会话创建时间
- 预留操作耗时
- 冲突检测性能
- 内存使用情况

## 🎯 总结

这个架构设计方案通过以下核心创新解决了商品占用和条件筛选的复杂问题：

1. **混合式商品表示**：在保持业务语义清晰的同时，实现了精确的占用管理
2. **三阶段处理模式**：条件验证、执行预留、效果应用的清晰分离
3. **事务性会话管理**：确保操作的原子性和一致性
4. **智能排他性管理**：自动检测冲突并推荐最优组合
5. **完整的可观测性**：提供详细的状态报告和性能指标

该方案既解决了技术难题，又保持了良好的性能和可维护性，为复杂的促销业务场景提供了强有力的技术支撑。
