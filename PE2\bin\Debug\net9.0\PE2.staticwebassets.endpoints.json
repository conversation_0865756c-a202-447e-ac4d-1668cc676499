{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "simple.a18q5nlcb0.html", "AssetFile": "simple.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000131943528"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7578"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"r7SMTb2SoN/s2+NsxbMPbF8f1JC/pYaOMF9gsXk0c28=\""}, {"Name": "ETag", "Value": "W/\"0iOWCDzoJynmCoepavmurixyw3WoyPu95/YsxLQ4BwI=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:43:57 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a18q5nlcb0"}, {"Name": "integrity", "Value": "sha256-0iOWCDzoJynmCoepavmurixyw3WoyPu95/YsxLQ4BwI="}, {"Name": "label", "Value": "simple.html"}]}, {"Route": "simple.a18q5nlcb0.html", "AssetFile": "simple.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "39459"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"0iOWCDzoJynmCoepavmurixyw3WoyPu95/YsxLQ4BwI=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:43:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a18q5nlcb0"}, {"Name": "integrity", "Value": "sha256-0iOWCDzoJynmCoepavmurixyw3WoyPu95/YsxLQ4BwI="}, {"Name": "label", "Value": "simple.html"}]}, {"Route": "simple.a18q5nlcb0.html.gz", "AssetFile": "simple.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7578"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"r7SMTb2SoN/s2+NsxbMPbF8f1JC/pYaOMF9gsXk0c28=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:43:57 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a18q5nlcb0"}, {"Name": "integrity", "Value": "sha256-r7SMTb2SoN/s2+NsxbMPbF8f1JC/pYaOMF9gsXk0c28="}, {"Name": "label", "Value": "simple.html.gz"}]}, {"Route": "simple.html", "AssetFile": "simple.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000131943528"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7578"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"r7SMTb2SoN/s2+NsxbMPbF8f1JC/pYaOMF9gsXk0c28=\""}, {"Name": "ETag", "Value": "W/\"0iOWCDzoJynmCoepavmurixyw3WoyPu95/YsxLQ4BwI=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:43:57 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0iOWCDzoJynmCoepavmurixyw3WoyPu95/YsxLQ4BwI="}]}, {"Route": "simple.html", "AssetFile": "simple.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "39459"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"0iOWCDzoJynmCoepavmurixyw3WoyPu95/YsxLQ4BwI=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:43:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0iOWCDzoJynmCoepavmurixyw3WoyPu95/YsxLQ4BwI="}]}, {"Route": "simple.html.gz", "AssetFile": "simple.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7578"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"r7SMTb2SoN/s2+NsxbMPbF8f1JC/pYaOMF9gsXk0c28=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:43:57 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r7SMTb2SoN/s2+NsxbMPbF8f1JC/pYaOMF9gsXk0c28="}]}, {"Route": "standalone.5ipweew5fc.html", "AssetFile": "standalone.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:09:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "standalone.html"}]}, {"Route": "standalone.5ipweew5fc.html", "AssetFile": "standalone.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:09:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "standalone.html"}]}, {"Route": "standalone.5ipweew5fc.html.gz", "AssetFile": "standalone.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:09:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "standalone.html.gz"}]}, {"Route": "standalone.html", "AssetFile": "standalone.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:09:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "standalone.html", "AssetFile": "standalone.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:09:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "standalone.html.gz", "AssetFile": "standalone.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:09:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}]}