using Microsoft.AspNetCore.Mvc;
using PE2.PromotionEngine.Rules;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Reflection;
using System.Collections;
using System.Text.Json.Serialization;

namespace PE2.Controllers;

/// <summary>
/// 促销规则元数据控制器 - 通过反射动态生成配置信息
/// </summary>
[ApiController]
[Route("api/promotion/metadata")]
public class PromotionMetadataController : ControllerBase
{
    private readonly ILogger<PromotionMetadataController> _logger;

    public PromotionMetadataController(ILogger<PromotionMetadataController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 获取所有促销类型的配置元数据
    /// </summary>
    [HttpGet("types")]
    public IActionResult GetPromotionTypes()
    {
        try
        {
            var metadata = GeneratePromotionTypesMetadata();
            return Ok(metadata);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取促销类型元数据失败");
            return StatusCode(500, "获取元数据失败");
        }
    }

    /// <summary>
    /// 获取指定促销类型的详细配置信息
    /// </summary>
    [HttpGet("types/{ruleType}")]
    public IActionResult GetPromotionTypeDetail(string ruleType)
    {
        try
        {
            var detail = GenerateRuleTypeDetail(ruleType);
            if (detail == null)
                return NotFound($"未找到促销类型: {ruleType}");

            return Ok(detail);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取促销类型 {RuleType} 详细信息失败", ruleType);
            return StatusCode(500, "获取详细信息失败");
        }
    }    /// <summary>
    /// 通过反射生成促销类型元数据
    /// </summary>
    private object GeneratePromotionTypesMetadata()
    {
        var assembly = Assembly.GetExecutingAssembly();
        var ruleTypes = assembly.GetTypes()
            .Where(t => t.IsSubclassOf(typeof(PromotionRuleBase)) && !t.IsAbstract)
            .ToList();

        _logger.LogInformation($"找到 {ruleTypes.Count} 个促销规则类型:");
        foreach (var type in ruleTypes)
        {
            _logger.LogInformation($"- {type.Name} ({type.FullName})");
        }

        var categories = new Dictionary<string, Dictionary<string, object>>();

        foreach (var ruleType in ruleTypes)
        {
            var categoryInfo = GetCategoryInfo(ruleType);
            var ruleInfo = GetRuleInfo(ruleType);

            _logger.LogInformation($"处理规则类型: {ruleType.Name} -> 分类: {categoryInfo.Key}, 类型: {ruleInfo.TypeKey}");

            if (!categories.ContainsKey(categoryInfo.Key))
            {
                categories[categoryInfo.Key] = new Dictionary<string, object>
                {
                    ["name"] = categoryInfo.Name,
                    ["icon"] = categoryInfo.Icon,
                    ["description"] = categoryInfo.Description,
                    ["types"] = new Dictionary<string, object>()
                };
            }

            var typesDict = categories[categoryInfo.Key]["types"] as Dictionary<string, object>;
            if (typesDict != null)
            {
                typesDict[ruleInfo.TypeKey] = new
                {
                    name = ruleInfo.Name,
                    description = ruleInfo.Description,
                    ruleType = ruleType.Name,
                    fields = GenerateFieldMetadata(ruleType)
                };
            }
        }

        _logger.LogInformation($"生成了 {categories.Count} 个分类");
        foreach (var category in categories)
        {
            var typesDict = category.Value["types"] as Dictionary<string, object>;
            _logger.LogInformation($"- {category.Key}: {typesDict?.Count ?? 0} 个类型");
        }

        return categories;
    }

    /// <summary>
    /// 生成指定规则类型的详细配置信息
    /// </summary>
    private object? GenerateRuleTypeDetail(string ruleType)
    {
        var assembly = Assembly.GetExecutingAssembly();
        var type = assembly.GetTypes()
            .FirstOrDefault(t => t.Name == ruleType && t.IsSubclassOf(typeof(PromotionRuleBase)));

        if (type == null)
            return null;

        return new
        {
            ruleType = type.Name,
            name = GetDisplayName(type),
            description = GetDescription(type),
            fields = GenerateFieldMetadata(type),
            category = GetCategoryInfo(type),
            examples = GenerateExamples(type)
        };
    }

    /// <summary>
    /// 生成字段元数据
    /// </summary>
    private List<object> GenerateFieldMetadata(Type ruleType)
    {
        var fields = new List<object>();
        var properties = ruleType.GetProperties(BindingFlags.Public | BindingFlags.Instance);

        // 首先添加基类字段
        fields.AddRange(GenerateBaseFields());

        // 然后添加具体规则类型的字段
        foreach (var prop in properties)
        {
            // 跳过基类属性和特殊属性
            if (typeof(PromotionRuleBase).GetProperty(prop.Name) != null ||
                prop.Name == "RuleType")
                continue;

            var fieldMetadata = GenerateFieldMetadata(prop);
            if (fieldMetadata != null)
                fields.Add(fieldMetadata);
        }

        return fields;
    }

    /// <summary>
    /// 生成基础字段元数据
    /// </summary>
    private List<object> GenerateBaseFields()
    {
        return new List<object>
        {
            new { name = "id", label = "规则ID", type = "input", required = true, group = "basic" },
            new { name = "name", label = "规则名称", type = "input", required = true, group = "basic" },
            new { name = "description", label = "规则描述", type = "textarea", group = "basic" },
            new { name = "priority", label = "优先级", type = "number", @default = 0, group = "basic" },
            new { name = "isEnabled", label = "是否启用", type = "switch", @default = true, group = "basic" },
            new { name = "startTime", label = "生效时间", type = "datetime", group = "time" },
            new { name = "endTime", label = "失效时间", type = "datetime", group = "time" },
            new { name = "isRepeatable", label = "是否可重复", type = "switch", @default = true, group = "advanced" },
            new { name = "maxApplications", label = "最大应用次数", type = "number", @default = 0, group = "advanced" },
            new { name = "canStackWithOthers", label = "是否可与其他促销叠加", type = "switch", @default = true, group = "advanced" },
            new {
                name = "productExclusivity",
                label = "商品互斥级别",
                type = "select",
                @default = "None",
                group = "advanced",
                options = new[]
                {
                    new { value = "None", label = "无互斥" },
                    new { value = "SameType", label = "同类型互斥" },
                    new { value = "All", label = "全部互斥" }
                }
            },
            new { name = "applicableCustomerTypes", label = "适用客户类型", type = "array-simple", group = "advanced" },
            new { name = "exclusiveRuleIds", label = "互斥规则ID", type = "array-simple", group = "advanced" }
        };
    }    /// <summary>
    /// 生成单个字段的元数据
    /// </summary>
    private object? GenerateFieldMetadata(PropertyInfo prop)
    {
        var fieldTypeInfo = GetFieldType(prop.PropertyType, prop.Name);
        var label = GetDisplayName(prop) ?? FormatPropertyName(prop.Name);
        var group = GetFieldGroup(prop);

        // 如果是复杂类型，则使用增强的元数据结构
        if (fieldTypeInfo is IDictionary<string, object> complexType && complexType.ContainsKey("metadata"))
        {
            return new
            {
                name = ToCamelCase(prop.Name),
                label = label,
                type = complexType["type"],
                required = IsRequired(prop),
                group = group,
                @default = GetDefaultValue(prop),
                options = GetSelectOptions(prop),
                validation = GetValidationRules(prop),
                metadata = complexType["metadata"],
                isNullable = complexType.ContainsKey("isNullable") ? complexType["isNullable"] : false,
                elementType = complexType.ContainsKey("elementType") ? complexType["elementType"] : null,
                typeName = complexType.ContainsKey("typeName") ? complexType["typeName"] : null
            };
        }
        else
        {
            // 简单类型使用原有结构，但增强商品选择器识别
            var simpleType = fieldTypeInfo is string str ? str : "input";
            
            // 检查是否为商品ID相关字段
            if (IsProductIdField(prop.Name, prop.PropertyType))
            {
                simpleType = IsCollectionType(prop.PropertyType) ? "product-selector-multiple" : "product-selector-single";
            }
            
            return new
            {
                name = ToCamelCase(prop.Name),
                label = label,
                type = simpleType,
                required = IsRequired(prop),
                group = group,
                @default = GetDefaultValue(prop),
                options = GetSelectOptions(prop),
                validation = GetValidationRules(prop)
            };
        }
    }    /// <summary>
    /// 获取字段类型 - 增强版，支持复杂类型深度解析
    /// </summary>
    private object GetFieldType(Type propType, string propName = "")
    {
        // 处理可空类型
        var isNullable = false;
        if (propType.IsGenericType && propType.GetGenericTypeDefinition() == typeof(Nullable<>))
        {
            isNullable = true;
            propType = propType.GetGenericArguments()[0];
        }

        // 处理集合类型
        if (IsCollectionType(propType))
        {
            return GenerateCollectionTypeMetadata(propType, isNullable, propName);
        }

        // 处理复杂对象类型
        if (IsComplexType(propType))
        {
            return GenerateComplexTypeMetadata(propType, isNullable);
        }        

        // 处理基础类型 - 检查是否为商品ID字段
        var simpleType = GetSimpleFieldType(propType);
        if (IsProductIdField(propName, propType))
        {
            simpleType = IsCollectionType(propType) ? "product-selector-multiple" : "product-selector-single";
        }
        
        return new Dictionary<string, object>
        {
            ["type"] = simpleType,
            ["isNullable"] = isNullable
        };
    }    /// <summary>
    /// 生成集合类型的元数据
    /// </summary>
    private object GenerateCollectionTypeMetadata(Type collectionType, bool isNullable, string propName = "")
    {
        var elementType = GetCollectionElementType(collectionType);
          if (elementType == null)
        {
            return new Dictionary<string, object>
            {
                ["type"] = "array-simple",
                ["isNullable"] = isNullable,
                ["elementType"] = "string"
            };
        }        

        // 检查是否为商品ID集合
        if (IsProductIdField(propName, collectionType))
        {
            return new Dictionary<string, object>
            {
                ["type"] = "product-selector-multiple",
                ["isNullable"] = isNullable,
                ["elementType"] = GetSimpleFieldType(elementType)
            };
        }

        // 如果集合元素是复杂类型
        if (IsComplexType(elementType))
        {
            return new Dictionary<string, object>
            {
                ["type"] = "array-complex",
                ["isNullable"] = isNullable,
                ["elementType"] = elementType.Name,
                ["metadata"] = new Dictionary<string, object>
                {
                    ["elementSchema"] = GenerateTypeSchema(elementType),
                    ["defaultItem"] = GenerateDefaultItem(elementType) ?? new object(),
                    ["operations"] = new[] { "add", "remove", "edit", "reorder" }
                }
            };
        }
        
        // 如果集合元素是基础类型
        var elementSimpleType = GetSimpleFieldType(elementType);
        var result = new Dictionary<string, object>
        {
            ["type"] = "array-simple",
            ["isNullable"] = isNullable,
            ["elementType"] = elementSimpleType
        };
        
        if (elementType.IsEnum)
        {
            result["metadata"] = new Dictionary<string, object>
            {
                ["options"] = GetEnumOptions(elementType)
            };
        }
        
        return result;
    }

    /// <summary>
    /// 生成复杂类型的元数据
    /// </summary>
    private object GenerateComplexTypeMetadata(Type complexType, bool isNullable)
    {        return new Dictionary<string, object>
        {
            ["type"] = "object-complex",
            ["isNullable"] = isNullable,
            ["typeName"] = complexType.Name,
            ["metadata"] = new Dictionary<string, object>
            {
                ["schema"] = GenerateTypeSchema(complexType),
                ["defaultValue"] = GenerateDefaultItem(complexType) ?? new object(),
                ["formMode"] = GetFormMode(complexType)
            }
        };
    }    /// <summary>
    /// 生成类型结构定义
    /// </summary>
    private object GenerateTypeSchema(Type type)
    {
        var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance)
            .Where(p => !IsIgnoredProperty(p))
            .ToArray();

        var fields = new List<object>();
        
        foreach (var prop in properties)
        {
            var fieldType = GetFieldType(prop.PropertyType, prop.Name);
            
            fields.Add(new
            {
                name = ToCamelCase(prop.Name),
                label = GetDisplayName(prop) ?? FormatPropertyName(prop.Name),
                type = fieldType,
                required = IsRequired(prop),
                group = GetFieldGroup(prop),
                description = GetDescription(prop),
                validation = GetValidationRules(prop),
                @default = GetDefaultValue(prop)
            });
        }

        return new
        {
            typeName = type.Name,
            displayName = GetDisplayName(type) ?? FormatTypeName(type.Name),
            description = GetDescription(type),
            fields = fields,
            category = GetTypeCategory(type)
        };
    }

    /// <summary>
    /// 生成默认项
    /// </summary>
    private object? GenerateDefaultItem(Type type)
    {
        try
        {
            var instance = Activator.CreateInstance(type);
            return ConvertToJsonObject(instance);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 获取集合元素类型
    /// </summary>
    private Type? GetCollectionElementType(Type collectionType)
    {
        if (collectionType.IsArray)
            return collectionType.GetElementType();

        if (collectionType.IsGenericType)
        {
            var genericArgs = collectionType.GetGenericArguments();
            if (genericArgs.Length > 0)
                return genericArgs[0];
        }

        // 查找IEnumerable<T>接口
        var enumerableInterface = collectionType.GetInterfaces()
            .FirstOrDefault(i => i.IsGenericType && i.GetGenericTypeDefinition() == typeof(IEnumerable<>));
        
        return enumerableInterface?.GetGenericArguments()[0];
    }    /// <summary>
    /// 判断是否为商品ID相关字段
    /// </summary>
    private bool IsProductIdField(string fieldName, Type fieldType)
    {
        if (string.IsNullOrEmpty(fieldName))
            return false;

        var lowerFieldName = fieldName.ToLowerInvariant();
        
        // 检查字段名是否包含商品ID相关关键词
        var productIdKeywords = new[]
        {
            "productid", "productids", "applicableproductids",
            "targetproductid", "targetproductids",
            "giftproductid", "giftproductids",
            "itemid", "itemids", "skuid", "skuids"
        };

        return productIdKeywords.Any(keyword => lowerFieldName.Contains(keyword));
    }

    /// <summary>
    /// 判断是否为集合类型
    /// </summary>
    private bool IsCollectionType(Type type)
    {
        if (type == typeof(string))
            return false;

        return type.IsArray || 
               (type.IsGenericType && (
                   typeof(IList<>).IsAssignableFrom(type.GetGenericTypeDefinition()) ||
                   typeof(ICollection<>).IsAssignableFrom(type.GetGenericTypeDefinition()) ||
                   typeof(IEnumerable<>).IsAssignableFrom(type.GetGenericTypeDefinition())
               )) ||
               typeof(IEnumerable).IsAssignableFrom(type);
    }

    /// <summary>
    /// 判断是否为复杂类型
    /// </summary>
    private bool IsComplexType(Type type)
    {
        if (type.IsPrimitive || type.IsEnum || type == typeof(string) || 
            type == typeof(DateTime) || type == typeof(decimal) || type == typeof(Guid))
            return false;

        if (type.Namespace?.StartsWith("System") == true && !type.IsGenericType)
            return false;

        return type.IsClass || type.IsValueType;
    }    /// <summary>
    /// 获取基础字段类型
    /// </summary>
    private string GetSimpleFieldType(Type propType)
    {
        return propType.Name switch
        {
            "String" => "input",
            "Int32" or "Int64" => "number",
            "Decimal" or "Double" or "Single" => "number",
            "Boolean" => "switch",
            "DateTime" => "datetime",
            "Guid" => "input",
            _ when propType.IsEnum => "select",
            _ => "input"
        };
    }

    /// <summary>
    /// 获取表单模式
    /// </summary>
    private string GetFormMode(Type type)
    {
        var propertyCount = type.GetProperties(BindingFlags.Public | BindingFlags.Instance).Length;
        
        if (propertyCount <= 3)
            return "inline";
        else if (propertyCount <= 8)
            return "modal";
        else
            return "tabs";
    }

    /// <summary>
    /// 获取类型分类
    /// </summary>
    private string GetTypeCategory(Type type)
    {
        var typeName = type.Name.ToLower();
        
        if (typeName.Contains("condition"))
            return "condition";
        else if (typeName.Contains("config"))
            return "configuration";
        else if (typeName.Contains("tier"))
            return "tier";
        else if (typeName.Contains("strategy"))
            return "strategy";
        else
            return "general";
    }    /// <summary>
    /// 判断是否为忽略的属性
    /// </summary>
    private bool IsIgnoredProperty(PropertyInfo prop)
    {
        return prop.GetCustomAttribute<JsonIgnoreAttribute>() != null ||
               !prop.CanWrite ||
               prop.GetMethod?.IsPublic != true;
    }

    /// <summary>
    /// 获取枚举选项
    /// </summary>
    private object[] GetEnumOptions(Type enumType)
    {
        return Enum.GetValues(enumType)
            .Cast<object>()
            .Select(value => new
            {
                value = value.ToString(),
                label = GetEnumDisplayName(enumType, value) ?? value.ToString()
            })
            .ToArray();
    }    /// <summary>
    /// 获取枚举显示名称
    /// </summary>
    private string? GetEnumDisplayName(Type enumType, object value)
    {
        var field = enumType.GetField(value.ToString()!);
        return field?.GetCustomAttribute<DisplayAttribute>()?.Name ??
               field?.GetCustomAttribute<DescriptionAttribute>()?.Description;
    }

    /// <summary>
    /// 转换为JSON对象
    /// </summary>
    private object? ConvertToJsonObject(object? obj)
    {
        if (obj == null) return null;

        var type = obj.GetType();
        if (type.IsPrimitive || type == typeof(string) || type == typeof(DateTime) || type == typeof(decimal))
            return obj;

        var result = new Dictionary<string, object?>();
        var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);

        foreach (var prop in properties)
        {
            if (IsIgnoredProperty(prop)) continue;

            try
            {
                var value = prop.GetValue(obj);
                result[ToCamelCase(prop.Name)] = ConvertToJsonObject(value);
            }
            catch
            {
                // 忽略无法获取的属性值
            }
        }

        return result;
    }

    /// <summary>
    /// 获取分类信息
    /// </summary>
    private (string Key, string Name, string Icon, string Description) GetCategoryInfo(Type ruleType)
    {
        var namespaceParts = ruleType.Namespace?.Split('.') ?? Array.Empty<string>();
        var lastPart = namespaceParts.LastOrDefault() ?? "";

        // 根据类名判断分类
        var typeName = ruleType.Name;

        if (typeName.Contains("Gift"))
            return ("buyGift", "买赠规则", "🎁", "购买指定商品获得赠品的促销规则");
        if (typeName.Contains("Exchange"))
            return ("exchange", "换购规则", "🔄", "以优惠价格换购商品的促销规则");
        if (typeName.Contains("Free"))
            return ("buyFree", "买免规则", "🆓", "购买指定数量商品免费获得商品的规则");
        if (typeName.Contains("CashDiscount"))
            return ("cashDiscount", "减现规则", "💰", "满足条件时减免现金的促销规则");
        if (typeName.Contains("Discount"))
            return ("discount", "打折规则", "🏷️", "商品打折优惠的促销规则");
        if (typeName.Contains("SpecialPrice"))
            return ("specialPrice", "特价规则", "💲", "商品特价销售的促销规则");

        return ("other", "其他规则", "⚙️", "其他类型的促销规则");
    }

    /// <summary>
    /// 获取规则信息
    /// </summary>
    private (string TypeKey, string Name, string Description) GetRuleInfo(Type ruleType)
    {
        var name = GetDisplayName(ruleType) ?? FormatTypeName(ruleType.Name);
        var description = GetDescription(ruleType) ?? $"{name}促销规则";

        return (ruleType.Name, name, description);
    }

    // 辅助方法
    private string? GetDisplayName(MemberInfo member) =>
        member.GetCustomAttribute<DisplayNameAttribute>()?.DisplayName ??
        member.GetCustomAttribute<DisplayAttribute>()?.Name;

    private string? GetDescription(MemberInfo member) =>
        member.GetCustomAttribute<DescriptionAttribute>()?.Description ??
        member.GetCustomAttribute<DisplayAttribute>()?.Description;

    private bool IsRequired(PropertyInfo prop) =>
        prop.GetCustomAttribute<RequiredAttribute>() != null;

    private object? GetDefaultValue(PropertyInfo prop) =>
        prop.GetCustomAttribute<DefaultValueAttribute>()?.Value;

    private object[]? GetSelectOptions(PropertyInfo prop)
    {
        if (!prop.PropertyType.IsEnum) return null;        return Enum.GetValues(prop.PropertyType)
            .Cast<object>()
            .Select(value => new
            {
                value = value.ToString(),
                label = GetEnumDisplayName(prop.PropertyType, value) ?? value.ToString()
            })
            .ToArray();
    }

    private object? GetValidationRules(PropertyInfo prop)
    {
        var rules = new List<object>();

        if (prop.GetCustomAttribute<RangeAttribute>() is var range && range != null)
            rules.Add(new { type = "range", min = range.Minimum, max = range.Maximum });

        if (prop.GetCustomAttribute<StringLengthAttribute>() is var length && length != null)
            rules.Add(new { type = "length", min = length.MinimumLength, max = length.MaximumLength });

        return rules.Any() ? rules : null;
    }

    private string GetFieldGroup(PropertyInfo prop)
    {
        var name = prop.Name.ToLower();

        if (name.Contains("time")) return "time";
        if (name.Contains("condition") || name.Contains("amount") || name.Contains("quantity")) return "condition";
        if (name.Contains("gift") || name.Contains("discount") || name.Contains("benefit")) return "benefit";
        if (name.Contains("product")) return "condition";

        return "advanced";
    }

    private string ToCamelCase(string str) =>
        char.ToLowerInvariant(str[0]) + str[1..];

    private string FormatPropertyName(string name) =>
        System.Text.RegularExpressions.Regex.Replace(name, "([A-Z])", " $1").Trim();

    private string FormatTypeName(string name) =>
        name.Replace("Rule", "").Replace("Promotion", "");

    private object[] GenerateExamples(Type ruleType)
    {
        return new object[]
        {
            new {
                title = "基础示例",
                description = $"基本的{GetDisplayName(ruleType)}配置示例",
                config = new { /* 示例配置 */ }
            }
        };
    }
}
