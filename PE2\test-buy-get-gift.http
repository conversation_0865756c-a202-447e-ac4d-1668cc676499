### 买赠促销方案测试

### 1. 统一送赠品测试 - 购买A商品1件送1件B商品（不翻倍）
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "UNIFIED_GIFT_TEST_001",
  "customerId": "CUSTOMER_001",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 1000.00,
        "category": "电子产品"
      },
      "quantity": 1,
      "unitPrice": 1000.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 1000.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 1000.00
    }
  ]
}

### 2. 统一送赠品测试 - 购买A商品2件送1件B商品（不翻倍）
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "UNIFIED_GIFT_TEST_002",
  "customerId": "CUSTOMER_002",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 1000.00,
        "category": "电子产品"
      },
      "quantity": 2,
      "unitPrice": 1000.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 1000.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 1000.00
    }
  ]
}

### 3. 统一送赠品测试 - 购买A商品2件送2件B商品（翻倍）
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "UNIFIED_GIFT_TEST_003",
  "customerId": "CUSTOMER_003",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 1000.00,
        "category": "电子产品"
      },
      "quantity": 2,
      "unitPrice": 1000.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 1000.00,
        "category": "服装"
      },
      "quantity": 2,
      "unitPrice": 1000.00
    }
  ]
}

### 4. 梯度送赠品测试 - 购买A商品3件（按梯度送，不翻倍）
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "GRADIENT_GIFT_TEST_001",
  "customerId": "CUSTOMER_004",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 1000.00,
        "category": "电子产品"
      },
      "quantity": 3,
      "unitPrice": 1000.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 1000.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 1000.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 1000.00,
        "category": "家居"
      },
      "quantity": 2,
      "unitPrice": 1000.00
    }
  ]
}

### 5. 梯度送赠品测试 - 购买A商品4件（按梯度送，不翻倍）
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "GRADIENT_GIFT_TEST_002",
  "customerId": "CUSTOMER_005",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 1000.00,
        "category": "电子产品"
      },
      "quantity": 4,
      "unitPrice": 1000.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 1000.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 1000.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 1000.00,
        "category": "家居"
      },
      "quantity": 2,
      "unitPrice": 1000.00
    }
  ]
}

### 6. 梯度送赠品测试 - 购买A商品4件（翻倍且按梯度送）
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "GRADIENT_GIFT_TEST_003",
  "customerId": "CUSTOMER_006",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 1000.00,
        "category": "电子产品"
      },
      "quantity": 4,
      "unitPrice": 1000.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 1000.00,
        "category": "服装"
      },
      "quantity": 2,
      "unitPrice": 1000.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 1000.00,
        "category": "家居"
      },
      "quantity": 4,
      "unitPrice": 1000.00
    }
  ]
}

### 7. 梯度送赠品测试 - 购买A商品4件（翻倍且全部送）
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "GRADIENT_GIFT_TEST_004",
  "customerId": "CUSTOMER_007",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 1000.00,
        "category": "电子产品"
      },
      "quantity": 4,
      "unitPrice": 1000.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 1000.00,
        "category": "服装"
      },
      "quantity": 2,
      "unitPrice": 1000.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 1000.00,
        "category": "家居"
      },
      "quantity": 4,
      "unitPrice": 1000.00
    }
  ]
}

### 8. 组合送赠品测试 - 购买A、B商品各2件（不翻倍）
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "COMBINATION_GIFT_TEST_001",
  "customerId": "CUSTOMER_008",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 1000.00,
        "category": "电子产品"
      },
      "quantity": 2,
      "unitPrice": 1000.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 1000.00,
        "category": "服装"
      },
      "quantity": 2,
      "unitPrice": 1000.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 1000.00,
        "category": "家居"
      },
      "quantity": 1,
      "unitPrice": 1000.00
    }
  ]
}

### 9. 组合送赠品测试 - 购买A、B商品各2件（翻倍）
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "COMBINATION_GIFT_TEST_002",
  "customerId": "CUSTOMER_009",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 1000.00,
        "category": "电子产品"
      },
      "quantity": 2,
      "unitPrice": 1000.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 1000.00,
        "category": "服装"
      },
      "quantity": 2,
      "unitPrice": 1000.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 1000.00,
        "category": "家居"
      },
      "quantity": 2,
      "unitPrice": 1000.00
    }
  ]
}

### 10. 按金额送赠品测试 - 购买A商品满1000元送B商品
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "AMOUNT_BASED_GIFT_TEST",
  "customerId": "CUSTOMER_010",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 500.00,
        "category": "电子产品"
      },
      "quantity": 3,
      "unitPrice": 500.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 1000.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 1000.00
    }
  ]
}

### 11. 边界条件测试 - 刚好满足条件
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "BOUNDARY_TEST_001",
  "customerId": "CUSTOMER_011",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 1000.00,
        "category": "电子产品"
      },
      "quantity": 1,
      "unitPrice": 1000.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 1000.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 1000.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 1000.00,
        "category": "家居"
      },
      "quantity": 1,
      "unitPrice": 1000.00
    }
  ]
}

### 12. 边界条件测试 - 不满足条件
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "BOUNDARY_TEST_002",
  "customerId": "CUSTOMER_012",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 500.00,
        "category": "电子产品"
      },
      "quantity": 1,
      "unitPrice": 500.00
    }
  ]
}
