<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强元数据测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        .metadata-test {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 16px;
            color: #303133;
        }
        .json-display {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 16px;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 13px;
            line-height: 1.45;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>增强元数据与商品选择器测试</h1>
            
            <!-- 测试不同促销规则的元数据 -->
            <div class="metadata-test">
                <div class="test-title">CombinationBuyFreeRule 元数据测试</div>
                <el-button @click="testCombinationBuyFreeRule" type="primary">获取元数据</el-button>
                <el-button @click="clearResult" type="info">清空</el-button>
                <div v-if="combinationMetadata" class="json-display">
                    {{ JSON.stringify(combinationMetadata, null, 2) }}
                </div>
            </div>
            
            <div class="metadata-test">
                <div class="test-title">UnifiedSpecialPriceExchangeRule 元数据测试</div>
                <el-button @click="testUnifiedSpecialPriceExchangeRule" type="primary">获取元数据</el-button>
                <el-button @click="clearResult" type="info">清空</el-button>
                <div v-if="exchangeMetadata" class="json-display">
                    {{ JSON.stringify(exchangeMetadata, null, 2) }}
                </div>
            </div>
            
            <div class="metadata-test">
                <div class="test-title">ProductBuyFreeRule 元数据测试</div>
                <el-button @click="testProductBuyFreeRule" type="primary">获取元数据</el-button>
                <el-button @click="clearResult" type="info">清空</el-button>
                <div v-if="productMetadata" class="json-display">
                    {{ JSON.stringify(productMetadata, null, 2) }}
                </div>
            </div>
            
            <!-- 动态表单渲染测试 -->
            <div class="metadata-test">
                <div class="test-title">动态表单渲染测试</div>
                <el-select v-model="selectedRuleType" placeholder="选择规则类型" style="width: 300px; margin-right: 16px;">
                    <el-option value="CombinationBuyFreeRule" label="组合买赠规则" />
                    <el-option value="UnifiedSpecialPriceExchangeRule" label="统一特价换购规则" />
                    <el-option value="ProductBuyFreeRule" label="商品买赠规则" />
                </el-select>
                <el-button @click="renderForm" type="success">渲染表单</el-button>
                
                <div v-if="formMetadata" style="margin-top: 20px;">
                    <dynamic-form-renderer
                        :fields="formMetadata.fields"
                        :model-value="formData"
                        @update:model-value="formData = $event"
                        mode="edit"
                    />
                </div>
                
                <div v-if="formData && Object.keys(formData).length > 0" style="margin-top: 20px;">
                    <h3>表单数据：</h3>
                    <div class="json-display">
                        {{ JSON.stringify(formData, null, 2) }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="js/components/ProductSelector.js"></script>
    <script src="js/components/DynamicFormRenderer_template.js"></script>

    <script>
        const { createApp, ref, onMounted } = Vue;

        createApp({
            components: {
                ProductSelector,
                DynamicFormRenderer: DynamicFormRenderer_template
            },
            setup() {
                const combinationMetadata = ref(null);
                const exchangeMetadata = ref(null);
                const productMetadata = ref(null);
                const selectedRuleType = ref('CombinationBuyFreeRule');
                const formMetadata = ref(null);
                const formData = ref({});

                const testCombinationBuyFreeRule = async () => {
                    try {
                        const response = await fetch('/api/promotion/metadata/types/CombinationBuyFreeRule');
                        combinationMetadata.value = await response.json();
                    } catch (error) {
                        ElMessage.error('获取元数据失败: ' + error.message);
                    }
                };

                const testUnifiedSpecialPriceExchangeRule = async () => {
                    try {
                        const response = await fetch('/api/promotion/metadata/types/UnifiedSpecialPriceExchangeRule');
                        exchangeMetadata.value = await response.json();
                    } catch (error) {
                        ElMessage.error('获取元数据失败: ' + error.message);
                    }
                };

                const testProductBuyFreeRule = async () => {
                    try {
                        const response = await fetch('/api/promotion/metadata/types/ProductBuyFreeRule');
                        productMetadata.value = await response.json();
                    } catch (error) {
                        ElMessage.error('获取元数据失败: ' + error.message);
                    }
                };

                const clearResult = () => {
                    combinationMetadata.value = null;
                    exchangeMetadata.value = null;
                    productMetadata.value = null;
                    formMetadata.value = null;
                    formData.value = {};
                };

                const renderForm = async () => {
                    if (!selectedRuleType.value) {
                        ElMessage.warning('请先选择规则类型');
                        return;
                    }
                    
                    try {
                        const response = await fetch(`/api/promotion/metadata/types/${selectedRuleType.value}`);
                        formMetadata.value = await response.json();
                        formData.value = {};
                        ElMessage.success('表单渲染成功');
                    } catch (error) {
                        ElMessage.error('获取表单元数据失败: ' + error.message);
                    }
                };

                return {
                    combinationMetadata,
                    exchangeMetadata,
                    productMetadata,
                    selectedRuleType,
                    formMetadata,
                    formData,
                    testCombinationBuyFreeRule,
                    testUnifiedSpecialPriceExchangeRule,
                    testProductBuyFreeRule,
                    clearResult,
                    renderForm
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
