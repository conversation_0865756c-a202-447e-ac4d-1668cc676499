using PE2.Models;
using PE2.PromotionEngine.Rules;
using PE2.PromotionEngine.Rules.BuyFreeRules;
using PE2.PromotionEngine.Rules.BuyGiftRules;
using PE2.PromotionEngine.Rules.CashDiscountRules;
using PE2.PromotionEngine.Rules.DiscountRules;
using PE2.PromotionEngine.Rules.ExchangeRules;
using PE2.PromotionEngine.Rules.SpecialPriceRules;

namespace PE2.Tests.Infrastructure;

/// <summary>
/// 测试数据生成器
/// 提供标准化的测试数据，确保测试的一致性和可重复性
/// </summary>
public static class TestDataGenerator
{
    #region 标准商品数据

    /// <summary>
    /// 创建标准测试商品A
    /// </summary>
    public static Product CreateProductA() =>
        new()
        {
            Id = "A",
            Name = "商品A",
            Price = 50.00m,
            Category = "电子产品",
            Brand = "品牌A"
        };

    /// <summary>
    /// 创建标准测试商品B
    /// </summary>
    public static Product CreateProductB() =>
        new()
        {
            Id = "B",
            Name = "商品B",
            Price = 30.00m,
            Category = "服装",
            Brand = "品牌B"
        };

    /// <summary>
    /// 创建标准测试商品C
    /// </summary>
    public static Product CreateProductC() =>
        new()
        {
            Id = "C",
            Name = "商品C",
            Price = 20.00m,
            Category = "家居",
            Brand = "品牌C"
        };

    /// <summary>
    /// 创建标准测试商品D
    /// </summary>
    public static Product CreateProductD() =>
        new()
        {
            Id = "D",
            Name = "商品D",
            Price = 100.00m,
            Category = "奢侈品",
            Brand = "品牌D"
        };

    /// <summary>
    /// 创建标准测试商品E
    /// </summary>
    public static Product CreateProductE() =>
        new()
        {
            Id = "E",
            Name = "商品E",
            Price = 15.00m,
            Category = "日用品",
            Brand = "品牌E"
        };

    public static Product CreateProductF() =>
       new()
       {
           Id = "F",
           Name = "商品F",
           Price = 200m,
           Category = "日用品",
           Brand = "品牌E"
       };

    /// <summary>
    /// 获取所有标准测试商品
    /// </summary>
    public static List<Product> GetAllStandardProducts() =>
        [CreateProductA(), CreateProductB(), CreateProductC(), CreateProductD(), CreateProductE()];

    #endregion

    #region 自由组合折扣规则创建方法

    /// <summary>
    /// 创建基于数量的自由组合折扣规则
    /// A、B、C商品自由组合，满2件各自享受对应折扣：A商品9折，B商品8折，C商品7折
    /// </summary>
    public static FreeFormDiscountRule CreateFreeFormDiscountRule_ByQuantity()
    {
        return new FreeFormDiscountRule
        {
            Id = "FREEFORM_DISCOUNT_QTY_001",
            Name = "自由组合折扣-按数量",
            Description = "A、B、C商品自由组合，满2件各自享受对应折扣",
            IsEnabled = true,
            Priority = 100,

            StartTime = DateTime.Now.AddDays(-30),
            EndTime = DateTime.Now.AddDays(30),
            ProductConfigs =
            [
                new()
                {
                    ProductIds = ["A"],
                    DiscountTiers =
                    [
                        new() { MinQuantity = 1, DiscountRate = 0.9m },
                        new() { MinQuantity = 2, DiscountRate = 0.8m },
                        new() { MinQuantity = 3, DiscountRate = 0.7m }
                    ]
                },
                new()
                {
                    ProductIds = ["B"],
                    DiscountTiers =
                    [
                        new() { MinQuantity = 1, DiscountRate = 0.8m },
                        new() { MinQuantity = 2, DiscountRate = 0.7m },
                        new() { MinQuantity = 3, DiscountRate = 0.6m }
                    ]
                },
                new()
                {
                    ProductIds = ["C"],
                    DiscountTiers =
                    [
                        new() { MinQuantity = 1, DiscountRate = 0.7m },
                        new() { MinQuantity = 2, DiscountRate = 0.6m },
                        new() { MinQuantity = 3, DiscountRate = 0.5m }
                    ]
                }
            ]
        };
    }

    /// <summary>
    /// 创建基于金额的自由组合折扣规则
    /// A、B、C商品自由组合，满100元各自享受对应折扣：A商品9折，B商品8折，C商品7折
    /// </summary>
    public static FreeFormDiscountRule CreateFreeFormDiscountRule_ByAmount()
    {
        return new FreeFormDiscountRule
        {
            Id = "FREEFORM_DISCOUNT_AMT_001",
            Name = "自由组合折扣-按金额",
            Description = "A、B、C商品自由组合，满100元各自享受对应折扣",
            IsEnabled = true,
            Priority = 100,
            StartTime = DateTime.Now.AddDays(-30),
            EndTime = DateTime.Now.AddDays(30),
            ProductConfigs =
            [
                new()
                {
                    ProductIds = ["A"],
                    DiscountTiers =
                    [
                        new() { MinAmount = 100, DiscountRate = 0.9m },
                        new() { MinAmount = 200, DiscountRate = 0.8m },
                        new() { MinAmount = 300, DiscountRate = 0.7m }
                    ]
                },
                new()
                {
                    ProductIds = ["B"],
                    DiscountTiers =
                    [
                        new() { MinAmount = 100, DiscountRate = 0.8m },
                        new() { MinAmount = 200, DiscountRate = 0.7m },
                        new() { MinAmount = 300, DiscountRate = 0.6m }
                    ]
                },
                new()
                {
                    ProductIds = ["C"],
                    DiscountTiers =
                    [
                        new() { MinAmount = 100, DiscountRate = 0.7m },
                        new() { MinAmount = 200, DiscountRate = 0.6m },
                        new() { MinAmount = 300, DiscountRate = 0.5m }
                    ]
                }
            ]
        };
    }

    #endregion

    #region 标准购物车数据

    /// <summary>
    /// 创建经典测试购物车 (A:1, B:2, C:5)
    /// 这是项目文档中经常使用的标准测试场景
    /// </summary>
    public static ShoppingCart CreateClassicTestCart()
    {
        var cart = new ShoppingCart
        {
            Id = "CART_CLASSIC_TEST",
            CustomerId = "CUSTOMER_001",
            Items = new List<CartItem>
            {
                new()
                {
                    Product = CreateProductA(),
                    Quantity = 1,
                    UnitPrice = 50.00m
                },
                new()
                {
                    Product = CreateProductB(),
                    Quantity = 2,
                    UnitPrice = 30.00m
                },
                new()
                {
                    Product = CreateProductC(),
                    Quantity = 5,
                    UnitPrice = 20.00m
                }
            }
        };
        cart.InitializeActualPrices();
        return cart;
    }

    /// <summary>
    /// 创建简单测试购物车 (A:1)
    /// </summary>
    public static ShoppingCart CreateSimpleTestCart()
    {
        var cart = new ShoppingCart
        {
            Id = "CART_SIMPLE_TEST",
            CustomerId = "CUSTOMER_002",
            Items = new List<CartItem>
            {
                new()
                {
                    Product = CreateProductA(),
                    Quantity = 1,
                    UnitPrice = 50.00m
                }
            }
        };
        cart.InitializeActualPrices();
        return cart;
    }

    /// <summary>
    /// 创建复杂测试购物车 (A:3, B:4, C:2, D:1, E:6)
    /// </summary>
    public static ShoppingCart CreateComplexTestCart()
    {
        var cart = new ShoppingCart
        {
            Id = "CART_COMPLEX_TEST",
            CustomerId = "CUSTOMER_003",
            Items = new List<CartItem>
            {
                new()
                {
                    Product = CreateProductA(),
                    Quantity = 3,
                    UnitPrice = 50.00m
                },
                new()
                {
                    Product = CreateProductB(),
                    Quantity = 4,
                    UnitPrice = 30.00m
                },
                new()
                {
                    Product = CreateProductC(),
                    Quantity = 2,
                    UnitPrice = 20.00m
                },
                new()
                {
                    Product = CreateProductD(),
                    Quantity = 1,
                    UnitPrice = 100.00m
                },
                new()
                {
                    Product = CreateProductE(),
                    Quantity = 6,
                    UnitPrice = 15.00m
                }
            }
        };
        cart.InitializeActualPrices();
        return cart;
    }

    /// <summary>
    /// 创建空购物车
    /// </summary>
    public static ShoppingCart CreateEmptyCart()
    {
        return new ShoppingCart
        {
            Id = "CART_EMPTY",
            CustomerId = "CUSTOMER_EMPTY",
            Items = new List<CartItem>()
        };
    }

    /// <summary>
    /// 创建自定义购物车
    /// </summary>
    public static ShoppingCart CreateCustomCart(
        string cartId,
        string customerId,
        params (Product product, int quantity)[] items
    )
    {
        var cart = new ShoppingCart
        {
            Id = cartId,
            CustomerId = customerId,
            Items =
            [
                .. items.Select(item => new CartItem
                {
                    Product = item.product,
                    Quantity = item.quantity,
                    UnitPrice = item.product.Price
                })
            ]
        };
        cart.InitializeActualPrices();
        return cart;
    }

    #endregion

    #region 买赠规则测试数据

    /// <summary>
    /// 创建统一买赠规则：买2件A送1件B
    /// </summary>
    public static UnifiedGiftRule CreateUnifiedGiftRule_Buy2A_Get1B()
    {
        return new UnifiedGiftRule
        {
            Id = "UNIFIED_GIFT_001",
            Name = "买2件A送1件B",
            Description = "购买2件商品A，赠送1件商品B",
            Priority = 100,
            IsEnabled = true,
            ApplicableProductIds = ["A"],
            MinQuantity = 2,
            GiftProductIds = ["B"],
            GiftQuantity = 1,
            GiftSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit
        };
    }

    public static CombinationGiftRule CreateCombinationGiftRule_BuyAB_Get1C()
    {
        return new CombinationGiftRule
        {
            Id = "COMBINATION_GIFT_001",
            Name = "购买A+B组合，赠送1件C",
            Description = "同时购买商品A和商品B，赠送1件商品C",
            Priority = 95,
            IsEnabled = true,
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 1 },
                new() { ProductId = "B", RequiredQuantity = 1 }
            ],
            GiftProductIds = ["C"],
            GiftQuantity = 1,
            GiftSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit
        };
    }

    /// <summary>
    /// 创建阶梯买赠规则：买1送1，买2送3
    /// </summary>
    public static TieredGiftRule CreateTieredGiftRule_Progressive()
    {
        return new TieredGiftRule
        {
            Id = "TIERED_GIFT_001",
            Name = "阶梯买赠 - 买1送1，买2送3",
            Description = "购买1件送1件，购买2件送3件",
            Priority = 90,
            IsEnabled = true,
            ApplicableProductIds = ["A"],

            GiftTiers =
            [
                new()
                {
                    MinQuantity = 1,
                    GiftProductIds = ["B"],
                    GiftQuantity = 1
                },
                new()
                {
                    MinQuantity = 2,
                    GiftProductIds = ["B",],
                    GiftQuantity = 3
                }
            ]
        };
    }

    #endregion

    #region 打折规则测试数据

    /// <summary>
    /// 创建统一打折规则：商品B满2件8折
    /// </summary>
    public static UnifiedDiscountRule CreateUnifiedDiscountRule_B_2Pieces_80Percent()
    {
        return new UnifiedDiscountRule
        {
            Id = "UNIFIED_DISCOUNT_001",
            Name = "商品B满2件8折",
            Description = "购买商品B满2件享受8折优惠",
            Priority = 80,
            IsEnabled = true,
            ApplicableProductIds = ["B"],
            MinQuantity = 2,
            DiscountRate = 0.8m
        };
    }

    /// <summary>
    /// 创建统一打折规则：商品A满100元9折
    /// </summary>
    public static UnifiedDiscountRule CreateUnifiedDiscountRule_A_100Amount_90Percent()
    {
        return new UnifiedDiscountRule
        {
            Id = "UNIFIED_DISCOUNT_AMOUNT_001",
            Name = "商品A满100元9折",
            Description = "购买商品A满100元享受9折优惠",
            Priority = 75,
            IsEnabled = true,
            ApplicableProductIds = ["A"],
            MinAmount = 100m,
            DiscountRate = 0.9m
        };
    }

    /// <summary>
    /// 创建统一打折规则：商品B满50元8折
    /// </summary>
    public static UnifiedDiscountRule CreateUnifiedDiscountRule_B_50Amount_80Percent()
    {
        return new UnifiedDiscountRule
        {
            Id = "UNIFIED_DISCOUNT_AMOUNT_002",
            Name = "商品B满50元8折",
            Description = "购买商品B满50元享受8折优惠",
            Priority = 70,
            IsEnabled = true,
            ApplicableProductIds = ["B"],
            MinAmount = 50m,
            DiscountRate = 0.8m
        };
    }

    /// <summary>
    /// 创建阶梯打折规则：买2件9折，买3件8折
    /// </summary>
    public static TieredDiscountRule CreateTieredDiscountRule_Progressive()
    {
        return new TieredDiscountRule
        {
            Id = "TIERED_DISCOUNT_001",
            Name = "阶梯打折 - 买2件9折，买3件8折",
            Description = "购买2件享受9折，购买3件享受8折",
            Priority = 70,
            IsEnabled = true,
            ApplicableProductIds = ["A"],
            DiscountTiers =
            [
                new() { MinQuantity = 2, DiscountRate = 0.9m },
                new() { MinQuantity = 3, DiscountRate = 0.8m }
            ]
        };
    }

    /// <summary>
    /// 创建基于金额的阶梯打折规则：满100元9折，满200元8折
    /// </summary>
    public static TieredDiscountRule CreateTieredDiscountRule_ByAmount()
    {
        return new TieredDiscountRule
        {
            Id = "TIERED_DISCOUNT_AMOUNT_001",
            Name = "阶梯打折 - 满100元9折，满200元8折",
            Description = "购买满100元享受9折，满200元享受8折",
            Priority = 70,
            IsEnabled = true,
            ApplicableProductIds = ["A"],
            DiscountTiers =
            [
                new()
                {
                    MinAmount = 100m,
                    DiscountRate = 0.9m,
                    Description = "满100元9折"
                },
                new()
                {
                    MinAmount = 200m,
                    DiscountRate = 0.8m,
                    Description = "满200元8折"
                }
            ]
        };
    }

    /// <summary>
    /// 创建多商品阶梯打折规则：A+B商品买2件9折，买3件8折
    /// </summary>
    public static TieredDiscountRule CreateTieredDiscountRule_MultiProduct()
    {
        return new TieredDiscountRule
        {
            Id = "TIERED_DISCOUNT_MULTI_001",
            Name = "多商品阶梯打折 - A+B商品买2件9折，买3件8折",
            Description = "A+B商品组合购买2件享受9折，购买3件享受8折",
            Priority = 70,
            IsEnabled = true,
            ApplicableProductIds = ["A", "B"],
            DiscountTiers =
            [
                new()
                {
                    MinQuantity = 2,
                    DiscountRate = 0.9m,
                    Description = "2件9折"
                },
                new()
                {
                    MinQuantity = 3,
                    DiscountRate = 0.8m,
                    Description = "3件8折"
                }
            ]
        };
    }

    /// <summary>
    /// 创建递增折扣规则：商品A第1件9折，第2件8折，第3件及以上7折
    /// </summary>
    public static ProgressiveDiscountRule CreateProgressiveDiscountRule_A()
    {
        return new ProgressiveDiscountRule
        {
            Id = "PROGRESSIVE_DISCOUNT_001",
            Name = "递增折扣 - A商品第1件9折，第2件8折，第3件及以上7折",
            Description = "商品A递增折扣：第1件9折，第2件8折，第3件及以上7折",
            Priority = 65,
            IsEnabled = true,
            ApplicableProductIds = ["A"],
            ProgressiveTiers =
            [
                new() { DiscountRate = 0.9m, Description = "第1件9折" },
                new() { DiscountRate = 0.8m, Description = "第2件8折" },
                new() { DiscountRate = 0.7m, Description = "第3件及以上7折" }
            ]
        };
    }

    /// <summary>
    /// 创建循环递增折扣规则：第1件9折，第2件8折，第3件7折，循环应用
    /// </summary>
    public static CyclicDiscountRule CreateCyclicDiscountRule_Progressive()
    {
        return new CyclicDiscountRule
        {
            Id = "CYCLIC_DISCOUNT_001",
            Name = "循环递增折扣 - 第1件9折，第2件8折，第3件7折",
            Description = "商品A循环递增折扣：第1件9折，第2件8折，第3件7折，依次循环",
            Priority = 60,
            IsEnabled = true,
            ApplicableProductIds = ["A"],
            CyclicTiers =
            [
                new() { DiscountRate = 0.9m, Description = "第1件9折" },
                new() { DiscountRate = 0.8m, Description = "第2件8折" },
                new() { DiscountRate = 0.7m, Description = "第3件7折" }
            ]
        };
    }

    /// <summary>
    /// 创建多商品循环折扣规则：A+B商品循环折扣
    /// </summary>
    public static CyclicDiscountRule CreateCyclicDiscountRule_MultiProduct()
    {
        return new CyclicDiscountRule
        {
            Id = "CYCLIC_DISCOUNT_MULTI_001",
            Name = "多商品循环折扣 - A+B商品循环递增折扣",
            Description = "A+B商品循环递增折扣：第1件9折，第2件8折，第3件7折，依次循环",
            Priority = 60,
            IsEnabled = true,
            ApplicableProductIds = ["A", "B"],
            CyclicTiers =
            [
                new() { DiscountRate = 0.9m, Description = "第1件9折" },
                new() { DiscountRate = 0.8m, Description = "第2件8折" },
                new() { DiscountRate = 0.7m, Description = "第3件7折" }
            ]
        };
    }

    #endregion

    #region 组合折扣规则测试数据

    /// <summary>
    /// 创建组合折扣规则：购买A+B组合，对C商品8折
    /// </summary>
    public static CombinationDiscountRule CreateCombinationDiscountRule_AB_DiscountC()
    {
        return new CombinationDiscountRule
        {
            Id = "COMBINATION_DISCOUNT_001",
            Name = "购买A+B组合，对C商品8折",
            Description = "同时购买商品A和商品B，对商品C享受8折优惠",
            Priority = 75,
            IsEnabled = true,
            CombinationConditions =
            [
                new() { ProductIds = ["A"], RequiredQuantity = 1 },
                new() { ProductIds = ["B"], RequiredQuantity = 1 }
            ],
            DiscountProductIds = ["C"],
            DiscountRate = 0.8m
        };
    }

    /// <summary>
    /// 创建组合折扣规则：购买A+B组合，对B或C商品9折
    /// </summary>
    public static CombinationDiscountRule CreateCombinationDiscountRule_AB_DiscountBOrC()
    {
        return new CombinationDiscountRule
        {
            Id = "COMBINATION_DISCOUNT_002",
            Name = "购买A+B组合，对B+C商品9折",
            Description = "同时购买商品A和商品B，对商品B和C享受9折优惠",
            Priority = 70,
            IsEnabled = true,
            CombinationConditions =
            [
                new() { ProductIds = ["A"], RequiredQuantity = 1 },
                new() { ProductIds = ["B"], RequiredQuantity = 1 }
            ],
            DiscountProductIds = ["B", "C"],
            DiscountRate = 0.9m
        };
    }

    #endregion

    #region 特价规则测试数据

    /// <summary>
    /// 创建统一特价规则：商品A特价40元
    /// </summary>
    public static UnifiedSpecialPriceRule CreateUnifiedSpecialPriceRule_A_40Yuan()
    {
        return new UnifiedSpecialPriceRule
        {
            Id = "UNIFIED_SPECIAL_PRICE_001",
            Name = "商品A特价40元",
            Description = "商品A特价销售，每件40元",
            Priority = 60,
            IsEnabled = true,

            MinQuantity = 1,
            SpecialPrice = 40.00m,
            ApplicableProductIds = ["A"]
        };
    }

    #endregion

    #region 减现规则测试数据

    /// <summary>
    /// 创建统一减现规则：满100减20
    /// </summary>
    public static UnifiedCashDiscountRule CreateUnifiedCashDiscountRule_100Minus20()
    {
        return new UnifiedCashDiscountRule
        {
            Id = "UNIFIED_CASH_DISCOUNT_001",
            Name = "满100减20",
            Description = "订单满100元减20元",
            Priority = 50,
            IsEnabled = true,
            MinAmount = 100.00m,
            DiscountAmount = 20.00m
        };
    }

    /// <summary>
    /// 创建基于数量的统一减现规则：A商品满1件减10元
    /// </summary>
    public static UnifiedCashDiscountRule CreateUnifiedCashDiscountRule_QuantityBased()
    {
        return new UnifiedCashDiscountRule
        {
            Id = "UNIFIED_CASH_DISCOUNT_QTY_001",
            Name = "A商品满1件减10元",
            Description = "A商品满1件时，立减10元",
            Priority = 60,
            IsEnabled = true,
            IsRepeatable = true,
            ApplicableProductIds = ["A"],
            MinQuantity = 1,
            MinAmount = 0,
            DiscountAmount = 10.00m
        };
    }

    /// <summary>
    /// 创建基于金额的统一减现规则：A商品满100元减20元
    /// </summary>
    public static UnifiedCashDiscountRule CreateUnifiedCashDiscountRule_AmountBased()
    {
        return new UnifiedCashDiscountRule
        {
            Id = "UNIFIED_CASH_DISCOUNT_AMT_001",
            Name = "A商品满100元减20元",
            Description = "A商品满100元时，立减20元",
            Priority = 65,
            IsEnabled = true,
            IsRepeatable = false,
            ApplicableProductIds = ["A"],
            MinQuantity = 0,
            MinAmount = 100.00m,
            DiscountAmount = 20.00m
        };
    }

    /// <summary>
    /// 创建可重复应用的统一减现规则
    /// </summary>
    public static UnifiedCashDiscountRule CreateUnifiedCashDiscountRule_Repeatable()
    {
        return new UnifiedCashDiscountRule
        {
            Id = "UNIFIED_CASH_DISCOUNT_REP_001",
            Name = "A商品满1件减10元（可重复）",
            Description = "A商品满1件时，立减10元，可重复应用",
            Priority = 70,
            IsEnabled = true,
            IsRepeatable = true,
            MaxApplications = 5, // 可重复应用次数限制
            ApplicableProductIds = ["A"],
            MinQuantity = 1,
            MinAmount = 0,
            DiscountAmount = 10.00m
        };
    }

    /// <summary>
    /// 创建不可重复应用的统一减现规则
    /// </summary>
    public static UnifiedCashDiscountRule CreateUnifiedCashDiscountRule_NonRepeatable()
    {
        return new UnifiedCashDiscountRule
        {
            Id = "UNIFIED_CASH_DISCOUNT_NONREP_001",
            Name = "A商品满1件减10元（不可重复）",
            Description = "A商品满1件时，立减10元，不可重复应用",
            Priority = 75,
            IsEnabled = true,
            IsRepeatable = false,
            ApplicableProductIds = ["A"],
            MinQuantity = 1,
            MinAmount = 0,
            DiscountAmount = 10.00m
        };
    }

    /// <summary>
    /// 创建禁用的统一减现规则
    /// </summary>
    public static UnifiedCashDiscountRule CreateUnifiedCashDiscountRule_Disabled()
    {
        return new UnifiedCashDiscountRule
        {
            Id = "UNIFIED_CASH_DISCOUNT_DISABLED_001",
            Name = "禁用的减现规则",
            Description = "已禁用的减现规则",
            Priority = 80,
            IsEnabled = false,
            ApplicableProductIds = ["A"],
            MinQuantity = 1,
            MinAmount = 0,
            DiscountAmount = 10.00m
        };
    }

    /// <summary>
    /// 创建多商品统一减现规则
    /// </summary>
    public static UnifiedCashDiscountRule CreateUnifiedCashDiscountRule_MultipleProducts()
    {
        return new UnifiedCashDiscountRule
        {
            Id = "UNIFIED_CASH_DISCOUNT_MULTI_001",
            Name = "A+B商品满3件减15元",
            Description = "A+B商品满3件时，立减15元",
            Priority = 85,
            IsEnabled = true,
            IsRepeatable = false,
            ApplicableProductIds = ["A", "B"],
            MinQuantity = 3,
            MinAmount = 0,
            DiscountAmount = 15.00m
        };
    }

    /// <summary>
    /// 创建无效配置的统一减现规则
    /// </summary>
    public static UnifiedCashDiscountRule CreateUnifiedCashDiscountRule_InvalidConfig()
    {
        return new UnifiedCashDiscountRule
        {
            Id = "UNIFIED_CASH_DISCOUNT_INVALID_001",
            Name = "无效配置的减现规则",
            Description = "无效配置的减现规则",
            Priority = 90,
            IsEnabled = true,
            ApplicableProductIds = [], // 空的适用商品列表
            MinQuantity = 0,
            MinAmount = 0,
            DiscountAmount = 10.00m
        };
    }

    /// <summary>
    /// 创建梯度减现规则：A商品梯度减现（满1件减10元，满2件减30元，满3件减70元）
    /// </summary>
    public static GradientCashDiscountRule CreateGradientCashDiscountRule_Progressive()
    {
        return new GradientCashDiscountRule
        {
            Id = "GRADIENT_CASH_DISCOUNT_001",
            Name = "A商品梯度减现",
            Description = "A商品梯度减现：满1件减10元，满2件减30元，满3件减70元",
            Priority = 80,
            IsEnabled = true,
            IsRepeatable = false,
            ApplicableProductIds = ["A"],
            DiscountTiers =
            [
                new GradientDiscountTier
                {
                    MinQuantity = 1,
                    MinAmount = 0,
                    DiscountAmount = 10.00m,
                    Description = "满1件减10元"
                },
                new GradientDiscountTier
                {
                    MinQuantity = 2,
                    MinAmount = 0,
                    DiscountAmount = 30.00m,
                    Description = "满2件减30元"
                },
                new GradientDiscountTier
                {
                    MinQuantity = 3,
                    MinAmount = 0,
                    DiscountAmount = 70.00m,
                    Description = "满3件减70元"
                }
            ]
        };
    }

    /// <summary>
    /// 创建基于金额的梯度减现规则
    /// </summary>
    public static GradientCashDiscountRule CreateGradientCashDiscountRule_AmountBased()
    {
        return new GradientCashDiscountRule
        {
            Id = "GRADIENT_CASH_DISCOUNT_AMT_001",
            Name = "A商品金额梯度减现",
            Description = "A商品基于金额的梯度减现",
            Priority = 85,
            IsEnabled = true,
            IsRepeatable = false,
            ApplicableProductIds = ["A"],
            DiscountTiers =
            [
                new GradientDiscountTier
                {
                    MinQuantity = 0,
                    MinAmount = 100.00m,
                    DiscountAmount = 20.00m,
                    Description = "满100元减20元"
                },
                new GradientDiscountTier
                {
                    MinQuantity = 0,
                    MinAmount = 200.00m,
                    DiscountAmount = 50.00m,
                    Description = "满200元减50元"
                },
                new GradientDiscountTier
                {
                    MinQuantity = 0,
                    MinAmount = 300.00m,
                    DiscountAmount = 100.00m,
                    Description = "满300元减100元"
                }
            ]
        };
    }

    /// <summary>
    /// 创建可重复应用的梯度减现规则
    /// </summary>
    public static GradientCashDiscountRule CreateGradientCashDiscountRule_Repeatable()
    {
        return new GradientCashDiscountRule
        {
            Id = "GRADIENT_CASH_DISCOUNT_REP_001",
            Name = "A商品梯度减现（可重复）",
            Description = "A商品梯度减现，可重复应用",
            Priority = 90,
            IsEnabled = true,
            IsRepeatable = true,
            MaxApplications = 5, // 可重复应用次数限制
            ApplicableProductIds = ["A"],
            DiscountTiers =
            [
                new GradientDiscountTier
                {
                    MinQuantity = 1,
                    MinAmount = 0,
                    DiscountAmount = 10.00m,
                    Description = "满1件减10元"
                },
                new GradientDiscountTier
                {
                    MinQuantity = 3,
                    MinAmount = 0,
                    DiscountAmount = 70.00m,
                    Description = "满3件减70元"
                }
            ]
        };
    }

    /// <summary>
    /// 创建不可重复应用的梯度减现规则
    /// </summary>
    public static GradientCashDiscountRule CreateGradientCashDiscountRule_NonRepeatable()
    {
        return new GradientCashDiscountRule
        {
            Id = "GRADIENT_CASH_DISCOUNT_NONREP_001",
            Name = "A商品梯度减现（不可重复）",
            Description = "A商品梯度减现，不可重复应用",
            Priority = 95,
            IsEnabled = true,
            IsRepeatable = false,
            ApplicableProductIds = ["A"],
            DiscountTiers =
            [
                new GradientDiscountTier
                {
                    MinQuantity = 3,
                    MinAmount = 0,
                    DiscountAmount = 70.00m,
                    Description = "满3件减70元"
                }
            ]
        };
    }

    /// <summary>
    /// 创建禁用的梯度减现规则
    /// </summary>
    public static GradientCashDiscountRule CreateGradientCashDiscountRule_Disabled()
    {
        return new GradientCashDiscountRule
        {
            Id = "GRADIENT_CASH_DISCOUNT_DISABLED_001",
            Name = "禁用的梯度减现规则",
            Description = "已禁用的梯度减现规则",
            Priority = 100,
            IsEnabled = false,
            ApplicableProductIds = ["A"],
            DiscountTiers =
            [
                new GradientDiscountTier
                {
                    MinQuantity = 1,
                    MinAmount = 0,
                    DiscountAmount = 10.00m,
                    Description = "满1件减10元"
                }
            ]
        };
    }

    /// <summary>
    /// 创建多商品梯度减现规则
    /// </summary>
    public static GradientCashDiscountRule CreateGradientCashDiscountRule_MultipleProducts()
    {
        return new GradientCashDiscountRule
        {
            Id = "GRADIENT_CASH_DISCOUNT_MULTI_001",
            Name = "A+B商品梯度减现",
            Description = "A+B商品梯度减现",
            Priority = 105,
            IsEnabled = true,
            IsRepeatable = false,
            ApplicableProductIds = ["A", "B"],
            DiscountTiers =
            [
                new GradientDiscountTier
                {
                    MinQuantity = 2,
                    MinAmount = 0,
                    DiscountAmount = 15.00m,
                    Description = "满2件减15元"
                },
                new GradientDiscountTier
                {
                    MinQuantity = 4,
                    MinAmount = 0,
                    DiscountAmount = 30.00m,
                    Description = "满4件减30元"
                }
            ]
        };
    }

    /// <summary>
    /// 创建空梯度配置的梯度减现规则
    /// </summary>
    public static GradientCashDiscountRule CreateGradientCashDiscountRule_EmptyTiers()
    {
        return new GradientCashDiscountRule
        {
            Id = "GRADIENT_CASH_DISCOUNT_EMPTY_001",
            Name = "空梯度配置的减现规则",
            Description = "空梯度配置的减现规则",
            Priority = 110,
            IsEnabled = true,
            ApplicableProductIds = ["A"],
            DiscountTiers = [] // 空的梯度列表
        };
    }

    /// <summary>
    /// 创建无效配置的梯度减现规则
    /// </summary>
    public static GradientCashDiscountRule CreateGradientCashDiscountRule_InvalidConfig()
    {
        return new GradientCashDiscountRule
        {
            Id = "GRADIENT_CASH_DISCOUNT_INVALID_001",
            Name = "无效配置的梯度减现规则",
            Description = "无效配置的梯度减现规则",
            Priority = 115,
            IsEnabled = true,
            ApplicableProductIds = [], // 空的适用商品列表
            DiscountTiers =
            [
                new GradientDiscountTier
                {
                    MinQuantity = 0,
                    MinAmount = 0,
                    DiscountAmount = 10.00m,
                    Description = "无效梯度"
                }
            ]
        };
    }

    /// <summary>
    /// 创建组合减现规则：A商品2件+B商品1件减15元
    /// </summary>
    public static CombinationCashDiscountRule CreateCombinationCashDiscountRule_AB()
    {
        return new CombinationCashDiscountRule
        {
            Id = "COMBINATION_CASH_DISCOUNT_001",
            Name = "A+B组合减现",
            Description = "A商品2件+B商品1件减15元",
            Priority = 120,
            IsEnabled = true,
            IsRepeatable = false,
            DiscountAmount = 15.00m,
            CombinationConditions =
            [
                new CombinationDiscountCondition
                {
                    ProductId = "A",
                    RequiredQuantity = 2,
                    RequiredAmount = 0
                },
                new CombinationDiscountCondition
                {
                    ProductId = "B",
                    RequiredQuantity = 1,
                    RequiredAmount = 0
                }
            ]
        };
    }

    /// <summary>
    /// 创建基于金额的组合减现规则
    /// </summary>
    public static CombinationCashDiscountRule CreateCombinationCashDiscountRule_AmountBased()
    {
        return new CombinationCashDiscountRule
        {
            Id = "COMBINATION_CASH_DISCOUNT_AMT_001",
            Name = "A+B金额组合减现",
            Description = "A商品满100元+B商品满50元减25元",
            Priority = 125,
            IsEnabled = true,
            IsRepeatable = false,
            DiscountAmount = 25.00m,
            CombinationConditions =
            [
                new CombinationDiscountCondition
                {
                    ProductId = "A",
                    RequiredQuantity = 0,
                    RequiredAmount = 100.00m
                },
                new CombinationDiscountCondition
                {
                    ProductId = "B",
                    RequiredQuantity = 0,
                    RequiredAmount = 50.00m
                }
            ]
        };
    }

    /// <summary>
    /// 创建复杂组合减现规则
    /// </summary>
    public static CombinationCashDiscountRule CreateCombinationCashDiscountRule_Complex()
    {
        return new CombinationCashDiscountRule
        {
            Id = "COMBINATION_CASH_DISCOUNT_COMPLEX_001",
            Name = "A+B+C复杂组合减现",
            Description = "A商品3件+B商品2件+C商品1件减30元",
            Priority = 130,
            IsEnabled = true,
            IsRepeatable = false,
            DiscountAmount = 30.00m,
            CombinationConditions =
            [
                new CombinationDiscountCondition
                {
                    ProductId = "A",
                    RequiredQuantity = 3,
                    RequiredAmount = 0
                },
                new CombinationDiscountCondition
                {
                    ProductId = "B",
                    RequiredQuantity = 2,
                    RequiredAmount = 0
                },
                new CombinationDiscountCondition
                {
                    ProductId = "C",
                    RequiredQuantity = 1,
                    RequiredAmount = 0
                }
            ]
        };
    }

    /// <summary>
    /// 创建可重复应用的组合减现规则
    /// </summary>
    public static CombinationCashDiscountRule CreateCombinationCashDiscountRule_Repeatable()
    {
        return new CombinationCashDiscountRule
        {
            Id = "COMBINATION_CASH_DISCOUNT_REP_001",
            Name = "A+B组合减现（可重复）",
            Description = "A商品2件+B商品1件减15元，可重复应用",
            Priority = 135,
            IsEnabled = true,
            IsRepeatable = true,
            MaxApplications = 3, // 可重复应用次数限制
            DiscountAmount = 15.00m,
            CombinationConditions =
            [
                new CombinationDiscountCondition
                {
                    ProductId = "A",
                    RequiredQuantity = 2,
                    RequiredAmount = 0
                },
                new CombinationDiscountCondition
                {
                    ProductId = "B",
                    RequiredQuantity = 1,
                    RequiredAmount = 0
                }
            ]
        };
    }

    /// <summary>
    /// 创建不可重复应用的组合减现规则
    /// </summary>
    public static CombinationCashDiscountRule CreateCombinationCashDiscountRule_NonRepeatable()
    {
        return new CombinationCashDiscountRule
        {
            Id = "COMBINATION_CASH_DISCOUNT_NONREP_001",
            Name = "A+B组合减现（不可重复）",
            Description = "A商品2件+B商品1件减15元，不可重复应用",
            Priority = 140,
            IsEnabled = true,
            IsRepeatable = false,
            DiscountAmount = 15.00m,
            CombinationConditions =
            [
                new CombinationDiscountCondition
                {
                    ProductId = "A",
                    RequiredQuantity = 2,
                    RequiredAmount = 0
                },
                new CombinationDiscountCondition
                {
                    ProductId = "B",
                    RequiredQuantity = 1,
                    RequiredAmount = 0
                }
            ]
        };
    }

    /// <summary>
    /// 创建禁用的组合减现规则
    /// </summary>
    public static CombinationCashDiscountRule CreateCombinationCashDiscountRule_Disabled()
    {
        return new CombinationCashDiscountRule
        {
            Id = "COMBINATION_CASH_DISCOUNT_DISABLED_001",
            Name = "禁用的组合减现规则",
            Description = "已禁用的组合减现规则",
            Priority = 145,
            IsEnabled = false,
            DiscountAmount = 15.00m,
            CombinationConditions =
            [
                new CombinationDiscountCondition
                {
                    ProductId = "A",
                    RequiredQuantity = 2,
                    RequiredAmount = 0
                },
                new CombinationDiscountCondition
                {
                    ProductId = "B",
                    RequiredQuantity = 1,
                    RequiredAmount = 0
                }
            ]
        };
    }

    /// <summary>
    /// 创建混合条件的组合减现规则
    /// </summary>
    public static CombinationCashDiscountRule CreateCombinationCashDiscountRule_Mixed()
    {
        return new CombinationCashDiscountRule
        {
            Id = "COMBINATION_CASH_DISCOUNT_MIXED_001",
            Name = "A+B混合条件组合减现",
            Description = "A商品3件+B商品满100元减20元",
            Priority = 150,
            IsEnabled = true,
            IsRepeatable = false,
            DiscountAmount = 20.00m,
            CombinationConditions =
            [
                new CombinationDiscountCondition
                {
                    ProductId = "A",
                    RequiredQuantity = 3,
                    RequiredAmount = 0
                },
                new CombinationDiscountCondition
                {
                    ProductId = "B",
                    RequiredQuantity = 0,
                    RequiredAmount = 100.00m
                }
            ]
        };
    }

    /// <summary>
    /// 创建空组合条件的组合减现规则
    /// </summary>
    public static CombinationCashDiscountRule CreateCombinationCashDiscountRule_EmptyConditions()
    {
        return new CombinationCashDiscountRule
        {
            Id = "COMBINATION_CASH_DISCOUNT_EMPTY_001",
            Name = "空组合条件的减现规则",
            Description = "空组合条件的减现规则",
            Priority = 155,
            IsEnabled = true,
            DiscountAmount = 15.00m,
            CombinationConditions = [] // 空的组合条件列表
        };
    }

    /// <summary>
    /// 创建无效配置的组合减现规则
    /// </summary>
    public static CombinationCashDiscountRule CreateCombinationCashDiscountRule_InvalidConfig()
    {
        return new CombinationCashDiscountRule
        {
            Id = "COMBINATION_CASH_DISCOUNT_INVALID_001",
            Name = "无效配置的组合减现规则",
            Description = "无效配置的组合减现规则",
            Priority = 160,
            IsEnabled = true,
            DiscountAmount = 15.00m,
            CombinationConditions =
            [
                new CombinationDiscountCondition
                {
                    ProductId = "A",
                    RequiredQuantity = 0,
                    RequiredAmount = 0
                } // 无效条件
            ]
        };
    }

    #endregion

    #region 买免规则测试数据

    /// <summary>
    /// 创建商品买免规则：买3件C免1件
    /// </summary>
    public static ProductBuyFreeRule CreateProductBuyFreeRule_Buy3C_Free1()
    {
        return new ProductBuyFreeRule
        {
            Id = "PRODUCT_BUY_FREE_001",
            Name = "买3件C免1件",
            Description = "购买3件商品C，免费获得1件",
            Priority = 40,
            IsEnabled = true,
            ApplicableProductIds = ["C"],
            BuyQuantity = 3,
            FreeQuantity = 1
        };
    }

    #endregion

    #region 测试场景数据

    /// <summary>
    /// 创建标准测试场景：包含多种规则的完整测试环境
    /// </summary>
    public static class StandardTestScenarios
    {
        /// <summary>
        /// 场景1：经典测试场景 - 多规则竞争
        /// 购物车：A:1, B:2, C:5
        /// 规则：买赠、打折、特价等多种规则
        /// </summary>
        public static (
            ShoppingCart cart,
            List<PromotionRuleBase> rules
        ) GetClassicMultiRuleScenario()
        {
            var cart = CreateClassicTestCart();
            var rules = new List<PromotionRuleBase>
            {
                CreateUnifiedGiftRule_Buy2A_Get1B(),
                CreateUnifiedDiscountRule_B_2Pieces_80Percent(),
                CreateUnifiedSpecialPriceRule_A_40Yuan(),
                CreateUnifiedCashDiscountRule_100Minus20(),
                CreateProductBuyFreeRule_Buy3C_Free1()
            };
            return (cart, rules);
        }

        /// <summary>
        /// 场景2：单规则测试场景
        /// </summary>
        public static (ShoppingCart cart, PromotionRuleBase rule) GetSingleRuleScenario()
        {
            var cart = CreateSimpleTestCart();
            var rule = CreateUnifiedSpecialPriceRule_A_40Yuan();
            return (cart, rule);
        }

        /// <summary>
        /// 场景3：无适用规则场景
        /// </summary>
        public static (
            ShoppingCart cart,
            List<PromotionRuleBase> rules
        ) GetNoApplicableRulesScenario()
        {
            var cart = CreateEmptyCart();
            var rules = new List<PromotionRuleBase>
            {
                CreateUnifiedGiftRule_Buy2A_Get1B(),
                CreateUnifiedDiscountRule_B_2Pieces_80Percent()
            };
            return (cart, rules);
        }

        /// <summary>
        /// 场景4：复杂组合优化场景
        /// </summary>
        public static (
            ShoppingCart cart,
            List<PromotionRuleBase> rules
        ) GetComplexOptimizationScenario()
        {
            var cart = CreateComplexTestCart();
            var rules = new List<PromotionRuleBase>
            {
                CreateUnifiedGiftRule_Buy2A_Get1B(),
                CreateTieredGiftRule_Progressive(),
                CreateUnifiedDiscountRule_B_2Pieces_80Percent(),
                CreateTieredDiscountRule_Progressive(),
                CreateUnifiedSpecialPriceRule_A_40Yuan(),
                CreateUnifiedCashDiscountRule_100Minus20(),
                CreateProductBuyFreeRule_Buy3C_Free1()
            };
            return (cart, rules);
        }
    }

    #endregion

    #region 边界测试数据

    /// <summary>
    /// 创建边界测试数据
    /// </summary>
    public static class BoundaryTestData
    {
        /// <summary>
        /// 创建最小数量边界测试购物车
        /// </summary>
        public static ShoppingCart CreateMinQuantityCart()
        {
            return CreateCustomCart(
                "CART_MIN_QTY",
                "CUSTOMER_MIN",
                (CreateProductA(), 1),
                (CreateProductB(), 1)
            );
        }

        /// <summary>
        /// 创建最大数量边界测试购物车
        /// </summary>
        public static ShoppingCart CreateMaxQuantityCart()
        {
            return CreateCustomCart(
                "CART_MAX_QTY",
                "CUSTOMER_MAX",
                (CreateProductA(), 999),
                (CreateProductB(), 999),
                (CreateProductC(), 999)
            );
        }

        /// <summary>
        /// 创建零价格商品测试购物车
        /// </summary>
        public static ShoppingCart CreateZeroPriceCart()
        {
            var freeProduct = new Product
            {
                Id = "FREE",
                Name = "免费商品",
                Price = 0.00m,
                Category = "免费"
            };

            return CreateCustomCart(
                "CART_ZERO_PRICE",
                "CUSTOMER_ZERO",
                (freeProduct, 1),
                (CreateProductA(), 1)
            );
        }

        /// <summary>
        /// 创建高价商品测试购物车
        /// </summary>
        public static ShoppingCart CreateHighPriceCart()
        {
            var expensiveProduct = new Product
            {
                Id = "EXPENSIVE",
                Name = "昂贵商品",
                Price = 99999.99m,
                Category = "奢侈品"
            };

            return CreateCustomCart("CART_HIGH_PRICE", "CUSTOMER_HIGH", (expensiveProduct, 1));
        }
    }

    #endregion

    #region 性能测试数据

    /// <summary>
    /// 创建性能测试数据
    /// </summary>
    public static class PerformanceTestData
    {
        /// <summary>
        /// 创建大型购物车（用于性能测试）
        /// </summary>
        public static ShoppingCart CreateLargeCart(int itemCount = 50)
        {
            var cart = new ShoppingCart
            {
                Id = $"CART_LARGE_{itemCount}",
                CustomerId = "CUSTOMER_PERF",
                Items = new List<CartItem>()
            };

            var products = GetAllStandardProducts();
            var random = new Random(42); // 固定种子确保可重复性

            for (int i = 0; i < itemCount; i++)
            {
                var product = products[i % products.Count];
                var quantity = random.Next(1, 10);

                cart.Items.Add(
                    new CartItem
                    {
                        Product = new Product
                        {
                            Id = $"{product.Id}_{i}",
                            Name = $"{product.Name}_{i}",
                            Price = product.Price + (decimal)(random.NextDouble() * 10),
                            Category = product.Category,
                            Brand = product.Brand
                        },
                        Quantity = quantity,
                        UnitPrice = product.Price
                    }
                );
            }

            cart.InitializeActualPrices();
            return cart;
        }

        /// <summary>
        /// 创建大量规则（用于性能测试）
        /// </summary>
        public static List<PromotionRuleBase> CreateManyRules(int ruleCount = 20)
        {
            var rules = new List<PromotionRuleBase>();
            var random = new Random(42);

            for (int i = 0; i < ruleCount; i++)
            {
                // 随机创建不同类型的规则
                var ruleType = i % 4;
                switch (ruleType)
                {
                    case 0:
                        rules.Add(CreateRandomDiscountRule(i, random));
                        break;
                    case 1:
                        rules.Add(CreateRandomGiftRule(i, random));
                        break;
                    case 2:
                        //rules.Add(CreateRandomSpecialPriceRule(i, random));
                        break;
                    case 3:
                        rules.Add(CreateRandomCashDiscountRule(i, random));
                        break;
                }
            }

            return rules;
        }

        private static UnifiedDiscountRule CreateRandomDiscountRule(int index, Random random)
        {
            var products = GetAllStandardProducts();
            var product = products[index % products.Count];

            return new UnifiedDiscountRule
            {
                Id = $"PERF_DISCOUNT_{index}",
                Name = $"性能测试折扣规则{index}",
                Priority = random.Next(1, 100),
                IsEnabled = true,
                ApplicableProductIds = [product.Id],
                MinQuantity = random.Next(1, 5), // 1-4件
                DiscountRate = 0.7m + (decimal)(random.NextDouble() * 0.2), // 0.7-0.9
            };
        }

        private static UnifiedGiftRule CreateRandomGiftRule(int index, Random random)
        {
            var products = GetAllStandardProducts();
            var buyProduct = products[index % products.Count];
            var giftProduct = products[(index + 1) % products.Count];

            return new UnifiedGiftRule
            {
                Id = $"PERF_GIFT_{index}",
                Name = $"性能测试买赠规则{index}",
                Priority = random.Next(1, 100),
                IsEnabled = true,
                ApplicableProductIds = [buyProduct.Id],
                MinQuantity = random.Next(1, 3), // 1-2件
                GiftProductIds = [giftProduct.Id],
                GiftQuantity = 1,
            };
        }



        private static UnifiedCashDiscountRule CreateRandomCashDiscountRule(
            int index,
            Random random
        )
        {
            return new UnifiedCashDiscountRule
            {
                Id = $"PERF_CASH_{index}",
                Name = $"性能测试减现规则{index}",
                Priority = random.Next(1, 100),
                IsEnabled = true,
                MinAmount = 50m + (decimal)(random.NextDouble() * 200), // 50-250
                DiscountAmount = 5m + (decimal)(random.NextDouble() * 20), // 5-25
            };
        }
    }

    #endregion

    #region 性能测试辅助方法

    /// <summary>
    /// 创建大型测试购物车
    /// </summary>
    public static ShoppingCart CreateLargeTestCart(int itemCount)
    {
        var products = GetAllStandardProducts();
        var random = new Random(42); // 固定种子确保可重复性
        var cart = new ShoppingCart
        {
            Id = "LARGE_TEST_CART",
            CustomerId = "PERF_CUSTOMER",
            Items = []
        };

        for (int i = 0; i < itemCount; i++)
        {
            var product = products[i % products.Count];
            cart.Items.Add(
                new CartItem
                {
                    Product = product,
                    Quantity = random.Next(1, 5),
                    UnitPrice = product.Price
                }
            );
        }

        cart.InitializeActualPrices();
        return cart;
    }

    #endregion

    #region 换购规则测试数据

    public static UnifiedSpecialPriceExchangeRule CreateUnifiedSpecialPriceExchangeRule_BuyA_Add1_ExchangeB()
    {
        return new UnifiedSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_SPECIAL_PRICE_001",
            Name = "买A商品1元换购B商品",
            Description = "购买A商品1件，可1元换购B商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["B"], AddAmount = 1.00m }]
        };
    }

    public static UnifiedSpecialPriceExchangeRule CreateUnifiedSpecialPriceExchangeRule_QuantityBased()
    {
        return new UnifiedSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_SPECIAL_PRICE_001B",
            Name = "买A商品2件1元换购B商品",
            Description = "购买A商品2件，可1元换购B商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 2 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["B"], AddAmount = 1.00m }]
        };
    }

    public static UnifiedSpecialPriceExchangeRule CreateUnifiedSpecialPriceExchangeRule_AmountBased()
    {
        return new UnifiedSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_SPECIAL_PRICE_002",
            Name = "买A商品满100元1元换购C商品",
            Description = "购买A商品满100元，可1元换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredAmount = 100.00m }],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], AddAmount = 1.00m }]
        };
    }

    public static UnifiedSpecialPriceExchangeRule CreateUnifiedSpecialPriceExchangeRule_Repeatable()
    {
        return new UnifiedSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_SPECIAL_PRICE_003",
            Name = "可重复的特价换购",
            Description = "购买A商品满1件，可1元换购C商品（可重复）",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = true,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], AddAmount = 1.00m }]
        };
    }

    public static UnifiedSpecialPriceExchangeRule CreateUnifiedSpecialPriceExchangeRule_Disabled()
    {
        return new UnifiedSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_SPECIAL_PRICE_004",
            Name = "已禁用的特价换购",
            Description = "已禁用的特价换购规则",
            IsEnabled = false,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], AddAmount = 1.00m }]
        };
    }

    public static UnifiedSpecialPriceExchangeRule CreateUnifiedSpecialPriceExchangeRule_CustomerBenefit()
    {
        return new UnifiedSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_SPECIAL_PRICE_005",
            Name = "客户利益最大化特价换购",
            Description = "购买A商品1件，可1元换购B或C商品（客户利益最大化）",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["B", "C"], AddAmount = 1.00m }]
        };
    }

    public static UnifiedSpecialPriceExchangeRule CreateUnifiedSpecialPriceExchangeRule_MerchantBenefit()
    {
        return new UnifiedSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_SPECIAL_PRICE_006",
            Name = "商户利益最大化特价换购",
            Description = "购买A商品1件，可1元换购B或C商品（商户利益最大化）",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.MerchantBenefit,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["B", "C"], AddAmount = 1.00m }]
        };
    }

    public static UnifiedSpecialPriceExchangeRule CreateUnifiedSpecialPriceExchangeRule_FreeExchange()
    {
        return new UnifiedSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_SPECIAL_PRICE_007",
            Name = "免费换购",
            Description = "购买A商品1件，可免费换购B商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["B"], AddAmount = 0.00m }]
        };
    }

    public static UnifiedDiscountExchangeRule CreateUnifiedDiscountExchangeRule_AmountBased()
    {
        return new UnifiedDiscountExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_DISCOUNT_002",
            Name = "买A商品满100元9折换购C商品",
            Description = "购买A商品满100元，可9折换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredAmount = 100.00m }],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountRate = 0.9m }]
        };
    }

    public static UnifiedDiscountExchangeRule CreateUnifiedDiscountExchangeRule_Disabled()
    {
        return new UnifiedDiscountExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_DISCOUNT_003",
            Name = "已禁用的折扣换购",
            Description = "已禁用的折扣换购规则",
            IsEnabled = false,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountRate = 0.9m }]
        };
    }

    public static UnifiedDiscountExchangeRule CreateUnifiedDiscountExchangeRule_BuyA_90Percent_ExchangeB()
    {
        return new UnifiedDiscountExchangeRule
        {
            Id = "EXCHANGE_DISCOUNT_001",
            Name = "买A商品9折换购B商品",
            Description = "购买A商品大于等于1件时，可以0.9折换购B商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["B"], DiscountRate = 0.9m }]
        };
    }

    public static UnifiedDiscountExchangeRule CreateUnifiedDiscountExchangeRule_QuantityBased()
    {
        return new UnifiedDiscountExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_DISCOUNT_004",
            Name = "买A商品满2件9折换购B商品",
            Description = "购买A商品满2件，可9折换购B商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 2 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["B"], DiscountRate = 0.9m }]
        };
    }

    public static UnifiedDiscountExchangeRule CreateUnifiedDiscountExchangeRule_50Percent()
    {
        return new UnifiedDiscountExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_DISCOUNT_005",
            Name = "买A商品5折换购B商品",
            Description = "购买A商品大于等于1件时，可以5折换购B商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["B"], DiscountRate = 0.5m }]
        };
    }

    public static UnifiedDiscountExchangeRule CreateUnifiedDiscountExchangeRule_Repeatable()
    {
        return new UnifiedDiscountExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_DISCOUNT_006",
            Name = "可重复的折扣换购",
            Description = "购买A商品每1件，可9折换购1件B商品（可重复）",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = true,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["B"], DiscountRate = 0.9m }]
        };
    }

    public static UnifiedDiscountExchangeRule CreateUnifiedDiscountExchangeRule_NonRepeatable()
    {
        return new UnifiedDiscountExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_DISCOUNT_007",
            Name = "不可重复的折扣换购",
            Description = "购买A商品大于等于1件时，可9折换购1件B商品（不可重复）",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["B"], DiscountRate = 0.9m }]
        };
    }

    public static UnifiedDiscountExchangeRule CreateUnifiedDiscountExchangeRule_CustomerBenefit()
    {
        return new UnifiedDiscountExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_DISCOUNT_008",
            Name = "客户利益最大化折扣换购",
            Description = "购买A商品大于等于1件时，可9折换购B或D商品（客户利益最大化）",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["B", "D"], DiscountRate = 0.9m }]
        };
    }

    public static UnifiedDiscountExchangeRule CreateUnifiedDiscountExchangeRule_MerchantBenefit()
    {
        return new UnifiedDiscountExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_DISCOUNT_009",
            Name = "商户利益最大化折扣换购",
            Description = "购买A商品大于等于1件时，可9折换购B或D商品（商户利益最大化）",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.MerchantBenefit,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["B", "D"], DiscountRate = 0.9m }]
        };
    }

    public static UnifiedDiscountExchangeRule CreateUnifiedDiscountExchangeRule_ZeroDiscount()
    {
        return new UnifiedDiscountExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_DISCOUNT_010",
            Name = "零折扣换购",
            Description = "购买A商品大于等于1件时，可0折换购B商品（无效配置）",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["B"], DiscountRate = 0m }]
        };
    }

    public static UnifiedDiscountExchangeRule CreateUnifiedDiscountExchangeRule_FreeExchange()
    {
        return new UnifiedDiscountExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_DISCOUNT_011",
            Name = "免费换购",
            Description = "购买A商品大于等于1件时，可免费换购B商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["B"], DiscountRate = 0m }]
        };
    }

    public static UnifiedDiscountAmountExchangeRule CreateUnifiedDiscountAmountExchangeRule_AmountBased()
    {
        return new UnifiedDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_DISCOUNT_AMOUNT_002",
            Name = "买A商品满100元减5元换购C商品",
            Description = "购买A商品满100元，可减5元换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredAmount = 100.00m }],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountAmount = 5.00m }]
        };
    }

    public static UnifiedDiscountAmountExchangeRule CreateUnifiedDiscountAmountExchangeRule_Disabled()
    {
        return new UnifiedDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_UNIFIED_DISCOUNT_AMOUNT_003",
            Name = "已禁用的定额减换购",
            Description = "已禁用的定额减换购规则",
            IsEnabled = false,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountAmount = 1.00m }]
        };
    }

    public static UnifiedDiscountAmountExchangeRule CreateUnifiedDiscountAmountExchangeRule_BuyA_Minus100_ExchangeB()
    {
        return new UnifiedDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_DISCOUNT_AMOUNT_001",
            Name = "买A商品减100元换购B商品",
            Description = "购买A商品大于等于1件时，可以优惠100元购买B商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions =
            [
                new() { ExchangeProductIds = ["B", "D"], DiscountAmount = 100.00m }
            ]
        };
    }

    public static UnifiedDiscountAmountExchangeRule CreateUnifiedDiscountAmountExchangeRule_BuyA_Minus20_ExchangeB()
    {
        return new UnifiedDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_DISCOUNT_AMOUNT_004",
            Name = "买A商品减20元换购B商品",
            Description = "购买A商品大于等于1件时，可以优惠20元购买B商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["B"], DiscountAmount = 20.00m }]
        };
    }

    public static UnifiedDiscountAmountExchangeRule CreateUnifiedDiscountAmountExchangeRule_QuantityBased()
    {
        return new UnifiedDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_DISCOUNT_AMOUNT_005",
            Name = "买A商品2件减10元换购B商品",
            Description = "购买A商品大于等于2件时，可以优惠10元购买B商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 2 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["B"], DiscountAmount = 10.00m }]
        };
    }

    public static UnifiedDiscountAmountExchangeRule CreateUnifiedDiscountAmountExchangeRule_ZeroDiscount()
    {
        return new UnifiedDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_DISCOUNT_AMOUNT_006",
            Name = "零优惠金额换购",
            Description = "购买A商品大于等于1件时，可以优惠0元购买B商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["B"], DiscountAmount = 0.00m }]
        };
    }

    public static UnifiedDiscountAmountExchangeRule CreateUnifiedDiscountAmountExchangeRule_Repeatable()
    {
        return new UnifiedDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_DISCOUNT_AMOUNT_007",
            Name = "可重复的优惠换购",
            Description = "购买A商品大于等于1件时，可以优惠10元购买B商品（可重复）",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = true,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["B"], DiscountAmount = 10.00m }]
        };
    }

    public static UnifiedDiscountAmountExchangeRule CreateUnifiedDiscountAmountExchangeRule_NonRepeatable()
    {
        return new UnifiedDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_DISCOUNT_AMOUNT_008",
            Name = "不可重复的优惠换购",
            Description = "购买A商品大于等于1件时，可以优惠15元购买B商品（不可重复）",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions = [new() { ExchangeProductIds = ["B"], DiscountAmount = 15.00m }]
        };
    }

    public static UnifiedDiscountAmountExchangeRule CreateUnifiedDiscountAmountExchangeRule_CustomerBenefit()
    {
        return new UnifiedDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_DISCOUNT_AMOUNT_009",
            Name = "客户利益最大化优惠换购",
            Description = "购买A商品大于等于1件时，可以优惠50元购买B或D商品（客户利益最大化）",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions =
            [
                new() { ExchangeProductIds = ["B", "D"], DiscountAmount = 50.00m }
            ]
        };
    }

    public static UnifiedDiscountAmountExchangeRule CreateUnifiedDiscountAmountExchangeRule_MerchantBenefit()
    {
        return new UnifiedDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_DISCOUNT_AMOUNT_010",
            Name = "商户利益最大化优惠换购",
            Description = "购买A商品大于等于1件时，可以优惠50元购买B或D商品（商户利益最大化）",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.MerchantBenefit,
            BuyConditions = [new() { ProductIds = ["A"], RequiredQuantity = 1 }],
            ExchangeConditions =
            [
                new() { ExchangeProductIds = ["B", "D"], DiscountAmount = 50.00m }
            ]
        };
    }

    public static CombinationSpecialPriceExchangeRule CreateCombinationSpecialPriceExchangeRule_AmountBased()
    {
        return new CombinationSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_SPECIAL_PRICE_002",
            Name = "买A+B商品满金额100元换购C商品",
            Description = "购买A商品满100元、B商品满50元，可100元换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredAmount = 100.00m },
                new() { ProductId = ["B"], RequiredAmount = 50.00m }
            ],

            ExchangeConditions = [new() { ExchangeProductIds = ["C"], AddAmount = 100.00m }]
        };
    }

    public static CombinationSpecialPriceExchangeRule CreateCombinationSpecialPriceExchangeRule_Disabled()
    {
        return new CombinationSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_SPECIAL_PRICE_003",
            Name = "已禁用的组合特价换购",
            Description = "已禁用的组合特价换购规则",
            IsEnabled = false,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], AddAmount = 100.00m }]
        };
    }

    public static CombinationSpecialPriceExchangeRule CreateCombinationSpecialPriceExchangeRule_BuyAB_Add100_ExchangeC()
    {
        return new CombinationSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_SPECIAL_PRICE_001",
            Name = "买A+B商品加100元换购C商品",
            Description = "购买A商品1件、B商品1件，可加100元换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions =
            [
                new()
                {
                    ExchangeProductIds = ["C"],
                    AddAmount = 100.00m,
                    ExchangeQuantity = 1
                }
            ]
        };
    }

    public static CombinationSpecialPriceExchangeRule CreateCombinationSpecialPriceExchangeRule_BuyAB_Add10_ExchangeC()
    {
        return new CombinationSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_SPECIAL_PRICE_004",
            Name = "买A+B商品加10元换购C商品",
            Description = "购买A商品1件、B商品1件，可加10元换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions =
            [
                new()
                {
                    ExchangeProductIds = ["C"],
                    AddAmount = 10.00m,
                    ExchangeQuantity = 1
                }
            ]
        };
    }

    public static CombinationSpecialPriceExchangeRule CreateCombinationSpecialPriceExchangeRule_QuantityBased()
    {
        return new CombinationSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_SPECIAL_PRICE_005",
            Name = "基于数量的组合特价换购",
            Description = "购买A商品2件、B商品3件，可加50元换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 2 },
                new() { ProductId = ["B"], RequiredQuantity = 3 }
            ],
            ExchangeConditions =
            [
                new()
                {
                    ExchangeProductIds = ["C"],
                    AddAmount = 50.00m,
                    ExchangeQuantity = 1
                }
            ]
        };
    }

    public static CombinationSpecialPriceExchangeRule CreateCombinationSpecialPriceExchangeRule_FreeExchange()
    {
        return new CombinationSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_SPECIAL_PRICE_006",
            Name = "免费换购组合规则",
            Description = "购买A商品1件、B商品1件，可免费换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions =
            [
                new()
                {
                    ExchangeProductIds = ["C"],
                    AddAmount = 0.00m,
                    ExchangeQuantity = 1
                }
            ]
        };
    }

    public static CombinationSpecialPriceExchangeRule CreateCombinationSpecialPriceExchangeRule_Repeatable()
    {
        return new CombinationSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_SPECIAL_PRICE_007",
            Name = "可重复的组合特价换购",
            Description = "购买A商品1件、B商品1件，可加100元换购C商品（可重复）",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = true,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions =
            [
                new()
                {
                    ExchangeProductIds = ["C"],
                    AddAmount = 100.00m,
                    ExchangeQuantity = 1
                }
            ]
        };
    }

    public static CombinationSpecialPriceExchangeRule CreateCombinationSpecialPriceExchangeRule_NonRepeatable()
    {
        return new CombinationSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_SPECIAL_PRICE_008",
            Name = "不可重复的组合特价换购",
            Description = "购买A商品1件、B商品1件，可加100元换购C商品（不可重复）",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions =
            [
                new()
                {
                    ExchangeProductIds = ["C"],
                    AddAmount = 100.00m,
                    ExchangeQuantity = 1
                }
            ]
        };
    }

    public static CombinationSpecialPriceExchangeRule CreateCombinationSpecialPriceExchangeRule_MultipleExchangeOptions()
    {
        return new CombinationSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_SPECIAL_PRICE_009",
            Name = "多种换购选择的组合规则",
            Description = "购买A商品1件、B商品1件，可选择换购C或D商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions =
            [
                new()
                {
                    ExchangeProductIds = ["C"],
                    AddAmount = 100.00m,
                    ExchangeQuantity = 1
                },
                new()
                {
                    ExchangeProductIds = ["D"],
                    AddAmount = 150.00m,
                    ExchangeQuantity = 1
                }
            ]
        };
    }

    public static CombinationSpecialPriceExchangeRule CreateCombinationSpecialPriceExchangeRule_ComplexConditions()
    {
        return new CombinationSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_SPECIAL_PRICE_010",
            Name = "复杂条件的组合特价换购",
            Description = "购买A商品2件且满100元、B商品1件且满50元，可加200元换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit,
            CombinationConditions =
            [
                new()
                {
                    ProductId = ["A"],
                    RequiredQuantity = 2,
                    RequiredAmount = 100.00m
                },
                new()
                {
                    ProductId = ["B"],
                    RequiredQuantity = 1,
                    RequiredAmount = 50.00m
                }
            ],
            ExchangeConditions =
            [
                new()
                {
                    ExchangeProductIds = ["C"],
                    AddAmount = 200.00m,
                    ExchangeQuantity = 1
                }
            ]
        };
    }

    public static CombinationSpecialPriceExchangeRule CreateCombinationSpecialPriceExchangeRule_ThreeProductCombo()
    {
        return new CombinationSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_SPECIAL_PRICE_011",
            Name = "三商品组合特价换购",
            Description = "购买A、B、C商品各1件，可加300元换购D商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 },
                new() { ProductId = ["C"], RequiredQuantity = 1 }
            ],
            ExchangeConditions =
            [
                new()
                {
                    ExchangeProductIds = ["D"],
                    AddAmount = 300.00m,
                    ExchangeQuantity = 1
                }
            ]
        };
    }

    public static CombinationSpecialPriceExchangeRule CreateCombinationSpecialPriceExchangeRule_InvalidConfig()
    {
        return new CombinationSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_SPECIAL_PRICE_012",
            Name = "无效配置的组合特价换购",
            Description = "无效配置的组合特价换购规则",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit,
            CombinationConditions = [],
            ExchangeConditions = []
        };
    }

    public static CombinationSpecialPriceExchangeRule CreateCombinationSpecialPriceExchangeRule_EmptyConditions()
    {
        return new CombinationSpecialPriceExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_SPECIAL_PRICE_013",
            Name = "空条件的组合特价换购",
            Description = "空条件的组合特价换购规则",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit,
            CombinationConditions = [],
            ExchangeConditions = []
        };
    }

    // CombinationDiscountExchangeRule 测试数据
    public static CombinationDiscountExchangeRule CreateCombinationDiscountExchangeRule_BuyAB_90Percent_ExchangeC()
    {
        return new CombinationDiscountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_001",
            Name = "买A+B商品9折换购C商品",
            Description = "购买A、B商品各满1件，可9折换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountRate = 0.9m }]
        };
    }

    public static CombinationDiscountExchangeRule CreateCombinationDiscountExchangeRule_BuyAB_50Percent_ExchangeC()
    {
        return new CombinationDiscountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_002",
            Name = "买A+B商品5折换购C商品",
            Description = "购买A、B商品各满1件，可5折换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountRate = 0.5m }]
        };
    }

    public static CombinationDiscountExchangeRule CreateCombinationDiscountExchangeRule_QuantityBased()
    {
        return new CombinationDiscountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_003",
            Name = "基于数量的组合折扣换购",
            Description = "购买A商品满2件、B商品满2件，可9折换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 2 },
                new() { ProductId = ["B"], RequiredQuantity = 2 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountRate = 0.9m }]
        };
    }

    public static CombinationDiscountExchangeRule CreateCombinationDiscountExchangeRule_AmountBased()
    {
        return new CombinationDiscountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_004",
            Name = "基于金额的组合折扣换购",
            Description = "购买A商品满100元、B商品满50元，可9折换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredAmount = 100.00m },
                new() { ProductId = ["B"], RequiredAmount = 50.00m }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountRate = 0.9m }]
        };
    }

    public static CombinationDiscountExchangeRule CreateCombinationDiscountExchangeRule_Disabled()
    {
        return new CombinationDiscountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_005",
            Name = "已禁用的组合折扣换购",
            Description = "已禁用的组合折扣换购规则",
            IsEnabled = false,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountRate = 0.9m }]
        };
    }

    public static CombinationDiscountExchangeRule CreateCombinationDiscountExchangeRule_ZeroDiscount()
    {
        return new CombinationDiscountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_006",
            Name = "零折扣组合换购",
            Description = "零折扣的组合折扣换购规则",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountRate = 0.0m }]
        };
    }

    public static CombinationDiscountExchangeRule CreateCombinationDiscountExchangeRule_FreeExchange()
    {
        return new CombinationDiscountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_007",
            Name = "免费换购组合",
            Description = "100%折扣的组合换购规则",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountRate = 1.0m }]
        };
    }

    public static CombinationDiscountExchangeRule CreateCombinationDiscountExchangeRule_Repeatable()
    {
        return new CombinationDiscountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_008",
            Name = "可重复组合折扣换购",
            Description = "可重复应用的组合折扣换购规则",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = true,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountRate = 0.9m }]
        };
    }

    public static CombinationDiscountExchangeRule CreateCombinationDiscountExchangeRule_NonRepeatable()
    {
        return new CombinationDiscountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_009",
            Name = "不可重复组合折扣换购",
            Description = "不可重复应用的组合折扣换购规则",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountRate = 0.9m }]
        };
    }

    public static CombinationDiscountExchangeRule CreateCombinationDiscountExchangeRule_MultipleExchangeOptions()
    {
        return new CombinationDiscountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_010",
            Name = "多选项组合折扣换购",
            Description = "多个换购选项的组合折扣换购规则",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions =
            [
                new() { ExchangeProductIds = ["C"], DiscountRate = 0.9m },
                new() { ExchangeProductIds = ["D"], DiscountRate = 0.8m },
                new() { ExchangeProductIds = ["E"], DiscountRate = 0.7m }
            ]
        };
    }

    public static CombinationDiscountExchangeRule CreateCombinationDiscountExchangeRule_MerchantBenefit()
    {
        return new CombinationDiscountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_011",
            Name = "商家优先组合折扣换购",
            Description = "商家优先策略的组合折扣换购规则",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.MerchantBenefit,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions =
            [
                new() { ExchangeProductIds = ["C"], DiscountRate = 0.9m }, // 低价商品
                new() { ExchangeProductIds = ["D"], DiscountRate = 0.8m } // 高价商品
            ]
        };
    }

    public static CombinationDiscountExchangeRule CreateCombinationDiscountExchangeRule_ComplexConditions()
    {
        return new CombinationDiscountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_012",
            Name = "复杂条件组合折扣换购",
            Description = "复杂组合条件的折扣换购规则",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new()
                {
                    ProductId = ["A"],
                    RequiredQuantity = 2,
                    RequiredAmount = 100.00m
                },
                new()
                {
                    ProductId = ["B"],
                    RequiredQuantity = 1,
                    RequiredAmount = 50.00m
                }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountRate = 0.8m }]
        };
    }

    public static CombinationDiscountExchangeRule CreateCombinationDiscountExchangeRule_ThreeProductCombo()
    {
        return new CombinationDiscountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_013",
            Name = "三商品组合折扣换购",
            Description = "三个商品组合的折扣换购规则",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 },
                new() { ProductId = ["C"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["D"], DiscountRate = 0.9m }]
        };
    }

    public static CombinationDiscountExchangeRule CreateCombinationDiscountExchangeRule_InvalidConfig()
    {
        return new CombinationDiscountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_014",
            Name = "无效配置组合折扣换购",
            Description = "无效配置的组合折扣换购规则",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions = [], // 空条件
            ExchangeConditions = []
        };
    }

    public static CombinationDiscountExchangeRule CreateCombinationDiscountExchangeRule_EdgeDiscountRate()
    {
        return new CombinationDiscountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_015",
            Name = "边界折扣率组合换购",
            Description = "边界折扣率的组合折扣换购规则",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountRate = 0.01m }] // 1%折扣
        };
    }

    public static CombinationDiscountExchangeRule CreateCombinationDiscountExchangeRule_EmptyConditions()
    {
        return new CombinationDiscountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_016",
            Name = "空条件组合折扣换购",
            Description = "空组合条件的折扣换购规则",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions = [],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountRate = 0.9m }]
        };
    }

    // CombinationDiscountAmountExchangeRule 测试数据
    public static CombinationDiscountAmountExchangeRule CreateCombinationDiscountAmountExchangeRule_BuyAB_1Yuan_OffC()
    {
        return new CombinationDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_AMOUNT_001",
            Name = "买A+B商品减1元换购C商品",
            Description = "购买A、B商品各满1件，可减1元换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountAmount = 1.00m }]
        };
    }

    public static CombinationDiscountAmountExchangeRule CreateCombinationDiscountAmountExchangeRule_BuyAB_5Yuan_OffC()
    {
        return new CombinationDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_AMOUNT_002",
            Name = "买A+B商品减5元换购C商品",
            Description = "购买A、B商品各满1件，可减5元换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountAmount = 5.00m }]
        };
    }

    public static CombinationDiscountAmountExchangeRule CreateCombinationDiscountAmountExchangeRule_QuantityBased()
    {
        return new CombinationDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_AMOUNT_003",
            Name = "基于数量的组合定额减换购",
            Description = "购买A商品满2件、B商品满2件，可减2元换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 2 },
                new() { ProductId = ["B"], RequiredQuantity = 2 }
            ],
            ExchangeConditions =
            [
                new()
                {
                    ExchangeProductIds = ["C"],
                    DiscountAmount = 2.00m,
                    ExchangeQuantity = 1
                }
            ]
        };
    }

    public static CombinationDiscountAmountExchangeRule CreateCombinationDiscountAmountExchangeRule_AmountBased()
    {
        return new CombinationDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_AMOUNT_004",
            Name = "基于金额的组合定额减换购",
            Description = "购买A商品满100元、B商品满50元，可减5元换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredAmount = 100.00m },
                new() { ProductId = ["B"], RequiredAmount = 50.00m }
            ],
            ExchangeConditions =
            [
                new()
                {
                    ExchangeProductIds = ["C"],
                    DiscountAmount = 5.00m,
                    ExchangeQuantity = 1
                }
            ]
        };
    }

    public static CombinationDiscountAmountExchangeRule CreateCombinationDiscountAmountExchangeRule_Disabled()
    {
        return new CombinationDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_AMOUNT_005",
            Name = "已禁用的组合定额减换购",
            Description = "已禁用的组合定额减换购规则",
            IsEnabled = false,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions =
            [
                new()
                {
                    ExchangeProductIds = ["C"],
                    DiscountAmount = 1.00m,
                    ExchangeQuantity = 1
                }
            ]
        };
    }

    public static CombinationDiscountAmountExchangeRule CreateCombinationDiscountAmountExchangeRule_BuyAB_50Yuan_OffC()
    {
        return new CombinationDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_AMOUNT_006",
            Name = "买A+B商品减50元换购C商品",
            Description = "购买A、B商品各满1件，可减50元换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountAmount = 50.00m }]
        };
    }

    public static CombinationDiscountAmountExchangeRule CreateCombinationDiscountAmountExchangeRule_ZeroDiscount()
    {
        return new CombinationDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_AMOUNT_007",
            Name = "零减免金额组合换购",
            Description = "购买A、B商品各满1件，减免0元换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountAmount = 0m }]
        };
    }

    public static CombinationDiscountAmountExchangeRule CreateCombinationDiscountAmountExchangeRule_NegativeDiscount()
    {
        return new CombinationDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_AMOUNT_008",
            Name = "负减免金额组合换购",
            Description = "购买A、B商品各满1件，减免-1元换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountAmount = -1.00m }]
        };
    }

    public static CombinationDiscountAmountExchangeRule CreateCombinationDiscountAmountExchangeRule_Repeatable()
    {
        return new CombinationDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_AMOUNT_009",
            Name = "可重复组合定额减换购",
            Description = "购买A、B商品各满1件，可重复减1元换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = true,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountAmount = 1.00m }]
        };
    }

    public static CombinationDiscountAmountExchangeRule CreateCombinationDiscountAmountExchangeRule_NonRepeatable()
    {
        return new CombinationDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_AMOUNT_010",
            Name = "不可重复组合定额减换购",
            Description = "购买A、B商品各满1件，仅可一次减1元换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountAmount = 1.00m }]
        };
    }

    public static CombinationDiscountAmountExchangeRule CreateCombinationDiscountAmountExchangeRule_MultipleExchangeOptions()
    {
        return new CombinationDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_AMOUNT_011",
            Name = "多种换购选择组合定额减",
            Description = "购买A、B商品各满1件，可减2元换购C、D、E商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions =
            [
                new() { ExchangeProductIds = ["C", "D", "E"], DiscountAmount = 2.00m }
            ]
        };
    }

    public static CombinationDiscountAmountExchangeRule CreateCombinationDiscountAmountExchangeRule_MerchantBenefit()
    {
        return new CombinationDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_AMOUNT_012",
            Name = "商家利益最大化组合定额减",
            Description = "购买A、B商品各满1件，可减2元换购C、D、E商品（商家利益最大化）",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            ExchangeSelectionStrategy = BenefitSelectionStrategy.MerchantBenefit,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions =
            [
                new() { ExchangeProductIds = ["C", "D", "E"], DiscountAmount = 2.00m }
            ]
        };
    }

    public static CombinationDiscountAmountExchangeRule CreateCombinationDiscountAmountExchangeRule_ComplexConditions()
    {
        return new CombinationDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_AMOUNT_013",
            Name = "复杂组合条件定额减换购",
            Description = "购买A商品满2件且B商品满80元，可减3元换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 2 },
                new() { ProductId = ["B"], RequiredAmount = 80.00m }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountAmount = 3.00m }]
        };
    }

    public static CombinationDiscountAmountExchangeRule CreateCombinationDiscountAmountExchangeRule_ThreeProductCombo()
    {
        return new CombinationDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_AMOUNT_014",
            Name = "三商品组合定额减换购",
            Description = "购买A、B、C商品各满1件，可减2元换购D商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 },
                new() { ProductId = ["C"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["D"], DiscountAmount = 2.00m }]
        };
    }

    public static CombinationDiscountAmountExchangeRule CreateCombinationDiscountAmountExchangeRule_SmallDiscount()
    {
        return new CombinationDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_AMOUNT_015",
            Name = "小额减免组合换购",
            Description = "购买A、B商品各满1件，可减0.5元换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountAmount = 0.50m }]
        };
    }

    public static CombinationDiscountAmountExchangeRule CreateCombinationDiscountAmountExchangeRule_InvalidConfig()
    {
        return new CombinationDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_AMOUNT_016",
            Name = "无效配置组合换购",
            Description = "无效配置的组合定额减换购规则",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions = [], // 空条件
            ExchangeConditions = []
        };
    }

    public static CombinationDiscountAmountExchangeRule CreateCombinationDiscountAmountExchangeRule_EmptyConditions()
    {
        return new CombinationDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_AMOUNT_017",
            Name = "空组合条件换购",
            Description = "空组合条件的定额减换购规则",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions = [],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountAmount = 1.00m }]
        };
    }

    public static CombinationDiscountAmountExchangeRule CreateCombinationDiscountAmountExchangeRule_LargeDiscount()
    {
        return new CombinationDiscountAmountExchangeRule
        {
            Id = "EXCHANGE_COMBINATION_DISCOUNT_AMOUNT_018",
            Name = "大额减免组合换购",
            Description = "购买A、B商品各满1件，可减100元换购C商品",
            IsEnabled = true,
            Priority = 100,
            IsRepeatable = false,
            CombinationConditions =
            [
                new() { ProductId = ["A"], RequiredQuantity = 1 },
                new() { ProductId = ["B"], RequiredQuantity = 1 }
            ],
            ExchangeConditions = [new() { ExchangeProductIds = ["C"], DiscountAmount = 100.00m }]
        };
    }

    #endregion
}
