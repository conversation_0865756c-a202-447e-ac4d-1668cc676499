### 买赠促销分摊逻辑测试

### 1. 同商品买赠测试 - 买2赠1（商品A）
### 预期结果：购买2件A商品保持原价100元，赠送1件A商品价格为0
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "SAME_PRODUCT_BUY_GET_TEST_001",
  "customerId": "CUSTOMER_001",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 3,
      "unitPrice": 100.00
    }
  ]
}

### 2. 不同商品买赠测试 - 买A送B
### 预期结果：购买1件A商品保持原价100元，赠送1件B商品价格为0
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "DIFFERENT_PRODUCT_BUY_GET_TEST_001",
  "customerId": "CUSTOMER_002",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 1,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 50.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 50.00
    }
  ]
}

### 3. 复杂买赠测试 - 买A送B，同时买2赠1（商品C）
### 预期结果：A保持原价，B作为赠品价格为0，C拆分为2件原价+1件赠品
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "COMPLEX_BUY_GET_TEST_001",
  "customerId": "CUSTOMER_003",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 1,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 50.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 50.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 30.00,
        "category": "家居"
      },
      "quantity": 3,
      "unitPrice": 30.00
    }
  ]
}

### 4. 边界条件测试 - 买3赠1，但只有3件商品
### 预期结果：3件商品拆分为2件原价+1件赠品
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "BOUNDARY_BUY_GET_TEST_001",
  "customerId": "CUSTOMER_004",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 3,
      "unitPrice": 100.00
    }
  ]
}

### 5. 多次买赠测试 - 买6件A，可以触发2次买2赠1
### 预期结果：6件商品拆分为4件原价+2件赠品
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "MULTIPLE_BUY_GET_TEST_001",
  "customerId": "CUSTOMER_005",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 6,
      "unitPrice": 100.00
    }
  ]
}

### 6. 梯度买赠测试 - 购买A商品触发梯度赠品
### 预期结果：A保持原价，根据梯度赠送B或C
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "GRADIENT_BUY_GET_TEST_001",
  "customerId": "CUSTOMER_006",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 2,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 30.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 30.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 90.00,
        "category": "家居"
      },
      "quantity": 1,
      "unitPrice": 90.00
    }
  ]
}

### 7. 组合买赠测试 - 购买A+B组合送C
### 预期结果：A和B保持原价，C作为赠品价格为0
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "COMBINATION_BUY_GET_TEST_001",
  "customerId": "CUSTOMER_007",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 1,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 80.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 80.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 40.00,
        "category": "家居"
      },
      "quantity": 1,
      "unitPrice": 40.00
    }
  ]
}

### 8. 赠品选择策略测试 - 商家利益最大化
### 预期结果：A保持原价，从B、C、D中选择价值最低的作为赠品
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "GIFT_STRATEGY_TEST_001",
  "customerId": "CUSTOMER_008",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 1,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B（低价值）",
        "price": 50.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 50.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C（中价值）",
        "price": 80.00,
        "category": "家居"
      },
      "quantity": 1,
      "unitPrice": 80.00
    },
    {
      "product": {
        "id": "D",
        "name": "商品D（高价值）",
        "price": 120.00,
        "category": "数码"
      },
      "quantity": 1,
      "unitPrice": 120.00
    }
  ]
}

### 验证要点：
### 1. 购买的商品应该保持原价不变
### 2. 赠品应该作为单独的购物车项，价格为0
### 3. 同商品买赠应该正确拆分（如买2赠1变成2件原价+1件赠品）
### 4. 总优惠金额应该等于赠品的价值
### 5. 购物车中应该能清楚区分购买商品和赠品
### 6. 促销详情应该正确记录购买条件和赠品信息
