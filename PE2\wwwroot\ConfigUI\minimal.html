<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>促销配置系统 - 最小化版本</title>
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3.3.8/dist/vue.global.js"></script>
    
    <!-- 基础样式 -->
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
        }
        .promotion-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }
        .type-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .type-card:hover {
            border-color: #3b82f6;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }
        .type-card.selected {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        .form-section {
            margin-bottom: 24px;
            padding: 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #374151;
        }
        .form-item {
            margin-bottom: 16px;
        }
        .form-item label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            color: #374151;
        }
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .btn {
            padding: 8px 16px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #2563eb;
        }
        .json-preview {
            background: #1f2937;
            color: #f9fafb;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            max-height: 600px;
            overflow-y: auto;
        }
        .error {
            color: #ef4444;
            background: #fef2f2;
            border: 1px solid #fecaca;
            padding: 12px;
            border-radius: 6px;
            margin: 16px 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div>
                <h1>促销规则配置系统</h1>
                
                <div v-if="error" class="error">
                    {{ error }}
                </div>
                
                <div v-if="!promotionTypesLoaded" class="error">
                    配置文件未加载。请检查 js/config/promotionTypes.js 文件。
                </div>
                
                <div v-if="promotionTypesLoaded">
                    <h3>选择促销类型</h3>
                    <div class="promotion-types">
                        <div 
                            v-for="(category, categoryKey) in promotionTypes" 
                            :key="categoryKey"
                            class="type-card"
                            :class="{ selected: selectedCategory === categoryKey }"
                            @click="selectCategory(categoryKey, category)"
                        >
                            <div style="font-size: 24px; margin-bottom: 8px;">{{ category.icon }}</div>
                            <div style="font-weight: 600; margin-bottom: 4px;">{{ category.name }}</div>
                            <div style="font-size: 12px; color: #6b7280;">
                                {{ Object.keys(category.types).length }} 个类型
                            </div>
                        </div>
                    </div>
                    
                    <div v-if="selectedCategory && selectedTypeData">
                        <div class="form-section">
                            <div class="section-title">{{ selectedTypeData.name }}</div>
                            <p style="color: #6b7280; margin-bottom: 16px;">{{ selectedTypeData.description }}</p>
                            
                            <div class="form-item">
                                <label>规则ID *</label>
                                <input 
                                    type="text" 
                                    class="form-input"
                                    v-model="formData.id"
                                    placeholder="输入唯一的规则ID"
                                />
                            </div>
                            
                            <div class="form-item">
                                <label>规则名称 *</label>
                                <input 
                                    type="text" 
                                    class="form-input"
                                    v-model="formData.name"
                                    placeholder="输入规则名称"
                                />
                            </div>
                            
                            <div class="form-item">
                                <label>描述</label>
                                <textarea 
                                    class="form-input"
                                    v-model="formData.description"
                                    placeholder="输入规则描述"
                                    rows="3"
                                ></textarea>
                            </div>
                            
                            <button class="btn" @click="generateConfig">生成配置</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div>
                <h3>配置预览</h3>
                <pre class="json-preview">{{ jsonPreview }}</pre>
            </div>
        </div>
    </div>

    <!-- 加载配置 -->
    <script src="./js/config/promotionTypes.js"></script>
    
    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    error: null,
                    promotionTypesLoaded: false,
                    promotionTypes: {},
                    selectedCategory: null,
                    selectedTypeKey: null,
                    selectedTypeData: null,
                    formData: {
                        id: '',
                        name: '',
                        description: ''
                    }
                };
            },
            computed: {
                jsonPreview() {
                    if (!this.selectedTypeData) {
                        return '// 请先选择促销类型';
                    }
                    
                    const config = {
                        ruleType: this.selectedTypeKey,
                        ...this.formData
                    };
                    
                    return JSON.stringify(config, null, 2);
                }
            },
            mounted() {
                try {
                    if (window.PROMOTION_TYPES) {
                        this.promotionTypesLoaded = true;
                        this.promotionTypes = window.PROMOTION_TYPES;
                        console.log('✅ 促销类型配置已加载');
                    } else {
                        this.error = 'PROMOTION_TYPES 配置对象未找到';
                    }
                } catch (e) {
                    this.error = '加载配置时出错: ' + e.message;
                    console.error('配置加载错误:', e);
                }
            },
            methods: {
                selectCategory(categoryKey, category) {
                    this.selectedCategory = categoryKey;
                    // 选择第一个可用的类型
                    const firstTypeKey = Object.keys(category.types)[0];
                    if (firstTypeKey) {
                        this.selectedTypeKey = firstTypeKey;
                        this.selectedTypeData = category.types[firstTypeKey];
                    }
                },
                generateConfig() {
                    if (!this.formData.id || !this.formData.name) {
                        alert('请填写规则ID和名称');
                        return;
                    }
                    
                    const config = {
                        ruleType: this.selectedTypeKey,
                        ...this.formData,
                        priority: 0,
                        isEnabled: true
                    };
                    
                    console.log('生成的配置:', config);
                    
                    // 可以在这里添加导出功能
                    const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `promotion-rule-${this.formData.id}.json`;
                    a.click();
                    URL.revokeObjectURL(url);
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
