<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态表单系统集成测试</title>
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3.3.8/dist/vue.global.js"></script>
    
    <!-- Element Plus UI Framework -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    
    <!-- Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.iife.min.js"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei';
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>动态表单系统 - 组合特价规则测试</h1>
            
            <el-row :gutter="24">
                <el-col :span="8">
                    <h3>促销类型选择</h3>
                    <el-select v-model="selectedRuleType" placeholder="选择促销规则类型" style="width: 100%">
                        <el-option 
                            v-for="option in ruleTypeOptions" 
                            :key="option.value"
                            :label="option.label" 
                            :value="option.value">
                        </el-option>
                    </el-select>
                </el-col>
                <el-col :span="8">
                    <h3>操作</h3>
                    <el-button @click="loadMetadata" type="primary" :loading="loading">
                        加载元数据
                    </el-button>
                    <el-button @click="resetForm" type="default">
                        重置表单
                    </el-button>
                </el-col>
                <el-col :span="8">
                    <h3>状态</h3>
                    <el-tag :type="status === 'success' ? 'success' : status === 'error' ? 'danger' : 'info'">
                        {{ statusText }}
                    </el-tag>
                </el-col>
            </el-row>
            
            <el-divider />
            
            <div v-if="promotionTypes && selectedRuleType">
                <dynamic-form-renderer
                    v-model="formData"
                    :promotion-types="promotionTypes"
                    selected-category="specialPrice"
                    :selected-type="selectedRuleType"
                    mode="create"
                    @submit="handleFormSubmit">
                </dynamic-form-renderer>
            </div>
            
            <div v-else>
                <el-empty description="请选择促销规则类型以开始配置" />
            </div>
            
            <el-divider />
            
            <h3>表单数据预览</h3>
            <pre style="background: #f5f5f5; padding: 16px; border-radius: 4px; overflow: auto; max-height: 400px;">{{ JSON.stringify(formData, null, 2) }}</pre>
        </div>
    </div>

    <!-- 加载组件 -->
    <script src="./js/services/apiService.js"></script>
    <script src="./js/services/metadataService.js"></script>
    <script src="./js/components/ConditionConfigRenderer.js"></script>
    <script src="./js/components/ComplexArrayRenderer.js"></script>
    <script src="./js/components/EnhancedConditionRenderer.js"></script>
    <script src="./js/components/DynamicFormRenderer_template.js"></script>
    
    <script>
        const { createApp } = Vue;
        
        const app = createApp({
            components: {
                'dynamic-form-renderer': DynamicFormRenderer
            },
            setup() {
                const { ref, computed, watch } = Vue;
                
                const loading = ref(false);
                const selectedRuleType = ref('');
                const promotionTypes = ref(null);
                const formData = ref({});
                const metadata = ref(null);
                
                // 规则类型选项
                const ruleTypeOptions = ref([
                    { label: '组合特价规则', value: 'CombinationSpecialPriceRule' },
                    { label: '产品特价规则', value: 'ProductSpecialPriceRule' },
                    { label: '折扣规则', value: 'ProductDiscountRule' },
                    { label: '买赠规则', value: 'ProductBuyGiftRule' },
                    { label: '换购规则', value: 'UnifiedDiscountExchangeRule' }
                ]);
                
                // 状态计算
                const status = computed(() => {
                    if (loading.value) return 'loading';
                    if (promotionTypes.value) return 'success';
                    return 'info';
                });
                
                const statusText = computed(() => {
                    if (loading.value) return '加载中...';
                    if (promotionTypes.value) return '元数据已加载';
                    return '等待加载';
                });
                
                // 加载元数据
                const loadMetadata = async () => {
                    if (!selectedRuleType.value) {
                        ElementPlus.ElMessage.warning('请先选择促销规则类型');
                        return;
                    }
                    
                    loading.value = true;
                    try {
                        // 获取规则类型的详细元数据
                        const response = await fetch(`/api/promotion/metadata/types/${selectedRuleType.value}`);
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        
                        const ruleMetadata = await response.json();
                        console.log('Rule metadata loaded:', ruleMetadata);
                        
                        // 构造促销类型数据结构
                        promotionTypes.value = {
                            specialPrice: {
                                name: '特价规则',
                                description: '商品特价销售的促销规则',
                                types: {
                                    [selectedRuleType.value]: {
                                        name: ruleMetadata.name || '特价规则',
                                        description: ruleMetadata.description || '设置商品特价销售条件',
                                        ruleType: selectedRuleType.value,
                                        fields: ruleMetadata.fields || []
                                    }
                                }
                            }
                        };
                        
                        // 重置表单数据
                        formData.value = {};
                        
                        ElementPlus.ElMessage.success('元数据加载成功');
                    } catch (error) {
                        console.error('Load metadata failed:', error);
                        ElementPlus.ElMessage.error('加载元数据失败：' + error.message);
                    } finally {
                        loading.value = false;
                    }
                };
                
                // 重置表单
                const resetForm = () => {
                    formData.value = {};
                    ElementPlus.ElMessage.info('表单已重置');
                };
                
                // 处理表单提交
                const handleFormSubmit = (data) => {
                    console.log('Form submitted:', data);
                    ElementPlus.ElMessage.success('表单提交成功！');
                };
                
                // 监听规则类型变化
                watch(selectedRuleType, (newType) => {
                    if (newType && promotionTypes.value) {
                        // 自动重新加载元数据
                        loadMetadata();
                    }
                });
                
                return {
                    loading,
                    selectedRuleType,
                    promotionTypes,
                    formData,
                    ruleTypeOptions,
                    status,
                    statusText,
                    loadMetadata,
                    resetForm,
                    handleFormSubmit
                };
            }
        });
        
        // 注册Element Plus
        app.use(ElementPlus);
        
        // 注册Element Plus图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
        
        // 挂载应用
        app.mount('#app');
    </script>
</body>
</html>
