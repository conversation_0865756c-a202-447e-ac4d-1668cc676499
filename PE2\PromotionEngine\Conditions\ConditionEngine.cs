using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Observability;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace PE2.PromotionEngine.Conditions;

/// <summary>
/// 条件引擎实现 - 统一化条件验证框架
/// </summary>
public class ConditionEngine : IConditionEngine
{
    private readonly IObservabilityEngine _observability;
    private readonly ConcurrentDictionary<string, CompiledCondition> _compiledConditions;
    private readonly ConcurrentDictionary<string, ConditionResult> _conditionCache;
    private readonly Dictionary<ConditionType, IConditionValidator> _validators;

    public ConditionEngine(IObservabilityEngine observability)
    {
        _observability = observability;
        _compiledConditions = new ConcurrentDictionary<string, CompiledCondition>();
        _conditionCache = new ConcurrentDictionary<string, ConditionResult>();
        _validators = InitializeValidators();
    }

    public async Task<ConditionResult> ValidateAsync(ConditionExpression expression, ShoppingCart cart)
    {
        var stopwatch = Stopwatch.StartNew();
        var traceId = Guid.NewGuid().ToString();

        try
        {
            _observability.TrackConditionValidation(traceId, expression.Id, "开始验证条件");

            // 检查缓存
            var cacheKey = GenerateCacheKey(expression, cart);
            if (expression.IsCacheable && _conditionCache.TryGetValue(cacheKey, out var cachedResult))
            {
                _observability.TrackConditionValidation(traceId, expression.Id, "使用缓存结果");
                return cachedResult;
            }

            // 使用编译后的条件（如果存在）
            if (_compiledConditions.TryGetValue(expression.Id, out var compiled) && compiled.IsValid)
            {
                var result = compiled.ValidationFunction(cart);
                result.ValidationTimeMs = stopwatch.ElapsedMilliseconds;
                
                if (expression.IsCacheable)
                {
                    _conditionCache.TryAdd(cacheKey, result);
                }

                _observability.TrackConditionValidation(traceId, expression.Id, "使用编译条件验证完成", result);
                return result;
            }

            // 动态验证
            var validationResult = await ValidateDynamicAsync(expression, cart, traceId);
            validationResult.ValidationTimeMs = stopwatch.ElapsedMilliseconds;

            if (expression.IsCacheable)
            {
                _conditionCache.TryAdd(cacheKey, validationResult);
            }

            _observability.TrackConditionValidation(traceId, expression.Id, "动态验证完成", validationResult);
            return validationResult;
        }
        catch (Exception ex)
        {
            _observability.TrackConditionValidation(traceId, expression.Id, $"验证失败: {ex.Message}", ex);
            return new ConditionResult
            {
                IsSatisfied = false,
                Gap = $"验证异常: {ex.Message}",
                ValidationTimeMs = stopwatch.ElapsedMilliseconds
            };
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public async Task<Dictionary<string, ConditionResult>> ValidateBatchAsync(
        Dictionary<string, ConditionExpression> expressions, 
        ShoppingCart cart)
    {
        var tasks = expressions.Select(async kvp => new
        {
            Key = kvp.Key,
            Result = await ValidateAsync(kvp.Value, cart)
        });

        var results = await Task.WhenAll(tasks);
        return results.ToDictionary(r => r.Key, r => r.Result);
    }

    public async Task<ConditionAnalysis> AnalyzeAsync(ConditionExpression expression, ShoppingCart cart)
    {
        var analysis = new ConditionAnalysis();

        // 计算复杂度
        analysis.ComplexityScore = CalculateComplexity(expression);

        // 预估验证时间
        analysis.EstimatedValidationTimeMs = EstimateValidationTime(expression);

        // 生成优化建议
        analysis.OptimizationSuggestions = GenerateOptimizationSuggestions(expression);

        // 分析依赖字段
        analysis.RequiredDataFields = ExtractRequiredFields(expression);

        return analysis;
    }

    public CompiledCondition CompileCondition(ConditionExpression expression)
    {
        try
        {
            var compiled = new CompiledCondition
            {
                Id = expression.Id,
                CompiledAt = DateTime.Now,
                ValidationFunction = BuildValidationFunction(expression),
                IsValid = true
            };

            _compiledConditions.AddOrUpdate(expression.Id, compiled, (key, old) => compiled);
            return compiled;
        }
        catch (Exception ex)
        {
            _observability.TrackConditionValidation(Guid.NewGuid().ToString(), expression.Id, 
                $"编译失败: {ex.Message}", ex);
            
            return new CompiledCondition
            {
                Id = expression.Id,
                IsValid = false
            };
        }
    }

    private async Task<ConditionResult> ValidateDynamicAsync(ConditionExpression expression, ShoppingCart cart, string traceId)
    {
        // 处理组合条件
        if (expression.SubConditions.Any())
        {
            return await ValidateCompositeCondition(expression, cart, traceId);
        }

        // 处理单一条件
        if (_validators.TryGetValue(expression.Type, out var validator))
        {
            return await validator.ValidateAsync(expression, cart);
        }

        return new ConditionResult
        {
            IsSatisfied = false,
            Gap = $"不支持的条件类型: {expression.Type}"
        };
    }

    private async Task<ConditionResult> ValidateCompositeCondition(ConditionExpression expression, ShoppingCart cart, string traceId)
    {
        var subResults = new Dictionary<string, ConditionResult>();
        var allSatisfied = true;
        var anySatisfied = false;

        foreach (var subCondition in expression.SubConditions)
        {
            var subResult = await ValidateAsync(subCondition, cart);
            subResults[subCondition.Id] = subResult;

            if (subResult.IsSatisfied)
            {
                anySatisfied = true;
            }
            else
            {
                allSatisfied = false;
            }
        }

        var result = new ConditionResult
        {
            SubResults = subResults
        };

        switch (expression.Operator)
        {
            case LogicalOperator.And:
                result.IsSatisfied = allSatisfied;
                result.SatisfactionPercentage = subResults.Values.Average(r => r.SatisfactionPercentage);
                break;
            case LogicalOperator.Or:
                result.IsSatisfied = anySatisfied;
                result.SatisfactionPercentage = subResults.Values.Max(r => r.SatisfactionPercentage);
                break;
            case LogicalOperator.Not:
                var firstResult = subResults.Values.FirstOrDefault();
                result.IsSatisfied = firstResult != null && !firstResult.IsSatisfied;
                result.SatisfactionPercentage = firstResult != null ? 100 - firstResult.SatisfactionPercentage : 0;
                break;
        }

        return result;
    }

    private Dictionary<ConditionType, IConditionValidator> InitializeValidators()
    {
        return new Dictionary<ConditionType, IConditionValidator>
        {
            { ConditionType.ProductQuantity, new ProductQuantityValidator() },
            { ConditionType.ProductAmount, new ProductAmountValidator() },
            { ConditionType.CategoryQuantity, new CategoryQuantityValidator() },
            { ConditionType.CategoryAmount, new CategoryAmountValidator() },
            { ConditionType.TotalQuantity, new TotalQuantityValidator() },
            { ConditionType.TotalAmount, new TotalAmountValidator() },
            { ConditionType.MemberLevel, new MemberLevelValidator() },
            { ConditionType.TimeRange, new TimeRangeValidator() }
        };
    }

    private Func<ShoppingCart, ConditionResult> BuildValidationFunction(ConditionExpression expression)
    {
        // 这里可以使用表达式树或动态编译来构建高性能的验证函数
        // 为简化示例，这里返回一个基本的验证函数
        return cart => ValidateDynamicAsync(expression, cart, Guid.NewGuid().ToString()).Result;
    }

    private string GenerateCacheKey(ConditionExpression expression, ShoppingCart cart)
    {
        // 生成基于条件和购物车状态的缓存键
        var cartHash = $"{cart.TotalQuantity}_{cart.TotalAmount}_{string.Join(",", cart.Items.Select(i => $"{i.Product.Id}:{i.Quantity}"))}";
        return $"{expression.Id}_{cartHash.GetHashCode()}";
    }

    private int CalculateComplexity(ConditionExpression expression)
    {
        var complexity = 1;
        complexity += expression.SubConditions.Sum(sub => CalculateComplexity(sub));
        complexity += expression.Parameters.Count;
        return complexity;
    }

    private long EstimateValidationTime(ConditionExpression expression)
    {
        // 基于条件复杂度估算验证时间
        var baseTime = 1L; // 1ms基础时间
        var complexity = CalculateComplexity(expression);
        return baseTime * complexity;
    }

    private List<string> GenerateOptimizationSuggestions(ConditionExpression expression)
    {
        var suggestions = new List<string>();

        if (expression.SubConditions.Count > 5)
        {
            suggestions.Add("考虑简化条件组合，减少子条件数量");
        }

        if (!expression.IsCacheable)
        {
            suggestions.Add("考虑启用条件缓存以提高性能");
        }

        return suggestions;
    }

    private List<string> ExtractRequiredFields(ConditionExpression expression)
    {
        var fields = new List<string>();

        switch (expression.Type)
        {
            case ConditionType.ProductQuantity:
            case ConditionType.ProductAmount:
                fields.Add("Items");
                break;
            case ConditionType.TotalQuantity:
                fields.Add("TotalQuantity");
                break;
            case ConditionType.TotalAmount:
                fields.Add("TotalAmount");
                break;
            case ConditionType.MemberLevel:
                fields.Add("MemberId");
                break;
        }

        foreach (var subCondition in expression.SubConditions)
        {
            fields.AddRange(ExtractRequiredFields(subCondition));
        }

        return fields.Distinct().ToList();
    }
}
