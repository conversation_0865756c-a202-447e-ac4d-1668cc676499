using System.Text.Json.Serialization;
using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.BuyGiftRules;

/// <summary>
/// 买赠促销规则基类
/// 核心原则：只处理已扫码进入购物车的商品，通过调整价格实现赠品效果
/// </summary>
public abstract class BaseBuyGiftRule : PromotionRuleBase
{
    /// <summary>
    /// 赠品选择策略：客户利益最大化 vs 商家利益最大化
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public BenefitSelectionStrategy GiftSelectionStrategy { get; set; } =
        BenefitSelectionStrategy.CustomerBenefit;

    /// <summary>
    /// 验证买赠商品是否在购物车中
    /// POS系统核心：只能对已扫码的商品进行买赠处理
    /// </summary>
    protected bool ValidateBuyGiftProductsInCart(ShoppingCart cart, List<string> productIds) =>
        cart.Items.Any(x => x.Quantity > 0 && productIds.Contains(x.Product.Id));

    /// <summary>
    /// 验证赠品是否在购物车中
    /// POS系统核心：只能对已扫码的商品进行赠品处理
    /// </summary>
    protected bool ValidateGiftProductsInCart(ShoppingCart cart, List<string> giftProductIds) =>
        cart.Items.Any(x => x.Quantity > 0 && giftProductIds.Contains(x.Product.Id));

    /// <summary>
    /// 计算商品的总数量
    /// </summary>
    protected int CalculateTotalQuantity(ShoppingCart cart, List<string> productIds)
    {
        return productIds.Sum(id => cart.GetAvailableProductQuantity(id));
    }

    /// <summary>
    /// 计算商品的总金额
    /// </summary>
    protected decimal CalculateTotalAmount(ShoppingCart cart, List<string> productIds)
    {
        return productIds.Sum(id => cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));
    }

    /// <summary>
    /// 根据策略选择要赠送的商品
    /// </summary>
    protected List<(string ProductId, int Quantity, decimal UnitPrice)> SelectGiftProducts(
        List<(string ProductId, int Quantity, decimal UnitPrice)> candidateProducts,
        int giftQuantity
    )
    {
        if (!candidateProducts.Any() || giftQuantity <= 0)
            return new List<(string, int, decimal)>();

        var giftProducts = new List<(string ProductId, int Quantity, decimal UnitPrice)>();
        var remainingGiftQuantity = giftQuantity;

        // 根据策略排序
        var sortedProducts = GiftSelectionStrategy switch
        {
            BenefitSelectionStrategy.CustomerBenefit
                => candidateProducts.OrderByDescending(p => p.UnitPrice), // 客户利益：赠送高价商品
            BenefitSelectionStrategy.MerchantBenefit => candidateProducts.OrderBy(p => p.UnitPrice), // 商家利益：赠送低价商品
            _ => candidateProducts.OrderByDescending(p => p.UnitPrice)
        };

        foreach (var product in sortedProducts)
        {
            if (remainingGiftQuantity <= 0)
                break;

            var giftQty = Math.Min(product.Quantity, remainingGiftQuantity);
            if (giftQty > 0)
            {
                giftProducts.Add((product.ProductId, giftQty, product.UnitPrice));
                remainingGiftQuantity -= giftQty;
            }
        }

        return giftProducts;
    }

    /// <summary>
    /// 应用赠品效果到购物车
    /// 通过将商品的ActualUnitPrice设置为0来实现赠品
    /// </summary>
    protected void ApplyGiftEffectToCart(
        ShoppingCart cart,
        List<(string ProductId, int Quantity, decimal UnitPrice)> giftProducts,
        AppliedPromotion promotion
    )
    {
        var newItemsToAdd = new List<CartItem>();

        foreach (var giftProduct in giftProducts)
        {
            var remainingGiftQuantity = giftProduct.Quantity;

            var cartItems = cart
                .Items.Where(x =>
                    x.Product.Id == giftProduct.ProductId
                    && Math.Abs(x.UnitPrice - giftProduct.UnitPrice) < 0.01m
                    && x.Quantity > 0
                )
                .ToList();

            foreach (var cartItem in cartItems)
            {
                if (remainingGiftQuantity <= 0)
                    break;

                var giftQuantity = Math.Min(cartItem.Quantity, remainingGiftQuantity);

                if (giftQuantity == cartItem.Quantity)
                {
                    // 整个购物车项都变成赠品
                    cartItem.ActualUnitPrice = 0;
                    cartItem.IsGift = true;
                    AddGiftPromotionDetail(cartItem, promotion, giftProduct.UnitPrice);
                }
                else
                {
                    // 部分数量变成赠品，需要拆分购物车项
                    var newItems = SplitCartItemForGift(
                        cartItem,
                        giftQuantity,
                        promotion,
                        giftProduct.UnitPrice
                    );
                    newItemsToAdd.AddRange(newItems);
                }

                remainingGiftQuantity -= giftQuantity;
            }
        }

        // 将新的赠品项添加到购物车
        cart.Items.AddRange(newItemsToAdd);
    }

    /// <summary>
    /// 拆分购物车项以处理部分赠品的情况
    /// </summary>
    private List<CartItem> SplitCartItemForGift(
        CartItem originalItem,
        int giftQuantity,
        AppliedPromotion promotion,
        decimal originalUnitPrice
    )
    {
        var newItems = new List<CartItem>();

        // 减少原购物车项的数量（保持原价）
        originalItem.Quantity -= giftQuantity;

        // 创建新的赠品项
        var giftCartItem = new CartItem
        {
            Product = originalItem.Product,
            Quantity = giftQuantity,
            UnitPrice = originalUnitPrice,
            ActualUnitPrice = 0, // 赠品
            IsGift = true
        };

        AddGiftPromotionDetail(giftCartItem, promotion, originalUnitPrice);
        newItems.Add(giftCartItem);

        return newItems;
    }

    /// <summary>
    /// 为赠品添加促销详情
    /// </summary>
    private void AddGiftPromotionDetail(
        CartItem cartItem,
        AppliedPromotion promotion,
        decimal originalUnitPrice
    )
    {
        var totalValue = originalUnitPrice * cartItem.Quantity;
        var strategyDescription =
            GiftSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                ? "客户利益最大化"
                : "商家利益最大化";

        var promotionDetail = new ItemPromotionDetail
        {
            RuleId = promotion.RuleId,
            RuleName = promotion.RuleName,
            PromotionType = promotion.PromotionType,
            DiscountAmount = totalValue,
            Description = $"买赠促销：赠送{cartItem.Quantity}件，价值{totalValue:C}（{strategyDescription}）",
            IsGiftRelated = true
        };

        cartItem.AddPromotionDetail(promotionDetail);
    }

    /// <summary>
    /// 消耗购买条件商品（记录消耗但不修改购物车）
    /// </summary>
    protected List<ConsumedItem> ConsumeConditionProducts(
        ShoppingCart cart,
        List<string> productIds,
        int requiredQuantity
    )
    {
        var consumedItems = new List<ConsumedItem>();
        var remainingQuantity = requiredQuantity;

        foreach (var productId in productIds)
        {
            if (remainingQuantity <= 0)
                break;

            var cartItems = cart
                .Items.Where(x => x.Product.Id == productId && x.Quantity > 0)
                .ToList();

            foreach (var cartItem in cartItems)
            {
                if (remainingQuantity <= 0)
                    break;

                var consumeQuantity = Math.Min(cartItem.Quantity, remainingQuantity);

                var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == productId);
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += consumeQuantity;
                }
                else
                {
                    consumedItems.Add(
                        new ConsumedItem
                        {
                            ProductId = productId,
                            ProductName = cartItem.Product.Name,
                            Quantity = consumeQuantity,
                            UnitPrice = cartItem.UnitPrice
                        }
                    );
                }

                remainingQuantity -= consumeQuantity;
            }
        }

        return consumedItems;
    }

    /// <summary>
    /// 发送券到会员账户（需要CRM支持）
    /// </summary>
    protected async Task<bool> SendCouponToMemberAsync(string memberId, CouponGift couponGift)
    {
        try
        {
            // 这里应该调用CRM系统的API来发送券
            // 暂时返回true表示成功
            await Task.Delay(100); // 模拟异步调用

            // TODO: 实际实现时需要调用CRM系统
            // var result = await _crmService.SendCouponToMemberAsync(memberId, couponGift);
            // return result.IsSuccess;

            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }
}

/// <summary>
/// 券赠品
/// </summary>
public class CouponGift
{
    /// <summary>
    /// 券ID
    /// </summary>
    public string CouponId { get; set; } = string.Empty;

    /// <summary>
    /// 券名称
    /// </summary>
    public string CouponName { get; set; } = string.Empty;

    /// <summary>
    /// 券面值
    /// </summary>
    public decimal CouponValue { get; set; }

    /// <summary>
    /// 券数量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 有效期（天数）
    /// </summary>
    public int ValidityDays { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}
