/**
 * 主要的促销配置系统组件 - 原有风格 + 反射功能
 * 保持三栏布局，集成动态表单渲染器和元数据服务
 * 支持基于反射API的完全动态化配置
 */

const PromotionConfigMainComponentHybrid = {
    name: 'PromotionConfigSystem',
    components: {
        DynamicFormRenderer: window.DynamicFormRenderer,
        RuleManager: window.RuleManager
    },
    setup() {
        const { ref, reactive, computed, onMounted, watch } = Vue;
        const { ElMessage, ElMessageBox, ElLoading } = ElementPlus;

        // ===== 响应式数据 =====
        const loading = ref(false);
        const saving = ref(false);
        const metadataLoading = ref(true);
        
        // 促销类型元数据（从API动态加载）
        const promotionTypes = ref({});
        const selectedCategory = ref(null);
        const selectedType = ref(null);
        const currentStep = ref(1);
        
        // 表单数据
        const formData = ref({
            ruleId: '',
            ruleName: '',
            description: '',
            priority: 0,
            isEnabled: true,
            startTime: null,
            endTime: null,
            isRepeatable: true,
            maxApplications: 0,
            canStackWithOthers: true,
            productExclusivity: 'None',
            applicableCustomerTypes: [],
            exclusiveRuleIds: []
        });
        
        // UI状态
        const showPreviewModal = ref(false);
        const showRuleManager = ref(false);
        const errors = ref({});
        
        // 服务实例
        const metadataService = ref(null);
        
        // ===== 计算属性 =====
        const selectedCategoryData = computed(() => {
            if (!selectedCategory.value || !promotionTypes.value[selectedCategory.value]) {
                return null;
            }
            return promotionTypes.value[selectedCategory.value];
        });

        const selectedTypeData = computed(() => {
            if (!selectedCategoryData.value || !selectedType.value) {
                return null;
            }
            return selectedCategoryData.value.types?.[selectedType.value] || null;
        });

        const isValidConfig = computed(() => {
            return !!(formData.value.ruleId && formData.value.ruleName && selectedTypeData.value);
        });

        const formattedJson = computed(() => {
            const config = { ...formData.value };
            if (selectedTypeData.value) {
                config.ruleType = selectedTypeData.value.ruleType || selectedType.value;
            }
            return JSON.stringify(config, null, 2);
        });

        const conditionFields = computed(() => {
            if (!selectedTypeData.value || !selectedTypeData.value.fields) return [];
            return selectedTypeData.value.fields.filter(field => 
                field.group === 'conditions' || field.name.includes('Condition') || field.type === 'array'
            );
        });

        const advancedFields = computed(() => {
            if (!selectedTypeData.value || !selectedTypeData.value.fields) return [];
            return selectedTypeData.value.fields.filter(field => 
                field.group === 'advanced' || (!field.group && !conditionFields.value.includes(field))
            );
        });

        // ===== 初始化方法 =====
        const initializeSystem = async () => {
            try {
                metadataLoading.value = true;
                
                // 初始化元数据服务
                if (!window.apiService) {
                    throw new Error('API服务未初始化');
                }
                
                metadataService.value = window.getMetadataService(window.apiService);
                
                // 测试API连接
                const isConnected = await metadataService.value.checkApiConnection();
                if (!isConnected) {
                    ElMessage.warning('无法连接到后端API，使用本地配置数据');
                    // 如果API不可用，回退到静态配置
                    promotionTypes.value = window.PROMOTION_TYPES || {};
                } else {
                    // 从API加载元数据
                    const metadata = await metadataService.value.getPromotionTypes();
                    promotionTypes.value = metadata;
                    ElMessage.success('动态元数据加载成功');
                }
                
            } catch (error) {
                console.error('系统初始化失败:', error);
                ElMessage.error(`系统初始化失败: ${error.message}`);
                
                // 回退到静态配置
                promotionTypes.value = window.PROMOTION_TYPES || {};
                
            } finally {
                metadataLoading.value = false;
            }        };        // ===== 事件处理方法 =====        
        const selectCategory = (categoryKey, categoryData) => {
            console.log('选择促销分类:', categoryKey, categoryData);
            selectedCategory.value = categoryKey;
            selectedType.value = null;
            resetFormData();
            currentStep.value = 1; // 回到第1步
        };

        const selectType = async (typeKey, typeData) => {
            console.log('选择促销类型:', typeKey, typeData);
            
            selectedType.value = typeKey;
            resetFormData();
            
            // 如果有元数据服务，尝试加载详细的类型信息
            if (metadataService.value) {
                try {
                    loading.value = true;
                    ElMessage.info(`正在加载${typeData?.name || typeKey}的详细配置...`);
                    
                    const detailedTypeData = await metadataService.value.getPromotionTypeDetail(typeKey);
                    console.log('获取到详细类型数据:', detailedTypeData);
                    
                    if (detailedTypeData && detailedTypeData.fields) {
                        // 更新当前类型的字段信息
                        if (promotionTypes.value[selectedCategory.value] && 
                            promotionTypes.value[selectedCategory.value].types && 
                            promotionTypes.value[selectedCategory.value].types[typeKey]) {
                            promotionTypes.value[selectedCategory.value].types[typeKey].fields = detailedTypeData.fields;
                            console.log('更新后的促销类型结构:', promotionTypes.value[selectedCategory.value].types[typeKey]);
                        }
                        
                        // 初始化表单数据结构
                        detailedTypeData.fields.forEach(field => {
                            if (formData.value[field.name] === undefined) {
                                const defaultValue = field.default || getDefaultValue(field.type);
                                formData.value[field.name] = defaultValue;
                                console.log(`初始化字段 ${field.name}:`, defaultValue);
                            }
                        });
                        
                        ElMessage.success(`已加载${typeData?.name || typeKey}的${detailedTypeData.fields.length}个配置字段`);
                    }
                } catch (error) {
                    console.error('加载类型详细信息失败:', error);
                    ElMessage.warning('无法加载详细配置，使用基本配置');
                    
                    // 回退到基本配置
                    if (typeData.fields) {
                        typeData.fields.forEach(field => {
                            if (formData.value[field.name] === undefined) {
                                formData.value[field.name] = field.default || getDefaultValue(field.type);
                            }
                        });
                    }
                } finally {
                    loading.value = false;
                }
            } else {
                // 没有元数据服务时的回退逻辑
                if (typeData.fields) {
                    typeData.fields.forEach(field => {
                        if (formData.value[field.name] === undefined) {
                            formData.value[field.name] = field.default || getDefaultValue(field.type);
                        }
                    });
                }
            }
            
            // 切换到条件配置步骤
            currentStep.value = 2;
            console.log('切换到第2步，当前表单数据:', formData.value);
        };

        const resetFormData = () => {
            formData.value = {
                ruleId: '',
                ruleName: '',
                description: '',
                priority: 0,
                isEnabled: true,
                startTime: null,
                endTime: null,
                isRepeatable: true,
                maxApplications: 0,
                canStackWithOthers: true,
                productExclusivity: 'None',
                applicableCustomerTypes: [],
                exclusiveRuleIds: []
            };
            errors.value = {};
        };        const getDefaultValue = (type) => {
            if (typeof type === 'string') {
                switch (type) {
                    case 'number':
                    case 'decimal':
                    case 'integer':
                        return 0;
                    case 'boolean':
                    case 'switch':
                        return false;
                    case 'array':
                    case 'array-simple':
                    case 'array-complex':
                        return [];
                    case 'object':
                        return {};
                    case 'product-selector':
                        return [];
                    default:
                        return '';
                }
            } else if (typeof type === 'object' && type !== null) {
                // 处理复杂类型定义
                const actualType = type.type || 'string';
                switch (actualType) {
                    case 'number':
                    case 'decimal':
                    case 'integer':
                        return 0;
                    case 'boolean':
                    case 'switch':
                        return false;
                    case 'array':
                    case 'array-simple':
                    case 'array-complex':
                        return [];
                    case 'object':
                        return {};
                    case 'product-selector':
                        return [];
                    default:
                        return '';
                }
            }
            return '';
        };

        const generateRuleId = () => {
            const timestamp = Date.now().toString().slice(-6);
            const random = Math.random().toString(36).substring(2, 5);
            return `RULE_${selectedType.value}_${timestamp}_${random}`.toUpperCase();
        };

        const generateRuleName = (typeData) => {
            return `${typeData?.name || '新规则'}_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '')}`;
        };

        const autoGenerateIds = () => {
            if (!selectedTypeData.value) {
                ElMessage.warning('请先选择促销类型');
                return;
            }
            
            formData.value.ruleId = generateRuleId();
            formData.value.ruleName = generateRuleName(selectedTypeData.value);
            
            ElMessage.success('ID和名称已自动生成');
        };

        const updateField = (fieldName, value) => {
            formData.value[fieldName] = value;
            validateField(fieldName);
        };

        const validateField = (fieldName) => {
            delete errors.value[fieldName];
            
            if ((fieldName === 'ruleId' || fieldName === 'ruleName') && !formData.value[fieldName]) {
                errors.value[fieldName] = '此字段为必填项';
            }
        };

        const nextStep = () => {
            if (currentStep.value === 1) {
                ElMessage.warning('请先选择促销类型');
                return;
            }
            if (currentStep.value === 2) {
                validateField('ruleId');
                validateField('ruleName');
                if (Object.keys(errors.value).length > 0) {
                    ElMessage.warning('请填写必填字段');
                    return;
                }
            }
            if (currentStep.value < 3) {
                currentStep.value++;
            }
        };

        const prevStep = () => {
            if (currentStep.value > 1) {
                currentStep.value--;
            }
        };

        const exportConfig = () => {
            if (!isValidConfig.value) {
                ElMessage.warning('请先完成配置');
                return;
            }
            
            const config = { 
                ...formData.value, 
                ruleType: selectedTypeData.value.ruleType || selectedType.value 
            };
            const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `promotion-rule-${formData.value.ruleId}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            ElMessage.success('配置文件已导出成功');
        };

        const importConfig = () => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = (e) => {
                const file = e.target.files[0];
                if (!file) return;
                
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const importData = JSON.parse(e.target.result);
                        
                        // 验证导入数据
                        if (!importData.ruleId || !importData.ruleType) {
                            throw new Error('导入的配置文件格式无效');
                        }

                        // 查找对应的类型
                        let foundCategory = null;
                        let foundType = null;
                        
                        Object.keys(promotionTypes.value).forEach(categoryKey => {
                            const category = promotionTypes.value[categoryKey];
                            if (category.types) {
                                Object.keys(category.types).forEach(typeKey => {
                                    const type = category.types[typeKey];
                                    if (type.ruleType === importData.ruleType) {
                                        foundCategory = categoryKey;
                                        foundType = typeKey;
                                    }
                                });
                            }
                        });

                        if (!foundCategory || !foundType) {
                            throw new Error(`未找到匹配的促销类型: ${importData.ruleType}`);
                        }

                        // 应用导入的配置
                        selectedCategory.value = foundCategory;
                        selectedType.value = foundType;
                        formData.value = { ...importData };
                        currentStep.value = 2;
                        
                        ElMessage.success('配置导入成功');
                        
                    } catch (error) {
                        console.error('导入失败:', error);
                        ElMessage.error(`导入失败: ${error.message}`);
                    }
                };
                reader.readAsText(file);
            };
            input.click();
        };

        const saveToBackend = async () => {
            if (!isValidConfig.value) {
                ElMessage.warning('请先完成配置');
                return;
            }

            try {
                saving.value = true;
                const result = await window.apiService.createPromotionRule(formData.value);
                ElMessage.success('配置已保存到后端');
                console.log('保存结果:', result);
            } catch (error) {
                console.error('保存到后端失败:', error);
                ElMessage.error(`保存失败: ${error.message}`);
            } finally {
                saving.value = false;
            }
        };

        const copyToClipboard = () => {
            navigator.clipboard.writeText(formattedJson.value).then(() => {
                ElMessage.success('JSON配置已复制到剪贴板');
            });
        };

        const resetForm = () => {
            selectedCategory.value = null;
            selectedType.value = null;
            resetFormData();
            currentStep.value = 1;
            ElMessage.success('表单已重置');
        };

        // ===== 生命周期 =====
        onMounted(() => {
            initializeSystem();
        });

        // ===== 返回供模板使用 =====
        return {
            // 响应式数据
            loading,
            saving,
            metadataLoading,
            promotionTypes,
            selectedCategory,
            selectedType,
            currentStep,
            formData,
            showPreviewModal,
            showRuleManager,
            errors,
            
            // 计算属性
            selectedCategoryData,
            selectedTypeData,
            isValidConfig,
            formattedJson,
            conditionFields,
            advancedFields,
            
            // 方法
            selectCategory,
            selectType,
            autoGenerateIds,
            updateField,
            validateField,
            nextStep,
            prevStep,
            exportConfig,
            importConfig,
            saveToBackend,
            copyToClipboard,
            resetForm
        };
    },
    template: `
        <div class="app-container">
            <div class="main-content">
                <!-- 顶部标题栏 -->
                <div class="header">
                    <div class="header-title">
                        <div style="width: 40px; height: 40px; background: var(--ant-primary-color); border-radius: var(--ant-border-radius-base); display: flex; align-items: center; justify-content: center; color: white;">
                            <el-icon size="20"><Setting /></el-icon>
                        </div>
                        <div>
                            <h1>促销规则配置管理系统</h1>
                            <div class="header-subtitle">专业的促销活动配置平台 + 反射API</div>
                        </div>
                    </div>
                    
                    <div class="header-actions">
                        <el-button @click="showRuleManager = true">
                            <el-icon><List /></el-icon>
                            规则管理
                        </el-button>                        <el-button @click="autoGenerateIds" type="info" :disabled="!selectedType">
                            <el-icon><Refresh /></el-icon>
                            自动生成ID
                        </el-button>
                        <el-button @click="importConfig">
                            <el-icon><Upload /></el-icon>
                            导入配置
                        </el-button>
                        <el-button @click="saveToBackend" type="success" :loading="saving" :disabled="!isValidConfig">
                            <el-icon><Upload /></el-icon>
                            保存到后端
                        </el-button>
                        <el-button type="primary" @click="exportConfig" :disabled="!isValidConfig">
                            <el-icon><Download /></el-icon>
                            导出配置
                        </el-button>
                    </div>
                </div>

                <!-- 主布局 -->
                <div class="layout" v-loading="metadataLoading" element-loading-text="正在加载促销类型...">
                    <!-- 左侧促销类型选择 -->
                    <div class="sidebar">
                        <div class="sidebar-section">
                            <div class="sidebar-title">
                                <el-icon><Grid /></el-icon>
                                促销类型
                            </div>
                            <div class="promotion-type-grid">
                                <div 
                                    v-for="(category, key) in promotionTypes" 
                                    :key="key"
                                    class="promotion-type-card"
                                    :class="{ active: selectedCategory === key }"
                                    @click="selectCategory(key, category)"
                                >
                                    <div class="card-header">
                                        <div class="card-icon">{{ category.icon || '⚙️' }}</div>
                                        <div class="card-title">{{ category.name }}</div>
                                    </div>
                                    <div class="card-description">
                                        {{ Object.keys(category.types || {}).length }} 个配置方案
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div v-if="selectedCategory" class="sidebar-section">
                            <div class="sidebar-title">
                                <el-icon><Setting /></el-icon>
                                配置方案
                            </div>
                            <div class="promotion-type-grid">
                                <div 
                                    v-for="(type, typeKey) in selectedCategoryData.types" 
                                    :key="typeKey"
                                    class="promotion-type-card"
                                    :class="{ active: selectedType === typeKey }"
                                    @click="selectType(typeKey, type)"
                                    style="margin-bottom: 8px;"
                                >
                                    <div class="card-title" style="font-size: 14px; margin-bottom: 4px;">
                                        {{ type.name }}
                                    </div>
                                    <div class="card-description" style="font-size: 12px;">
                                        {{ type.description }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 中间配置表单 -->
                    <div class="main-panel">
                        <div v-if="!selectedType" class="empty-state">
                            <div class="empty-state-icon">
                                <el-icon><Grid /></el-icon>
                            </div>
                            <h3 style="margin-bottom: 12px;">选择促销类型开始配置</h3>
                            <p>从左侧选择您需要的促销类型，开始创建专业的促销规则</p>
                        </div>

                        <div v-if="selectedType" class="form-container">
                            <!-- Ant Design 风格步骤指示器 -->
                            <div class="step-indicator">
                                <div 
                                    class="step" 
                                    :class="{ active: currentStep === 1, completed: currentStep > 1 }"
                                    @click="currentStep = 1"
                                >
                                    <div class="step-icon">
                                        <el-icon v-if="currentStep > 1"><Check /></el-icon>
                                        <span v-else>1</span>
                                    </div>
                                    <div class="step-content">
                                        <div class="step-title">基础信息</div>
                                        <div class="step-description">配置规则基本信息</div>
                                    </div>
                                </div>
                                <div 
                                    class="step" 
                                    :class="{ active: currentStep === 2, completed: currentStep > 2 }"
                                    @click="currentStep = 2"
                                >
                                    <div class="step-icon">
                                        <el-icon v-if="currentStep > 2"><Check /></el-icon>
                                        <span v-else>2</span>
                                    </div>
                                    <div class="step-content">
                                        <div class="step-title">条件配置</div>
                                        <div class="step-description">设置促销触发条件</div>
                                    </div>
                                </div>
                                <div 
                                    class="step" 
                                    :class="{ active: currentStep === 3 }"
                                    @click="currentStep = 3"
                                >
                                    <div class="step-icon">
                                        <span>3</span>
                                    </div>
                                    <div class="step-content">
                                        <div class="step-title">高级设置</div>
                                        <div class="step-description">配置高级选项</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 表单内容 -->
                            <transition name="fade" mode="out-in">
                                <div v-if="currentStep === 1" key="step1">
                                    <div class="form-section">
                                        <div class="section-header">
                                            <div class="section-icon">
                                                <el-icon><Edit /></el-icon>
                                            </div>
                                            <div class="section-title">基础信息配置</div>
                                        </div>
                                        
                                        <el-form :model="formData" label-width="120px" label-position="top">
                                            <div class="form-grid">
                                                <el-form-item label="规则ID" required>
                                                    <el-input 
                                                        v-model="formData.ruleId"
                                                        :class="{ 'is-error': errors.ruleId }"
                                                        @blur="validateField('ruleId')"
                                                    />
                                                    <div v-if="errors.ruleId" style="color: var(--ant-error-color); font-size: 12px; margin-top: 4px;">
                                                        {{ errors.ruleId }}
                                                    </div>
                                                </el-form-item>
                                                
                                                <el-form-item label="规则名称" required>
                                                    <el-input 
                                                        v-model="formData.ruleName"
                                                        :class="{ 'is-error': errors.ruleName }"
                                                        @blur="validateField('ruleName')"
                                                    />
                                                    <div v-if="errors.ruleName" style="color: var(--ant-error-color); font-size: 12px; margin-top: 4px;">
                                                        {{ errors.ruleName }}
                                                    </div>
                                                </el-form-item>
                                            </div>

                                            <el-form-item label="规则描述">
                                                <el-input 
                                                    type="textarea" 
                                                    v-model="formData.description" 
                                                    placeholder="详细描述这个促销规则的作用和适用场景"
                                                    :rows="3"
                                                />
                                            </el-form-item>

                                            <div class="form-grid">
                                                <el-form-item label="优先级">
                                                    <el-input-number 
                                                        v-model="formData.priority"
                                                        :min="0"
                                                        :max="100"
                                                        style="width: 100%;"
                                                    />
                                                </el-form-item>
                                                
                                                <el-form-item label="规则状态">
                                                    <el-switch 
                                                        v-model="formData.isEnabled"
                                                        active-text="启用"
                                                        inactive-text="禁用"
                                                    />
                                                </el-form-item>
                                            </div>

                                            <div class="form-grid">
                                                <el-form-item label="生效时间">
                                                    <el-date-picker
                                                        v-model="formData.startTime"
                                                        type="datetime"
                                                        placeholder="选择开始时间"
                                                        style="width: 100%;"
                                                    />
                                                </el-form-item>
                                                
                                                <el-form-item label="失效时间">
                                                    <el-date-picker
                                                        v-model="formData.endTime"
                                                        type="datetime"
                                                        placeholder="选择结束时间"
                                                        style="width: 100%;"
                                                    />
                                                </el-form-item>
                                            </div>
                                        </el-form>
                                    </div>
                                </div>

                                <div v-else-if="currentStep === 2" key="step2">
                                    <div class="form-section">
                                        <div class="section-header">
                                            <div class="section-icon">
                                                <el-icon><Setting /></el-icon>
                                            </div>
                                            <div class="section-title">促销条件配置 (动态表单)</div>                                        </div>
                                          <!-- 使用动态表单渲染器 -->
                                        <DynamicFormRenderer
                                            v-if="selectedType && selectedCategory"
                                            v-model="formData"
                                            :promotion-types="promotionTypes"
                                            :selected-category="selectedCategory"
                                            :selected-type="selectedType"
                                            mode="create"
                                        />
                                    </div>
                                </div>

                                <div v-else-if="currentStep === 3" key="step3">
                                    <div class="form-section">
                                        <div class="section-header">
                                            <div class="section-icon">
                                                <el-icon><Tools /></el-icon>
                                            </div>
                                            <div class="section-title">高级设置</div>
                                        </div>
                                        
                                        <el-form :model="formData" label-width="120px" label-position="top">
                                            <div class="form-grid">
                                                <el-form-item label="是否可重复">
                                                    <el-switch 
                                                        v-model="formData.isRepeatable"
                                                        active-text="可重复"
                                                        inactive-text="不可重复"
                                                    />
                                                </el-form-item>
                                                
                                                <el-form-item label="最大应用次数">
                                                    <el-input-number 
                                                        v-model="formData.maxApplications"
                                                        :min="0"
                                                        style="width: 100%;"
                                                    />
                                                </el-form-item>
                                            </div>

                                            <el-form-item label="商品互斥级别">
                                                <el-select v-model="formData.productExclusivity" style="width: 100%;">
                                                    <el-option value="None" label="无互斥" />
                                                    <el-option value="SameType" label="同类型互斥" />
                                                    <el-option value="All" label="全部互斥" />
                                                </el-select>
                                            </el-form-item>
                                        </el-form>
                                    </div>
                                </div>
                            </transition>

                            <!-- 步骤导航按钮 -->
                            <div class="step-navigation">
                                <el-button 
                                    v-if="currentStep > 1" 
                                    @click="prevStep"
                                    size="large"
                                >
                                    <el-icon><ArrowLeft /></el-icon>
                                    上一步
                                </el-button>
                                <div v-else></div>
                                
                                <el-button 
                                    v-if="currentStep < 3" 
                                    type="primary" 
                                    @click="nextStep"
                                    size="large"
                                >
                                    下一步
                                    <el-icon><ArrowRight /></el-icon>
                                </el-button>
                                <el-button 
                                    v-else 
                                    type="primary" 
                                    @click="saveToBackend"
                                    size="large"
                                    :loading="saving"
                                >
                                    <el-icon><Check /></el-icon>
                                    完成配置
                                </el-button>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧预览面板 -->
                    <div class="right-panel">
                        <div class="preview-header">
                            <div class="preview-title">
                                <el-icon><View /></el-icon>
                                实时预览
                            </div>
                            <div class="preview-subtitle">配置将实时生成JSON格式</div>
                        </div>

                        <div class="preview-content">
                            <div class="json-editor">{{ formattedJson }}</div>
                            
                            <div class="validation-status" :class="isValidConfig ? 'valid' : 'invalid'">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <el-icon v-if="isValidConfig"><SuccessFilled /></el-icon>
                                    <el-icon v-else><WarningFilled /></el-icon>
                                    {{ isValidConfig ? '配置验证通过' : '配置不完整' }}
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <el-button @click="copyToClipboard" size="small">
                                <el-icon><DocumentCopy /></el-icon>
                                复制
                            </el-button>
                            <el-button @click="resetForm" size="small">
                                <el-icon><RefreshLeft /></el-icon>
                                重置
                            </el-button>
                            <el-button type="primary" @click="exportConfig" :disabled="!isValidConfig" size="small">
                                <el-icon><Download /></el-icon>
                                导出
                            </el-button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 规则管理器弹窗 -->
            <el-dialog 
                v-model="showRuleManager" 
                title="促销规则管理" 
                width="1200px"
                :before-close="() => showRuleManager = false"
                fullscreen
            >
                <RuleManager @close="showRuleManager = false" />
            </el-dialog>
        </div>
    `
};

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PromotionConfigMainComponentHybrid;
} else if (typeof window !== 'undefined') {
    window.PromotionConfigMainComponentHybrid = PromotionConfigMainComponentHybrid;
}
