using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Processors;

/// <summary>
/// 折扣金额分摊处理器
/// 负责将促销优惠金额合理分摊到参与促销的商品中
/// </summary>
public class DiscountAllocationProcessor
{
    /// <summary>
    /// 处理促销结果，进行折扣分摊
    /// </summary>
    public void ProcessPromotionResult(PromotionResult result)
    {
        // 初始化处理后的购物车
        result.ProcessedCart = result.OriginalCart.Clone();
        result.ProcessedCart.InitializeActualPrices();

        // 按顺序处理每个应用的促销
        foreach (var appliedPromotion in result.AppliedPromotions)
        {
            ProcessSinglePromotion(result.ProcessedCart, appliedPromotion);
        }

        // 处理四舍五入问题
        HandleRoundingIssues(result.ProcessedCart, result.AppliedPromotions);
    }

    /// <summary>
    /// 处理单个促销的折扣分摊
    /// </summary>
    private void ProcessSinglePromotion(ShoppingCart cart, AppliedPromotion promotion)
    {
        switch (promotion.PromotionType)
        {
            case "PercentageDiscount":
            case "FixedAmountDiscount":
            case "CategoryDiscount":
            case "TotalAmountDiscount":
                ProcessDiscountPromotion(cart, promotion);
                break;
            case "BuyXGetY":
                ProcessBuyXGetYPromotion(cart, promotion);
                break;
            case "BundleOffer":
                ProcessBundlePromotion(cart, promotion);
                break;
        }
    }

    /// <summary>
    /// 处理折扣类促销
    /// </summary>
    private void ProcessDiscountPromotion(ShoppingCart cart, AppliedPromotion promotion)
    {
        var consumedItems = promotion.ConsumedItems;
        var totalDiscountAmount = promotion.DiscountAmount;

        if (!consumedItems.Any() || totalDiscountAmount <= 0)
            return;

        // 计算参与促销的商品总价值
        var totalOriginalAmount = consumedItems.Sum(c => c.TotalPrice);

        if (totalOriginalAmount <= 0)
            return;

        var remainingDiscount = totalDiscountAmount;

        // 按比例分摊折扣
        for (int i = 0; i < consumedItems.Count; i++)
        {
            var consumedItem = consumedItems[i];
            var cartItems = cart.Items.Where(x =>
                x.Product.Id == consumedItem.ProductId &&
                !x.IsGift &&
                x.UnitPrice == consumedItem.UnitPrice).ToList();

            if (!cartItems.Any()) continue;

            // 计算该商品应分摊的折扣金额
            decimal itemDiscountAmount;
            if (i == consumedItems.Count - 1)
            {
                // 最后一个商品承担剩余的折扣金额（处理四舍五入）
                itemDiscountAmount = remainingDiscount;
            }
            else
            {
                var discountRatio = consumedItem.TotalPrice / totalOriginalAmount;
                itemDiscountAmount = Math.Round(totalDiscountAmount * discountRatio, 2);
                remainingDiscount -= itemDiscountAmount;
            }

            // 分摊到具体的购物车项
            AllocateDiscountToCartItems(cartItems, consumedItem.Quantity, itemDiscountAmount, promotion);
        }
    }

    /// <summary>
    /// 处理买赠促销
    /// </summary>
    private void ProcessBuyXGetYPromotion(ShoppingCart cart, AppliedPromotion promotion)
    {
        // 处理消耗的商品（购买条件）
        ProcessDiscountPromotion(cart, promotion);

        // 处理赠品
        foreach (var giftItem in promotion.GiftItems)
        {
            // 查找对应的赠品商品
            var product = cart.Items.FirstOrDefault(x => x.Product.Id == giftItem.ProductId)?.Product;
            if (product != null)
            {
                //cart.AddGiftItem(product, giftItem.Quantity, promotion.RuleId, promotion.RuleName);
            }
        }
    }

    /// <summary>
    /// 处理组合套餐促销
    /// </summary>
    private void ProcessBundlePromotion(ShoppingCart cart, AppliedPromotion promotion)
    {
        var consumedItems = promotion.ConsumedItems;
        var totalDiscountAmount = promotion.DiscountAmount;

        if (!consumedItems.Any() || totalDiscountAmount <= 0)
            return;

        // 套餐促销按商品价值比例分摊折扣
        var totalOriginalAmount = consumedItems.Sum(c => c.TotalPrice);
        var remainingDiscount = totalDiscountAmount;

        for (int i = 0; i < consumedItems.Count; i++)
        {
            var consumedItem = consumedItems[i];
            var cartItems = cart.Items.Where(x =>
                x.Product.Id == consumedItem.ProductId &&
                !x.IsGift &&
                x.UnitPrice == consumedItem.UnitPrice).ToList();

            if (!cartItems.Any()) continue;

            decimal itemDiscountAmount;
            if (i == consumedItems.Count - 1)
            {
                itemDiscountAmount = remainingDiscount;
            }
            else
            {
                var discountRatio = consumedItem.TotalPrice / totalOriginalAmount;
                itemDiscountAmount = Math.Round(totalDiscountAmount * discountRatio, 2);
                remainingDiscount -= itemDiscountAmount;
            }

            AllocateDiscountToCartItems(cartItems, consumedItem.Quantity, itemDiscountAmount, promotion);
        }
    }

    /// <summary>
    /// 将折扣分摊到具体的购物车项
    /// </summary>
    private void AllocateDiscountToCartItems(List<CartItem> cartItems, int consumedQuantity,
        decimal totalDiscountAmount, AppliedPromotion promotion)
    {
        var remainingQuantity = consumedQuantity;
        var remainingDiscount = totalDiscountAmount;

        foreach (var cartItem in cartItems)
        {
            if (remainingQuantity <= 0) break;

            var allocatedQuantity = Math.Min(cartItem.Quantity, remainingQuantity);
            var itemDiscountAmount = (totalDiscountAmount * allocatedQuantity) / consumedQuantity;

            // 四舍五入到分
            itemDiscountAmount = Math.Round(itemDiscountAmount, 2);

            // 确保不超过商品原价
            var maxDiscount = cartItem.UnitPrice * allocatedQuantity;
            itemDiscountAmount = Math.Min(itemDiscountAmount, maxDiscount);

            // 更新实际支付单价
            var discountPerUnit = itemDiscountAmount / allocatedQuantity;
            cartItem.ActualUnitPrice = Math.Max(0, cartItem.UnitPrice - discountPerUnit);

            // 添加促销详情
            cartItem.AddPromotionDetail(new ItemPromotionDetail
            {
                RuleId = promotion.RuleId,
                RuleName = promotion.RuleName,
                PromotionType = promotion.PromotionType,
                DiscountAmount = itemDiscountAmount,
                Description = $"{promotion.RuleName} - 优惠 {itemDiscountAmount:C}",
                IsGiftRelated = false
            });

            remainingQuantity -= allocatedQuantity;
            remainingDiscount -= itemDiscountAmount;
        }
    }

    /// <summary>
    /// 处理四舍五入导致的金额差异
    /// </summary>
    private void HandleRoundingIssues(ShoppingCart cart, List<AppliedPromotion> appliedPromotions)
    {
        var expectedTotalDiscount = appliedPromotions.Sum(p => p.DiscountAmount);
        var actualTotalDiscount = cart.TotalDiscountAmount;
        var difference = expectedTotalDiscount - actualTotalDiscount;

        if (Math.Abs(difference) < 0.01m) return; // 差异小于1分，忽略

        // 找到最大金额的非赠品商品进行调整
        var adjustableItem = cart.Items
            .Where(x => !x.IsGift && x.ActualUnitPrice > 0)
            .OrderByDescending(x => x.ActualSubTotal)
            .FirstOrDefault();

        if (adjustableItem != null)
        {
            // 调整实际支付单价
            var adjustment = difference / adjustableItem.Quantity;
            adjustableItem.ActualUnitPrice = Math.Max(0, adjustableItem.ActualUnitPrice - adjustment);

            // 更新促销详情
            var roundingDetail = new ItemPromotionDetail
            {
                RuleId = "ROUNDING_ADJUSTMENT",
                RuleName = "四舍五入调整",
                PromotionType = "RoundingAdjustment",
                DiscountAmount = difference,
                Description = $"四舍五入调整: {difference:C}",
                IsGiftRelated = false
            };

            adjustableItem.AddPromotionDetail(roundingDetail);
        }
    }

    /// <summary>
    /// 验证分摊结果的正确性
    /// </summary>
    public ValidationResult ValidateAllocation(PromotionResult result)
    {
        var validationResult = new ValidationResult();

        // 验证总金额是否正确
        var expectedFinalAmount = result.OriginalAmount - result.TotalDiscount;
        var actualFinalAmount = result.ProcessedCart.ActualTotalAmount;
        var amountDifference = Math.Abs(expectedFinalAmount - actualFinalAmount);

        validationResult.IsValid = amountDifference < 0.01m;
        validationResult.AmountDifference = amountDifference;

        if (!validationResult.IsValid)
        {
            validationResult.ErrorMessage = $"金额验证失败：期望 {expectedFinalAmount:C}，实际 {actualFinalAmount:C}，差异 {amountDifference:C}";
        }

        // 验证赠品是否正确标记
        var giftItems = result.ProcessedCart.Items.Where(x => x.IsGift).ToList();
        foreach (var giftItem in giftItems)
        {
            if (giftItem.ActualUnitPrice != 0)
            {
                validationResult.IsValid = false;
                validationResult.ErrorMessage += $" 赠品 {giftItem.Product.Name} 价格应为0，实际为 {giftItem.ActualUnitPrice:C}";
            }
        }

        return validationResult;
    }
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; set; } = true;
    public decimal AmountDifference { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
}
