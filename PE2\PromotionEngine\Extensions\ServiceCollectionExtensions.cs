global using Microsoft.Extensions.DependencyInjection;
using PE2.PromotionEngine.Core;
using PE2.PromotionEngine.Observability;
using PE2.PromotionEngine.Conditions;
using PE2.PromotionEngine.Inventory;
using PE2.PromotionEngine.Performance;
using PE2.PromotionEngine.Allocation;

namespace PE2.PromotionEngine.Extensions;

/// <summary>
/// 服务集合扩展方法
/// 用于注册促销引擎的所有组件
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加促销引擎服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddPromotionEngine(this IServiceCollection services)
    {
        // 注册核心组件
        services.AddSingleton<IObservabilityEngine, ObservabilityEngine>();
        services.AddSingleton<IConditionEngine, ConditionEngine>();
        services.AddSingleton<IInventoryManager, InventoryManager>();
        services.AddSingleton<IPerformanceOptimizer, PerformanceOptimizer>();
        services.AddSingleton<IAllocationEngine, AllocationEngine>();
        
        // 注册促销编排器
        services.AddScoped<IPromotionOrchestrator, PromotionOrchestrator>();

        // 注册新架构组件
        services.AddSingleton<IPromotionRuleFactory, PromotionRuleFactory>();

        return services;
    }

    /// <summary>
    /// 添加促销引擎服务（带配置）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddPromotionEngine(this IServiceCollection services, Action<PromotionEngineOptions> configureOptions)
    {
        // 配置选项
        services.Configure(configureOptions);
        
        // 添加基础服务
        services.AddPromotionEngine();
        
        return services;
    }
}

/// <summary>
/// 促销引擎配置选项
/// </summary>
public sealed class PromotionEngineOptions
{
    /// <summary>
    /// 最大并发计算数
    /// </summary>
    public int MaxConcurrentCalculations { get; set; } = Environment.ProcessorCount * 4;

    /// <summary>
    /// 结果保留时间
    /// </summary>
    public TimeSpan ResultRetentionTime { get; set; } = TimeSpan.FromHours(2);

    /// <summary>
    /// 是否启用性能监控
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = true;

    /// <summary>
    /// 是否启用详细日志
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = false;

    /// <summary>
    /// 缓存配置
    /// </summary>
    public CacheOptions Cache { get; set; } = new();

    /// <summary>
    /// 内存池配置
    /// </summary>
    public MemoryPoolOptions MemoryPool { get; set; } = new();
}

/// <summary>
/// 缓存配置选项
/// </summary>
public sealed class CacheOptions
{
    /// <summary>
    /// 默认过期时间
    /// </summary>
    public TimeSpan DefaultExpiration { get; set; } = TimeSpan.FromMinutes(30);

    /// <summary>
    /// 最大缓存项数
    /// </summary>
    public int MaxCacheItems { get; set; } = 10000;

    /// <summary>
    /// 是否启用分布式缓存
    /// </summary>
    public bool EnableDistributedCache { get; set; } = false;
}

/// <summary>
/// 内存池配置选项
/// </summary>
public sealed class MemoryPoolOptions
{
    /// <summary>
    /// StringBuilder 池大小
    /// </summary>
    public int StringBuilderPoolSize { get; set; } = 100;

    /// <summary>
    /// List 池大小
    /// </summary>
    public int ListPoolSize { get; set; } = 50;

    /// <summary>
    /// Dictionary 池大小
    /// </summary>
    public int DictionaryPoolSize { get; set; } = 50;

    /// <summary>
    /// 是否启用内存监控
    /// </summary>
    public bool EnableMemoryMonitoring { get; set; } = true;
}
