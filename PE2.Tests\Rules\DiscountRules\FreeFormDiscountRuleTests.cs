using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.DiscountRules;

/// <summary>
/// 自由组合折扣规则测试类
/// 测试 FreeFormDiscountRule 的自由组合折扣计算和应用逻辑
/// </summary>
public class FreeFormDiscountRuleTests(ITestOutputHelper output) : TestBase(output)
{
    #region 基础自由组合折扣功能测试

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_ValidFreeFormDiscountScenario_ShouldApplyCorrectly()
    {
        // Arrange - 自由组合折扣：A、B、C商品自由组合，满2件各自享受对应折扣
        var rule = TestDataGenerator.CreateFreeFormDiscountRule_ByQuantity();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            (TestDataGenerator.CreateProductA(), 1), // 1件A
            (TestDataGenerator.CreateProductB(), 1), // 1件B，总共2件，满足条件
            (TestDataGenerator.CreateProductC(), 1) // 1件C，总共3件
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "自由组合折扣测试购物车");

        var priceA = TestDataGenerator.CreateProductA().Price; // 50元
        var priceB = TestDataGenerator.CreateProductB().Price; // 30元
        var priceC = TestDataGenerator.CreateProductC().Price; // 20元

        // 根据规则配置，总数量3件，A享受9折，B享受8折，C享受7折
        var expectedDiscountA = priceA * (1 - 0.7m); // 15元
        var expectedDiscountB = priceB * (1 - 0.6m); // 12元
        var expectedDiscountC = priceC * (1 - 0.5m); // 10元
        var expectedTotalDiscount = expectedDiscountA + expectedDiscountB + expectedDiscountC; // 17元

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "自由组合折扣测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证各商品的实际单价被调整
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        var productCItem = result.ProcessedCart.Items.First(i => i.Product.Id == "C");

        //AssertAmountEqual(priceA * 0.7m, productAItem.ActualUnitPrice, "商品A的实际单价应为9折");
        //AssertAmountEqual(priceB * 0.6m, productBItem.ActualUnitPrice, "商品B的实际单价应为8折");
        //AssertAmountEqual(priceC * 0.5m, productCItem.ActualUnitPrice, "商品C的实际单价应为7折");

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为各商品折扣的累计金额");

        Output.WriteLine($"A商品9折: {priceA:C} -> {priceA * 0.9m:C}, 节省: {expectedDiscountA:C}");
        Output.WriteLine($"B商品8折: {priceB:C} -> {priceB * 0.8m:C}, 节省: {expectedDiscountB:C}");
        Output.WriteLine($"C商品7折: {priceC:C} -> {priceC * 0.7m:C}, 节省: {expectedDiscountC:C}");
        Output.WriteLine($"总节省: {expectedTotalDiscount:C}");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_InsufficientQuantity_ShouldNotApply()
    {
        // Arrange - 数量不足：只有1件商品，不满足最低条件（2件）
        var rule = TestDataGenerator.CreateFreeFormDiscountRule_ByQuantity();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_002",
            (TestDataGenerator.CreateProductD(), 1) // 1件A，不满足最低条件
        );

        LogCartDetails(cart, "数量不足的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "数量不足场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "数量不足时应无优惠");

        // 验证商品价格未变
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "D");
        AssertAmountEqual(
            TestDataGenerator.CreateProductD().Price,
            productAItem.ActualUnitPrice,
            "价格应保持原价"
        );
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_PartialProductMatch_ShouldApplyToMatchingProducts()
    {
        // Arrange - 部分商品匹配：购物车中有规则外的商品
        var rule = TestDataGenerator.CreateFreeFormDiscountRule_ByQuantity(); // 适用于A、B、C
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_003",
            (TestDataGenerator.CreateProductA(), 1), // 1件A，在规则中
            (TestDataGenerator.CreateProductB(), 1), // 1件B，在规则中
            (TestDataGenerator.CreateProductD(), 2) // 2件D，不在规则中
        );

        LogCartDetails(cart, "部分商品匹配的购物车");

        var priceA = TestDataGenerator.CreateProductA().Price; // 50元
        var priceB = TestDataGenerator.CreateProductB().Price; // 30元
        var priceD = TestDataGenerator.CreateProductD().Price; // 15元

        // A、B总共2件，满足条件，各自享受对应折扣；D不在规则中，保持原价
        var expectedDiscountA = priceA * (1 - 0.8m);
        var expectedDiscountB = priceB * (1 - 0.7m);
        var expectedTotalDiscount = expectedDiscountA + expectedDiscountB;

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "部分商品匹配测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证A、B享受折扣，D保持原价
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        var productDItem = result.ProcessedCart.Items.First(i => i.Product.Id == "D");

        //AssertAmountEqual(priceA * 0.9m, productAItem.ActualUnitPrice, "商品A的实际单价应为9折");
        //AssertAmountEqual(priceB * 0.8m, productBItem.ActualUnitPrice, "商品B的实际单价应为8折");
        //AssertAmountEqual(priceD, productDItem.ActualUnitPrice, "商品D的实际单价应保持原价");

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为匹配商品的折扣累计金额");

        Output.WriteLine($"A商品享受折扣: {priceA:C} -> {priceA * 0.8m:C}");
        Output.WriteLine($"B商品享受折扣: {priceB:C} -> {priceB * 0.7m:C}");
        Output.WriteLine($"D商品保持原价: {priceD:C}");
    }

    #endregion

    #region 基于金额的自由组合折扣测试

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_AmountBasedFreeFormDiscount_ShouldApplyCorrectly()
    {
        // Arrange - 基于金额的自由组合折扣：满100元各自享受对应折扣
        var rule = TestDataGenerator.CreateFreeFormDiscountRule_ByAmount();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_AMOUNT_001",
            (TestDataGenerator.CreateProductA(), 2), // 2件A，100元
            (TestDataGenerator.CreateProductB(), 1) // 1件B，30元，总共130元
        );

        LogCartDetails(cart, "基于金额的自由组合折扣测试购物车");

        var priceA = TestDataGenerator.CreateProductA().Price; // 50元
        var priceB = TestDataGenerator.CreateProductB().Price; // 30元
        var totalAmount = priceA * 2 + priceB; // 130元

        // 总金额130元，满足100元条件，A享受9折，B享受8折
        var expectedDiscountA = priceA * (1 - 0.9m) * 2; // 10元
        var expectedDiscountB = priceB * (1 - 0.8m); // 6元
        var expectedTotalDiscount = expectedDiscountA + expectedDiscountB; // 16元

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "基于金额的自由组合折扣测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证各商品的实际单价被调整
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");

        //AssertAmountEqual(priceA * 0.9m, productAItem.ActualUnitPrice, "商品A的实际单价应为9折");
        //AssertAmountEqual(priceB * 0.8m, productBItem.ActualUnitPrice, "商品B的实际单价应为8折");

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为各商品折扣的累计金额");

        Output.WriteLine($"总金额: {totalAmount:C}, 满足100元条件");
        Output.WriteLine(
            $"A商品2件9折: {priceA * 2:C} -> {priceA * 0.9m * 2:C}, 节省: {expectedDiscountA:C}"
        );
        Output.WriteLine($"B商品1件8折: {priceB:C} -> {priceB * 0.8m:C}, 节省: {expectedDiscountB:C}");
        Output.WriteLine($"总节省: {expectedTotalDiscount:C}");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_AmountBasedInsufficientAmount_ShouldNotApply()
    {
        // Arrange - 金额不足：总价50元，不满足最低条件（100元）
        var rule = TestDataGenerator.CreateFreeFormDiscountRule_ByAmount();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_AMOUNT_002",
            (TestDataGenerator.CreateProductA(), 1) // 1件A，50元，不满足100元条件
        );

        LogCartDetails(cart, "金额不足的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "金额不足场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "金额不足时应无优惠");

        // 验证商品价格未变
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(
            TestDataGenerator.CreateProductA().Price,
            productAItem.ActualUnitPrice,
            "价格应保持原价"
        );
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Low")]
    public void Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateFreeFormDiscountRule_ByQuantity();
        var cart = TestDataGenerator.CreateEmptyCart();

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "空购物车");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertAmountEqual(0m, result.TotalDiscount, "空购物车应无优惠");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Low")]
    public void Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateFreeFormDiscountRule_ByQuantity();
        rule.IsEnabled = false; // 禁用规则

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_DISABLED",
            (TestDataGenerator.CreateProductA(), 2),
            (TestDataGenerator.CreateProductB(), 1)
        );

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "禁用规则场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");
        AssertAmountEqual(0m, result.TotalDiscount, "禁用规则应无优惠");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Medium")]
    public void Apply_NonApplicableProducts_ShouldNotApply()
    {
        // Arrange - 购物车中没有适用的商品
        var rule = TestDataGenerator.CreateFreeFormDiscountRule_ByQuantity(); // 适用于A、B、C商品
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_NON_APPLICABLE",
            (TestDataGenerator.CreateProductD(), 5) // 5件D，不在规则适用范围内
        );

        LogCartDetails(cart, "非适用商品购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "非适用商品场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");
        AssertAmountEqual(0m, result.TotalDiscount, "非适用商品应无优惠");

        // 验证商品价格未变
        var productDItem = result.ProcessedCart.Items.First(i => i.Product.Id == "D");
        AssertAmountEqual(
            TestDataGenerator.CreateProductD().Price,
            productDItem.ActualUnitPrice,
            "非适用商品价格应保持原价"
        );
    }

    #endregion

    #region 性能测试

    [Fact]
    [Trait("Category", "Performance")]
    [Trait("Priority", "Low")]
    public void Apply_LargeCart_ShouldPerformWell()
    {
        // Arrange
        var rule = TestDataGenerator.CreateFreeFormDiscountRule_ByQuantity();
        var cart = TestDataGenerator.CreateLargeTestCart(100); // 100个商品项

        // 添加适用的商品
        cart.Items.AddRange(
            [
                new()
                {
                    Product = TestDataGenerator.CreateProductA(),
                    Quantity = 20,
                    UnitPrice = TestDataGenerator.CreateProductA().Price
                },
                new()
                {
                    Product = TestDataGenerator.CreateProductB(),
                    Quantity = 15,
                    UnitPrice = TestDataGenerator.CreateProductB().Price
                },
                new()
                {
                    Product = TestDataGenerator.CreateProductC(),
                    Quantity = 10,
                    UnitPrice = TestDataGenerator.CreateProductC().Price
                }
            ]
        );
        cart.InitializeActualPrices();

        Output.WriteLine($"大型购物车测试: {cart.Items.Count} 个商品项");

        TestPromotionRuleService.Rules = [rule];
        // Act & Assert
        var executionTime = MeasureExecutionTime(() =>
        {
            var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;
            AssertPromotionResult(result, "大型购物车性能测试");
        });

        // 性能断言：大型购物车处理应在合理时间内完成
        AssertPerformance(executionTime, TimeSpan.FromMilliseconds(500), "大型购物车自由组合折扣规则计算");
    }

    #endregion
}
