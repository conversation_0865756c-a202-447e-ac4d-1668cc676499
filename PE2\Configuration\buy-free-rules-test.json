[{"$type": "ProductBuyFree", "id": "BUY_FREE_001", "name": "商品买免测试 - 买3免1（不翻倍）", "description": "购买A商品3件时，免1件", "priority": 70, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "freeItemSelectionStrategy": "MerchantBenefit", "applicableProductIds": ["A"], "buyQuantity": 3, "freeQuantity": 1, "isByAmount": false, "minAmount": 0}, {"$type": "ProductBuyFree", "id": "BUY_FREE_002", "name": "商品买免测试 - 买3免1（翻2倍）", "description": "购买A商品3件时，免1件，支持翻倍", "priority": 75, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 2, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "freeItemSelectionStrategy": "MerchantBenefit", "applicableProductIds": ["A"], "buyQuantity": 3, "freeQuantity": 1, "isByAmount": false, "minAmount": 0}, {"$type": "ProductBuyFree", "id": "BUY_FREE_003", "name": "商品买免测试 - 客户利益最大化", "description": "购买A或B商品3件时，免1件（优先免费高价商品）", "priority": 65, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "freeItemSelectionStrategy": "CustomerBenefit", "applicableProductIds": ["A", "B"], "buyQuantity": 3, "freeQuantity": 1, "isByAmount": false, "minAmount": 0}, {"$type": "CombinationBuyFree", "id": "COMBINATION_BUY_FREE_001", "name": "组合买免测试 - 买A+B免最低价（不翻倍）", "description": "购买A商品2件、B商品1件，免费送其中价格最低的1件商品", "priority": 80, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "freeItemSelectionStrategy": "MerchantBenefit", "freeQuantity": 1, "freeFromCombinationOnly": true, "combinationConditions": [{"productId": "A", "requiredQuantity": 2, "requiredAmount": 0}, {"productId": "B", "requiredQuantity": 1, "requiredAmount": 0}]}, {"$type": "CombinationBuyFree", "id": "COMBINATION_BUY_FREE_002", "name": "组合买免测试 - 买A+B免最低价（翻2倍）", "description": "购买A商品2件、B商品1件，免费送其中价格最低的1件商品，支持翻倍", "priority": 85, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 2, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "freeItemSelectionStrategy": "MerchantBenefit", "freeQuantity": 1, "freeFromCombinationOnly": true, "combinationConditions": [{"productId": "A", "requiredQuantity": 3, "requiredAmount": 0}, {"productId": "B", "requiredQuantity": 1, "requiredAmount": 0}]}, {"$type": "CombinationBuyFree", "id": "COMBINATION_BUY_FREE_003", "name": "组合买免测试 - 客户利益最大化", "description": "购买A+B+C各1件，免费送其中价格最高的1件商品", "priority": 75, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "freeItemSelectionStrategy": "CustomerBenefit", "freeQuantity": 1, "freeFromCombinationOnly": true, "combinationConditions": [{"productId": "A", "requiredQuantity": 1, "requiredAmount": 0}, {"productId": "B", "requiredQuantity": 1, "requiredAmount": 0}, {"productId": "C", "requiredQuantity": 1, "requiredAmount": 0}]}, {"$type": "ProductBuyFree", "id": "BUY_FREE_AMOUNT_001", "name": "按金额买免测试 - 满1000元免1件", "description": "购买A商品满1000元时，免1件A商品", "priority": 60, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "freeItemSelectionStrategy": "MerchantBenefit", "applicableProductIds": ["A"], "buyQuantity": 0, "freeQuantity": 1, "isByAmount": true, "minAmount": 1000.0}]