using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Rules;

namespace PE2.PromotionEngine.Core;

/// <summary>
/// 条件与执行分离器 - 实现促销条件验证与执行的清晰分离
/// 核心设计模式：两阶段提交 + 条件锁定 + 执行验证
/// </summary>
public sealed class ConditionExecutionSeparator
{
    private readonly ProductOccupationEngine _occupationEngine;
    private readonly ILogger<ConditionExecutionSeparator> _logger;

    public ConditionExecutionSeparator(
        ProductOccupationEngine occupationEngine,
        ILogger<ConditionExecutionSeparator> logger)
    {
        _occupationEngine = occupationEngine ?? throw new ArgumentNullException(nameof(occupationEngine));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 执行条件与执行分离的促销应用
    /// </summary>
    public async Task<SeparatedPromotionResult> ApplyPromotionWithSeparationAsync(
        ProcessedCart cart,
        PromotionRuleBase rule,
        CancellationToken cancellationToken = default)
    {
        using var session = await _occupationEngine.CreateSessionAsync(cart, cancellationToken: cancellationToken).ConfigureAwait(false);
        
        try
        {
            // 阶段1：条件验证和预留
            var conditionResult = await ValidateAndReserveConditionsAsync(session, rule, cancellationToken).ConfigureAwait(false);
            if (!conditionResult.IsSuccessful)
            {
                return new SeparatedPromotionResult
                {
                    IsSuccessful = false,
                    FailureStage = PromotionStage.ConditionValidation,
                    ErrorMessage = conditionResult.ErrorMessage,
                    ConditionResult = conditionResult
                };
            }

            // 阶段2：执行验证和预留
            var executionResult = await ValidateAndReserveExecutionAsync(session, rule, cancellationToken).ConfigureAwait(false);
            if (!executionResult.IsSuccessful)
            {
                // 释放条件预留
                await session.ReleaseReservationsAsync(rule.Id, cancellationToken).ConfigureAwait(false);
                
                return new SeparatedPromotionResult
                {
                    IsSuccessful = false,
                    FailureStage = PromotionStage.ExecutionValidation,
                    ErrorMessage = executionResult.ErrorMessage,
                    ConditionResult = conditionResult,
                    ExecutionResult = executionResult
                };
            }

            // 阶段3：应用促销效果
            var applicationResult = await ApplyPromotionEffectsAsync(session, rule, conditionResult, executionResult, cancellationToken).ConfigureAwait(false);
            if (!applicationResult.IsSuccessful)
            {
                // 释放所有预留
                await session.ReleaseReservationsAsync(rule.Id, cancellationToken).ConfigureAwait(false);
                
                return new SeparatedPromotionResult
                {
                    IsSuccessful = false,
                    FailureStage = PromotionStage.EffectApplication,
                    ErrorMessage = applicationResult.ErrorMessage,
                    ConditionResult = conditionResult,
                    ExecutionResult = executionResult,
                    ApplicationResult = applicationResult
                };
            }

            // 阶段4：提交会话
            var commitResult = await session.CommitAsync(cancellationToken).ConfigureAwait(false);
            if (!commitResult.IsSuccessful)
            {
                return new SeparatedPromotionResult
                {
                    IsSuccessful = false,
                    FailureStage = PromotionStage.Commit,
                    ErrorMessage = commitResult.ErrorMessage,
                    ConditionResult = conditionResult,
                    ExecutionResult = executionResult,
                    ApplicationResult = applicationResult
                };
            }

            return new SeparatedPromotionResult
            {
                IsSuccessful = true,
                ConditionResult = conditionResult,
                ExecutionResult = executionResult,
                ApplicationResult = applicationResult,
                FinalCart = session.Cart
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "应用促销时发生异常: RuleId={RuleId}", rule.Id);
            await session.RollbackAsync(cancellationToken).ConfigureAwait(false);
            
            return new SeparatedPromotionResult
            {
                IsSuccessful = false,
                FailureStage = PromotionStage.Exception,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// 阶段1：验证和预留条件商品
    /// </summary>
    private async Task<ConditionValidationResult> ValidateAndReserveConditionsAsync(
        IOccupationSession session,
        PromotionRuleBase rule,
        CancellationToken cancellationToken)
    {
        try
        {
            // 根据规则类型获取条件要求
            var conditionRequirements = ExtractConditionRequirements(rule);
            var reservationResults = new List<ReservationResult>();

            foreach (var requirement in conditionRequirements)
            {
                var reservationResult = await session.TryReserveForConditionAsync(
                    rule.Id,
                    rule.Name,
                    requirement.ProductIds,
                    requirement.RequiredQuantity,
                    rule.ProductExclusivity,
                    rule.CanStackWithOthers,
                    cancellationToken).ConfigureAwait(false);

                if (!reservationResult.IsSuccessful)
                {
                    return new ConditionValidationResult
                    {
                        IsSuccessful = false,
                        ErrorMessage = $"条件验证失败: {reservationResult.ErrorMessage}",
                        FailedRequirement = requirement,
                        Conflicts = reservationResult.Conflicts
                    };
                }

                reservationResults.Add(reservationResult);
            }

            return new ConditionValidationResult
            {
                IsSuccessful = true,
                Requirements = conditionRequirements,
                ReservationResults = reservationResults
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "条件验证时发生异常: RuleId={RuleId}", rule.Id);
            return new ConditionValidationResult
            {
                IsSuccessful = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// 阶段2：验证和预留执行商品
    /// </summary>
    private async Task<ExecutionValidationResult> ValidateAndReserveExecutionAsync(
        IOccupationSession session,
        PromotionRuleBase rule,
        CancellationToken cancellationToken)
    {
        try
        {
            // 根据规则类型获取执行要求
            var executionRequirements = ExtractExecutionRequirements(rule);
            var reservationResults = new List<ReservationResult>();

            foreach (var requirement in executionRequirements)
            {
                var reservationResult = await session.TryReserveForExecutionAsync(
                    rule.Id,
                    rule.Name,
                    requirement.ProductIds,
                    requirement.RequiredQuantity,
                    rule.ProductExclusivity,
                    rule.CanStackWithOthers,
                    cancellationToken).ConfigureAwait(false);

                if (!reservationResult.IsSuccessful)
                {
                    return new ExecutionValidationResult
                    {
                        IsSuccessful = false,
                        ErrorMessage = $"执行验证失败: {reservationResult.ErrorMessage}",
                        FailedRequirement = requirement,
                        Conflicts = reservationResult.Conflicts
                    };
                }

                reservationResults.Add(reservationResult);
            }

            return new ExecutionValidationResult
            {
                IsSuccessful = true,
                Requirements = executionRequirements,
                ReservationResults = reservationResults
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行验证时发生异常: RuleId={RuleId}", rule.Id);
            return new ExecutionValidationResult
            {
                IsSuccessful = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// 阶段3：应用促销效果
    /// </summary>
    private async Task<EffectApplicationResult> ApplyPromotionEffectsAsync(
        IOccupationSession session,
        PromotionRuleBase rule,
        ConditionValidationResult conditionResult,
        ExecutionValidationResult executionResult,
        CancellationToken cancellationToken)
    {
        return await Task.Run(() =>
        {
            try
            {
                var effects = new List<PromotionEffect>();

                // 根据规则类型应用不同的效果
                switch (rule)
                {
                    case IDiscountRule discountRule:
                        effects.AddRange(ApplyDiscountEffects(discountRule, executionResult));
                        break;
                    
                    case ISpecialPriceRule specialPriceRule:
                        effects.AddRange(ApplySpecialPriceEffects(specialPriceRule, executionResult));
                        break;
                    
                    case IBuyFreeRule buyFreeRule:
                        effects.AddRange(ApplyBuyFreeEffects(buyFreeRule, executionResult));
                        break;
                    
                    case IBuyGiftRule buyGiftRule:
                        effects.AddRange(ApplyBuyGiftEffects(buyGiftRule, executionResult));
                        break;
                    
                    default:
                        return new EffectApplicationResult
                        {
                            IsSuccessful = false,
                            ErrorMessage = $"不支持的规则类型: {rule.GetType().Name}"
                        };
                }

                // 应用效果到购物车
                ApplyEffectsToCart(session.Cart, effects);

                return new EffectApplicationResult
                {
                    IsSuccessful = true,
                    AppliedEffects = effects
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应用促销效果时发生异常: RuleId={RuleId}", rule.Id);
                return new EffectApplicationResult
                {
                    IsSuccessful = false,
                    ErrorMessage = ex.Message
                };
            }
        }, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 提取条件要求
    /// </summary>
    private List<ProductRequirement> ExtractConditionRequirements(PromotionRuleBase rule)
    {
        return rule switch
        {
            IBuyXGetYRule buyXGetY => [new ProductRequirement
            {
                ProductIds = buyXGetY.ApplicableProductIds,
                RequiredQuantity = buyXGetY.BuyQuantity,
                RequirementType = RequirementType.Condition,
                Description = $"购买{buyXGetY.BuyQuantity}件"
            }],
            
            IExchangeRule exchange => [new ProductRequirement
            {
                ProductIds = exchange.ConditionProductIds,
                RequiredQuantity = exchange.RequiredQuantity,
                RequirementType = RequirementType.Condition,
                Description = $"满足{exchange.RequiredQuantity}件条件"
            }],
            
            _ => []
        };
    }

    /// <summary>
    /// 提取执行要求
    /// </summary>
    private List<ProductRequirement> ExtractExecutionRequirements(PromotionRuleBase rule)
    {
        return rule switch
        {
            IBuyXGetYRule buyXGetY => [new ProductRequirement
            {
                ProductIds = buyXGetY.ApplicableProductIds,
                RequiredQuantity = buyXGetY.FreeQuantity,
                RequirementType = RequirementType.Execution,
                Description = $"免费{buyXGetY.FreeQuantity}件"
            }],
            
            IExchangeRule exchange => [new ProductRequirement
            {
                ProductIds = exchange.ExchangeProductIds,
                RequiredQuantity = exchange.ExchangeQuantity,
                RequirementType = RequirementType.Execution,
                Description = $"换购{exchange.ExchangeQuantity}件"
            }],
            
            IDiscountRule discount => [new ProductRequirement
            {
                ProductIds = discount.ApplicableProductIds,
                RequiredQuantity = discount.MinQuantity,
                RequirementType = RequirementType.Execution,
                Description = $"打折{discount.MinQuantity}件"
            }],
            
            _ => []
        };
    }

    /// <summary>
    /// 应用折扣效果
    /// </summary>
    private List<PromotionEffect> ApplyDiscountEffects(IDiscountRule rule, ExecutionValidationResult executionResult)
    {
        var effects = new List<PromotionEffect>();
        
        foreach (var reservationResult in executionResult.ReservationResults)
        {
            foreach (var reservedProduct in reservationResult.ReservedProducts)
            {
                var discountedPrice = reservedProduct.UnitPrice * rule.DiscountRate;
                effects.Add(new PromotionEffect
                {
                    ProductId = reservedProduct.ProductId,
                    EffectType = PromotionEffectType.PriceChange,
                    OriginalValue = reservedProduct.UnitPrice,
                    NewValue = discountedPrice,
                    Quantity = reservedProduct.ReservedQuantity,
                    Description = $"打{rule.DiscountRate:P0}折"
                });
            }
        }
        
        return effects;
    }

    /// <summary>
    /// 应用特价效果
    /// </summary>
    private List<PromotionEffect> ApplySpecialPriceEffects(ISpecialPriceRule rule, ExecutionValidationResult executionResult)
    {
        var effects = new List<PromotionEffect>();
        
        foreach (var reservationResult in executionResult.ReservationResults)
        {
            foreach (var reservedProduct in reservationResult.ReservedProducts)
            {
                effects.Add(new PromotionEffect
                {
                    ProductId = reservedProduct.ProductId,
                    EffectType = PromotionEffectType.PriceChange,
                    OriginalValue = reservedProduct.UnitPrice,
                    NewValue = rule.SpecialPrice,
                    Quantity = reservedProduct.ReservedQuantity,
                    Description = $"特价{rule.SpecialPrice:C}"
                });
            }
        }
        
        return effects;
    }

    /// <summary>
    /// 应用买免效果
    /// </summary>
    private List<PromotionEffect> ApplyBuyFreeEffects(IBuyFreeRule rule, ExecutionValidationResult executionResult)
    {
        var effects = new List<PromotionEffect>();
        
        foreach (var reservationResult in executionResult.ReservationResults)
        {
            foreach (var reservedProduct in reservationResult.ReservedProducts)
            {
                effects.Add(new PromotionEffect
                {
                    ProductId = reservedProduct.ProductId,
                    EffectType = PromotionEffectType.PriceChange,
                    OriginalValue = reservedProduct.UnitPrice,
                    NewValue = 0,
                    Quantity = reservedProduct.ReservedQuantity,
                    Description = "免费"
                });
            }
        }
        
        return effects;
    }

    /// <summary>
    /// 应用买赠效果
    /// </summary>
    private List<PromotionEffect> ApplyBuyGiftEffects(IBuyGiftRule rule, ExecutionValidationResult executionResult)
    {
        var effects = new List<PromotionEffect>();
        
        foreach (var reservationResult in executionResult.ReservationResults)
        {
            foreach (var reservedProduct in reservationResult.ReservedProducts)
            {
                effects.Add(new PromotionEffect
                {
                    ProductId = reservedProduct.ProductId,
                    EffectType = PromotionEffectType.PriceChange,
                    OriginalValue = reservedProduct.UnitPrice,
                    NewValue = 0,
                    Quantity = reservedProduct.ReservedQuantity,
                    Description = "赠品"
                });
            }
        }
        
        return effects;
    }

    /// <summary>
    /// 应用效果到购物车
    /// </summary>
    private void ApplyEffectsToCart(ProcessedCart cart, List<PromotionEffect> effects)
    {
        foreach (var effect in effects)
        {
            var cartItem = cart.Items.FirstOrDefault(i => i.ProductId == effect.ProductId);
            if (cartItem != null && effect.EffectType == PromotionEffectType.PriceChange)
            {
                cartItem.FinalUnitPrice = effect.NewValue;
            }
        }
    }
}

/// <summary>
/// 分离式促销结果
/// </summary>
public sealed class SeparatedPromotionResult
{
    public bool IsSuccessful { get; init; }
    public PromotionStage FailureStage { get; init; }
    public string ErrorMessage { get; init; } = string.Empty;
    public ConditionValidationResult? ConditionResult { get; init; }
    public ExecutionValidationResult? ExecutionResult { get; init; }
    public EffectApplicationResult? ApplicationResult { get; init; }
    public ProcessedCart? FinalCart { get; init; }
}

/// <summary>
/// 促销阶段
/// </summary>
public enum PromotionStage
{
    ConditionValidation,
    ExecutionValidation,
    EffectApplication,
    Commit,
    Exception
}

/// <summary>
/// 条件验证结果
/// </summary>
public sealed class ConditionValidationResult
{
    public bool IsSuccessful { get; init; }
    public string ErrorMessage { get; init; } = string.Empty;
    public List<ProductRequirement> Requirements { get; init; } = [];
    public List<ReservationResult> ReservationResults { get; init; } = [];
    public ProductRequirement? FailedRequirement { get; init; }
    public List<ConflictInfo> Conflicts { get; init; } = [];
}

/// <summary>
/// 执行验证结果
/// </summary>
public sealed class ExecutionValidationResult
{
    public bool IsSuccessful { get; init; }
    public string ErrorMessage { get; init; } = string.Empty;
    public List<ProductRequirement> Requirements { get; init; } = [];
    public List<ReservationResult> ReservationResults { get; init; } = [];
    public ProductRequirement? FailedRequirement { get; init; }
    public List<ConflictInfo> Conflicts { get; init; } = [];
}

/// <summary>
/// 效果应用结果
/// </summary>
public sealed class EffectApplicationResult
{
    public bool IsSuccessful { get; init; }
    public string ErrorMessage { get; init; } = string.Empty;
    public List<PromotionEffect> AppliedEffects { get; init; } = [];
}

/// <summary>
/// 商品要求
/// </summary>
public sealed class ProductRequirement
{
    public required List<string> ProductIds { get; init; }
    public int RequiredQuantity { get; init; }
    public RequirementType RequirementType { get; init; }
    public string Description { get; init; } = string.Empty;
}

/// <summary>
/// 要求类型
/// </summary>
public enum RequirementType
{
    Condition,
    Execution
}

/// <summary>
/// 促销效果
/// </summary>
public sealed class PromotionEffect
{
    public required string ProductId { get; init; }
    public PromotionEffectType EffectType { get; init; }
    public decimal OriginalValue { get; init; }
    public decimal NewValue { get; init; }
    public int Quantity { get; init; }
    public string Description { get; init; } = string.Empty;
}

/// <summary>
/// 促销效果类型
/// </summary>
public enum PromotionEffectType
{
    PriceChange,
    QuantityChange,
    Addition,
    Removal
}

// 规则接口定义
public interface IDiscountRule
{
    List<string> ApplicableProductIds { get; }
    decimal DiscountRate { get; }
    int MinQuantity { get; }
}

public interface ISpecialPriceRule
{
    List<string> ApplicableProductIds { get; }
    decimal SpecialPrice { get; }
    int MinQuantity { get; }
}

public interface IBuyFreeRule
{
    List<string> ApplicableProductIds { get; }
    int BuyQuantity { get; }
    int FreeQuantity { get; }
}

public interface IBuyGiftRule
{
    List<string> ConditionProductIds { get; }
    List<string> GiftProductIds { get; }
    int RequiredQuantity { get; }
    int GiftQuantity { get; }
}

public interface IBuyXGetYRule
{
    List<string> ApplicableProductIds { get; }
    int BuyQuantity { get; }
    int FreeQuantity { get; }
}

public interface IExchangeRule
{
    List<string> ConditionProductIds { get; }
    List<string> ExchangeProductIds { get; }
    int RequiredQuantity { get; }
    int ExchangeQuantity { get; }
}
