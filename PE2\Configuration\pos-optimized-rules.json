[{"$type": "UnifiedGift", "id": "POS_UNIFIED_001", "name": "POS优化 - 买3件A或B送1件A或B", "description": "购买A或B商品3件，从剩余的A或B中赠送1件（需要总共4件才能满足条件）", "priority": 30, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 3, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "isByAmount": false, "minAmount": 0, "giftSameProduct": true, "giftSelectionStrategy": "MerchantBenefit", "buyConditions": [{"productIds": ["A", "B"], "requiredQuantity": 3}], "giftConditions": [{"productIds": ["A", "B"], "giftQuantity": 1}]}, {"$type": "UnifiedGift", "id": "POS_UNIFIED_002", "name": "POS优化 - 客户利益最大化买赠", "description": "购买A商品2件，从剩余的B、C、D中选择价值最高的赠送1件", "priority": 25, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "giftSelectionStrategy": "CustomerBenefit", "buyConditions": [{"productIds": ["A"], "requiredQuantity": 2}], "giftConditions": [{"productIds": ["B", "C", "D"], "giftQuantity": 1}]}, {"$type": "GradientGift", "id": "POS_GRADIENT_001", "name": "POS优化 - 梯度送赠品", "description": "购买A商品梯度赠送，从剩余商品中选择赠品", "priority": 35, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "gradientStrategy": "ByGradient", "giftSelectionStrategy": "MerchantBenefit", "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1}], "gradientGiftConditions": [{"gradientLevel": 1, "requiredQuantity": 2, "requiredAmount": 0, "giftProductIds": ["A", "B"], "giftQuantity": 1, "description": "购买2件A从剩余的A、B中选择价值最低的送1件"}, {"gradientLevel": 2, "requiredQuantity": 4, "requiredAmount": 0, "giftProductIds": ["A", "B", "C"], "giftQuantity": 2, "description": "购买4件A从剩余的A、B、C中选择价值最低的送2件"}]}, {"$type": "CombinationGift", "id": "POS_COMBINATION_001", "name": "POS优化 - 组合送赠品", "description": "购买A商品2件+B商品1件，从剩余的A、B、C中选择赠品", "priority": 40, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 2, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftSelectionStrategy": "CustomerBenefit", "combinationConditions": [{"productId": "A", "requiredQuantity": 2, "minAmount": 0}, {"productId": "B", "requiredQuantity": 1, "minAmount": 0}], "giftConditions": [{"productIds": ["A", "B", "C"], "giftQuantity": 1}]}, {"$type": "UnifiedGift", "id": "POS_COMPLEX_001", "name": "POS复杂场景 - 10件A或B买赠", "description": "测试10件A或B的复杂买赠场景，买3送1，可翻倍2次", "priority": 50, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 2, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "isByAmount": false, "minAmount": 0, "giftSameProduct": true, "giftSelectionStrategy": "MerchantBenefit", "buyConditions": [{"productIds": ["A", "B"], "requiredQuantity": 3}], "giftConditions": [{"productIds": ["A", "B"], "giftQuantity": 1}]}]