[{"$type": "PercentageDiscount", "id": "RULE001", "name": "商品B满2件8折", "description": "购买商品B满2件享受8折优惠", "priority": 10, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 0, "applicableCustomerTypes": [], "exclusiveRuleIds": ["RULE007"], "canStackWithOthers": true, "productExclusivity": "None", "applicableProductIds": ["B"], "discountPercentage": 0.8, "minQuantity": 2, "maxDiscountQuantity": 0}, {"$type": "BuyXGetY", "id": "RULE002", "name": "买1件A+1件B半价", "description": "购买1件A商品和1件B商品，B商品半价", "priority": 20, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 0, "applicableCustomerTypes": [], "canStackWithOthers": true, "productExclusivity": "None", "exclusiveRuleIds": [], "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1}, {"productIds": ["B"], "requiredQuantity": 1}], "giftConditions": [{"productIds": ["B"], "giftQuantity": 1}], "giftSameProduct": true}, {"$type": "BuyXGetY", "id": "RULE003", "name": "买3件C送1件A", "description": "购买3件C商品，赠送1件A商品", "priority": 15, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 0, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "buyConditions": [{"productIds": ["C"], "requiredQuantity": 3}], "giftConditions": [{"productIds": ["A"], "giftQuantity": 1}], "giftSameProduct": false}, {"$type": "BundleOffer", "id": "RULE004", "name": "A+B+C组合套餐", "description": "购买A+B+C组合，套餐价99元", "priority": 25, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 0, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "bundleItems": [{"productId": "A", "requiredQuantity": 1}, {"productId": "B", "requiredQuantity": 1}, {"productId": "C", "requiredQuantity": 1}], "bundlePrice": 99.0, "allowExcessQuantity": true}, {"$type": "FixedAmountDiscount", "id": "RULE005", "name": "满100减20", "description": "购买满100元减20元", "priority": 5, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "applicableProductIds": [], "minAmount": 100.0, "discountAmount": 20.0, "maxDiscountAmount": 0}, {"$type": "CategoryDiscount", "id": "RULE006", "name": "服装类满3件8折", "description": "购买服装类商品满3件享受8折优惠", "priority": 12, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 0, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "applicableCategories": ["服装"], "minQuantity": 3, "discountPercentage": 0.8, "maxDiscountQuantity": 0, "calculateByCategory": false}, {"$type": "TotalAmountDiscount", "id": "RULE007", "name": "订单满100元5折", "description": "订单总金额满500元享受9折优惠", "priority": 1, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "minOrderAmount": 100.0, "discountPercentage": 0.5, "maxDiscountAmount": 100.0, "excludedProductIds": [], "excludedCategories": []}]