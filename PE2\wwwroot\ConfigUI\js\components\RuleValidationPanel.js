// 规则验证显示组件
const RuleValidationPanel = {
  props: {
    ruleData: {
      type: Object,
      default: () => ({})
    },
    autoValidate: {
      type: Boolean,
      default: true
    }
  },
  
  template: `
    <div class="rule-validation-panel">
      <div class="validation-header">
        <h3>
          <i class="el-icon-s-check"></i>
          规则验证
        </h3>
        <div class="validation-actions">
          <el-button 
            size="small" 
            @click="validateRule"
            :loading="validating"
          >
            重新验证
          </el-button>
          <el-button 
            size="small" 
            type="text"
            @click="showHelp = !showHelp"
          >
            <i class="el-icon-question"></i>
            帮助
          </el-button>
        </div>
      </div>
      
      <!-- 验证结果概览 -->
      <div class="validation-summary" v-if="validationResult">
        <div class="score-display">
          <div class="score-circle" :class="getScoreLevel(validationResult.score)">
            <span class="score-number">{{ validationResult.score }}</span>
            <span class="score-label">分</span>
          </div>
          <div class="score-text">
            <h4>规则质量评分</h4>
            <p>{{ getScoreDescription(validationResult.score) }}</p>
          </div>
        </div>
        
        <div class="validation-stats">
          <div class="stat-item error" v-if="validationResult.errors.length > 0">
            <i class="el-icon-close"></i>
            <span>{{ validationResult.errors.length }} 个错误</span>
          </div>
          <div class="stat-item warning" v-if="validationResult.warnings.length > 0">
            <i class="el-icon-warning"></i>
            <span>{{ validationResult.warnings.length }} 个警告</span>
          </div>
          <div class="stat-item suggestion" v-if="validationResult.suggestions.length > 0">
            <i class="el-icon-info"></i>
            <span>{{ validationResult.suggestions.length }} 个建议</span>
          </div>
          <div class="stat-item success" v-if="validationResult.isValid">
            <i class="el-icon-check"></i>
            <span>验证通过</span>
          </div>
        </div>
      </div>
      
      <!-- 详细验证信息 -->
      <div class="validation-details" v-if="validationResult">
        <!-- 错误列表 -->
        <div class="validation-section" v-if="validationResult.errors.length > 0">
          <h4 class="section-title error">
            <i class="el-icon-close-notification"></i>
            错误 ({{ validationResult.errors.length }})
          </h4>
          <div class="validation-list">
            <div 
              v-for="(error, index) in validationResult.errors" 
              :key="'error-' + index"
              class="validation-item error"
            >
              <i class="el-icon-close"></i>
              <span>{{ error }}</span>
            </div>
          </div>
        </div>
        
        <!-- 警告列表 -->
        <div class="validation-section" v-if="validationResult.warnings.length > 0">
          <h4 class="section-title warning">
            <i class="el-icon-warning-outline"></i>
            警告 ({{ validationResult.warnings.length }})
          </h4>
          <div class="validation-list">
            <div 
              v-for="(warning, index) in validationResult.warnings" 
              :key="'warning-' + index"
              class="validation-item warning"
            >
              <i class="el-icon-warning"></i>
              <span>{{ warning }}</span>
            </div>
          </div>
        </div>
        
        <!-- 建议列表 -->
        <div class="validation-section" v-if="validationResult.suggestions.length > 0">
          <h4 class="section-title suggestion">
            <i class="el-icon-info"></i>
            优化建议 ({{ validationResult.suggestions.length }})
          </h4>
          <div class="validation-list">
            <div 
              v-for="(suggestion, index) in validationResult.suggestions" 
              :key="'suggestion-' + index"
              class="validation-item suggestion"
            >
              <i class="el-icon-info"></i>
              <span>{{ suggestion }}</span>
            </div>
          </div>
        </div>
        
        <!-- 额外的优化建议 -->
        <div class="validation-section" v-if="optimizationSuggestions.length > 0">
          <h4 class="section-title optimization">
            <i class="el-icon-magic-stick"></i>
            智能优化建议 ({{ optimizationSuggestions.length }})
          </h4>
          <div class="validation-list">
            <div 
              v-for="(suggestion, index) in optimizationSuggestions" 
              :key="'opt-' + index"
              class="validation-item optimization"
              :class="suggestion.type"
            >
              <i :class="getSuggestionIcon(suggestion.type)"></i>
              <div class="suggestion-content">
                <span class="suggestion-message">{{ suggestion.message }}</span>
                <span class="suggestion-type">{{ getSuggestionTypeText(suggestion.type) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div class="validation-empty" v-if="!validationResult && !validating">
        <i class="el-icon-document-checked"></i>
        <p>开始配置规则以查看验证结果</p>
      </div>
      
      <!-- 加载状态 -->
      <div class="validation-loading" v-if="validating">
        <i class="el-icon-loading"></i>
        <p>正在验证规则...</p>
      </div>
      
      <!-- 帮助信息 -->
      <div class="validation-help" v-if="showHelp">
        <h4>验证说明</h4>
        <ul>
          <li><strong>错误</strong>：必须修复的问题，否则规则无法正常工作</li>
          <li><strong>警告</strong>：可能影响规则效果的问题，建议修复</li>
          <li><strong>建议</strong>：优化建议，可以提升规则的完整性和效果</li>
          <li><strong>评分</strong>：基于错误、警告和完整性的综合评分（0-100分）</li>
        </ul>
        <div class="score-levels">
          <div class="score-level excellent">90-100分：优秀</div>
          <div class="score-level good">70-89分：良好</div>
          <div class="score-level fair">50-69分：一般</div>
          <div class="score-level poor">0-49分：需要改进</div>
        </div>
      </div>
    </div>
  `,
  
  data() {
    return {
      validationResult: null,
      optimizationSuggestions: [],
      validating: false,
      showHelp: false
    };
  },
  
  watch: {
    ruleData: {
      handler(newData) {
        if (this.autoValidate && newData && Object.keys(newData).length > 0) {
          this.validateRule();
        }
      },
      deep: true,
      immediate: true
    }
  },
  
  methods: {
    validateRule() {
      if (!this.ruleData || Object.keys(this.ruleData).length === 0) {
        this.validationResult = null;
        this.optimizationSuggestions = [];
        return;
      }
      
      this.validating = true;
      
      // 模拟异步验证过程
      setTimeout(() => {
        try {
          this.validationResult = window.RuleValidator.validateRule(this.ruleData);
          this.optimizationSuggestions = window.RuleValidator.getOptimizationSuggestions(this.ruleData);
          
          // 发出验证结果事件
          this.$emit('validation-result', this.validationResult);
        } catch (error) {
          console.error('验证规则时出错:', error);
          this.validationResult = {
            isValid: false,
            errors: ['验证过程中出现错误: ' + error.message],
            warnings: [],
            suggestions: [],
            score: 0
          };
        }
        
        this.validating = false;
      }, 300);
    },
    
    getScoreLevel(score) {
      if (score >= 90) return 'excellent';
      if (score >= 70) return 'good';
      if (score >= 50) return 'fair';
      return 'poor';
    },
    
    getScoreDescription(score) {
      if (score >= 90) return '规则配置优秀，可以直接使用';
      if (score >= 70) return '规则配置良好，建议查看优化建议';
      if (score >= 50) return '规则配置一般，需要修复警告';
      return '规则配置需要改进，请修复错误';
    },
    
    getSuggestionIcon(type) {
      const icons = {
        performance: 'el-icon-lightning',
        ux: 'el-icon-user',
        business: 'el-icon-money',
        default: 'el-icon-info'
      };
      return icons[type] || icons.default;
    },
    
    getSuggestionTypeText(type) {
      const texts = {
        performance: '性能优化',
        ux: '用户体验',
        business: '业务优化'
      };
      return texts[type] || '建议';
    }
  }
};

// 全局注册组件
if (window.Vue && window.Vue.createApp) {
  window.RuleValidationPanel = RuleValidationPanel;
}
