using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Core;
using PE2.PromotionEngine.Rules.ExchangeRules;
using PE2.PromotionEngine.Observability;
using PE2.PromotionEngine.Conditions;
using PE2.PromotionEngine.Inventory;
using PE2.PromotionEngine.Performance;
using PE2.PromotionEngine.Allocation;
using Microsoft.Extensions.Logging;
using Moq;

namespace PE2.PromotionEngine.Tests;

/// <summary>
/// 新规则迁移测试
/// 验证迁移后的规则与原有规则的业务逻辑一致性
/// </summary>
public class NewRuleMigrationTests
{
    private readonly Mock<ILogger<NewUnifiedSpecialPriceExchangeRule>> _mockLogger;
    private readonly Mock<IObservabilityEngine> _mockObservability;
    private readonly Mock<IConditionEngine> _mockConditionEngine;
    private readonly Mock<IInventoryManager> _mockInventoryManager;
    private readonly Mock<IPerformanceOptimizer> _mockPerformanceOptimizer;
    private readonly Mock<IAllocationEngine> _mockAllocationEngine;

    public NewRuleMigrationTests()
    {
        _mockLogger = new Mock<ILogger<NewUnifiedSpecialPriceExchangeRule>>();
        _mockObservability = new Mock<IObservabilityEngine>();
        _mockConditionEngine = new Mock<IConditionEngine>();
        _mockInventoryManager = new Mock<IInventoryManager>();
        _mockPerformanceOptimizer = new Mock<IPerformanceOptimizer>();
        _mockAllocationEngine = new Mock<IAllocationEngine>();
    }

    /// <summary>
    /// 测试新统一特价换购规则的基本功能
    /// </summary>
    [Fact]
    public async Task NewUnifiedSpecialPriceExchangeRule_BasicFunctionality_ShouldWork()
    {
        // Arrange
        var configuration = CreateTestConfiguration();
        var rule = CreateTestRule(configuration);
        var cart = CreateTestShoppingCart();
        var processedCart = RuleMigrationHelper.ConvertToProcessedCart(cart);

        // Setup mocks
        SetupMocks(cart);

        // Act
        var isApplicable = await rule.IsApplicableAsync(cart, DateTime.UtcNow);
        var maxApplications = await rule.CalculateMaxApplicationsAsync(cart);
        var appliedPromotion = await rule.ApplyPromotionAsync(processedCart, 1);

        // Assert
        Assert.True(isApplicable);
        Assert.True(maxApplications > 0);
        Assert.NotNull(appliedPromotion);
        Assert.Equal(rule.Id, appliedPromotion.RuleId);
        Assert.Equal(rule.Name, appliedPromotion.RuleName);
    }

    /// <summary>
    /// 测试规则迁移一致性
    /// </summary>
    [Fact]
    public void RuleMigrationConsistency_ShouldMaintainBusinessLogic()
    {
        // Arrange
        var oldRule = CreateOldRule();
        var newRule = CreateTestRule(CreateTestConfiguration());

        // Act
        var validationResult = RuleMigrationHelper.ValidateMigrationConsistency(oldRule, newRule);

        // Assert
        Assert.True(validationResult.IsValid);
    }

    /// <summary>
    /// 测试ProcessedCart转换
    /// </summary>
    [Fact]
    public void ConvertToProcessedCart_ShouldPreserveCartData()
    {
        // Arrange
        var originalCart = CreateTestShoppingCart();

        // Act
        var processedCart = RuleMigrationHelper.ConvertToProcessedCart(originalCart);

        // Assert
        Assert.Equal(originalCart.Id, processedCart.Id);
        Assert.Equal(originalCart.CustomerId, processedCart.CustomerId);
        Assert.Equal(originalCart.Items.Count, processedCart.Items.Count);
        
        for (int i = 0; i < originalCart.Items.Count; i++)
        {
            var originalItem = originalCart.Items[i];
            var processedItem = processedCart.Items[i];
            
            Assert.Equal(originalItem.ProductId, processedItem.ProductId);
            Assert.Equal(originalItem.ProductName, processedItem.ProductName);
            Assert.Equal(originalItem.UnitPrice, processedItem.OriginalUnitPrice);
            Assert.Equal(originalItem.Quantity, processedItem.Quantity);
        }
    }

    /// <summary>
    /// 创建测试配置
    /// </summary>
    private static UnifiedSpecialPriceExchangeConfiguration CreateTestConfiguration()
    {
        return new UnifiedSpecialPriceExchangeConfiguration
        {
            Id = "TEST_RULE_001",
            Name = "测试统一特价换购规则",
            Description = "用于测试的统一特价换购规则",
            Priority = 100,
            IsEnabled = true,
            IsRepeatable = true,
            MaxApplications = 5,
            BuyCondition = new ExchangeBuyCondition
            {
                ProductIds = ["PRODUCT_A"],
                RequiredQuantity = 1,
                RequiredAmount = 0
            },
            ExchangeCondition = new ExchangeCondition
            {
                ExchangeProductIds = ["PRODUCT_B"],
                ExchangeQuantity = 1,
                ExchangeAmount = 10m
            },
            ExchangeConditions = 
            [
                new SpecialPriceExchangeCondition
                {
                    ExchangeProductIds = ["PRODUCT_B"],
                    ExchangeQuantity = 1,
                    SpecialPrice = 50m,
                    IsEnabled = true
                }
            ]
        };
    }

    /// <summary>
    /// 创建测试规则实例
    /// </summary>
    private NewUnifiedSpecialPriceExchangeRule CreateTestRule(UnifiedSpecialPriceExchangeConfiguration configuration)
    {
        return new NewUnifiedSpecialPriceExchangeRule(
            _mockLogger.Object,
            _mockObservability.Object,
            _mockConditionEngine.Object,
            _mockInventoryManager.Object,
            _mockPerformanceOptimizer.Object,
            _mockAllocationEngine.Object,
            configuration);
    }

    /// <summary>
    /// 创建测试购物车
    /// </summary>
    private static ShoppingCart CreateTestShoppingCart()
    {
        return new ShoppingCart
        {
            Id = "CART_001",
            CustomerId = "CUSTOMER_001",
            Items = 
            [
                new CartItem
                {
                    ProductId = "PRODUCT_A",
                    ProductName = "商品A",
                    UnitPrice = 100m,
                    ActualUnitPrice = 100m,
                    Quantity = 2
                },
                new CartItem
                {
                    ProductId = "PRODUCT_B",
                    ProductName = "商品B",
                    UnitPrice = 80m,
                    ActualUnitPrice = 80m,
                    Quantity = 1
                }
            ]
        };
    }

    /// <summary>
    /// 创建旧规则实例（用于一致性测试）
    /// </summary>
    private static UnifiedSpecialPriceExchangeRule CreateOldRule()
    {
        return new UnifiedSpecialPriceExchangeRule
        {
            Id = "TEST_RULE_001",
            Name = "测试统一特价换购规则",
            Description = "用于测试的统一特价换购规则",
            Priority = 100,
            IsEnabled = true,
            IsRepeatable = true,
            MaxApplications = 5
        };
    }

    /// <summary>
    /// 设置Mock对象
    /// </summary>
    private void SetupMocks(ShoppingCart cart)
    {
        // Setup ConditionEngine
        _mockConditionEngine
            .Setup(x => x.ValidateConditionAsync(It.IsAny<object>(), It.IsAny<ShoppingCart>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ConditionValidationResult { IsValid = true });

        // Setup InventoryManager
        _mockInventoryManager
            .Setup(x => x.GetAvailableQuantityAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(100);

        _mockInventoryManager
            .Setup(x => x.ReserveProductsAsync(It.IsAny<ReservationRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ReservationResult { IsSuccessful = true });

        _mockInventoryManager
            .Setup(x => x.ReleaseReservationAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Setup PerformanceOptimizer
        _mockPerformanceOptimizer
            .Setup(x => x.OptimizeRuleCombinationAsync(It.IsAny<OptimizationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new OptimizationResult { IsOptimal = true });

        // Setup AllocationEngine
        _mockAllocationEngine
            .Setup(x => x.AllocateDiscountAsync(It.IsAny<AllocationRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new AllocationResult 
            { 
                IsSuccessful = true,
                Allocations = []
            });
    }
}
