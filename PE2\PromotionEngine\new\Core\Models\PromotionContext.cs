using PE2.Models;
using PE2.PromotionEngine.Core;
using PE2.PromotionEngine.new.Core.Enums;

namespace PE2.PromotionEngine.new.Core.Models;

/// <summary>
/// 促销上下文
/// 包含促销执行过程中的所有上下文信息
/// </summary>
public sealed class PromotionContext
{
    /// <summary>
    /// 上下文ID
    /// </summary>
    public string Id { get; init; } = Guid.NewGuid().ToString("N");
    
    /// <summary>
    /// 促销规则ID
    /// </summary>
    public required string RuleId { get; init; }
    
    /// <summary>
    /// 促销规则名称
    /// </summary>
    public required string RuleName { get; init; }
    
    /// <summary>
    /// 促销类型
    /// </summary>
    public required PromotionType PromotionType { get; init; }
    
    /// <summary>
    /// 应用次数
    /// </summary>
    public int ApplicationCount { get; init; } = 1;
    
    /// <summary>
    /// 执行策略
    /// </summary>
    public ExecutionStrategy ExecutionStrategy { get; init; } = ExecutionStrategy.Standard;
    
    /// <summary>
    /// 占用会话
    /// </summary>
    public IOccupationSession? OccupationSession { get; init; }
    
    /// <summary>
    /// 购物车快照
    /// </summary>
    public ShoppingCart? CartSnapshot { get; init; }
    
    /// <summary>
    /// 执行开始时间
    /// </summary>
    public DateTime StartTime { get; init; } = DateTime.UtcNow;
    
    /// <summary>
    /// 客户ID
    /// </summary>
    public string? CustomerId { get; init; }
    
    /// <summary>
    /// 会员ID
    /// </summary>
    public string? MemberId { get; init; }
    
    /// <summary>
    /// 扩展属性
    /// </summary>
    public Dictionary<string, object> Properties { get; init; } = new();
    
    /// <summary>
    /// 追踪信息
    /// </summary>
    public List<string> TraceMessages { get; init; } = new();
    
    /// <summary>
    /// 添加追踪消息
    /// </summary>
    /// <param name="message">消息</param>
    public void AddTrace(string message)
    {
        TraceMessages.Add($"[{DateTime.UtcNow:HH:mm:ss.fff}] {message}");
    }
    
    /// <summary>
    /// 获取属性值
    /// </summary>
    /// <typeparam name="T">属性类型</typeparam>
    /// <param name="key">属性键</param>
    /// <returns>属性值</returns>
    public T? GetProperty<T>(string key)
    {
        if (Properties.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return default;
    }
    
    /// <summary>
    /// 设置属性值
    /// </summary>
    /// <param name="key">属性键</param>
    /// <param name="value">属性值</param>
    public void SetProperty(string key, object value)
    {
        Properties[key] = value;
    }
}

/// <summary>
/// 占用请求
/// </summary>
public sealed class OccupationRequest
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public required string ProductId { get; init; }
    
    /// <summary>
    /// 请求数量
    /// </summary>
    public int Quantity { get; init; }
    
    /// <summary>
    /// 占用级别
    /// </summary>
    public OccupationLevel Level { get; init; } = OccupationLevel.Standard;
    
    /// <summary>
    /// 占用类型（条件验证 vs 执行）
    /// </summary>
    public ReservationType ReservationType { get; init; } = ReservationType.Condition;
    
    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; init; } = 0;
    
    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; init; } = string.Empty;
    
    /// <summary>
    /// 扩展属性
    /// </summary>
    public Dictionary<string, object> Properties { get; init; } = new();
}

/// <summary>
/// 验证结果
/// </summary>
public sealed class ValidationResult
{
    /// <summary>
    /// 是否验证成功
    /// </summary>
    public bool IsValid { get; init; }
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; init; }
    
    /// <summary>
    /// 验证详情
    /// </summary>
    public List<ValidationDetail> Details { get; init; } = new();
    
    /// <summary>
    /// 占用的商品信息
    /// </summary>
    public List<OccupiedProduct> OccupiedProducts { get; init; } = new();
    
    /// <summary>
    /// 验证耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; init; }
    
    /// <summary>
    /// 创建成功结果
    /// </summary>
    /// <param name="occupiedProducts">占用的商品</param>
    /// <returns>验证结果</returns>
    public static ValidationResult Success(List<OccupiedProduct>? occupiedProducts = null)
    {
        return new ValidationResult
        {
            IsValid = true,
            OccupiedProducts = occupiedProducts ?? new()
        };
    }
    
    /// <summary>
    /// 创建失败结果
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>验证结果</returns>
    public static ValidationResult Failure(string errorMessage)
    {
        return new ValidationResult
        {
            IsValid = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// 验证详情
/// </summary>
public sealed class ValidationDetail
{
    /// <summary>
    /// 验证项名称
    /// </summary>
    public required string Name { get; init; }
    
    /// <summary>
    /// 是否通过
    /// </summary>
    public bool Passed { get; init; }
    
    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; init; } = string.Empty;
    
    /// <summary>
    /// 相关商品ID
    /// </summary>
    public List<string> RelatedProductIds { get; init; } = new();
}

/// <summary>
/// 占用的商品信息
/// </summary>
public sealed class OccupiedProduct
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public required string ProductId { get; init; }
    
    /// <summary>
    /// 占用数量
    /// </summary>
    public int OccupiedQuantity { get; init; }
    
    /// <summary>
    /// 占用级别
    /// </summary>
    public OccupationLevel Level { get; init; }
    
    /// <summary>
    /// 占用类型
    /// </summary>
    public ReservationType ReservationType { get; init; }
    
    /// <summary>
    /// 占用时间
    /// </summary>
    public DateTime OccupiedAt { get; init; } = DateTime.UtcNow;
}
