using PE2.Models;
using PE2.PromotionEngine.Models;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Rules.DiscountRules;

/// <summary>
/// 自由组合折扣规则
/// 针对多类商品，满X件或X元，享受对应的各自的折扣
/// 场景：A、B、C商品自由组合，只要数量满足2件，就能各自享受满2件时对应的折扣
/// </summary>
public class FreeFormDiscountRule : BaseDiscountRule
{
    public override string RuleType => "FreeFormDiscount";

    /// <summary>
    /// 商品折扣配置列表
    /// </summary>
    public List<ProductDiscountConfig> ProductConfigs { get; set; } = new();

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!ProductConfigs.Any())
            return false;

        // 验证商品是否在购物车中
        var allProductIds = ProductConfigs.Select(c => c.ProductId).ToList();
        if (!ValidateDiscountProductsInCart(cart, allProductIds))
            return false;

        // 检查是否满足最低组合条件
        var totalQuantity = CalculateTotalQuantity(cart, allProductIds);
        var minRequiredQuantity = ProductConfigs.SelectMany(c => c.DiscountTiers).Min(t => t.MinQuantity);
        
        return totalQuantity >= minRequiredQuantity;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        // 自由组合折扣通常只应用一次，因为会根据总数量给每个商品应用对应折扣
        return 1;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyFreeFormDiscount(cart);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用自由组合折扣促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用自由组合折扣促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyFreeFormDiscount(ShoppingCart cart)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var discountRecords = new List<GiftItem>(); // 折扣记录

        // 计算所有参与商品的总数量
        var allProductIds = ProductConfigs.Select(c => c.ProductId).ToList();
        var totalQuantity = CalculateTotalQuantity(cart, allProductIds);

        var promotion = new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType
        };

        // 为每个商品配置应用对应的折扣
        foreach (var config in ProductConfigs)
        {
            // 获取该商品的购物车项
            var productItems = cart.Items
                .Where(x => x.Product.Id == config.ProductId && x.Quantity > 0)
                .ToList();

            if (!productItems.Any()) continue;

            // 根据总数量确定该商品应享受的折扣梯度
            var applicableTier = config.DiscountTiers
                .Where(t => totalQuantity >= t.MinQuantity)
                .OrderByDescending(t => t.MinQuantity)
                .FirstOrDefault();

            if (applicableTier == null) continue;

            // 根据策略排序商品
            var sortedItems = SortItemsByStrategy(productItems);

            // 对该商品的所有项应用折扣
            foreach (var item in sortedItems)
            {
                var originalPrice = item.UnitPrice;
                var discountedPrice = originalPrice * applicableTier.DiscountRate;
                var discountAmount = (originalPrice - discountedPrice) * item.Quantity;

                if (discountAmount > 0)
                {
                    totalDiscountAmount += discountAmount;

                    var strategyDescription = DiscountSelectionStrategy == DiscountSelectionStrategy.CustomerBenefit
                        ? "客户利益最大化"
                        : "商家利益最大化";

                    var description = $"自由组合折扣：总{totalQuantity}件享{applicableTier.Description}，{applicableTier.DiscountRate:P1}折，节省{discountAmount:C}（{strategyDescription}）";

                    // 应用折扣到商品项
                    ApplyDiscountToCartItem(item, applicableTier.DiscountRate, promotion, description);

                    // 记录折扣
                    discountRecords.Add(CreateDiscountRecord(
                        item.Product.Id,
                        item.Product.Name,
                        item.Quantity,
                        discountAmount,
                        description
                    ));

                    // 记录消耗的商品
                    var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == item.Product.Id);
                    if (existingConsumed != null)
                    {
                        existingConsumed.Quantity += item.Quantity;
                    }
                    else
                    {
                        consumedItems.Add(new ConsumedItem
                        {
                            ProductId = item.Product.Id,
                            ProductName = item.Product.Name,
                            Quantity = item.Quantity,
                            UnitPrice = originalPrice
                        });
                    }
                }
            }
        }

        return (totalDiscountAmount, consumedItems, discountRecords);
    }
}

/// <summary>
/// 商品折扣配置
/// </summary>
public class ProductDiscountConfig
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 该商品的折扣梯度列表
    /// </summary>
    public List<DiscountTier> DiscountTiers { get; set; } = new();
}
