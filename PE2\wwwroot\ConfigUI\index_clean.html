<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>促销规则配置管理系统 - 专业版</title>
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3.3.8/dist/vue.global.js"></script>
    
    <!-- Element Plus UI Framework -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    
    <!-- Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.iife.min.js"></script>
    
    <!-- 自定义样式 -->
    <style>
        /* Ant Design 设计变量 */
        :root {
            --ant-primary-color: #1890ff;
            --ant-primary-color-hover: #40a9ff;
            --ant-primary-color-active: #096dd9;
            --ant-success-color: #52c41a;
            --ant-warning-color: #faad14;
            --ant-error-color: #ff4d4f;
            --ant-text-color: rgba(0, 0, 0, 0.85);
            --ant-text-color-secondary: rgba(0, 0, 0, 0.65);
            --ant-text-color-disabled: rgba(0, 0, 0, 0.25);
            --ant-background-color-base: #f0f2f5;
            --ant-component-background: #ffffff;
            --ant-border-color-base: #d9d9d9;
            --ant-border-color-split: #f0f0f0;
            --ant-border-radius-base: 6px;
            --ant-box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
            --ant-padding-lg: 24px;
            --ant-padding-md: 16px;
            --ant-padding-sm: 12px;
            --ant-padding-xs: 8px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ant-background-color-base);
            color: var(--ant-text-color);
            font-size: 14px;
            line-height: 1.5715;
            margin: 0;
            padding: 0;
        }
        
        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* 头部样式 */
        .header {
            background: var(--ant-component-background);
            border-bottom: 1px solid var(--ant-border-color-split);
            padding: var(--ant-padding-md) var(--ant-padding-lg);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: var(--ant-padding-sm);
        }

        .header-title h1 {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
            color: var(--ant-text-color);
        }

        .header-subtitle {
            font-size: 14px;
            color: var(--ant-text-color-secondary);
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: var(--ant-padding-xs);
        }
        
        /* 布局样式 */
        .layout {
            display: flex;
            flex: 1;
            min-height: 0;
        }

        .sidebar {
            width: 280px;
            background: var(--ant-component-background);
            border-right: 1px solid var(--ant-border-color-split);
            overflow-y: auto;
        }

        .sidebar-section {
            padding: var(--ant-padding-md);
        }

        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--ant-text-color);
            margin-bottom: var(--ant-padding-sm);
            display: flex;
            align-items: center;
            gap: var(--ant-padding-xs);
        }
        
        /* 促销类型卡片样式 */
        .promotion-type-grid {
            display: flex;
            flex-direction: column;
            gap: var(--ant-padding-xs);
        }

        .promotion-type-card {
            padding: var(--ant-padding-sm);
            border: 1px solid var(--ant-border-color-base);
            border-radius: var(--ant-border-radius-base);
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            background: var(--ant-component-background);
        }

        .promotion-type-card:hover {
            border-color: var(--ant-primary-color-hover);
        }

        .promotion-type-card.active {
            background: #e6f7ff;
            border-color: var(--ant-primary-color);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: var(--ant-padding-xs);
            margin-bottom: 4px;
        }
        
        .card-icon {
            font-size: 16px;
            color: var(--ant-primary-color);
        }

        .card-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--ant-text-color);
        }

        .card-description {
            font-size: 12px;
            color: var(--ant-text-color-secondary);
            line-height: 1.4;
        }

        /* 主面板样式 */
        .main-panel {
            flex: 1;
            background: var(--ant-component-background);
            padding: var(--ant-padding-lg);
            overflow-y: auto;
        }
        
        .empty-state {
            text-align: center;
            padding: 80px 20px;
            color: var(--ant-text-color-secondary);
        }
        
        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 20px;
            color: var(--ant-text-color-disabled);
        }

        /* Element Plus 组件样式覆写 */
        .el-button--primary {
            background-color: var(--ant-primary-color) !important;
            border-color: var(--ant-primary-color) !important;
        }

        .el-button--primary:hover {
            background-color: var(--ant-primary-color-hover) !important;
            border-color: var(--ant-primary-color-hover) !important;
        }

        .el-button--primary:active {
            background-color: var(--ant-primary-color-active) !important;
            border-color: var(--ant-primary-color-active) !important;
        }

        .el-input__wrapper {
            border-radius: var(--ant-border-radius-base) !important;
        }

        .el-select .el-input__wrapper {
            border-radius: var(--ant-border-radius-base) !important;
        }

        /* 简洁的过渡动画 */
        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.2s ease;
        }

        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- PromotionConfigMainComponent组件将在这里渲染 -->
    </div>

    <!-- 加载配置和组件 -->
    <script src="./js/config/promotionTypes.js"></script>
    <script src="./js/services/apiService.js"></script>
    <script src="./js/services/metadataService.js"></script>
    <script src="./js/components/DynamicFormRenderer.js"></script>
    <script src="./js/components/RuleManager.js"></script>
    <script src="./js/components/PromotionConfigMainComponent.js"></script>
    
    <script>
        console.log('✅ PROMOTION_TYPES 已加载到 window 对象', typeof window.PROMOTION_TYPES);
        console.log('✅ PromotionConfigMainComponent 组件:', typeof window.PromotionConfigMainComponent);
        console.log('✅ RuleManager 组件:', typeof window.RuleManager);
        console.log('✅ DynamicFormRenderer 组件:', typeof window.DynamicFormRenderer);
        
        // 创建Vue应用
        const { createApp } = Vue;
        
        // 确保组件已加载
        if (!window.PromotionConfigMainComponent) {
            console.error('❌ PromotionConfigMainComponent 未加载');
            document.getElementById('app').innerHTML = '<div style="padding: 20px; text-align: center; color: red;">组件加载失败，请检查控制台错误信息</div>';
        } else {
            const app = createApp(window.PromotionConfigMainComponent);
            
            // 注册Element Plus
            app.use(ElementPlus);
            
            // 注册Element Plus图标
            for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
                app.component(key, component);
            }
            
            // 全局错误处理
            app.config.errorHandler = (err, vm, info) => {
                console.error('Vue 应用错误:', err, info);
            };
            
            // 挂载应用
            app.mount('#app');
            console.log('✅ Vue 应用已成功挂载');
        }
    </script>
</body>
</html>
