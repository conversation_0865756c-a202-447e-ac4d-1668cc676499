using Microsoft.AspNetCore.Mvc;
using PE2.Services;
using PE2.PromotionEngine.Rules;

namespace PE2.Controllers;

/// <summary>
/// 促销规则管理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class PromotionRuleController : ControllerBase
{
    private readonly PromotionRuleService _ruleService;
    private readonly ILogger<PromotionRuleController> _logger;

    public PromotionRuleController(PromotionRuleService ruleService, ILogger<PromotionRuleController> logger)
    {
        _ruleService = ruleService;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有促销规则
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetAllRules()
    {
        try
        {
            var rules = await _ruleService.GetAllRulesAsync();
            return Ok(rules);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有促销规则时发生错误");
            return StatusCode(500, "获取促销规则时发生内部错误");
        }
    }

    /// <summary>
    /// 根据ID获取促销规则
    /// </summary>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetRuleById(string id)
    {
        try
        {
            var rule = await _ruleService.GetRuleByIdAsync(id);
            
            if (rule == null)
            {
                return NotFound($"促销规则 {id} 不存在");
            }

            return Ok(rule);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取促销规则 {RuleId} 时发生错误", id);
            return StatusCode(500, "获取促销规则时发生内部错误");
        }
    }

    /// <summary>
    /// 获取启用的促销规则
    /// </summary>
    [HttpGet("enabled")]
    public async Task<IActionResult> GetEnabledRules()
    {
        try
        {
            var rules = await _ruleService.GetEnabledRulesAsync();
            return Ok(rules);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取启用的促销规则时发生错误");
            return StatusCode(500, "获取促销规则时发生内部错误");
        }
    }

    /// <summary>
    /// 获取有效的促销规则
    /// </summary>
    [HttpGet("valid")]
    public async Task<IActionResult> GetValidRules([FromQuery] DateTime? checkTime = null)
    {
        try
        {
            var rules = await _ruleService.GetValidRulesAsync(checkTime);
            return Ok(rules);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取有效的促销规则时发生错误");
            return StatusCode(500, "获取促销规则时发生内部错误");
        }
    }

    /// <summary>
    /// 添加促销规则
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> AddRule([FromBody] PromotionRuleBase rule)
    {
        try
        {
            if (rule == null)
            {
                return BadRequest("促销规则不能为空");
            }

            if (string.IsNullOrEmpty(rule.Id))
            {
                return BadRequest("促销规则ID不能为空");
            }

            var success = await _ruleService.AddRuleAsync(rule);
            
            if (!success)
            {
                return Conflict($"促销规则 {rule.Id} 已存在或添加失败");
            }

            return CreatedAtAction(nameof(GetRuleById), new { id = rule.Id }, rule);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加促销规则时发生错误");
            return StatusCode(500, "添加促销规则时发生内部错误");
        }
    }

    /// <summary>
    /// 更新促销规则
    /// </summary>
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateRule(string id, [FromBody] PromotionRuleBase rule)
    {
        try
        {
            if (rule == null)
            {
                return BadRequest("促销规则不能为空");
            }

            if (id != rule.Id)
            {
                return BadRequest("路径中的ID与规则ID不匹配");
            }

            var success = await _ruleService.UpdateRuleAsync(rule);
            
            if (!success)
            {
                return NotFound($"促销规则 {id} 不存在或更新失败");
            }

            return Ok(rule);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新促销规则 {RuleId} 时发生错误", id);
            return StatusCode(500, "更新促销规则时发生内部错误");
        }
    }

    /// <summary>
    /// 删除促销规则
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteRule(string id)
    {
        try
        {
            var success = await _ruleService.DeleteRuleAsync(id);
            
            if (!success)
            {
                return NotFound($"促销规则 {id} 不存在");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除促销规则 {RuleId} 时发生错误", id);
            return StatusCode(500, "删除促销规则时发生内部错误");
        }
    }

    /// <summary>
    /// 启用/禁用促销规则
    /// </summary>
    [HttpPatch("{id}/enabled")]
    public async Task<IActionResult> SetRuleEnabled(string id, [FromBody] bool enabled)
    {
        try
        {
            var success = await _ruleService.SetRuleEnabledAsync(id, enabled);
            
            if (!success)
            {
                return NotFound($"促销规则 {id} 不存在");
            }

            return Ok(new { Id = id, Enabled = enabled });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置促销规则 {RuleId} 启用状态时发生错误", id);
            return StatusCode(500, "设置促销规则状态时发生内部错误");
        }
    }

    /// <summary>
    /// 重新加载促销规则
    /// </summary>
    [HttpPost("reload")]
    public async Task<IActionResult> ReloadRules()
    {
        try
        {
            await _ruleService.ReloadRulesAsync();
            return Ok("促销规则已重新加载");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新加载促销规则时发生错误");
            return StatusCode(500, "重新加载促销规则时发生内部错误");
        }
    }

    /// <summary>
    /// 验证规则文件
    /// </summary>
    [HttpGet("validate-file")]
    public async Task<IActionResult> ValidateRulesFile()
    {
        try
        {
            var (isValid, errorMessage) = await _ruleService.ValidateRulesFileAsync();
            return Ok(new { IsValid = isValid, ErrorMessage = errorMessage });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证规则文件时发生错误");
            return StatusCode(500, "验证规则文件时发生内部错误");
        }
    }
}
