using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.SpecialPriceRules;

/// <summary>
/// 特价促销规则基类
/// 核心原则：通过修改商品的ActualUnitPrice来实现特价效果
/// </summary>
public abstract class BaseSpecialPriceRule : PromotionRuleBase
{
    /// <summary>
    /// 特价商品选择策略：客户利益最大化 vs 商家利益最大化
    /// </summary>
    public SpecialPriceSelectionStrategy SpecialPriceSelectionStrategy { get; set; } = SpecialPriceSelectionStrategy.CustomerBenefit;

    /// <summary>
    /// 规则类型（由子类实现）
    /// </summary>
    public abstract override string RuleType { get; }

    /// <summary>
    /// 检查促销条件是否满足（由子类实现）
    /// </summary>
    protected abstract override bool CheckConditions(ShoppingCart cart);

    /// <summary>
    /// 计算可应用的最大次数（由子类实现）
    /// </summary>
    public abstract override int CalculateMaxApplications(ShoppingCart cart);

    /// <summary>
    /// 应用促销规则（由子类实现）
    /// </summary>
    public abstract override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1);

    /// <summary>
    /// 验证特价商品是否在购物车中
    /// </summary>
    protected bool ValidateSpecialPriceProductsInCart(ShoppingCart cart, List<string> productIds)
    {
        foreach (var productId in productIds)
        {
            var hasProduct = cart.Items.Any(x => x.Product.Id == productId && x.Quantity > 0);
            if (!hasProduct)
            {
                return false; // 特价商品不在购物车中，条件不成立
            }
        }
        return true;
    }

    /// <summary>
    /// 计算商品的总数量
    /// </summary>
    protected int CalculateTotalQuantity(ShoppingCart cart, List<string> productIds)
    {
        return productIds.Sum(id => cart.GetAvailableProductQuantity(id));
    }

    /// <summary>
    /// 计算商品的总金额
    /// </summary>
    protected decimal CalculateTotalAmount(ShoppingCart cart, List<string> productIds)
    {
        return productIds.Sum(id =>
            cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));
    }

    /// <summary>
    /// 应用特价到指定商品项
    /// </summary>
    protected void ApplySpecialPriceToCartItem(CartItem cartItem, decimal specialPrice, AppliedPromotion promotion, string description)
    {
        var originalPrice = cartItem.UnitPrice;
        var discountAmount = originalPrice - specialPrice;

        // 应用特价
        cartItem.ActualUnitPrice = specialPrice;

        // 记录促销详情
        var promotionDetail = new ItemPromotionDetail
        {
            RuleId = promotion.RuleId,
            RuleName = promotion.RuleName,
            PromotionType = promotion.PromotionType,
            DiscountAmount = discountAmount * cartItem.Quantity,
            Description = description,
            IsGiftRelated = false
        };

        cartItem.AddPromotionDetail(promotionDetail);
    }

    /// <summary>
    /// 根据策略排序商品（用于优化特价分配）
    /// </summary>
    protected List<CartItem> SortItemsByStrategy(List<CartItem> items)
    {
        return SpecialPriceSelectionStrategy switch
        {
            SpecialPriceSelectionStrategy.CustomerBenefit => items.OrderByDescending(x => x.UnitPrice).ToList(), // 客户利益：优先给高价商品特价
            SpecialPriceSelectionStrategy.MerchantBenefit => items.OrderBy(x => x.UnitPrice).ToList(), // 商家利益：优先给低价商品特价
            _ => items.OrderByDescending(x => x.UnitPrice).ToList()
        };
    }

    /// <summary>
    /// 创建特价记录（作为GiftItem记录，但实际是特价）
    /// </summary>
    protected GiftItem CreateSpecialPriceRecord(string productId, string productName, int quantity, decimal discountAmount, string description)
    {
        return new GiftItem
        {
            ProductId = productId,
            ProductName = productName,
            Quantity = quantity,
            Value = discountAmount,
            Description = description
        };
    }

    /// <summary>
    /// 消耗购买条件商品（记录消耗但不修改购物车）
    /// </summary>
    protected List<ConsumedItem> ConsumeConditionProducts(ShoppingCart cart, List<string> productIds, int requiredQuantity)
    {
        var consumedItems = new List<ConsumedItem>();
        var remainingQuantity = requiredQuantity;

        foreach (var productId in productIds)
        {
            if (remainingQuantity <= 0) break;

            var cartItems = cart.Items
                .Where(x => x.Product.Id == productId && x.Quantity > 0)
                .ToList();

            foreach (var cartItem in cartItems)
            {
                if (remainingQuantity <= 0) break;

                var consumeQuantity = Math.Min(cartItem.Quantity, remainingQuantity);

                var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == productId);
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += consumeQuantity;
                }
                else
                {
                    consumedItems.Add(new ConsumedItem
                    {
                        ProductId = productId,
                        ProductName = cartItem.Product.Name,
                        Quantity = consumeQuantity,
                        UnitPrice = cartItem.UnitPrice
                    });
                }

                remainingQuantity -= consumeQuantity;
            }
        }

        return consumedItems;
    }

    /// <summary>
    /// 按比例分摊特价到多个商品
    /// </summary>
    protected void ApplyProportionalSpecialPrice(List<CartItem> items, decimal totalSpecialPrice, AppliedPromotion promotion, string description)
    {
        var totalOriginalAmount = items.Sum(x => x.SubTotal);
        if (totalOriginalAmount <= 0) return;

        var remainingSpecialPrice = totalSpecialPrice;

        for (int i = 0; i < items.Count; i++)
        {
            var item = items[i];
            decimal itemSpecialPrice;

            if (i == items.Count - 1)
            {
                // 最后一个商品承担剩余的特价金额（避免精度问题）
                itemSpecialPrice = remainingSpecialPrice;
            }
            else
            {
                // 按比例计算该商品的特价金额
                itemSpecialPrice = Math.Round(totalSpecialPrice * item.SubTotal / totalOriginalAmount, 2);
            }

            // 计算单价特价
            var unitSpecialPrice = itemSpecialPrice / item.Quantity;
            
            // 应用特价
            item.ActualUnitPrice = unitSpecialPrice;

            // 记录促销详情
            var discountAmount = item.SubTotal - itemSpecialPrice;
            var promotionDetail = new ItemPromotionDetail
            {
                RuleId = promotion.RuleId,
                RuleName = promotion.RuleName,
                PromotionType = promotion.PromotionType,
                DiscountAmount = discountAmount,
                Description = $"{description}，特价{itemSpecialPrice:C}",
                IsGiftRelated = false
            };

            item.AddPromotionDetail(promotionDetail);
            remainingSpecialPrice -= itemSpecialPrice;
        }
    }
}

/// <summary>
/// 特价商品选择策略枚举
/// </summary>
public enum SpecialPriceSelectionStrategy
{
    /// <summary>
    /// 客户利益最大化：优先给高价值商品特价
    /// </summary>
    CustomerBenefit = 0,

    /// <summary>
    /// 商家利益最大化：优先给低价值商品特价
    /// </summary>
    MerchantBenefit = 1
}
