global using System.Collections.Concurrent;
global using System.Diagnostics;
global using System.Text.Json;
global using Microsoft.Extensions.Logging;
using PE2.Models;
using PE2.PromotionEngine.Models;
using System.Text;
using OfficeOpenXml;
using System.Xml.Linq;

namespace PE2.PromotionEngine.Observability;

/// <summary>
/// 可观测性引擎实现 - 促销计算过程追踪和分析
/// </summary>
public sealed class ObservabilityEngine : IObservabilityEngine, IDisposable
{
    private readonly ILogger<ObservabilityEngine> _logger;
    private readonly ConcurrentDictionary<string, CalculationTrace> _activeTraces;
    private readonly ConcurrentDictionary<string, PerformanceMetric> _performanceMetrics;
    private readonly ConcurrentQueue<CalculationTrace> _completedTraces;
    private readonly ActivitySource _activitySource;
    private readonly Timer _cleanupTimer;
    private readonly SemaphoreSlim _exportSemaphore;
    private bool _disposed;

    public ObservabilityEngine(ILogger<ObservabilityEngine> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _activeTraces = new ConcurrentDictionary<string, CalculationTrace>();
        _performanceMetrics = new ConcurrentDictionary<string, PerformanceMetric>();
        _completedTraces = new ConcurrentQueue<CalculationTrace>();
        _activitySource = new ActivitySource("PE2.PromotionEngine");
        _exportSemaphore = new SemaphoreSlim(1, 1);
        
        // 每5分钟清理一次过期的追踪数据
        _cleanupTimer = new Timer(CleanupExpiredTraces, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        
        _logger.LogInformation("可观测性引擎已初始化");
    }

    public string StartCalculationTrace(ShoppingCart cart, string algorithmType)
    {
        var traceId = Guid.NewGuid().ToString("N");
        var activity = _activitySource.StartActivity("PromotionCalculation");
        
        var trace = new CalculationTrace
        {
            TraceId = traceId,
            StartTime = DateTime.UtcNow,
            AlgorithmType = algorithmType,
            CartSnapshot = CreateCartSnapshot(cart),
            Activity = activity,
            Steps = new ConcurrentQueue<CalculationStep>(),
            Metrics = new ConcurrentDictionary<string, object>()
        };

        _activeTraces.TryAdd(traceId, trace);
        
        activity?.SetTag("trace.id", traceId);
        activity?.SetTag("algorithm.type", algorithmType);
        activity?.SetTag("cart.items.count", cart.Items.Count);
        activity?.SetTag("cart.total.amount", cart.TotalAmount);

        _logger.LogInformation("开始促销计算追踪 TraceId: {TraceId}, 算法: {AlgorithmType}, 商品数量: {ItemCount}", 
            traceId, algorithmType, cart.Items.Count);

        return traceId;
    }

    public void EndCalculationTrace(string traceId, PromotionResult result)
    {
        if (!_activeTraces.TryRemove(traceId, out var trace))
        {
            _logger.LogWarning("未找到活动追踪 TraceId: {TraceId}", traceId);
            return;
        }

        trace.EndTime = DateTime.UtcNow;
        trace.TotalDurationMs = (long)(trace.EndTime - trace.StartTime).TotalMilliseconds;
        trace.Result = result;
        trace.IsCompleted = true;

        trace.Activity?.SetTag("calculation.duration.ms", trace.TotalDurationMs);
        trace.Activity?.SetTag("calculation.success", result.IsSuccessful);
        trace.Activity?.SetTag("applied.promotions.count", result.AppliedPromotions.Count);
        trace.Activity?.SetTag("total.discount.amount", result.TotalDiscountAmount);
        trace.Activity?.Stop();

        _completedTraces.Enqueue(trace);

        _logger.LogInformation("结束促销计算追踪 TraceId: {TraceId}, 耗时: {Duration}ms, 成功: {Success}", 
            traceId, trace.TotalDurationMs, result.IsSuccessful);
    }

    public void TrackRuleFiltering(string traceId, List<PromotionRuleBase> allRules, List<PromotionRuleBase> filteredRules, string filterCriteria)
    {
        if (!_activeTraces.TryGetValue(traceId, out var trace))
            return;

        var step = new CalculationStep
        {
            Id = Guid.NewGuid().ToString("N"),
            TraceId = traceId,
            StepType = StepType.RuleFiltering,
            Timestamp = DateTime.UtcNow,
            Description = $"规则筛选: {filterCriteria}",
            Data = new Dictionary<string, object>
            {
                ["allRulesCount"] = allRules.Count,
                ["filteredRulesCount"] = filteredRules.Count,
                ["filterCriteria"] = filterCriteria,
                ["filteredRuleIds"] = filteredRules.Select(r => r.Id).ToList(),
                ["excludedRuleIds"] = allRules.Except(filteredRules).Select(r => r.Id).ToList()
            }
        };

        trace.Steps.Enqueue(step);
        trace.Activity?.AddEvent(new ActivityEvent("rule.filtering", DateTimeOffset.UtcNow, new ActivityTagsCollection
        {
            ["all.rules.count"] = allRules.Count,
            ["filtered.rules.count"] = filteredRules.Count,
            ["filter.criteria"] = filterCriteria
        }));

        _logger.LogDebug("规则筛选 TraceId: {TraceId}, 原始: {AllCount}, 筛选后: {FilteredCount}, 条件: {Criteria}", 
            traceId, allRules.Count, filteredRules.Count, filterCriteria);
    }

    public void TrackConditionValidation(string traceId, string ruleId, string description, object? data = null)
    {
        if (!_activeTraces.TryGetValue(traceId, out var trace))
            return;

        var step = new CalculationStep
        {
            Id = Guid.NewGuid().ToString("N"),
            TraceId = traceId,
            StepType = StepType.ConditionValidation,
            Timestamp = DateTime.UtcNow,
            Description = description,
            RuleId = ruleId,
            Data = data != null ? new Dictionary<string, object> { ["validationData"] = data } : new Dictionary<string, object>()
        };

        trace.Steps.Enqueue(step);
        trace.Activity?.AddEvent(new ActivityEvent("condition.validation", DateTimeOffset.UtcNow, new ActivityTagsCollection
        {
            ["rule.id"] = ruleId,
            ["description"] = description
        }));

        _logger.LogDebug("条件验证 TraceId: {TraceId}, 规则: {RuleId}, 描述: {Description}", traceId, ruleId, description);
    }

    public void TrackPromotionApplication(string traceId, string ruleId, PromotionApplication application)
    {
        if (!_activeTraces.TryGetValue(traceId, out var trace))
            return;

        var step = new CalculationStep
        {
            Id = Guid.NewGuid().ToString("N"),
            TraceId = traceId,
            StepType = StepType.PromotionApplication,
            Timestamp = DateTime.UtcNow,
            Description = $"应用促销: {application.RuleName}",
            RuleId = ruleId,
            Data = new Dictionary<string, object>
            {
                ["applicationCount"] = application.ApplicationCount,
                ["discountAmount"] = application.DiscountAmount,
                ["consumedItems"] = application.ConsumedItems.Select(ci => new { ci.ProductId, ci.Quantity, ci.UnitPrice }).ToList(),
                ["giftItems"] = application.GiftItems.Select(gi => new { gi.ProductId, gi.Quantity, gi.UnitPrice }).ToList()
            }
        };

        trace.Steps.Enqueue(step);
        trace.Activity?.AddEvent(new ActivityEvent("promotion.application", DateTimeOffset.UtcNow, new ActivityTagsCollection
        {
            ["rule.id"] = ruleId,
            ["application.count"] = application.ApplicationCount,
            ["discount.amount"] = application.DiscountAmount
        }));

        _logger.LogDebug("促销应用 TraceId: {TraceId}, 规则: {RuleId}, 折扣: {DiscountAmount}", 
            traceId, ruleId, application.DiscountAmount);
    }

    public void TrackOptimizationSearch(string traceId, string searchStep, object? searchData = null)
    {
        if (!_activeTraces.TryGetValue(traceId, out var trace))
            return;

        var step = new CalculationStep
        {
            Id = Guid.NewGuid().ToString("N"),
            TraceId = traceId,
            StepType = StepType.OptimizationSearch,
            Timestamp = DateTime.UtcNow,
            Description = searchStep,
            Data = searchData != null ? new Dictionary<string, object> { ["searchData"] = searchData } : new Dictionary<string, object>()
        };

        trace.Steps.Enqueue(step);
        trace.Activity?.AddEvent(new ActivityEvent("optimization.search", DateTimeOffset.UtcNow, new ActivityTagsCollection
        {
            ["search.step"] = searchStep
        }));

        _logger.LogDebug("优化搜索 TraceId: {TraceId}, 步骤: {SearchStep}", traceId, searchStep);
    }

    public void TrackAllocationCalculation(string traceId, string allocationStep, object? allocationData = null)
    {
        if (!_activeTraces.TryGetValue(traceId, out var trace))
            return;

        var step = new CalculationStep
        {
            Id = Guid.NewGuid().ToString("N"),
            TraceId = traceId,
            StepType = StepType.AllocationCalculation,
            Timestamp = DateTime.UtcNow,
            Description = allocationStep,
            Data = allocationData != null ? new Dictionary<string, object> { ["allocationData"] = allocationData } : new Dictionary<string, object>()
        };

        trace.Steps.Enqueue(step);
        trace.Activity?.AddEvent(new ActivityEvent("allocation.calculation", DateTimeOffset.UtcNow, new ActivityTagsCollection
        {
            ["allocation.step"] = allocationStep
        }));

        _logger.LogDebug("分摊计算 TraceId: {TraceId}, 步骤: {AllocationStep}", traceId, allocationStep);
    }

    public void RecordPerformanceMetric(string metricName, double value, Dictionary<string, string>? tags = null)
    {
        var metric = new PerformanceMetric
        {
            Name = metricName,
            Value = value,
            Timestamp = DateTime.UtcNow,
            Tags = tags ?? new Dictionary<string, string>()
        };

        _performanceMetrics.AddOrUpdate(metricName, metric, (key, existing) => metric);

        _logger.LogDebug("性能指标 名称: {MetricName}, 值: {Value}", metricName, value);
    }

    private CartSnapshot CreateCartSnapshot(ShoppingCart cart)
    {
        return new CartSnapshot
        {
            TotalAmount = cart.TotalAmount,
            TotalQuantity = cart.TotalQuantity,
            ItemCount = cart.Items.Count,
            Items = cart.Items.Select(item => new CartItemSnapshot
            {
                ProductId = item.Product.Id,
                ProductName = item.Product.Name,
                Quantity = item.Quantity,
                UnitPrice = item.UnitPrice,
                TotalPrice = item.TotalPrice
            }).ToList()
        };
    }

    private void CleanupExpiredTraces(object? state)
    {
        try
        {
            var cutoffTime = DateTime.UtcNow.AddHours(-24); // 保留24小时的追踪数据
            var expiredTraces = new List<string>();

            foreach (var kvp in _activeTraces)
            {
                if (kvp.Value.StartTime < cutoffTime)
                {
                    expiredTraces.Add(kvp.Key);
                }
            }

            foreach (var traceId in expiredTraces)
            {
                if (_activeTraces.TryRemove(traceId, out var trace))
                {
                    trace.Activity?.Stop();
                    _logger.LogInformation("清理过期追踪 TraceId: {TraceId}", traceId);
                }
            }

            // 清理完成的追踪数据，只保留最近1000条
            while (_completedTraces.Count > 1000)
            {
                _completedTraces.TryDequeue(out _);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期追踪数据时发生错误");
        }
    }

    public DecisionTree BuildDecisionTree(string traceId)
    {
        if (!_activeTraces.TryGetValue(traceId, out var trace) &&
            !_completedTraces.Any(t => t.TraceId == traceId))
        {
            throw new ArgumentException($"未找到追踪数据: {traceId}", nameof(traceId));
        }

        var targetTrace = trace ?? _completedTraces.First(t => t.TraceId == traceId);
        var steps = targetTrace.Steps.ToList().OrderBy(s => s.Timestamp).ToList();

        var decisionTree = new DecisionTree
        {
            TraceId = traceId,
            TotalDurationMs = targetTrace.TotalDurationMs
        };

        var rootNode = new DecisionTreeNode
        {
            Id = "root",
            Type = DecisionNodeType.FinalDecision,
            Description = "促销计算根节点",
            DurationMs = targetTrace.TotalDurationMs,
            IsSuccessful = targetTrace.Result?.IsSuccessful ?? false
        };

        // 按步骤类型分组构建决策树
        var groupedSteps = steps.GroupBy(s => s.StepType).ToList();

        foreach (var group in groupedSteps)
        {
            var groupNode = new DecisionTreeNode
            {
                Id = $"{group.Key}_{Guid.NewGuid():N}",
                Type = MapStepTypeToNodeType(group.Key),
                Description = GetStepTypeDescription(group.Key),
                DurationMs = CalculateGroupDuration(group.ToList()),
                IsSuccessful = true
            };

            foreach (var step in group)
            {
                var stepNode = new DecisionTreeNode
                {
                    Id = step.Id,
                    Type = MapStepTypeToNodeType(step.StepType),
                    Description = step.Description,
                    Result = step.RuleId ?? "N/A",
                    DurationMs = 0, // 单个步骤时间很短，主要看整体
                    Data = step.Data,
                    IsSuccessful = true
                };
                groupNode.Children.Add(stepNode);
            }

            rootNode.Children.Add(groupNode);
        }

        decisionTree.Root = rootNode;
        return decisionTree;
    }

    public CalculationReport GenerateCalculationReport(string traceId)
    {
        if (!_activeTraces.TryGetValue(traceId, out var trace) &&
            !_completedTraces.Any(t => t.TraceId == traceId))
        {
            throw new ArgumentException($"未找到追踪数据: {traceId}", nameof(traceId));
        }

        var targetTrace = trace ?? _completedTraces.First(t => t.TraceId == traceId);
        var steps = targetTrace.Steps.ToList();

        var report = new CalculationReport
        {
            TraceId = traceId,
            StartTime = targetTrace.StartTime,
            EndTime = targetTrace.EndTime,
            TotalDurationMs = targetTrace.TotalDurationMs
        };

        // 统计各阶段耗时
        var phaseTimings = new Dictionary<string, long>();
        var stepGroups = steps.GroupBy(s => s.StepType);

        foreach (var group in stepGroups)
        {
            phaseTimings[group.Key.ToString()] = CalculateGroupDuration(group.ToList());
        }
        report.PhaseTimings = phaseTimings;

        // 统计规则处理情况
        var ruleSteps = steps.Where(s => !string.IsNullOrEmpty(s.RuleId)).ToList();
        report.ProcessedRulesCount = ruleSteps.Select(s => s.RuleId).Distinct().Count();

        // 统计应用的促销数量
        var applicationSteps = steps.Where(s => s.StepType == StepType.PromotionApplication).ToList();
        report.AppliedPromotionsCount = applicationSteps.Count;

        // 性能指标
        report.PerformanceMetrics = new Dictionary<string, double>
        {
            ["averageStepDuration"] = steps.Any() ? targetTrace.TotalDurationMs / (double)steps.Count : 0,
            ["stepsPerSecond"] = targetTrace.TotalDurationMs > 0 ? steps.Count * 1000.0 / targetTrace.TotalDurationMs : 0
        };

        // 生成优化建议
        report.OptimizationSuggestions = GenerateOptimizationSuggestions(targetTrace, steps);

        return report;
    }

    public PerformanceAnalysis GetPerformanceAnalysis(TimeSpan timeRange)
    {
        var cutoffTime = DateTime.UtcNow.Subtract(timeRange);
        var recentTraces = _completedTraces.Where(t => t.EndTime >= cutoffTime).ToList();

        if (!recentTraces.Any())
        {
            return new PerformanceAnalysis
            {
                TimeRange = timeRange,
                TotalCalculations = 0
            };
        }

        var durations = recentTraces.Select(t => t.TotalDurationMs).ToList();
        durations.Sort();

        var analysis = new PerformanceAnalysis
        {
            TimeRange = timeRange,
            TotalCalculations = recentTraces.Count,
            AverageCalculationTimeMs = durations.Average(),
            MaxCalculationTimeMs = durations.Max(),
            MinCalculationTimeMs = durations.Min(),
            P95CalculationTimeMs = GetPercentile(durations, 0.95),
            P99CalculationTimeMs = GetPercentile(durations, 0.99),
            ErrorRate = recentTraces.Count(t => t.Result?.IsSuccessful == false) / (double)recentTraces.Count
        };

        // 统计最常用的促销规则
        var ruleUsage = new Dictionary<string, int>();
        foreach (var trace in recentTraces)
        {
            var ruleSteps = trace.Steps.Where(s => !string.IsNullOrEmpty(s.RuleId));
            foreach (var step in ruleSteps)
            {
                ruleUsage[step.RuleId!] = ruleUsage.GetValueOrDefault(step.RuleId!, 0) + 1;
            }
        }
        analysis.MostUsedRules = ruleUsage.OrderByDescending(kvp => kvp.Value).Take(10).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

        // 识别性能瓶颈
        analysis.Bottlenecks = IdentifyPerformanceBottlenecks(recentTraces);

        return analysis;
    }

    public async Task<string> ExportTraceDataAsync(string traceId, ExportFormat format)
    {
        await _exportSemaphore.WaitAsync().ConfigureAwait(false);
        try
        {
            if (!_activeTraces.TryGetValue(traceId, out var trace) &&
                !_completedTraces.Any(t => t.TraceId == traceId))
            {
                throw new ArgumentException($"未找到追踪数据: {traceId}", nameof(traceId));
            }

            var targetTrace = trace ?? _completedTraces.First(t => t.TraceId == traceId);

            return format switch
            {
                ExportFormat.Json => await ExportToJsonAsync(targetTrace).ConfigureAwait(false),
                ExportFormat.Xml => await ExportToXmlAsync(targetTrace).ConfigureAwait(false),
                ExportFormat.Csv => await ExportToCsvAsync(targetTrace).ConfigureAwait(false),
                ExportFormat.Excel => await ExportToExcelAsync(targetTrace).ConfigureAwait(false),
                _ => throw new ArgumentException($"不支持的导出格式: {format}", nameof(format))
            };
        }
        finally
        {
            _exportSemaphore.Release();
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _cleanupTimer?.Dispose();
        _exportSemaphore?.Dispose();
        _activitySource?.Dispose();

        foreach (var trace in _activeTraces.Values)
        {
            trace.Activity?.Stop();
        }

        _disposed = true;
        _logger.LogInformation("可观测性引擎已释放");
    }
}
