<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template Fix Verification</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Element Plus JS -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .status-indicator {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #f0f9ff;
            border: 1px solid #67c23a;
            color: #67c23a;
        }
        .error {
            background-color: #fef2f2;
            border: 1px solid #f56c6c;
            color: #f56c6c;
        }
        .info {
            background-color: #f4f4f5;
            border: 1px solid #909399;
            color: #909399;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>Template Fix Verification</h1>
            
            <div class="status-indicator" :class="templateStatus.class">
                <strong>{{ templateStatus.title }}: </strong>{{ templateStatus.message }}
            </div>
            
            <div class="status-indicator" :class="componentStatus.class">
                <strong>{{ componentStatus.title }}: </strong>{{ componentStatus.message }}
            </div>
            
            <div v-if="showTestForm">
                <h2>Test Form Rendering</h2>
                <dynamic-form-renderer
                    v-model="testFormData"
                    :rule-type="'CombinationSpecialPrice'"
                />
            </div>
        </div>
    </div>

    <!-- Load Components -->
    <script src="./js/components/ProductSelector.js"></script>
    <script src="./js/components/ComplexArrayRenderer.js"></script>
    <script src="./js/components/ConditionConfigRenderer.js"></script>
    <script src="./js/components/EnhancedConditionRenderer.js"></script>
    <script src="./js/components/DynamicFormRenderer_template.js"></script>

    <script>
        const { createApp, ref, reactive, computed, onMounted } = Vue;
        
        const app = createApp({
            setup() {
                const templateStatus = ref({
                    class: 'info',
                    title: 'Template Status',
                    message: 'Checking...'
                });
                
                const componentStatus = ref({
                    class: 'info', 
                    title: 'Component Status',
                    message: 'Checking...'
                });
                
                const showTestForm = ref(false);
                const testFormData = ref({
                    ruleId: 'test_rule',
                    ruleName: 'Test Rule',
                    combinationConditions: []
                });
                
                onMounted(() => {
                    // Check if components are loaded properly
                    try {
                        if (typeof ProductSelector !== 'undefined' && 
                            typeof DynamicFormRenderer !== 'undefined' &&
                            typeof ComplexArrayRenderer !== 'undefined') {
                            componentStatus.value = {
                                class: 'success',
                                title: 'Component Loading',
                                message: 'All components loaded successfully!'
                            };
                            
                            // Test template compilation
                            try {
                                const testComponent = Vue.compile(`
                                    <div>
                                        <product-selector 
                                            v-if="true"
                                            :model-value="[]"
                                            :multiple="true"
                                        />
                                        <div v-else-if="false">Test</div>
                                    </div>
                                `);
                                
                                templateStatus.value = {
                                    class: 'success',
                                    title: 'Template Compilation',
                                    message: 'Template compiles without errors!'
                                };
                                
                                showTestForm.value = true;
                            } catch (error) {
                                templateStatus.value = {
                                    class: 'error',
                                    title: 'Template Compilation Error',
                                    message: error.message
                                };
                            }
                        } else {
                            componentStatus.value = {
                                class: 'error',
                                title: 'Component Loading Error',
                                message: 'Some components failed to load'
                            };
                        }
                    } catch (error) {
                        componentStatus.value = {
                            class: 'error',
                            title: 'Component Check Error',
                            message: error.message
                        };
                    }
                });
                
                return {
                    templateStatus,
                    componentStatus,
                    showTestForm,
                    testFormData
                };
            }
        });
        
        // Register Element Plus
        app.use(ElementPlus);
        
        // Register Element Plus Icons
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
        
        // Register custom components if available
        if (typeof ProductSelector !== 'undefined') {
            app.component('ProductSelector', ProductSelector);
            app.component('product-selector', ProductSelector);
        }
        
        if (typeof ComplexArrayRenderer !== 'undefined') {
            app.component('ComplexArrayRenderer', ComplexArrayRenderer);
            app.component('complex-array-renderer', ComplexArrayRenderer);
        }
        
        if (typeof DynamicFormRenderer !== 'undefined') {
            app.component('DynamicFormRenderer', DynamicFormRenderer);
            app.component('dynamic-form-renderer', DynamicFormRenderer);
        }
        
        if (typeof ConditionConfigRenderer !== 'undefined') {
            app.component('ConditionConfigRenderer', ConditionConfigRenderer);
            app.component('condition-config-renderer', ConditionConfigRenderer);
        }
        
        if (typeof EnhancedConditionRenderer !== 'undefined') {
            app.component('EnhancedConditionRenderer', EnhancedConditionRenderer);
            app.component('enhanced-condition-renderer', EnhancedConditionRenderer);
        }
        
        app.mount('#app');
    </script>
</body>
</html>
