using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.ExchangeRules;

/// <summary>
/// 统一特价换购规则测试
/// 测试场景：购买A商品1件时，若再增加1元可换购B商品
/// </summary>
public class UnifiedSpecialPriceExchangeRuleTests(ITestOutputHelper output) : TestBase(output)
{
    #region 基本功能测试

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "High")]
    public void Apply_BasicSpecialPriceExchange_ShouldApplyCorrectly()
    {
        // Arrange - 创建基本特价换购规则：买A加1元换B
        var rule = TestDataGenerator.CreateUnifiedSpecialPriceExchangeRule_BuyA_Add1_ExchangeB();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            (TestDataGenerator.CreateProductA(), 1), // A商品50元
            (TestDataGenerator.CreateProductB(), 1)  // B商品30元
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "应用前购物车");

        var originalBPrice = TestDataGenerator.CreateProductB().Price; // 30元
        var expectedExchangePrice = 1.00m; // 加1元换购
        var expectedDiscount = originalBPrice - expectedExchangePrice; // 29元优惠

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "基本特价换购");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证B商品实际支付价格为1元
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        AssertAmountEqual(expectedExchangePrice, productBItem.ActualUnitPrice, "B商品换购价格应为1元");

        // 验证总优惠金额
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应为换购折扣金额");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "High")]
    public void Apply_QuantityBasedCondition_ShouldApplyCorrectly()
    {
        // Arrange - 创建基于数量的特价换购规则
        var rule = TestDataGenerator.CreateUnifiedSpecialPriceExchangeRule_QuantityBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_002",
            (TestDataGenerator.CreateProductA(), 2), // 满足数量条件
            (TestDataGenerator.CreateProductB(), 1)
        );

        LogCartDetails(cart, "基于数量的特价换购购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "基于数量的特价换购");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);
        Assert.True(result.TotalDiscount > 0, "应有换购优惠");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "High")]
    public void Apply_AmountBasedCondition_ShouldApplyCorrectly()
    {
        // Arrange - 创建基于金额的特价换购规则
        var rule = TestDataGenerator.CreateUnifiedSpecialPriceExchangeRule_AmountBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_003",
            (TestDataGenerator.CreateProductA(), 3), // 150元，满足金额条件
            (TestDataGenerator.CreateProductC(), 1)
        );

        LogCartDetails(cart, "基于金额的特价换购购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "基于金额的特价换购");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);
        Assert.True(result.TotalDiscount > 0, "应有换购优惠");
    }

    #endregion

    #region 条件不满足测试

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_InsufficientBuyQuantity_ShouldNotApply()
    {
        // Arrange - 购买数量不足
        var rule = TestDataGenerator.CreateUnifiedSpecialPriceExchangeRule_BuyA_Add1_ExchangeB();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_004",
            (TestDataGenerator.CreateProductB(), 1) // 只有B商品，没有A商品
        );

        LogCartDetails(cart, "购买数量不足的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "购买数量不足场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "购买数量不足时应无优惠");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_NoExchangeProductInCart_ShouldNotApply()
    {
        // Arrange - 购物车中没有换购商品
        var rule = TestDataGenerator.CreateUnifiedSpecialPriceExchangeRule_BuyA_Add1_ExchangeB();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_005",
            (TestDataGenerator.CreateProductA(), 1) // 只有A商品，没有B商品
        );

        LogCartDetails(cart, "无换购商品的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "无换购商品场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "无换购商品时应无优惠");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_InsufficientBuyAmount_ShouldNotApply()
    {
        // Arrange - 购买金额不足
        var rule = TestDataGenerator.CreateUnifiedSpecialPriceExchangeRule_AmountBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_006",
            (TestDataGenerator.CreateProductA(), 1), // 50元，不满足100元条件
            (TestDataGenerator.CreateProductC(), 1)
        );

        LogCartDetails(cart, "购买金额不足的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "购买金额不足场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "购买金额不足时应无优惠");
    }

    #endregion

    #region 利益策略测试

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "High")]
    public void Apply_CustomerBenefitStrategy_ShouldSelectHighPriceProduct()
    {
        // Arrange - 客户利益最大化：应选择高价商品换购
        var rule = TestDataGenerator.CreateUnifiedSpecialPriceExchangeRule_CustomerBenefit();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_007",
            (TestDataGenerator.CreateProductA(), 1), // 满足购买条件
            (TestDataGenerator.CreateProductB(), 1), // 低价换购商品30元
            (TestDataGenerator.CreateProductC(), 1)  // 高价换购商品20元
        );

        LogCartDetails(cart, "客户利益最大化购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "客户利益最大化场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证选择了高价商品B进行换购（客户利益最大化）
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        var productCItem = result.ProcessedCart.Items.First(i => i.Product.Id == "C");

        Assert.True(productBItem.ActualUnitPrice < productBItem.UnitPrice, "高价商品B应享受换购优惠");
        AssertAmountEqual(productCItem.UnitPrice, productCItem.ActualUnitPrice, "低价商品C价格应保持不变");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "High")]
    public void Apply_MerchantBenefitStrategy_ShouldSelectLowPriceProduct()
    {
        // Arrange - 商户利益最大化：应选择低价商品换购
        var rule = TestDataGenerator.CreateUnifiedSpecialPriceExchangeRule_MerchantBenefit();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_008",
            (TestDataGenerator.CreateProductA(), 1), // 满足购买条件
            (TestDataGenerator.CreateProductB(), 1), // 高价换购商品30元
            (TestDataGenerator.CreateProductC(), 1)  // 低价换购商品20元
        );

        LogCartDetails(cart, "商户利益最大化购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "商户利益最大化场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证选择了低价商品C进行换购（商户利益最大化）
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        var productCItem = result.ProcessedCart.Items.First(i => i.Product.Id == "C");

        AssertAmountEqual(productBItem.UnitPrice, productBItem.ActualUnitPrice, "高价商品B价格应保持不变");
        Assert.True(productCItem.ActualUnitPrice < productCItem.UnitPrice, "低价商品C应享受换购优惠");
    }

    #endregion

    #region 可重复性测试

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public async Task Apply_RepeatableRule_ShouldApplyMultipleTimes()
    {
        // Arrange - 可重复的特价换购规则
        var rule = TestDataGenerator.CreateUnifiedSpecialPriceExchangeRule_Repeatable();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_009",
            (TestDataGenerator.CreateProductA(), 3), // 3件A商品
            (TestDataGenerator.CreateProductB(), 3)  // 3件B商品可换购
        );

        LogCartDetails(cart, "可重复规则购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "可重复规则场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);
        Assert.True(result.TotalDiscount > 0, "可重复规则应有优惠");
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Low")]
    public async Task Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange - 空购物车
        var rule = TestDataGenerator.CreateUnifiedSpecialPriceExchangeRule_BuyA_Add1_ExchangeB();
        var cart = TestDataGenerator.CreateCustomCart("EMPTY_CART", "CUSTOMER_000");

        LogCartDetails(cart, "空购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "空购物车场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "空购物车应无优惠");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Low")]
    public void Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange - 禁用的规则
        var rule = TestDataGenerator.CreateUnifiedSpecialPriceExchangeRule_Disabled();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_011",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1)
        );

        LogCartDetails(cart, "禁用规则购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "禁用规则场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "规则已禁用");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "禁用规则应无优惠");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public async Task Apply_ZeroAddAmount_ShouldApplyAsFreeExchange()
    {
        // Arrange - 加0元换购（免费换购）
        var rule = TestDataGenerator.CreateUnifiedSpecialPriceExchangeRule_FreeExchange();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_012",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1)
        );

        LogCartDetails(cart, "零加价换购购物车");

        var originalBPrice = TestDataGenerator.CreateProductB().Price; // 30元
        var expectedExchangePrice = 0.00m; // 免费换购
        var expectedDiscount = originalBPrice; // 全额优惠

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "零加价换购场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证B商品免费
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        AssertAmountEqual(expectedExchangePrice, productBItem.ActualUnitPrice, "B商品应免费");

        // 验证总优惠金额
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应为B商品原价");
    }

    #endregion
}