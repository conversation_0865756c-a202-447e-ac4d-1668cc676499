[{"$type": "UnifiedCashDiscount", "id": "UNIFIED_CASH_DISCOUNT_001", "name": "统一减现测试 - 满1件立减10元（翻倍）", "description": "A商品满1件时，立减10元，支持翻倍", "priority": 70, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 0, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "applicableProductIds": ["A"], "minQuantity": 1, "minAmount": 0, "discountAmount": 10.0, "isByAmount": false}, {"$type": "UnifiedCashDiscount", "id": "UNIFIED_CASH_DISCOUNT_002", "name": "统一减现测试 - 满1000元立减100元（不翻倍）", "description": "A商品满1000元时，立减100元，不翻倍", "priority": 65, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "applicableProductIds": ["A"], "minQuantity": 0, "minAmount": 1000.0, "discountAmount": 100.0, "isByAmount": true}, {"$type": "GradientCashDiscount", "id": "GRADIENT_CASH_DISCOUNT_001", "name": "梯度减现测试 - 买A商品梯度减现（不翻倍）", "description": "买A类商品大于等于1件时，应收金额立减10元；大于等于2件时，应收金额立减30元；大于等于3件时，应收金额立减70元", "priority": 80, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "applicableProductIds": ["A"], "isByAmount": false, "discountTiers": [{"minQuantity": 1, "minAmount": 0, "discountAmount": 10.0, "description": "满1件立减10元"}, {"minQuantity": 2, "minAmount": 0, "discountAmount": 30.0, "description": "满2件立减30元"}, {"minQuantity": 3, "minAmount": 0, "discountAmount": 70.0, "description": "满3件立减70元"}]}, {"$type": "GradientCashDiscount", "id": "GRADIENT_CASH_DISCOUNT_002", "name": "梯度减现测试 - 买A商品梯度减现（翻倍）", "description": "买A类商品大于等于3件时，应收金额立减70元，支持翻倍", "priority": 85, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 2, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "applicableProductIds": ["A"], "isByAmount": false, "discountTiers": [{"minQuantity": 3, "minAmount": 0, "discountAmount": 70.0, "description": "满3件立减70元"}]}, {"$type": "CombinationCashDiscount", "id": "COMBINATION_CASH_DISCOUNT_001", "name": "组合减现测试 - 买A+B立减10元（不翻倍）", "description": "购买A、B商品，若数量各大于1件时，则可以立减10元", "priority": 75, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "discountAmount": 10.0, "combinationConditions": [{"productId": "A", "requiredQuantity": 1, "requiredAmount": 0}, {"productId": "B", "requiredQuantity": 1, "requiredAmount": 0}]}, {"$type": "CombinationCashDiscount", "id": "COMBINATION_CASH_DISCOUNT_002", "name": "组合减现测试 - 买A+B立减10元（翻倍）", "description": "购买A、B商品，若数量各大于1件时，则可以立减10元，支持翻倍", "priority": 90, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 2, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "discountAmount": 10.0, "combinationConditions": [{"productId": "A", "requiredQuantity": 1, "requiredAmount": 0}, {"productId": "B", "requiredQuantity": 1, "requiredAmount": 0}]}, {"$type": "GradientCashDiscount", "id": "GRADIENT_CASH_DISCOUNT_AMOUNT_001", "name": "按金额梯度减现测试", "description": "满100元减10元，满200元减30元，满300元减70元", "priority": 60, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "applicableProductIds": ["A", "B", "C"], "isByAmount": true, "discountTiers": [{"minQuantity": 0, "minAmount": 100.0, "discountAmount": 10.0, "description": "满100元立减10元"}, {"minQuantity": 0, "minAmount": 200.0, "discountAmount": 30.0, "description": "满200元立减30元"}, {"minQuantity": 0, "minAmount": 300.0, "discountAmount": 70.0, "description": "满300元立减70元"}]}]