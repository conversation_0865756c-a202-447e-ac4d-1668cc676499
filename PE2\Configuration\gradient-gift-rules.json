[{"$type": "GradientGift", "id": "GRADIENT_GIFT_001", "name": "梯度送赠品 - 按梯度送", "description": "购买A商品梯度赠送，按最高梯度送", "priority": 25, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "gradientStrategy": "ByGradient", "giftSelectionStrategy": "CustomerBenefit", "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1}], "gradientGiftConditions": [{"gradientLevel": 1, "requiredQuantity": 1, "requiredAmount": 0, "giftProductIds": ["B"], "giftQuantity": 1, "description": "购买1件A送1件B"}, {"gradientLevel": 2, "requiredQuantity": 2, "requiredAmount": 0, "giftProductIds": ["C"], "giftQuantity": 1, "description": "购买2件A送1件C"}, {"gradientLevel": 3, "requiredQuantity": 3, "requiredAmount": 0, "giftProductIds": ["D"], "giftQuantity": 2, "description": "购买3件A送2件D"}]}, {"$type": "GradientGift", "id": "GRADIENT_GIFT_002", "name": "梯度送赠品 - 全部送", "description": "购买A商品梯度赠送，送所有达到的梯度", "priority": 25, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "gradientStrategy": "SendAll", "giftSelectionStrategy": "CustomerBenefit", "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1}], "gradientGiftConditions": [{"gradientLevel": 1, "requiredQuantity": 1, "requiredAmount": 0, "giftProductIds": ["B"], "giftQuantity": 1, "description": "购买1件A送1件B"}, {"gradientLevel": 2, "requiredQuantity": 2, "requiredAmount": 0, "giftProductIds": ["C"], "giftQuantity": 1, "description": "购买2件A送1件C"}]}, {"$type": "GradientGift", "id": "GRADIENT_GIFT_003", "name": "梯度送赠品 - 多选一商家利益最大化", "description": "购买A商品梯度赠送，每个梯度选择价值最低的赠品", "priority": 25, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "gradientStrategy": "ByGradient", "giftSelectionStrategy": "MerchantBenefit", "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1}], "gradientGiftConditions": [{"gradientLevel": 1, "requiredQuantity": 1, "requiredAmount": 0, "giftProductIds": ["B", "C"], "giftQuantity": 1, "description": "购买1件A从B、C中选择价值最低的送1件"}, {"gradientLevel": 2, "requiredQuantity": 2, "requiredAmount": 0, "giftProductIds": ["D", "E", "F"], "giftQuantity": 1, "description": "购买2件A从D、E、F中选择价值最低的送1件"}]}, {"$type": "GradientGift", "id": "GRADIENT_GIFT_004", "name": "梯度送赠品 - 可翻倍", "description": "购买A商品梯度赠送，可重复应用", "priority": 25, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 3, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "gradientStrategy": "ByGradient", "giftSelectionStrategy": "CustomerBenefit", "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1}], "gradientGiftConditions": [{"gradientLevel": 1, "requiredQuantity": 2, "requiredAmount": 0, "giftProductIds": ["B"], "giftQuantity": 1, "description": "每购买2件A送1件B"}]}]