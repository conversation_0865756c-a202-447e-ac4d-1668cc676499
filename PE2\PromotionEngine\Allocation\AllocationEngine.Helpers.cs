global using Microsoft.Extensions.Logging;
using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Allocation;

/// <summary>
/// 分配引擎辅助方法部分
/// </summary>
public sealed partial class AllocationEngine
{
    #region 缓存和性能相关

    /// <summary>
    /// 生成分配缓存键
    /// </summary>
    private string GenerateAllocationCacheKey(AllocationRequest request)
    {
        var sb = _stringBuilderPool.Get();
        try
        {
            sb.Clear();
            sb.Append("Allocation_");
            sb.Append(request.Id);
            sb.Append("_");
            sb.Append(request.Strategy);
            sb.Append("_");
            sb.Append(request.RoundingStrategy);
            sb.Append("_");
            sb.Append(request.Promotion.DiscountAmount);
            sb.Append("_");
            sb.Append(string.Join(",", request.Items.Select(i => $"{i.ProductId}:{i.Quantity}:{i.UnitPrice}").OrderBy(x => x)));
            
            return sb.ToString();
        }
        finally
        {
            _stringBuilderPool.Return(sb);
        }
    }

    /// <summary>
    /// 生成预览缓存键
    /// </summary>
    private string GeneratePreviewCacheKey(AllocationRequest request)
    {
        var sb = _stringBuilderPool.Get();
        try
        {
            sb.Clear();
            sb.Append("Preview_");
            sb.Append(request.Strategy);
            sb.Append("_");
            sb.Append(request.Promotion.DiscountAmount);
            sb.Append("_");
            sb.Append(string.Join(",", request.Items.Select(i => $"{i.ProductId}:{i.TotalPrice}").OrderBy(x => x)));
            
            return sb.ToString();
        }
        finally
        {
            _stringBuilderPool.Return(sb);
        }
    }

    /// <summary>
    /// 记录分配性能指标
    /// </summary>
    private void RecordAllocationMetric(string strategy, long elapsedMs, int itemCount)
    {
        var metric = _performanceMetrics.GetOrAdd(strategy, _ => new AllocationMetrics());
        
        metric.TotalAllocations++;
        metric.TotalTimeMs += elapsedMs;
        metric.AverageTimeMs = metric.TotalTimeMs / (double)metric.TotalAllocations;
        metric.MaxTimeMs = Math.Max(metric.MaxTimeMs, elapsedMs);
        metric.MinTimeMs = metric.MinTimeMs == 0 ? elapsedMs : Math.Min(metric.MinTimeMs, elapsedMs);
        metric.LastAllocationTime = DateTime.Now;
        metric.ItemCount = itemCount;
    }

    /// <summary>
    /// 清理过期缓存
    /// </summary>
    private void CleanupExpiredCache(object? state)
    {
        try
        {
            var cutoffTime = DateTime.Now.Subtract(_cacheExpiry);
            
            // 清理分配缓存
            var expiredAllocations = _allocationCache
                .Where(kvp => kvp.Value.AllocationTimeMs < cutoffTime.Ticks)
                .Select(kvp => kvp.Key)
                .ToList();
            
            foreach (var key in expiredAllocations)
            {
                _allocationCache.TryRemove(key, out _);
            }

            // 清理预览缓存
            var expiredPreviews = _previewCache
                .Where(kvp => kvp.Value.EstimatedTotalAmount < cutoffTime.Ticks)
                .Select(kvp => kvp.Key)
                .ToList();
            
            foreach (var key in expiredPreviews)
            {
                _previewCache.TryRemove(key, out _);
            }

            if (expiredAllocations.Count > 0 || expiredPreviews.Count > 0)
            {
                _logger.LogDebug("清理过期缓存: 分配{Alloc}个, 预览{Preview}个", 
                    expiredAllocations.Count, expiredPreviews.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期缓存时发生异常");
        }
    }

    #endregion

    #region 分析和优化相关

    /// <summary>
    /// 分析最优策略
    /// </summary>
    private AllocationStrategy AnalyzeOptimalStrategy(AllocationRequest request)
    {
        try
        {
            var itemCount = request.Items.Count;
            var totalAmount = request.Items.Sum(i => i.TotalPrice);
            var averagePrice = totalAmount / itemCount;
            var priceVariance = request.Items.Sum(i => Math.Pow((double)(i.UnitPrice - averagePrice), 2)) / itemCount;

            // 如果价格差异很大，推荐按金额比例分配
            if (priceVariance > (double)(averagePrice * averagePrice * 0.5m))
            {
                return AllocationStrategy.ProportionalByAmount;
            }

            // 如果有自定义权重，推荐权重分配
            if (request.CustomWeights.Any())
            {
                return AllocationStrategy.CustomWeight;
            }

            // 如果商品数量较少且价格相近，推荐平均分配
            if (itemCount <= 5 && priceVariance < (double)(averagePrice * averagePrice * 0.1m))
            {
                return AllocationStrategy.EqualDistribution;
            }

            // 默认推荐按金额比例分配
            return AllocationStrategy.ProportionalByAmount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分析最优策略时发生异常");
            return AllocationStrategy.ProportionalByAmount;
        }
    }

    /// <summary>
    /// 计算预览分配
    /// </summary>
    private List<AllocationDetail> CalculatePreviewAllocation(AllocationRequest request)
    {
        var previewDetails = new List<AllocationDetail>();
        
        try
        {
            var eligibleItems = request.Items.Where(i => i.IsEligibleForAllocation).ToList();
            if (!eligibleItems.Any()) return previewDetails;

            switch (request.Strategy)
            {
                case AllocationStrategy.ProportionalByAmount:
                    var totalAmount = eligibleItems.Sum(i => i.TotalPrice);
                    foreach (var item in eligibleItems)
                    {
                        var ratio = totalAmount > 0 ? item.TotalPrice / totalAmount : 0;
                        var allocatedAmount = request.Promotion.DiscountAmount * ratio;
                        
                        previewDetails.Add(new AllocationDetail
                        {
                            ProductId = item.ProductId,
                            ProductName = item.ProductName,
                            OriginalAmount = item.TotalPrice,
                            AllocatedAmount = allocatedAmount,
                            AllocationRatio = ratio,
                            UnitAllocationAmount = allocatedAmount / item.Quantity,
                            FinalUnitPrice = item.UnitPrice - (allocatedAmount / item.Quantity)
                        });
                    }
                    break;

                case AllocationStrategy.EqualDistribution:
                    var allocatedAmountPerItem = request.Promotion.DiscountAmount / eligibleItems.Count;
                    foreach (var item in eligibleItems)
                    {
                        previewDetails.Add(new AllocationDetail
                        {
                            ProductId = item.ProductId,
                            ProductName = item.ProductName,
                            OriginalAmount = item.TotalPrice,
                            AllocatedAmount = allocatedAmountPerItem,
                            AllocationRatio = 1m / eligibleItems.Count,
                            UnitAllocationAmount = allocatedAmountPerItem / item.Quantity,
                            FinalUnitPrice = item.UnitPrice - (allocatedAmountPerItem / item.Quantity)
                        });
                    }
                    break;

                default:
                    // 对于其他策略，使用简化的预览计算
                    var simpleAllocation = request.Promotion.DiscountAmount / eligibleItems.Count;
                    foreach (var item in eligibleItems)
                    {
                        previewDetails.Add(new AllocationDetail
                        {
                            ProductId = item.ProductId,
                            ProductName = item.ProductName,
                            OriginalAmount = item.TotalPrice,
                            AllocatedAmount = simpleAllocation,
                            AllocationRatio = 1m / eligibleItems.Count,
                            UnitAllocationAmount = simpleAllocation / item.Quantity,
                            FinalUnitPrice = item.UnitPrice - (simpleAllocation / item.Quantity)
                        });
                    }
                    break;
            }

            return previewDetails;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "计算预览分配时发生异常");
            return previewDetails;
        }
    }

    /// <summary>
    /// 分析潜在问题
    /// </summary>
    private List<string> AnalyzePotentialIssues(AllocationRequest request, List<AllocationDetail> previewDetails)
    {
        var issues = new List<string>();

        try
        {
            // 检查负单价
            var negativePrice = previewDetails.Where(d => d.FinalUnitPrice < 0).ToList();
            if (negativePrice.Any())
            {
                issues.Add($"存在{negativePrice.Count}个商品的最终单价为负");
            }

            // 检查分配不均
            if (previewDetails.Count > 1)
            {
                var allocations = previewDetails.Select(d => d.AllocatedAmount).ToList();
                var average = allocations.Average();
                var maxDeviation = allocations.Max(a => Math.Abs(a - average));
                
                if (maxDeviation > average * 0.5m)
                {
                    issues.Add("分配金额差异较大，可能存在不公平分配");
                }
            }

            // 检查精度损失
            var totalAllocated = previewDetails.Sum(d => d.AllocatedAmount);
            var difference = Math.Abs(request.Promotion.DiscountAmount - totalAllocated);
            
            if (difference > 0.05m)
            {
                issues.Add($"分配精度损失较大: {difference:C}");
            }

            // 检查舍入影响
            if (request.RoundingStrategy != RoundingStrategy.NoRounding)
            {
                var roundingImpact = previewDetails.Sum(d => Math.Abs(d.AllocatedAmount - Math.Round(d.AllocatedAmount, 2)));
                if (roundingImpact > 0.1m)
                {
                    issues.Add($"舍入操作可能产生较大影响: {roundingImpact:C}");
                }
            }

            return issues;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分析潜在问题时发生异常");
            issues.Add($"问题分析异常: {ex.Message}");
            return issues;
        }
    }

    /// <summary>
    /// 估算精度损失
    /// </summary>
    private decimal EstimatePrecisionLoss(AllocationRequest request, List<AllocationDetail> previewDetails)
    {
        try
        {
            var totalAllocated = previewDetails.Sum(d => d.AllocatedAmount);
            return Math.Abs(request.Promotion.DiscountAmount - totalAllocated);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "估算精度损失时发生异常");
            return 0;
        }
    }

    /// <summary>
    /// 获取策略复杂度
    /// </summary>
    private static int GetStrategyComplexity(AllocationStrategy strategy)
    {
        return strategy switch
        {
            AllocationStrategy.EqualDistribution => 1,
            AllocationStrategy.ProportionalByAmount => 2,
            AllocationStrategy.ProportionalByQuantity => 2,
            AllocationStrategy.CustomWeight => 3,
            AllocationStrategy.HighValueFirst => 4,
            AllocationStrategy.LowValueFirst => 4,
            AllocationStrategy.ByCategory => 5,
            AllocationStrategy.ByProfitMargin => 5,
            _ => 2
        };
    }

    /// <summary>
    /// 确定复杂度等级
    /// </summary>
    private static AllocationComplexity DetermineComplexityLevel(Dictionary<string, int> factors)
    {
        var totalComplexity = factors.Values.Sum();
        
        return totalComplexity switch
        {
            <= 10 => AllocationComplexity.Simple,
            <= 20 => AllocationComplexity.Moderate,
            <= 50 => AllocationComplexity.Complex,
            _ => AllocationComplexity.VeryComplex
        };
    }

    /// <summary>
    /// 估算处理时间
    /// </summary>
    private static long EstimateProcessingTime(AllocationComplexity complexity, int itemCount)
    {
        var baseTime = complexity switch
        {
            AllocationComplexity.Simple => 1,
            AllocationComplexity.Moderate => 5,
            AllocationComplexity.Complex => 20,
            AllocationComplexity.VeryComplex => 100,
            _ => 5
        };

        return baseTime + (itemCount / 10);
    }

    /// <summary>
    /// 生成优化建议
    /// </summary>
    private List<string> GenerateOptimizationSuggestions(AllocationComplexityAnalysis analysis)
    {
        var suggestions = new List<string>();

        if (analysis.ComplexityLevel >= AllocationComplexity.Complex)
        {
            suggestions.Add("考虑使用更简单的分配策略以提升性能");
            suggestions.Add("实施缓存机制减少重复计算");
        }

        if (analysis.ComplexityFactors.TryGetValue("ItemCount", out var itemCount) && itemCount > 100)
        {
            suggestions.Add("商品数量较多，建议使用批量处理优化性能");
        }

        if (analysis.ComplexityFactors.TryGetValue("HasCustomWeights", out var hasWeights) && hasWeights > 0)
        {
            suggestions.Add("自定义权重增加了复杂度，确保权重配置合理");
        }

        return suggestions;
    }

    /// <summary>
    /// 计算分配统计信息
    /// </summary>
    private void CalculateAllocationStatistics(AllocationResult result)
    {
        if (result.AllocationDetails?.Any() != true) return;

        var allocations = result.AllocationDetails.Select(d => d.AllocatedAmount).ToList();
        
        result.Statistics.ParticipatingItemsCount = result.AllocationDetails.Count;
        result.Statistics.AverageAllocationAmount = allocations.Average();
        result.Statistics.MaxAllocationAmount = allocations.Max();
        result.Statistics.MinAllocationAmount = allocations.Min();
        
        // 计算标准差
        var average = allocations.Average();
        var variance = allocations.Sum(a => Math.Pow((double)(a - average), 2)) / allocations.Count;
        result.Statistics.AllocationStandardDeviation = (decimal)Math.Sqrt(variance);
        
        // 计算精度损失
        result.Statistics.PrecisionLoss = Math.Abs(result.OriginalDiscountAmount - result.TotalAllocatedAmount);
        
        // 计算舍入次数
        result.Statistics.RoundingOperationsCount = result.AllocationDetails.Count(d => d.RoundingAdjustment != 0);
    }

    #endregion

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed) return;

        try
        {
            _cacheCleanupTimer?.Dispose();
            _allocationSemaphore?.Dispose();
            
            _allocationCache.Clear();
            _previewCache.Clear();
            _performanceMetrics.Clear();
            
            _logger.LogInformation("分配引擎已释放资源");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "释放分配引擎资源时发生异常");
        }
        finally
        {
            _disposed = true;
        }
    }
}

/// <summary>
/// 分配性能指标类
/// </summary>
internal sealed class AllocationMetrics
{
    /// <summary>总分配次数</summary>
    public long TotalAllocations { get; set; }
    
    /// <summary>总耗时(毫秒)</summary>
    public long TotalTimeMs { get; set; }
    
    /// <summary>平均耗时(毫秒)</summary>
    public double AverageTimeMs { get; set; }
    
    /// <summary>最大耗时(毫秒)</summary>
    public long MaxTimeMs { get; set; }
    
    /// <summary>最小耗时(毫秒)</summary>
    public long MinTimeMs { get; set; }
    
    /// <summary>最后分配时间</summary>
    public DateTime LastAllocationTime { get; set; }
    
    /// <summary>处理项目数量</summary>
    public int ItemCount { get; set; }
}
