using PE2.PromotionEngine.Rules.SpecialPriceRules;
using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.SpecialPriceRules;

/// <summary>
/// 组合特价规则测试类
/// 测试 CombinationSpecialPriceRule 的组合特价计算和应用逻辑
/// </summary>
public class CombinationSpecialPriceRuleTests(ITestOutputHelper output) : TestBase(output)
{
    #region 基础组合特价功能测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_ValidCombinationSpecialPrice_ShouldApplyCorrectly()
    {
        // Arrange - A、B商品各购买1件时，特价600元
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_001",
            Name = "组合特价测试 - 买A+B特价600元",
            Description = "A、B商品各购买数量大于等于1件时，特价600元",
            Priority = 90,
            IsEnabled = true,
            SpecialPrice = 600.00m,
            CombinationConditions =
            [
                new CombinationSpecialPriceCondition { ProductId = "A", RequiredQuantity = 1 },
                new CombinationSpecialPriceCondition { ProductId = "B", RequiredQuantity = 1 }
            ]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            (TestDataGenerator.CreateProductA(), 1), // 满足A商品条件
            (TestDataGenerator.CreateProductB(), 1) // 满足B商品条件
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "应用前购物车");

        var originalTotalPrice =
            TestDataGenerator.CreateProductA().Price + TestDataGenerator.CreateProductB().Price; // 200+30 = 230元
        var expectedSpecialPrice = 600m; // 组合特价600元
        var expectedTotalDiscount = originalTotalPrice - expectedSpecialPrice; // 230-600 = -370元（实际是增加价格）

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "A+B组合特价600元");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_ExcessQuantityCombination_ShouldApplyCorrectly()
    {
        // Arrange - A、B商品各购买超过1件时，仍应用组合特价
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_001",
            Name = "组合特价测试 - 买A+B特价600元",
            SpecialPrice = 600.00m,
            IsEnabled = true,
            CombinationConditions =
            [
                new CombinationSpecialPriceCondition { ProductId = "A", RequiredQuantity = 1 },
                new CombinationSpecialPriceCondition { ProductId = "B", RequiredQuantity = 1 }
            ]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_002",
            (TestDataGenerator.CreateProductA(), 3), // 超过需要的1件A
            (TestDataGenerator.CreateProductB(), 2) // 超过需要的1件B
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "超过数量的购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "超过数量的组合特价场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_MissingFirstProduct_ShouldNotApply()
    {
        // Arrange - 缺少第一个商品A
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_001",
            Name = "组合特价测试 - 买A+B特价600元",
            SpecialPrice = 600.00m,
            IsEnabled = true,
            CombinationConditions =
            [
                new CombinationSpecialPriceCondition { ProductId = "A", RequiredQuantity = 1 },
                new CombinationSpecialPriceCondition { ProductId = "B", RequiredQuantity = 1 }
            ]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_003",
            (TestDataGenerator.CreateProductB(), 2), // 只有B，没有A
            (TestDataGenerator.CreateProductC(), 1)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "缺少第一个商品的购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "缺少第一个商品场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "缺少组合商品时应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_MissingSecondProduct_ShouldNotApply()
    {
        // Arrange - 缺少第二个商品B
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_001",
            Name = "组合特价测试 - 买A+B特价600元",
            SpecialPrice = 600.00m,
            IsEnabled = true,
            CombinationConditions =
            [
                new CombinationSpecialPriceCondition { ProductId = "A", RequiredQuantity = 1 },
                new CombinationSpecialPriceCondition { ProductId = "B", RequiredQuantity = 1 }
            ]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_004",
            (TestDataGenerator.CreateProductA(), 2), // 只有A，没有B
            (TestDataGenerator.CreateProductC(), 1)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "缺少第二个商品的购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "缺少第二个商品场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "缺少组合商品时应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 数量不足测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_InsufficientFirstProductQuantity_ShouldNotApply()
    {
        // Arrange - 第一个商品A数量不足
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_001",
            Name = "组合特价测试 - 买A+B特价600元",
            SpecialPrice = 600.00m,
            IsEnabled = true,
            CombinationConditions =
            [
                new CombinationSpecialPriceCondition { ProductId = "A", RequiredQuantity = 2 }, // 需要2件A
                new CombinationSpecialPriceCondition { ProductId = "B", RequiredQuantity = 1 }
            ]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_005",
            (TestDataGenerator.CreateProductA(), 1), // 只有1件A，不足2件
            (TestDataGenerator.CreateProductB(), 2) // B数量足够
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "第一个商品数量不足的购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "第一个商品数量不足场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "数量不足时应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_InsufficientSecondProductQuantity_ShouldNotApply()
    {
        // Arrange - 第二个商品B数量不足
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_001",
            Name = "组合特价测试 - 买A+B特价600元",
            SpecialPrice = 600.00m,
            IsEnabled = true,
            CombinationConditions =
            [
                new CombinationSpecialPriceCondition { ProductId = "A", RequiredQuantity = 1 },
                new CombinationSpecialPriceCondition { ProductId = "B", RequiredQuantity = 3 } // 需要3件B
            ]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_006",
            (TestDataGenerator.CreateProductA(), 2), // A数量足够
            (TestDataGenerator.CreateProductB(), 2) // 只有2件B，不足3件
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "第二个商品数量不足的购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "第二个商品数量不足场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "数量不足时应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange - 空购物车
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_001",
            Name = "组合特价测试 - 买A+B特价600元",
            SpecialPrice = 600.00m,
            IsEnabled = true,
            CombinationConditions =
            [
                new CombinationSpecialPriceCondition { ProductId = "A", RequiredQuantity = 1 },
                new CombinationSpecialPriceCondition { ProductId = "B", RequiredQuantity = 1 }
            ]
        };

        var cart = TestDataGenerator.CreateEmptyCart();

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "空购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "空购物车场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "空购物车应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public async Task Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange - 禁用的规则
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_001",
            Name = "组合特价测试 - 买A+B特价600元",
            SpecialPrice = 600.00m,
            IsEnabled = false, // 禁用规则
            CombinationConditions =
            [
                new CombinationSpecialPriceCondition { ProductId = "A", RequiredQuantity = 1 },
                new CombinationSpecialPriceCondition { ProductId = "B", RequiredQuantity = 1 }
            ]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_010",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1)
        );

        //ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "禁用规则测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "禁用规则场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "禁用规则应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_ExactCombinationQuantity_ShouldApplyCorrectly()
    {
        // Arrange - 恰好满足组合数量
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_BOUNDARY_001",
            Name = "组合特价测试 - 边界条件",
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 2 },
                new() { ProductId = "B", RequiredQuantity = 3 }
            ],
            SpecialPrice = 400.00m,
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_008",
            (TestDataGenerator.CreateProductA(), 2), // 恰好满足
            (TestDataGenerator.CreateProductB(), 3) // 恰好满足
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "组合边界测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "组合边界场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证组合特价被正确应用
        var expectedDiscount = (2 * 200m + 3 * 150m) - 400m; // 原价1050 - 特价400 = 650优惠
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应应用组合特价优惠650元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_JustBelowCombinationQuantity_ShouldNotApply()
    {
        // Arrange - 刚好低于组合数量要求
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_BOUNDARY_002",
            Name = "组合特价测试 - 低于边界",
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 3 },
                new() { ProductId = "B", RequiredQuantity = 2 }
            ],
            SpecialPrice = 350.00m,
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_009",
            (TestDataGenerator.CreateProductA(), 2), // 低于要求
            (TestDataGenerator.CreateProductB(), 2) // 满足要求
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "低于组合边界测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "低于组合边界场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public async Task Apply_ZeroCombinationPrice_ShouldApplyCorrectly()
    {
        // Arrange - 零价格组合（免费）
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_ZERO_001",
            Name = "组合特价测试 - 免费组合",
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 1 },
                new() { ProductId = "B", RequiredQuantity = 1 }
            ],
            SpecialPrice = 0.00m, // 免费
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_010",
            (TestDataGenerator.CreateProductA(), 2),
            (TestDataGenerator.CreateProductB(), 2)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "免费组合测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "免费组合场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证免费组合优惠
        var expectedDiscount =
            TestDataGenerator.CreateProductA().Price + TestDataGenerator.CreateProductB().Price; // A+B原价350元全部优惠
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应应用免费组合优惠 ");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 可重复性测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_MultipleApplications_ShouldBeConsistent()
    {
        // Arrange - 多次应用同一规则应该得到一致结果
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_REPEAT_001",
            Name = "组合特价测试 - 可重复性",
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 2 },
                new() { ProductId = "B", RequiredQuantity = 1 }
            ],
            SpecialPrice = 300.00m,
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_011",
            (TestDataGenerator.CreateProductA(), 4),
            (TestDataGenerator.CreateProductB(), 2)
        );

        ValidateTestData(cart, [rule]);
        TestPromotionRuleService.Rules = [rule];

        // Act - 多次执行
        var result1 = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;
        var result2 = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;
        var result3 = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert - 结果应该完全一致
        AssertAmountEqual(result1.TotalDiscount, result2.TotalDiscount, "第一次和第二次结果应一致");
        AssertAmountEqual(result2.TotalDiscount, result3.TotalDiscount, "第二次和第三次结果应一致");

        Assert.Equal(result1.AppliedPromotions.Count, result2.AppliedPromotions.Count);
        Assert.Equal(result2.AppliedPromotions.Count, result3.AppliedPromotions.Count);

        // 验证购物车一致性
        AssertCartConsistency(result1.ProcessedCart);
        AssertCartConsistency(result2.ProcessedCart);
        AssertCartConsistency(result3.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_MultipleCombinationSets_ShouldApplyToAll()
    {
        // Arrange - 多套组合应该都被应用
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_MULTI_001",
            Name = "组合特价测试 - 多套组合",
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 1 },
                new() { ProductId = "B", RequiredQuantity = 1 }
            ],
            SpecialPrice = 250.00m,
            IsRepeatable = true,
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_012",
            (TestDataGenerator.CreateProductA(), 3), // 可组成3套
            (TestDataGenerator.CreateProductB(), 3) // 可组成3套
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "多套组合测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "多套组合场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证多套组合优惠（3套组合）
        var expectedDiscount = 3 * ((200m + 150m) - 250m); // 3 * (350-250) = 300元
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应应用3套组合优惠300元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 复杂场景测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_MultipleRulesConflict_ShouldApplyOptimal()
    {
        // Arrange - 多个组合规则冲突，应选择最优的
        var rule1 = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_CONFLICT_001",
            Name = "组合特价测试 - 规则1",
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 2 },
                new() { ProductId = "B", RequiredQuantity = 1 }
            ],
            SpecialPrice = 450.00m,
            Priority = 50,
            IsEnabled = true
        };

        var rule2 = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_CONFLICT_002",
            Name = "组合特价测试 - 规则2",
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 2 },
                new() { ProductId = "B", RequiredQuantity = 1 }
            ],
            SpecialPrice = 350.00m, // 更优
            Priority = 60,
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_013",
            (TestDataGenerator.CreateProductA(), 2),
            (TestDataGenerator.CreateProductB(), 1)
        );

        ValidateTestData(cart, [rule1, rule2]);
        LogCartDetails(cart, "多组合规则冲突测试购物车");

        TestPromotionRuleService.Rules = [rule1, rule2];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "多组合规则冲突场景");
        LogPromotionResultDetails(result);

        // 验证应用了更优的规则
        Assert.Single(result.AppliedPromotions);

        // 验证更优的组合特价（350元）
        var expectedDiscount = (2 * 200m + 1 * 150m) - 350m; // 550-350 = 200元优惠
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应应用更优的组合特价优惠200元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_ComplexCombinationStructure_ShouldApplyCorrectly()
    {
        // Arrange - 复杂组合结构测试
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_COMPLEX_001",
            Name = "组合特价测试 - 复杂结构",
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 3 },
                new() { ProductId = "B", RequiredQuantity = 2 },
                new() { ProductId = "C", RequiredQuantity = 1 }
            ],
            SpecialPrice = 800.00m,
            IsRepeatable = true,
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_014",
            (TestDataGenerator.CreateProductA(), 6), // 可组成2套
            (TestDataGenerator.CreateProductB(), 4), // 可组成2套
            (TestDataGenerator.CreateProductC(), 3) // 可组成2套（限制因子）
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "复杂组合结构测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "复杂组合结构场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证复杂组合优惠（2套组合）
        var originalPrice = 6 * 200m + 4 * 150m + 3 * 100m; // 2100元
        var appliedCombinations = 2; // 最多可组成2套
        var combinationPrice = appliedCombinations * 800m; // 1600元
        var remainingPrice = 0; // 所有商品都被组合覆盖
        var expectedDiscount = originalPrice - combinationPrice - remainingPrice; // 2100-1600 = 500元

        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应应用复杂组合优惠500元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_PartialCombinationWithRemainder_ShouldHandleCorrectly()
    {
        // Arrange - 部分组合，有剩余商品
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_PARTIAL_001",
            Name = "组合特价测试 - 部分组合",
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 2 },
                new() { ProductId = "B", RequiredQuantity = 3 }
            ],
            SpecialPrice = 500.00m,
            IsRepeatable = true,
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_015",
            (TestDataGenerator.CreateProductA(), 5), // 可组成2套，剩余1个
            (TestDataGenerator.CreateProductB(), 7) // 可组成2套，剩余1个
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "部分组合测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "部分组合场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证部分组合优惠（2套组合，剩余商品按原价）
        var originalPrice = 5 * 200m + 7 * 150m; // 2050元
        var appliedCombinations = 2; // 最多可组成2套
        var combinationPrice = appliedCombinations * 500m; // 1000元
        var remainingPrice = 1 * 200m + 1 * 150m; // 剩余商品350元
        var expectedDiscount = originalPrice - combinationPrice - remainingPrice; // 2050-1000-350 = 700元

        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应应用部分组合优惠700元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 配置验证测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_NullCombinationConditions_ShouldNotApply()
    {
        // Arrange - 空组合条件
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_NULL_001",
            Name = "组合特价测试 - 空组合条件",
            CombinationConditions = null, // 空条件
            SpecialPrice = 300.00m,
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_016",
            (TestDataGenerator.CreateProductA(), 2),
            (TestDataGenerator.CreateProductB(), 2)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "空组合条件测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "空组合条件场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public async Task Apply_EmptyCombinationConditions_ShouldNotApply()
    {
        // Arrange - 空组合条件列表
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_EMPTY_001",
            Name = "组合特价测试 - 空组合条件列表",
            CombinationConditions = [], // 空列表
            SpecialPrice = 300.00m,
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_017",
            (TestDataGenerator.CreateProductA(), 2),
            (TestDataGenerator.CreateProductB(), 2)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "空组合条件列表测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "空组合条件列表场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_NegativeRequiredQuantity_ShouldHandleCorrectly()
    {
        // Arrange - 负数要求数量
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_NEGATIVE_001",
            Name = "组合特价测试 - 负数要求数量",
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = -1 }, // 负数
                new() { ProductId = "B", RequiredQuantity = 2 }
            ],
            SpecialPrice = 300.00m,
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_018",
            (TestDataGenerator.CreateProductA(), 2),
            (TestDataGenerator.CreateProductB(), 2)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "负数要求数量测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "负数要求数量场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确处理（可能忽略负数条件或应用有效条件）
        // 具体行为取决于业务逻辑实现

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_NegativeSpecialPrice_ShouldHandleCorrectly()
    {
        // Arrange - 负数特价
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_NEGATIVE_PRICE_001",
            Name = "组合特价测试 - 负数特价",
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 1 },
                new() { ProductId = "B", RequiredQuantity = 1 }
            ],
            SpecialPrice = -100.00m, // 负数价格
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_019",
            (TestDataGenerator.CreateProductA(), 2),
            (TestDataGenerator.CreateProductB(), 2)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "负数特价测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "负数特价场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确处理（可能忽略负数价格或应用有效价格）
        // 具体行为取决于业务逻辑实现

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 性能测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_LargeQuantityCart_ShouldPerformWell()
    {
        // Arrange - 大数量购物车性能测试
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_PERFORMANCE_001",
            Name = "组合特价测试 - 性能测试",
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 10 },
                new() { ProductId = "B", RequiredQuantity = 5 }
            ],
            SpecialPrice = 2000.00m,
            IsRepeatable = true,
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_020",
            (TestDataGenerator.CreateProductA(), 1000), // 大数量
            (TestDataGenerator.CreateProductB(), 500) // 大数量
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "大数量组合性能测试购物车");

        TestPromotionRuleService.Rules = [rule];

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        stopwatch.Stop();

        // Assert
        AssertPromotionResult(result, "大数量组合性能测试场景");
        LogPromotionResultDetails(result);

        // 验证性能（应在合理时间内完成，如1秒）
        Assert.True(
            stopwatch.ElapsedMilliseconds < 1000,
            $"性能测试超时：{stopwatch.ElapsedMilliseconds}ms"
        );

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_ManyCombinationConditions_ShouldPerformWell()
    {
        // Arrange - 大量组合条件性能测试

        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_MANY_CONDITIONS_001",
            Name = "组合特价测试 - 大量条件",
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 1 },
                new() { ProductId = "B", RequiredQuantity = 1 }
            ],
            MaxApplications = int.MaxValue,
            IsRepeatable = true,
            SpecialPrice = 1000.00m,
            IsEnabled = true
        };

        // 创建包含所有所需商品的购物车

        var cart = TestDataGenerator.CreateLargeTestCart(100);

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "大量组合条件性能测试购物车");

        TestPromotionRuleService.Rules = [rule];

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        stopwatch.Stop();

        // Assert
        AssertPromotionResult(result, "大量组合条件性能测试场景");
        LogPromotionResultDetails(result);

        // 验证性能（应在合理时间内完成，如1秒）
        Assert.True(
            stopwatch.ElapsedMilliseconds < 1000,
            $"大量条件性能测试超时：{stopwatch.ElapsedMilliseconds}ms"
        );

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 利益最大化策略测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_CustomerBenefitMaximization_ShouldChooseOptimal()
    {
        // Arrange - 客户利益最大化：选择对客户最有利的组合规则
        var rule1 = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_BENEFIT_001",
            Name = "组合特价测试 - 利益最大化1",
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 2 },
                new() { ProductId = "B", RequiredQuantity = 1 }
            ],
            SpecialPrice = 450.00m,
            Priority = 70,
            IsEnabled = true
        };

        var rule2 = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_BENEFIT_002",
            Name = "组合特价测试 - 利益最大化2",
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 2 },
                new() { ProductId = "B", RequiredQuantity = 1 }
            ],
            SpecialPrice = 300.00m, // 更优
            Priority = 60,
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_022",
            (TestDataGenerator.CreateProductA(), 2),
            (TestDataGenerator.CreateProductB(), 1)
        );

        ValidateTestData(cart, [rule1, rule2]);
        LogCartDetails(cart, "组合利益最大化测试购物车");

        TestPromotionRuleService.Rules = [rule1, rule2];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "组合利益最大化场景");
        LogPromotionResultDetails(result);

        // 验证选择了对客户最有利的规则（更低的特价）
        Assert.Single(result.AppliedPromotions);

        // 验证总优惠最大化
        var expectedDiscount = (2 * 200m + 1 * 150m) - 300m; // 550-300 = 250元
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应实现总优惠最大化250元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_CombinationOptimization_ShouldMaximizeCombinations()
    {
        // Arrange - 组合优化：最大化组合数量以获得最大优惠
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_OPTIMIZATION_001",
            Name = "组合特价测试 - 组合优化",
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 3 },
                new() { ProductId = "B", RequiredQuantity = 2 }
            ],
            SpecialPrice = 600.00m,
            IsRepeatable = true,
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_023",
            (TestDataGenerator.CreateProductA(), 10), // 可组成3套（限制因子）
            (TestDataGenerator.CreateProductB(), 8) // 可组成4套
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "组合优化测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "组合优化场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证最大化组合数量（3套组合）
        var originalPrice = 10 * 200m + 8 * 150m; // 3200元
        var appliedCombinations = 3; // 最多可组成3套
        var combinationPrice = appliedCombinations * 600m; // 1800元
        var remainingPrice = 1 * 200m + 2 * 150m; // 剩余商品500元
        var expectedDiscount = originalPrice - combinationPrice - remainingPrice; // 3200-1800-500 = 900元

        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应实现组合优化的总优惠900元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 数据完整性测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public async Task Apply_CombinationCalculationAccuracy_ShouldMaintainPrecision()
    {
        // Arrange - 组合计算精度测试
        var rule = new CombinationSpecialPriceRule
        {
            Id = "COMBINATION_SPECIAL_PRICE_PRECISION_001",
            Name = "组合特价测试 - 计算精度",
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 3 },
                new() { ProductId = "B", RequiredQuantity = 2 }
            ],
            SpecialPrice = 666.66m, // 精确价格
            IsRepeatable = true,
            MaxApplications = 5,
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_024",
            (TestDataGenerator.CreateProductA(), 6), // 可组成2套
            (TestDataGenerator.CreateProductB(), 5) // 可组成2套，剩余1个
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "组合计算精度测试购物车");

        var originalTotal = cart.Items.Sum(i => i.Product.Price * i.Quantity);

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "组合计算精度场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证精确的组合计算
        var appliedCombinations = 2; // 最多可组成2套
        var combinationPrice = appliedCombinations * 666.66m; // 1333.32元
        var remainingPrice = 1 * 150m; // 剩余商品150元
        var expectedDiscount = originalTotal - combinationPrice - remainingPrice;

        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应实现精确的组合计算");

        // 验证购物车总额计算精度
        var processedTotal = result.ProcessedCart.Items.Sum(i => i.ActualUnitPrice * i.Quantity);
        var expectedTotal = originalTotal - result.TotalDiscount;

        AssertAmountEqual(expectedTotal, processedTotal, "处理后购物车总额应精确");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion
}
