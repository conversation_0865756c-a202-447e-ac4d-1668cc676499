using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.ExchangeRules;

/// <summary>
/// 组合特价换购规则测试
/// 测试场景：购买A、B商品各大于等于1件时，可以加100元换C商品
/// </summary>
public class CombinationSpecialPriceExchangeRuleTests : TestBase
{
    public CombinationSpecialPriceExchangeRuleTests(ITestOutputHelper output) : base(output)
    {
    }

    #region 基本功能测试

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "High")]
    public void Apply_BasicCombinationSpecialPriceExchange_ShouldApplyCorrectly()
    {
        // Arrange - 创建基本组合特价换购规则：买A+B加100元换C
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_BuyAB_Add100_ExchangeC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            (TestDataGenerator.CreateProductA(), 1), // A商品50元
            (TestDataGenerator.CreateProductB(), 1), // B商品30元
            (TestDataGenerator.CreateProductC(), 1)  // C商品20元作为换购商品
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "应用前购物车");

        var originalCPrice = TestDataGenerator.CreateProductC().Price; // 20元
        var expectedExchangePrice = 100.00m; // 加100元换购
        var expectedActualPrice = expectedExchangePrice; // 实际支付100元
        var expectedDiscount = originalCPrice > expectedExchangePrice ? originalCPrice - expectedExchangePrice : 0m;

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "基本组合特价换购");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证C商品实际支付价格
        var productCItem = result.ProcessedCart.Items.First(i => i.Product.Id == "C");
        AssertAmountEqual(expectedActualPrice, productCItem.ActualUnitPrice, "C商品换购价格应为100元");

        // 验证总优惠金额
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应为换购折扣金额");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "High")]
    public void Apply_CombinationWithLowerAddAmount_ShouldApplyCorrectly()
    {
        // Arrange - 组合换购加较少金额
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_BuyAB_Add10_ExchangeC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_002",
            (TestDataGenerator.CreateProductA(), 1), // A商品50元
            (TestDataGenerator.CreateProductB(), 1), // B商品30元
            (TestDataGenerator.CreateProductC(), 1)  // C商品20元
        );

        LogCartDetails(cart, "组合换购加较少金额购物车");

        var originalCPrice = TestDataGenerator.CreateProductC().Price; // 20元
        var expectedExchangePrice = 10.00m; // 加10元换购
        var expectedDiscount = originalCPrice - expectedExchangePrice; // 10元优惠

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "组合换购加较少金额");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证C商品实际支付价格为10元
        var productCItem = result.ProcessedCart.Items.First(i => i.Product.Id == "C");
        AssertAmountEqual(expectedExchangePrice, productCItem.ActualUnitPrice, "C商品换购价格应为10元");

        // 验证总优惠金额
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应为换购折扣金额");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_QuantityBasedCombination_ShouldApplyCorrectly()
    {
        // Arrange - 基于数量的组合条件
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_QuantityBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_003",
            (TestDataGenerator.CreateProductA(), 2), // 满足A商品数量条件
            (TestDataGenerator.CreateProductB(), 2), // 满足B商品数量条件
            (TestDataGenerator.CreateProductC(), 1)
        );

        LogCartDetails(cart, "基于数量的组合条件购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "基于数量的组合条件");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证总优惠金额
        Assert.True(result.TotalDiscount >= 0);
        AssertCartConsistency(result.ProcessedCart, "基于数量的组合换购");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_AmountBasedCombination_ShouldApplyCorrectly()
    {
        // Arrange - 基于金额的组合条件
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_AmountBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_003",
            (TestDataGenerator.CreateProductA(), 3), // 150元，满足A商品金额条件
            (TestDataGenerator.CreateProductB(), 2), // 60元，满足B商品金额条件
            (TestDataGenerator.CreateProductC(), 1)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "基于金额的组合条件购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "基于金额的组合条件");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.NotEmpty(result.AppliedPromotions);
        Assert.True(result.TotalDiscount >= 0);
        AssertCartConsistency(result.ProcessedCart, "基于金额的组合换购");
    }

    #endregion

    #region 条件不满足测试

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_MissingFirstProduct_ShouldNotApply()
    {
        // Arrange - 缺少第一个组合商品
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_BuyAB_Add100_ExchangeC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_005",
            (TestDataGenerator.CreateProductB(), 1), // 只有B商品，缺少A商品
            (TestDataGenerator.CreateProductC(), 1)
        );

        LogCartDetails(cart, "缺少第一个组合商品购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "缺少第一个组合商品");
        LogPromotionResultDetails(result);

        // 验证没有规则被应用
        Assert.Empty(result.AppliedPromotions);
        AssertAmountEqual(0.00m, result.TotalDiscount, "缺少组合商品时不应有优惠");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_MissingSecondProduct_ShouldNotApply()
    {
        // Arrange - 缺少第二个组合商品
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_BuyAB_Add100_ExchangeC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_006",
            (TestDataGenerator.CreateProductA(), 1), // 只有A商品，缺少B商品
            (TestDataGenerator.CreateProductC(), 1)
        );

        LogCartDetails(cart, "缺少第二个组合商品购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "缺少第二个组合商品");
        LogPromotionResultDetails(result);

        // 验证没有规则被应用
        Assert.Empty(result.AppliedPromotions);
        AssertAmountEqual(0.00m, result.TotalDiscount, "缺少组合商品时不应有优惠");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_NoExchangeProductInCart_ShouldNotApply()
    {
        // Arrange - 购物车中没有换购商品
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_BuyAB_Add100_ExchangeC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_007",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1)
        // 没有添加C商品
        );

        LogCartDetails(cart, "没有换购商品购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "没有换购商品");
        LogPromotionResultDetails(result);

        // 验证没有规则被应用
        Assert.Empty(result.AppliedPromotions);
        AssertAmountEqual(0.00m, result.TotalDiscount, "没有换购商品时不应有优惠");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_InsufficientCombinationQuantity_ShouldNotApply()
    {
        // Arrange - 组合商品数量不足
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_QuantityBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_008",
            (TestDataGenerator.CreateProductA(), 1), // 不满足数量条件（需要2件）
            (TestDataGenerator.CreateProductB(), 2),
            (TestDataGenerator.CreateProductC(), 1)
        );

        LogCartDetails(cart, "组合商品数量不足购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "组合商品数量不足");
        LogPromotionResultDetails(result);

        // 验证没有规则被应用
        Assert.Empty(result.AppliedPromotions);
        AssertAmountEqual(0.00m, result.TotalDiscount, "组合商品数量不足时不应有优惠");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_InsufficientCombinationAmount_ShouldNotApply()
    {
        // Arrange - 组合商品金额不足
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_AmountBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_009",
            (TestDataGenerator.CreateProductA(), 1), // 50元，不满足金额条件
            (TestDataGenerator.CreateProductB(), 1), // 30元，不满足金额条件
            (TestDataGenerator.CreateProductC(), 1)
        );

        LogCartDetails(cart, "组合商品金额不足购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "组合商品金额不足");
        LogPromotionResultDetails(result);

        // 验证没有规则被应用
        Assert.Empty(result.AppliedPromotions);
        AssertAmountEqual(0.00m, result.TotalDiscount, "组合商品金额不足时不应有优惠");
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Low")]
    public void Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_BuyAB_Add100_ExchangeC();
        var cart = TestDataGenerator.CreateEmptyCart();

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "空购物车");
        Assert.Empty(result.AppliedPromotions);
        AssertAmountEqual(0.00m, result.TotalDiscount, "空购物车应无优惠");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Low")]
    public void Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange - 规则被禁用
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_Disabled();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_009",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1),
            (TestDataGenerator.CreateProductC(), 1)
        );

        LogCartDetails(cart, "规则被禁用购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "规则被禁用");
        LogPromotionResultDetails(result);

        // 验证没有规则被应用
        Assert.Empty(result.AppliedPromotions);
        AssertAmountEqual(0.00m, result.TotalDiscount, "规则被禁用时不应有优惠");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "High")]
    public void Apply_ZeroAddAmount_ShouldApplyAsFreeExchange()
    {
        // Arrange - 加0元换购（免费换购）
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_FreeExchange();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_004",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1),
            (TestDataGenerator.CreateProductC(), 1)
        );

        LogCartDetails(cart, "免费换购购物车");

        var originalCPrice = TestDataGenerator.CreateProductC().Price; // 20元
        var expectedDiscount = originalCPrice; // 免费换购，优惠全额

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "免费换购");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证C商品实际支付价格为0元
        var productCItem = result.ProcessedCart.Items.First(i => i.Product.Id == "C");
        AssertAmountEqual(0.00m, productCItem.ActualUnitPrice, "C商品免费换购价格应为0元");

        // 验证总优惠金额
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应为C商品原价");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_ExactCombinationRequirements_ShouldApplyCorrectly()
    {
        // Arrange - 刚好满足组合条件
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_BuyAB_Add100_ExchangeC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_010",
            (TestDataGenerator.CreateProductA(), 1), // 刚好1件
            (TestDataGenerator.CreateProductB(), 1), // 刚好1件
            (TestDataGenerator.CreateProductC(), 1)
        );

        LogCartDetails(cart, "刚好满足组合条件购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "刚好满足组合条件");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);
        AssertCartConsistency(result.ProcessedCart, "刚好满足组合条件");
    }

    #endregion

    #region 可重复性测试

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_RepeatableRule_ShouldApplyMultipleTimes()
    {
        // Arrange - 可重复的组合特价换购规则
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_Repeatable();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_011",
            (TestDataGenerator.CreateProductA(), 3), // 3件A商品
            (TestDataGenerator.CreateProductB(), 3), // 3件B商品
            (TestDataGenerator.CreateProductC(), 3)  // 3件C商品可换购
        );

        LogCartDetails(cart, "可重复规则购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "可重复规则");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.NotEmpty(result.AppliedPromotions);
        Assert.True(result.TotalDiscount >= 0);

        // 验证多次应用（通过检查应用的促销数量或优惠金额）
        Assert.True(result.AppliedPromotions.Count >= 1, "可重复规则应该能应用");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_NonRepeatableRule_ShouldApplyOnlyOnce()
    {
        // Arrange - 不可重复的组合特价换购规则
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_NonRepeatable();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_012",
            (TestDataGenerator.CreateProductA(), 5), // 足够多的A商品
            (TestDataGenerator.CreateProductB(), 5), // 足够多的B商品
            (TestDataGenerator.CreateProductC(), 5)  // 足够多的C商品
        );

        LogCartDetails(cart, "不可重复规则购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "不可重复规则");
        LogPromotionResultDetails(result);

        // 验证规则只应用一次
        if (result.AppliedPromotions.Any())
        {
            Assert.Single(result.AppliedPromotions, "不可重复规则应该只应用一次");
        }
    }

    #endregion

    #region 复杂场景测试

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "High")]
    public void Apply_MultipleExchangeOptions_ShouldSelectOptimal()
    {
        // Arrange - 多种换购选择
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_MultipleExchangeOptions();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_013",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1),
            (TestDataGenerator.CreateProductC(), 1), // 20元
            (TestDataGenerator.CreateProductD(), 1), // 100元
            (TestDataGenerator.CreateProductE(), 1)  // 15元
        );

        LogCartDetails(cart, "多种换购选择购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "多种换购选择");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.NotEmpty(result.AppliedPromotions);

        // 验证选择了最优的换购商品（客户利益最大化应选择高价商品）
        var hasExchangeApplied = result.ProcessedCart.Items.Any(item =>
            item.ActualUnitPrice != item.UnitPrice &&
            (item.Product.Id == "C" || item.Product.Id == "D" || item.Product.Id == "E"));
        Assert.True(hasExchangeApplied, "应该有换购商品被应用");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "High")]
    public void Apply_ComplexCombinationConditions_ShouldHandleCorrectly()
    {
        // Arrange - 复杂组合条件（数量+金额混合）
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_ComplexConditions();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_014",
            (TestDataGenerator.CreateProductA(), 2), // 满足数量条件
            (TestDataGenerator.CreateProductB(), 3), // 90元，满足金额条件
            (TestDataGenerator.CreateProductC(), 1)
        );

        LogCartDetails(cart, "复杂组合条件购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "复杂组合条件");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.NotEmpty(result.AppliedPromotions);
        Assert.True(result.TotalDiscount >= 0);
        AssertCartConsistency(result.ProcessedCart, "复杂组合条件");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_WithOtherPromotions_ShouldHandleCorrectly()
    {
        // Arrange - 与其他促销规则交互
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_BuyAB_Add100_ExchangeC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_015",
            (TestDataGenerator.CreateProductA(), 2),
            (TestDataGenerator.CreateProductB(), 2),
            (TestDataGenerator.CreateProductC(), 2)
        );

        // 先应用其他促销规则（模拟）
        var productAItem = cart.Items.First(x => x.Product.Id == "A");
        productAItem.ActualUnitPrice = 45.00m; // 模拟A商品已享受其他优惠

        LogCartDetails(cart, "与其他促销交互购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "与其他促销交互");
        LogPromotionResultDetails(result);
        AssertCartConsistency(result.ProcessedCart, "与其他促销交互");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_ThreeProductCombination_ShouldApplyCorrectly()
    {
        // Arrange - 三商品组合
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_ThreeProductCombo();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_016",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1),
            (TestDataGenerator.CreateProductC(), 1),
            (TestDataGenerator.CreateProductD(), 1) // 换购商品
        );

        LogCartDetails(cart, "三商品组合购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "三商品组合");
        LogPromotionResultDetails(result);
        AssertCartConsistency(result.ProcessedCart, "三商品组合");
    }

    #endregion

    #region 性能测试

    [Fact]
    [Trait("Category", "Performance")]
    [Trait("Priority", "Low")]
    public void Apply_LargeCart_ShouldPerformEfficiently()
    {
        // Arrange - 大型购物车性能测试
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_BuyAB_Add100_ExchangeC();
        var cart = TestDataGenerator.CreateLargeTestCart(100);


        LogCartDetails(cart, "大型购物车性能测试");

        TestPromotionRuleService.Rules = [rule];
        // Act & Assert
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;
        stopwatch.Stop();

        Assert.True(stopwatch.ElapsedMilliseconds < 1000, "大型购物车处理应在1秒内完成");
        Output.WriteLine($"大型购物车处理耗时: {stopwatch.ElapsedMilliseconds}ms");
        LogPromotionResultDetails(result);
    }

    #endregion

    #region 配置验证测试

    [Fact]
    [Trait("Category", "Validation")]
    [Trait("Priority", "Low")]
    public void Apply_InvalidConfiguration_ShouldHandleGracefully()
    {
        // Arrange - 无效配置
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_InvalidConfig();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_017",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1),
            (TestDataGenerator.CreateProductC(), 1)
        );

        LogCartDetails(cart, "无效配置购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "无效配置");
        LogPromotionResultDetails(result);

        // 验证没有规则被应用
        Assert.Empty(result.AppliedPromotions);
        AssertAmountEqual(0.00m, result.TotalDiscount, "无效配置时不应有优惠");
    }

    [Fact]
    [Trait("Category", "Validation")]
    [Trait("Priority", "Medium")]
    public void CalculateMaxApplications_ValidConditions_ShouldReturnCorrectCount()
    {
        // Arrange
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_Repeatable();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_018",
            (TestDataGenerator.CreateProductA(), 5),
            (TestDataGenerator.CreateProductB(), 5),
            (TestDataGenerator.CreateProductC(), 3)
        );

        LogCartDetails(cart, "计算最大应用次数购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        LogPromotionResultDetails(result);

        // 验证规则能够被应用
        if (result.AppliedPromotions.Any())
        {
            Assert.True(result.AppliedPromotions.Count > 0, "可重复规则应该能被应用");
            Output.WriteLine($"实际应用次数: {result.AppliedPromotions.Count}");
        }
        else
        {
            Output.WriteLine("规则未被应用，可能是条件不满足");
        }
    }

    [Fact]
    [Trait("Category", "Validation")]
    [Trait("Priority", "Low")]
    public void Apply_EmptyCombinationConditions_ShouldNotApply()
    {
        // Arrange - 空组合条件
        var rule = TestDataGenerator.CreateCombinationSpecialPriceExchangeRule_EmptyConditions();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_019",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1),
            (TestDataGenerator.CreateProductC(), 1)
        );

        LogCartDetails(cart, "空组合条件购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "空组合条件");
        LogPromotionResultDetails(result);

        // 验证没有规则被应用
        Assert.Empty(result.AppliedPromotions);
        AssertAmountEqual(0.00m, result.TotalDiscount, "空组合条件时不应有优惠");
    }

    #endregion
}