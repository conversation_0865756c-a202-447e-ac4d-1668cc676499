using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules;

/// <summary>
/// 买X送Y规则（如：买2送1）
/// </summary>
public class BuyXGetYRule : PromotionRuleBase
{
    public override string RuleType => "BuyXGetY";

    /// <summary>
    /// 购买商品的条件
    /// </summary>
    public List<BuyCondition> BuyConditions { get; set; } = new();

    /// <summary>
    /// 赠送商品的条件
    /// </summary>
    public List<GiftCondition> GiftConditions { get; set; } = new();

    /// <summary>
    /// 是否赠送相同商品
    /// </summary>
    public bool GiftSameProduct { get; set; } = true;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        // 检查购买条件
        foreach (var condition in BuyConditions)
        {
            var availableQuantity = condition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
            if (availableQuantity < condition.RequiredQuantity)
                return false;
        }

        // 如果赠送相同商品，需要确保有足够的商品可以赠送
        if (GiftSameProduct)
        {
            foreach (var buyCondition in BuyConditions)
            {
                var totalRequired = buyCondition.RequiredQuantity;
                foreach (var giftCondition in GiftConditions)
                {
                    totalRequired += giftCondition.GiftQuantity;
                }

                var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                if (availableQuantity < totalRequired)
                    return false;
            }
        }
        else
        {
            // 检查赠品是否有库存（这里简化处理，实际应该检查库存系统）
            foreach (var giftCondition in GiftConditions)
            {
                var availableQuantity = giftCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                if (availableQuantity < giftCondition.GiftQuantity)
                    return false;
            }
        }

        return true;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = int.MaxValue;

        // 根据购买条件计算最大应用次数
        foreach (var condition in BuyConditions)
        {
            var availableQuantity = condition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
            var maxByCondition = availableQuantity / condition.RequiredQuantity;
            maxApplications = Math.Min(maxApplications, maxByCondition);
        }

        // 如果赠送相同商品，需要考虑赠品数量
        if (GiftSameProduct)
        {
            foreach (var buyCondition in BuyConditions)
            {
                var totalRequired = buyCondition.RequiredQuantity;
                foreach (var giftCondition in GiftConditions)
                {
                    totalRequired += giftCondition.GiftQuantity;
                }

                var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                var maxByGift = availableQuantity / totalRequired;
                maxApplications = Math.Min(maxApplications, maxByGift);
            }
        }

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications == int.MaxValue ? 0 : maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var totalDiscountAmount = 0m;
            var consumedItems = new List<ConsumedItem>();
            var giftItems = new List<GiftItem>();

            for (int app = 0; app < applicationCount; app++)
            {
                // 消耗购买条件中的商品
                foreach (var buyCondition in BuyConditions)
                {
                    var remainingQuantity = buyCondition.RequiredQuantity;

                    foreach (var productId in buyCondition.ProductIds)
                    {
                        if (remainingQuantity <= 0) break;

                        var availableItems = cart.Items
                            .Where(x => x.Product.Id == productId && x.AvailableQuantity > 0)
                            .ToList();

                        foreach (var item in availableItems)
                        {
                            if (remainingQuantity <= 0) break;

                            var consumeQuantity = Math.Min(item.AvailableQuantity, remainingQuantity);

                            // 记录消耗的商品
                            var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == productId);
                            if (existingConsumed != null)
                            {
                                existingConsumed.Quantity += consumeQuantity;
                            }
                            else
                            {
                                consumedItems.Add(new ConsumedItem
                                {
                                    ProductId = productId,
                                    ProductName = item.Product.Name,
                                    Quantity = consumeQuantity,
                                    UnitPrice = item.UnitPrice
                                });
                            }

                            item.Quantity -= consumeQuantity;
                            remainingQuantity -= consumeQuantity;
                        }
                    }
                }

                // 处理赠品 - 只记录赠品信息，不从购物车中扣除
                foreach (var giftCondition in GiftConditions)
                {
                    if (GiftSameProduct)
                    {
                        // 赠送相同商品
                        foreach (var buyCondition in BuyConditions)
                        {
                            var remainingGiftQuantity = giftCondition.GiftQuantity;

                            foreach (var productId in buyCondition.ProductIds)
                            {
                                if (remainingGiftQuantity <= 0) break;

                                // 找到对应的商品信息
                                var productItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
                                if (productItem != null)
                                {
                                    var giftQuantity = Math.Min(remainingGiftQuantity, giftCondition.GiftQuantity);
                                    var giftValue = giftQuantity * productItem.UnitPrice;

                                    totalDiscountAmount += giftValue;

                                    // 记录赠品信息
                                    var existingGift = giftItems.FirstOrDefault(x => x.ProductId == productId);
                                    if (existingGift != null)
                                    {
                                        existingGift.Quantity += giftQuantity;
                                        existingGift.Value += giftValue;
                                    }
                                    else
                                    {
                                        giftItems.Add(new GiftItem
                                        {
                                            ProductId = productId,
                                            ProductName = productItem.Product.Name,
                                            Quantity = giftQuantity,
                                            Value = giftValue
                                        });
                                    }

                                    remainingGiftQuantity -= giftQuantity;
                                }
                            }
                        }
                    }
                    else
                    {
                        // 赠送指定商品
                        var remainingGiftQuantity = giftCondition.GiftQuantity;

                        foreach (var productId in giftCondition.ProductIds)
                        {
                            if (remainingGiftQuantity <= 0) break;

                            // 找到对应的商品信息
                            var productItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
                            if (productItem != null)
                            {
                                var giftQuantity = Math.Min(remainingGiftQuantity, giftCondition.GiftQuantity);
                                var giftValue = giftQuantity * productItem.UnitPrice;

                                totalDiscountAmount += giftValue;

                                // 记录赠品信息
                                var existingGift = giftItems.FirstOrDefault(x => x.ProductId == productId);
                                if (existingGift != null)
                                {
                                    existingGift.Quantity += giftQuantity;
                                    existingGift.Value += giftValue;
                                }
                                else
                                {
                                    giftItems.Add(new GiftItem
                                    {
                                        ProductId = productId,
                                        ProductName = productItem.Product.Name,
                                        Quantity = giftQuantity,
                                        Value = giftValue
                                    });
                                }

                                remainingGiftQuantity -= giftQuantity;
                            }
                        }
                    }
                }
            }

            // 清理数量为0的商品项
            cart.Items.RemoveAll(x => x.Quantity <= 0);

            application.DiscountAmount = totalDiscountAmount;
            application.ConsumedItems = consumedItems;
            application.GiftItems = giftItems;
            application.IsSuccessful = totalDiscountAmount > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用买赠促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = $"应用买赠促销时发生错误: {ex.Message}";
        }

        return application;
    }
}

/// <summary>
/// 购买条件
/// </summary>
public class BuyCondition
{
    /// <summary>
    /// 商品ID列表
    /// </summary>
    public List<string> ProductIds { get; set; } = new();

    /// <summary>
    /// 需要购买的数量
    /// </summary>
    public int RequiredQuantity { get; set; }
}

/// <summary>
/// 赠品条件
/// </summary>
public class GiftCondition
{
    /// <summary>
    /// 赠品商品ID列表
    /// </summary>
    public List<string> ProductIds { get; set; } = new();

    /// <summary>
    /// 赠送数量
    /// </summary>
    public int GiftQuantity { get; set; }
}
