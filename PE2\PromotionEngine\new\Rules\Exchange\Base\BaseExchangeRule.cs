using System.Text.Json.Serialization;
using Microsoft.Extensions.Logging;
using PE2.Models;
using PE2.PromotionEngine.Core;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.new.Core.Base;
using PE2.PromotionEngine.new.Core.Enums;
using PE2.PromotionEngine.new.Core.Models;

namespace PE2.PromotionEngine.new.Rules.Exchange.Base;

/// <summary>
/// 换购促销规则基类 - 新架构版本
/// 核心原则：换购商品必须已在购物车中（POS扫码原则），不能程序添加新商品
/// 集成商品占用管理、条件执行分离和排他性管理功能
/// </summary>
public abstract class BaseExchangeRule : PromotionRuleBase
{
    protected BaseExchangeRule(ILogger? logger = null) : base(logger) { }
    
    /// <summary>
    /// 促销类型
    /// </summary>
    public override PromotionType PromotionType => PromotionType.Exchange;
    
    /// <summary>
    /// 换购策略：梯度换购 vs 全部换购
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public ExchangeStrategy ExchangeStrategy { get; set; } = ExchangeStrategy.ByGradient;
    
    /// <summary>
    /// 换购商品选择策略：客户利益最大化 vs 商家利益最大化
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public BenefitSelectionStrategy ExchangeSelectionStrategy { get; set; } = BenefitSelectionStrategy.CustomerBenefit;
    
    /// <summary>
    /// 是否支持翻倍（重复应用）
    /// </summary>
    public bool SupportMultiplier { get; set; } = true;
    
    /// <summary>
    /// 最大翻倍次数（0表示无限制）
    /// </summary>
    public int MaxMultiplier { get; set; } = 0;
    
    #region 商品占用管理
    
    /// <summary>
    /// 获取换购规则的商品占用请求
    /// </summary>
    public override List<OccupationRequest> GetOccupationRequests(ShoppingCart cart)
    {
        var requests = new List<OccupationRequest>();
        
        // 获取购买条件商品的占用请求
        var buyConditionRequests = GetBuyConditionOccupationRequests(cart);
        requests.AddRange(buyConditionRequests);
        
        // 获取换购商品的占用请求
        var exchangeRequests = GetExchangeOccupationRequests(cart);
        requests.AddRange(exchangeRequests);
        
        return requests;
    }
    
    /// <summary>
    /// 获取购买条件商品的占用请求
    /// </summary>
    protected abstract List<OccupationRequest> GetBuyConditionOccupationRequests(ShoppingCart cart);
    
    /// <summary>
    /// 获取换购商品的占用请求
    /// </summary>
    protected abstract List<OccupationRequest> GetExchangeOccupationRequests(ShoppingCart cart);
    
    #endregion
    
    #region 条件验证
    
    /// <summary>
    /// 验证换购条件
    /// </summary>
    protected override async Task<ValidationResult> ValidateSpecificConditionsAsync(
        ShoppingCart cart, 
        IOccupationSession session, 
        CancellationToken cancellationToken)
    {
        var details = new List<ValidationDetail>();
        
        // 验证购买条件
        var buyConditionResult = await ValidateBuyConditionsAsync(cart, session, cancellationToken)
            .ConfigureAwait(false);
        details.AddRange(buyConditionResult.Details);
        
        if (!buyConditionResult.IsValid)
        {
            return ValidationResult.Failure("购买条件不满足") with { Details = details };
        }
        
        // 验证换购商品可用性
        var exchangeAvailabilityResult = await ValidateExchangeAvailabilityAsync(cart, session, cancellationToken)
            .ConfigureAwait(false);
        details.AddRange(exchangeAvailabilityResult.Details);
        
        if (!exchangeAvailabilityResult.IsValid)
        {
            return ValidationResult.Failure("换购商品不可用") with { Details = details };
        }
        
        // 验证换购逻辑
        var exchangeLogicResult = await ValidateExchangeLogicAsync(cart, session, cancellationToken)
            .ConfigureAwait(false);
        details.AddRange(exchangeLogicResult.Details);
        
        if (!exchangeLogicResult.IsValid)
        {
            return ValidationResult.Failure("换购逻辑验证失败") with { Details = details };
        }
        
        return ValidationResult.Success() with { Details = details };
    }
    
    /// <summary>
    /// 验证购买条件
    /// </summary>
    protected abstract Task<ValidationResult> ValidateBuyConditionsAsync(
        ShoppingCart cart, 
        IOccupationSession session, 
        CancellationToken cancellationToken);
    
    /// <summary>
    /// 验证换购商品可用性
    /// </summary>
    protected abstract Task<ValidationResult> ValidateExchangeAvailabilityAsync(
        ShoppingCart cart, 
        IOccupationSession session, 
        CancellationToken cancellationToken);
    
    /// <summary>
    /// 验证换购逻辑
    /// </summary>
    protected virtual async Task<ValidationResult> ValidateExchangeLogicAsync(
        ShoppingCart cart, 
        IOccupationSession session, 
        CancellationToken cancellationToken)
    {
        // 默认实现：检查换购商品和条件商品是否有重叠
        var buyProductIds = GetBuyConditionProductIds(cart);
        var exchangeProductIds = GetExchangeProductIds(cart);
        
        var overlap = buyProductIds.Intersect(exchangeProductIds).ToList();
        if (overlap.Any())
        {
            _logger?.LogWarning("换购规则存在商品重叠，规则ID: {RuleId}, 重叠商品: {OverlapProducts}", 
                Id, string.Join(", ", overlap));
            
            // 根据策略处理重叠
            if (ExchangeSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit)
            {
                // 客户利益最大化：允许重叠，但需要确保有足够的商品
                return await ValidateOverlapForCustomerBenefitAsync(cart, session, overlap, cancellationToken)
                    .ConfigureAwait(false);
            }
            else
            {
                // 商家利益最大化：严格分离
                return ValidationResult.Failure("换购商品和条件商品不能重叠（商家利益最大化策略）");
            }
        }
        
        return ValidationResult.Success();
    }
    
    /// <summary>
    /// 为客户利益最大化验证重叠商品
    /// </summary>
    protected virtual async Task<ValidationResult> ValidateOverlapForCustomerBenefitAsync(
        ShoppingCart cart, 
        IOccupationSession session, 
        List<string> overlapProductIds,
        CancellationToken cancellationToken)
    {
        // 检查重叠商品是否有足够的数量同时满足购买条件和换购需求
        foreach (var productId in overlapProductIds)
        {
            var availableQuantity = cart.Items
                .Where(x => x.Product.Id == productId)
                .Sum(x => x.Quantity);
            
            var requiredForBuy = GetRequiredQuantityForBuyCondition(cart, productId);
            var requiredForExchange = GetRequiredQuantityForExchange(cart, productId);
            
            if (availableQuantity < requiredForBuy + requiredForExchange)
            {
                return ValidationResult.Failure(
                    $"商品 {productId} 数量不足，需要 {requiredForBuy + requiredForExchange} 件，实际 {availableQuantity} 件");
            }
        }
        
        return ValidationResult.Success();
    }
    
    #endregion
    
    #region 促销执行
    
    /// <summary>
    /// 执行换购促销
    /// </summary>
    protected override async Task<ExecutionResult> ExecuteSpecificPromotionAsync(
        ShoppingCart cart, 
        IOccupationSession session, 
        int applicationCount,
        CancellationToken cancellationToken)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var exchangeItems = new List<GiftItem>();
        var details = new List<ExecutionDetail>();
        
        for (int i = 0; i < applicationCount; i++)
        {
            var stepResult = await ExecuteSingleExchangeAsync(cart, session, i, cancellationToken)
                .ConfigureAwait(false);
            
            if (!stepResult.IsSuccessful)
            {
                details.Add(new ExecutionDetail
                {
                    StepName = $"换购执行第{i + 1}次",
                    IsSuccessful = false,
                    Message = stepResult.ErrorMessage ?? "执行失败"
                });
                break;
            }
            
            totalDiscountAmount += stepResult.DiscountAmount;
            consumedItems.AddRange(stepResult.ConsumedItems);
            exchangeItems.AddRange(stepResult.GiftItems);
            details.AddRange(stepResult.Details);
        }
        
        return ExecutionResult.Success(
            totalDiscountAmount,
            consumedItems,
            exchangeItems,
            applicationCount) with { Details = details };
    }
    
    /// <summary>
    /// 执行单次换购
    /// </summary>
    protected abstract Task<ExecutionResult> ExecuteSingleExchangeAsync(
        ShoppingCart cart, 
        IOccupationSession session, 
        int applicationIndex,
        CancellationToken cancellationToken);
    
    #endregion
    
    #region 辅助方法
    
    /// <summary>
    /// 获取购买条件商品ID列表
    /// </summary>
    protected abstract List<string> GetBuyConditionProductIds(ShoppingCart cart);
    
    /// <summary>
    /// 获取换购商品ID列表
    /// </summary>
    protected abstract List<string> GetExchangeProductIds(ShoppingCart cart);
    
    /// <summary>
    /// 获取购买条件所需的商品数量
    /// </summary>
    protected abstract int GetRequiredQuantityForBuyCondition(ShoppingCart cart, string productId);
    
    /// <summary>
    /// 获取换购所需的商品数量
    /// </summary>
    protected abstract int GetRequiredQuantityForExchange(ShoppingCart cart, string productId);
    
    /// <summary>
    /// 选择换购商品（根据策略）
    /// </summary>
    protected virtual List<string> SelectExchangeProducts(
        ShoppingCart cart, 
        List<string> candidateProductIds, 
        int requiredQuantity)
    {
        if (!candidateProductIds.Any())
            return new List<string>();
        
        // 获取商品价格信息
        var productPrices = candidateProductIds
            .Select(id => new
            {
                ProductId = id,
                Price = cart.Items.FirstOrDefault(x => x.Product.Id == id)?.Product.Price ?? 0
            })
            .Where(p => p.Price > 0)
            .ToList();
        
        // 根据策略排序
        var sortedProducts = ExchangeSelectionStrategy switch
        {
            BenefitSelectionStrategy.CustomerBenefit => productPrices.OrderByDescending(p => p.Price), // 客户利益：换购高价商品
            BenefitSelectionStrategy.MerchantBenefit => productPrices.OrderBy(p => p.Price), // 商家利益：换购低价商品
            _ => productPrices.OrderByDescending(p => p.Price)
        };
        
        var selectedProducts = new List<string>();
        var remainingQuantity = requiredQuantity;
        
        foreach (var (productId, _) in sortedProducts)
        {
            if (remainingQuantity <= 0) break;
            
            var availableQuantity = cart.Items
                .Where(x => x.Product.Id == productId)
                .Sum(x => x.Quantity);
            
            if (availableQuantity > 0)
            {
                selectedProducts.Add(productId);
                remainingQuantity--;
            }
        }
        
        return selectedProducts;
    }
    
    /// <summary>
    /// 计算打折换购的实际支付金额
    /// </summary>
    protected decimal CalculateDiscountExchange(decimal originalPrice, decimal discountRate)
    {
        return originalPrice * discountRate;
    }
    
    /// <summary>
    /// 计算优惠换购的实际支付金额
    /// </summary>
    protected decimal CalculateDiscountAmountExchange(decimal originalPrice, decimal discountAmount)
    {
        return Math.Max(0, originalPrice - discountAmount);
    }
    
    /// <summary>
    /// 创建虚拟购物车（用于模拟计算）
    /// </summary>
    protected ShoppingCart CreateVirtualCart(ShoppingCart originalCart)
    {
        return new ShoppingCart
        {
            Id = $"virtual_{originalCart.Id}_{Guid.NewGuid():N}",
            CustomerId = originalCart.CustomerId,
            MemberId = originalCart.MemberId,
            Items = originalCart.Items.Select(item => new CartItem
            {
                Product = item.Product,
                Quantity = item.Quantity,
                UnitPrice = item.UnitPrice,
                ActualUnitPrice = item.ActualUnitPrice
            }).ToList()
        };
    }
    
    #endregion
}
