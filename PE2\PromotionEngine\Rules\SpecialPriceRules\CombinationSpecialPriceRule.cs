using PE2.Models;
using PE2.PromotionEngine.Models;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Rules.SpecialPriceRules;

/// <summary>
/// 组合特价规则
/// 某A类商品满X件并且某B类商品满X件，特价N元
/// 场景：A、B商品各购买数量大于等于1件时，特价600元
/// </summary>
public class CombinationSpecialPriceRule : BaseSpecialPriceRule
{
    public override string RuleType => "CombinationSpecialPrice";

    /// <summary>
    /// 组合购买条件列表
    /// </summary>
    public List<CombinationSpecialPriceCondition> CombinationConditions { get; set; } = new();

    /// <summary>
    /// 特价金额
    /// </summary>
    public decimal SpecialPrice { get; set; }

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!CombinationConditions.Any())
            return false;

        // 验证组合商品是否在购物车中
        var allProductIds = CombinationConditions.Select(c => c.ProductId).ToList();
        if (!ValidateSpecialPriceProductsInCart(cart, allProductIds))
            return false;

        // 检查组合购买条件
        return CheckCombinationConditions(cart);
    }

    /// <summary>
    /// 检查组合购买条件
    /// </summary>
    private bool CheckCombinationConditions(ShoppingCart cart)
    {
        foreach (var condition in CombinationConditions)
        {
            var availableQuantity = cart.GetAvailableProductQuantity(condition.ProductId);
            var totalAmount = cart.Items
                .Where(x => x.Product.Id == condition.ProductId)
                .Sum(x => x.SubTotal);

            if (condition.RequiredQuantity > 0 && availableQuantity < condition.RequiredQuantity)
                return false;

            if (condition.RequiredAmount > 0 && totalAmount < condition.RequiredAmount)
                return false;
        }

        return true;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = int.MaxValue;

        // 计算每个组合条件的最大应用次数
        foreach (var condition in CombinationConditions)
        {
            var conditionMaxApplications = 0;

            if (condition.RequiredQuantity > 0)
            {
                var availableQuantity = cart.GetAvailableProductQuantity(condition.ProductId);
                conditionMaxApplications = IsRepeatable 
                    ? availableQuantity / condition.RequiredQuantity
                    : (availableQuantity >= condition.RequiredQuantity ? 1 : 0);
            }
            else if (condition.RequiredAmount > 0)
            {
                var totalAmount = cart.Items
                    .Where(x => x.Product.Id == condition.ProductId)
                    .Sum(x => x.SubTotal);
                conditionMaxApplications = IsRepeatable 
                    ? (int)(totalAmount / condition.RequiredAmount)
                    : (totalAmount >= condition.RequiredAmount ? 1 : 0);
            }
            else
            {
                conditionMaxApplications = 1;
            }

            maxApplications = Math.Min(maxApplications, conditionMaxApplications);
        }

        if (maxApplications == int.MaxValue)
            maxApplications = 1;

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyCombinationSpecialPrice(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用组合特价促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用组合特价促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyCombinationSpecialPrice(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var specialPriceRecords = new List<GiftItem>(); // 特价记录

        for (int app = 0; app < applicationCount; app++)
        {
            // 消耗组合条件商品（记录消耗但不修改购物车）
            var combinationConsumed = ConsumeCombinationProducts(cart);
            if (!combinationConsumed.Any()) break;

            // 获取参与组合的所有商品项
            var allProductIds = CombinationConditions.Select(c => c.ProductId).ToList();
            var combinationItems = cart.Items
                .Where(x => allProductIds.Contains(x.Product.Id) && x.Quantity > 0)
                .ToList();

            if (!combinationItems.Any()) break;

            // 根据策略排序商品
            var sortedItems = SortItemsByStrategy(combinationItems);

            var promotion = new AppliedPromotion
            {
                RuleId = Id,
                RuleName = Name,
                PromotionType = RuleType
            };

            // 计算本次应用的特价总金额
            var currentSpecialPrice = SpecialPrice;

            // 按比例分摊特价到所有组合商品
            var strategyDescription = SpecialPriceSelectionStrategy == SpecialPriceSelectionStrategy.CustomerBenefit
                ? "客户利益最大化"
                : "商家利益最大化";

            var conditionDescription = string.Join("、", CombinationConditions.Select(c => 
                c.RequiredQuantity > 0 ? $"{c.ProductId}满{c.RequiredQuantity}件" : $"{c.ProductId}满{c.RequiredAmount:C}"));
            
            var description = $"组合特价：{conditionDescription}特价{currentSpecialPrice:C}（第{app + 1}次应用，{strategyDescription}）";

            // 计算折扣前的总金额
            var totalOriginalAmount = sortedItems.Sum(x => x.SubTotal);
            var totalDiscountForThisApp = totalOriginalAmount - currentSpecialPrice;

            if (totalDiscountForThisApp > 0)
            {
                totalDiscountAmount += totalDiscountForThisApp;

                // 按比例分摊特价
                ApplyProportionalSpecialPrice(sortedItems, currentSpecialPrice, promotion, description);

                // 记录特价
                specialPriceRecords.Add(CreateSpecialPriceRecord(
                    string.Join(",", allProductIds),
                    "组合特价商品",
                    sortedItems.Sum(x => x.Quantity),
                    totalDiscountForThisApp,
                    description
                ));
            }

            // 合并消耗记录
            foreach (var item in combinationConsumed)
            {
                var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == item.ProductId);
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += item.Quantity;
                }
                else
                {
                    consumedItems.Add(item);
                }
            }
        }

        return (totalDiscountAmount, consumedItems, specialPriceRecords);
    }

    /// <summary>
    /// 消耗组合条件商品
    /// </summary>
    private List<ConsumedItem> ConsumeCombinationProducts(ShoppingCart cart)
    {
        var consumedItems = new List<ConsumedItem>();

        foreach (var condition in CombinationConditions)
        {
            var requiredQuantity = condition.RequiredQuantity;
            if (requiredQuantity <= 0) continue;

            var consumed = ConsumeConditionProducts(cart, new List<string> { condition.ProductId }, requiredQuantity);
            consumedItems.AddRange(consumed);
        }

        return consumedItems;
    }
}

/// <summary>
/// 组合特价条件
/// </summary>
public class CombinationSpecialPriceCondition
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 所需数量
    /// </summary>
    public int RequiredQuantity { get; set; }

    /// <summary>
    /// 所需金额
    /// </summary>
    public decimal RequiredAmount { get; set; }
}
