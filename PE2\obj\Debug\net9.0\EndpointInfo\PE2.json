{"openapi": "3.0.1", "info": {"title": "POSPE2 促销引擎 API", "version": "v1"}, "paths": {"/api/Demo/sample-cart": {"get": {"tags": ["Demo"], "responses": {"200": {"description": "OK"}}}}, "/api/Demo/full-demo": {"post": {"tags": ["Demo"], "responses": {"200": {"description": "OK"}}}}, "/api/Demo/complex-cart": {"get": {"tags": ["Demo"], "responses": {"200": {"description": "OK"}}}}, "/health": {"get": {"tags": ["Health"], "operationId": "HealthCheck", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringDateTime<>f__AnonymousType52"}}}}}}}, "/api/Performance/benchmark": {"post": {"tags": ["Performance"], "parameters": [{"name": "iterations", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 100}}], "responses": {"200": {"description": "OK"}}}}, "/api/Performance/stress-test": {"post": {"tags": ["Performance"], "parameters": [{"name": "concurrentRequests", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 50}}, {"name": "duration", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 30}}], "responses": {"200": {"description": "OK"}}}}, "/api/Performance/complexity-analysis": {"post": {"tags": ["Performance"], "responses": {"200": {"description": "OK"}}}}, "/api/Performance/memory-analysis": {"get": {"tags": ["Performance"], "responses": {"200": {"description": "OK"}}}}, "/api/Promotion/calculate": {"post": {"tags": ["Promotion"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShoppingCart"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ShoppingCart"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ShoppingCart"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Promotion/preview": {"post": {"tags": ["Promotion"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShoppingCart"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ShoppingCart"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ShoppingCart"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Promotion/apply/{ruleId}": {"post": {"tags": ["Promotion"], "parameters": [{"name": "ruleId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "applicationCount", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShoppingCart"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ShoppingCart"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ShoppingCart"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Promotion/validate": {"post": {"tags": ["Promotion"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromotionValidationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PromotionValidationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PromotionValidationRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Promotion/statistics": {"get": {"tags": ["Promotion"], "responses": {"200": {"description": "OK"}}}}, "/api/PromotionAnalysis/detailed-analysis": {"post": {"tags": ["PromotionAnalysis"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShoppingCart"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ShoppingCart"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ShoppingCart"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PromotionAnalysis/compare-scenarios": {"post": {"tags": ["PromotionAnalysis"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComparisonRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ComparisonRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ComparisonRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PromotionRule": {"get": {"tags": ["PromotionRule"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["PromotionRule"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromotionRuleBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PromotionRuleBase"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PromotionRuleBase"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PromotionRule/{id}": {"get": {"tags": ["PromotionRule"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["PromotionRule"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromotionRuleBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PromotionRuleBase"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PromotionRuleBase"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["PromotionRule"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/PromotionRule/enabled": {"get": {"tags": ["PromotionRule"], "responses": {"200": {"description": "OK"}}}}, "/api/PromotionRule/valid": {"get": {"tags": ["PromotionRule"], "parameters": [{"name": "checkTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/PromotionRule/{id}/enabled": {"patch": {"tags": ["PromotionRule"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}, "application/*+json": {"schema": {"type": "boolean"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PromotionRule/reload": {"post": {"tags": ["PromotionRule"], "responses": {"200": {"description": "OK"}}}}, "/api/PromotionRule/validate-file": {"get": {"tags": ["PromotionRule"], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"CartItem": {"type": "object", "properties": {"product": {"$ref": "#/components/schemas/Product"}, "quantity": {"type": "integer", "format": "int32"}, "unitPrice": {"type": "number", "format": "double"}, "actualUnitPrice": {"type": "number", "format": "double"}, "subTotal": {"type": "number", "format": "double", "readOnly": true}, "actualSubTotal": {"type": "number", "format": "double", "readOnly": true}, "isGift": {"type": "boolean"}, "isConsumed": {"type": "boolean"}, "appliedPromotionRuleIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "promotionDetails": {"type": "array", "items": {"$ref": "#/components/schemas/ItemPromotionDetail"}, "nullable": true}, "availableQuantity": {"type": "integer", "format": "int32", "readOnly": true}, "totalDiscountAmount": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}, "ComparisonRequest": {"type": "object", "properties": {"cart": {"$ref": "#/components/schemas/ShoppingCart"}, "specificRuleIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ItemPromotionDetail": {"type": "object", "properties": {"ruleId": {"type": "string", "nullable": true}, "ruleName": {"type": "string", "nullable": true}, "promotionType": {"type": "string", "nullable": true}, "discountAmount": {"type": "number", "format": "double"}, "description": {"type": "string", "nullable": true}, "isGiftRelated": {"type": "boolean"}}, "additionalProperties": false}, "Product": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double"}, "category": {"type": "string", "nullable": true}, "barcode": {"type": "string", "nullable": true}, "brand": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "PromotionRuleBase": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "ruleType": {"type": "string", "nullable": true, "readOnly": true}, "priority": {"type": "integer", "format": "int32"}, "isEnabled": {"type": "boolean"}, "startTime": {"type": "string", "format": "date-time", "nullable": true}, "endTime": {"type": "string", "format": "date-time", "nullable": true}, "isRepeatable": {"type": "boolean"}, "maxApplications": {"type": "integer", "format": "int32"}, "applicableCustomerTypes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "exclusiveRuleIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "PromotionValidationRequest": {"type": "object", "properties": {"cart": {"$ref": "#/components/schemas/ShoppingCart"}, "ruleIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ShoppingCart": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "customerId": {"type": "string", "nullable": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/CartItem"}, "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "totalQuantity": {"type": "integer", "format": "int32", "readOnly": true}, "totalAmount": {"type": "number", "format": "double", "readOnly": true}, "actualTotalAmount": {"type": "number", "format": "double", "readOnly": true}, "totalDiscountAmount": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}, "StringDateTime<>f__AnonymousType52": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}}}}