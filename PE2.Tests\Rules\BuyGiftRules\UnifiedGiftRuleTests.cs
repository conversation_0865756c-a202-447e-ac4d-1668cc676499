using PE2.PromotionEngine.Rules;
using PE2.PromotionEngine.Rules.BuyGiftRules;
using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.BuyGiftRules;

/// <summary>
/// 统一买赠规则测试类
/// 测试 UnifiedGiftRule 的各种业务场景和边界条件
/// </summary>
public class UnifiedGiftRuleTests(ITestOutputHelper output) : TestBase(output)
{
    #region 基础统一买赠功能测试

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "High")]
    public void Apply_ValidUnifiedGiftScenario_ShouldApplyCorrectly()
    {
        // Arrange - 买2件A送1件B
        var rule = TestDataGenerator.CreateUnifiedGiftRule_Buy2A_Get1B();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            (TestDataGenerator.CreateProductA(), 2), // 满足买条件：2件A
            (TestDataGenerator.CreateProductB(), 1)  // 满足赠条件：有1件B可赠
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "应用前购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "买2件A送1件B");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证赠品B的价格被调整为0
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        //AssertAmountEqual(0m, productBItem.ActualUnitPrice, "赠品B的实际单价应为0");

        // 验证总优惠金额等于赠品原价
        var expectedDiscount = TestDataGenerator.CreateProductB().Price; // 15元
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应等于赠品原价");
    }

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "High")]
    public void Apply_InsufficientBuyQuantity_ShouldNotApply()
    {
        // Arrange - 买条件不满足：只有1件A，需要2件
        var rule = TestDataGenerator.CreateUnifiedGiftRule_Buy2A_Get1B();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_002",
            (TestDataGenerator.CreateProductA(), 1), // 不满足买条件：只有1件A
            (TestDataGenerator.CreateProductB(), 1)  // 有赠品B
        );

        LogCartDetails(cart, "买条件不满足的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "买条件不满足");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "买条件不满足时应无优惠");
    }

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "High")]
    public void Apply_NoGiftProductInCart_ShouldNotApply()
    {
        // Arrange - 赠条件不满足：购物车中没有赠品B
        var rule = TestDataGenerator.CreateUnifiedGiftRule_Buy2A_Get1B();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_003",
            (TestDataGenerator.CreateProductA(), 2)  // 满足买条件：2件A
                                                     // 缺少赠品B
        );

        LogCartDetails(cart, "无赠品的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "无赠品场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "无赠品时应无优惠");
    }

    #endregion

    #region 多次应用测试

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Medium")]
    public void Apply_MultipleApplications_ShouldApplyCorrectly()
    {
        // Arrange - 可以应用多次：4件A + 2件B，可以应用2次买2送1
        var rule = TestDataGenerator.CreateUnifiedGiftRule_Buy2A_Get1B();
        rule.IsRepeatable = true; // 允许重复应用
        rule.MaxApplications = 5;

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_004",
            (TestDataGenerator.CreateProductA(), 4), // 可以买2送1两次
            (TestDataGenerator.CreateProductB(), 2)  // 有2件B可以作为赠品
        );

        LogCartDetails(cart, "多次应用场景购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "多次应用场景");
        LogPromotionResultDetails(result);

        // 验证规则被应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证所有B都变成赠品（价格为0）
        var productBItems = result.AppliedPromotions[0].GiftItems;
        //Assert.Single(productBItems);
        AssertAmountEqual(2, productBItems.Count, "所有B都应该是赠品");

        // 验证总优惠 = 2件B的原价
        var expectedDiscount = TestDataGenerator.CreateProductB().Price * 2; // 15 * 2 = 30元
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应等于2件B的原价");
    }

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Medium")]
    public void Apply_PartialGiftAvailable_ShouldApplyPartially()
    {
        // Arrange - 部分赠品可用：6件A（可买3次），但只有1件B
        var rule = TestDataGenerator.CreateUnifiedGiftRule_Buy2A_Get1B();
        rule.IsRepeatable = true;

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_005",
            (TestDataGenerator.CreateProductA(), 6), // 理论上可以买3次
            (TestDataGenerator.CreateProductB(), 1)  // 但只有1件B
        );

        LogCartDetails(cart, "部分赠品可用购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "部分赠品可用");
        LogPromotionResultDetails(result);

        // 验证规则被应用（但只能应用1次）
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证只有1件B变成赠品
        var productBItem = result.AppliedPromotions[0].GiftItems;
        AssertAmountEqual(1, productBItem.Count, "1件B应该是赠品");

        // 验证总优惠 = 1件B的原价
        var expectedDiscount = TestDataGenerator.CreateProductB().Price; // 15元
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应等于1件B的原价");
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Low")]
    public void Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateUnifiedGiftRule_Buy2A_Get1B();
        var cart = TestDataGenerator.CreateEmptyCart();

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "空购物车");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertAmountEqual(0m, result.TotalDiscount, "空购物车应无优惠");
    }

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Low")]
    public void Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateUnifiedGiftRule_Buy2A_Get1B();
        rule.IsEnabled = false; // 禁用规则

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_006",
            (TestDataGenerator.CreateProductA(), 2),
            (TestDataGenerator.CreateProductB(), 1)
        );

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "禁用规则");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");
        AssertAmountEqual(0m, result.TotalDiscount, "禁用规则应无优惠");
    }

    #endregion

    #region 客户利益最大化测试

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Medium")]
    public void Apply_CustomerBenefitStrategy_ShouldSelectHighestValueGifts()
    {
        // Arrange - 客户利益最大化：选择价值最高的赠品
        var rule = new UnifiedGiftRule
        {
            Id = "CUSTOMER_BENEFIT_UNIFIED_GIFT",
            Name = "买2件A送1件赠品（客户利益最大化）",
            Priority = 100,
            IsEnabled = true,
            ApplicableProductIds = ["A"],
            GiftProductIds = ["B", "C"], // B价格30元，C价格20元
            MinQuantity = 2,
            GiftQuantity = 1,
            GiftSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit // 客户利益最大化
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_007",
            (TestDataGenerator.CreateProductA(), 2), // 满足买条件
            (TestDataGenerator.CreateProductB(), 1), // B价格15元
            (TestDataGenerator.CreateProductC(), 1)  // C价格20元，应该被选为赠品
        );

        LogCartDetails(cart, "客户利益最大化购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "客户利益最大化");
        LogPromotionResultDetails(result);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证选择了价值更高的C作为赠品
        var productCItem = result.ProcessedCart.Items.First(i => i.Product.Id == "C");
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");

        Assert.True(result.AppliedPromotions.FirstOrDefault().GiftItems.All(x => x.ProductId == "B"), "B应该被选为赠品（价值更高）");
        //Assert.True(productBItem.ActualUnitPrice > 0, "B不应该被选为赠品");

        // 验证总优惠等于C的原价
        var expectedDiscount = TestDataGenerator.CreateProductB().Price; // 30元
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应等于B的原价");
    }

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Medium")]
    public void Apply_CustomerBenefitStrategy_MultipleGifts_ShouldSelectOptimal()
    {
        // Arrange - 客户利益最大化：多个赠品时选择最优组合
        var rule = new UnifiedGiftRule
        {
            Id = "CUSTOMER_BENEFIT_MULTIPLE_GIFTS",
            Name = "买2件A送2件赠品（客户利益最大化）",
            Priority = 100,
            IsEnabled = true,
            ApplicableProductIds = ["A", "B"],
            GiftProductIds = ["B", "C"], // B价格15元，C价格20元
            MinQuantity = 2,
            GiftQuantity = 2, // 送2件
            GiftSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_008",
            (TestDataGenerator.CreateProductA(), 1), // 满足买条件
            (TestDataGenerator.CreateProductB(), 2), // 2件B
            (TestDataGenerator.CreateProductC(), 2)  // 2件C
        );

        LogCartDetails(cart, "客户利益最大化多赠品购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "客户利益最大化多赠品");
        LogPromotionResultDetails(result);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证选择了价值最高的组合（2件C）
        var productCItems = TestDataGenerator.CreateProductC().Price;
        var productBItems = TestDataGenerator.CreateProductB().Price;

        //AssertAmountEqual(0m, productCItems[0].ActualUnitPrice, "C应该被选为赠品");
        Assert.True(result.AppliedPromotions.First().GiftItems.Count == 2 &&
            result.AppliedPromotions.First().GiftItems.Any(x => x.ProductId == "B") &&
            result.AppliedPromotions.First().GiftItems.Any(x => x.ProductId == "C"), "B + C被选为赠品");

        // 验证总优惠等于2件C的原价
        var expectedDiscount = productCItems + productBItems;
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应等于B+C原价");
    }

    #endregion

    #region 商户利益最大化测试

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Medium")]
    public void Apply_MerchantBenefitStrategy_ShouldSelectLowestValueGifts()
    {
        // Arrange - 商户利益最大化：选择价值最低的赠品
        var rule = new UnifiedGiftRule
        {
            Id = "MERCHANT_BENEFIT_UNIFIED_GIFT",
            Name = "买2件A送1件赠品（商户利益最大化）",
            Priority = 100,
            IsEnabled = true,
            ApplicableProductIds = ["A"],
            GiftProductIds = ["B", "C"], // B价格15元，C价格20元
            MinQuantity = 2,
            GiftQuantity = 1,
            GiftSelectionStrategy = BenefitSelectionStrategy.MerchantBenefit // 商户利益最大化
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_009",
            (TestDataGenerator.CreateProductA(), 2), // 满足买条件
            (TestDataGenerator.CreateProductB(), 1), // B价格30元，应该被选为赠品
            (TestDataGenerator.CreateProductC(), 1)  // C价格20元
        );

        LogCartDetails(cart, "商户利益最大化购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "商户利益最大化");
        LogPromotionResultDetails(result);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证选择了价值更低的B作为赠品
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        var productCItem = result.ProcessedCart.Items.First(i => i.Product.Id == "C");

        Assert.True(result.AppliedPromotions[0].GiftItems.All(x => x.ProductId == "C"), "B应该被选为赠品（价值更低）");
        Assert.True(productBItem.ActualUnitPrice > 0, "B不应该被选为赠品");

        // 验证总优惠等于B的原价
        var expectedDiscount = TestDataGenerator.CreateProductC().Price;
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应等于C的原价");
    }

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Medium")]
    public void Apply_MerchantBenefitStrategy_MultipleGifts_ShouldSelectOptimal()
    {
        // Arrange - 商户利益最大化：多个赠品时选择成本最低组合
        var rule = new UnifiedGiftRule
        {
            Id = "MERCHANT_BENEFIT_MULTIPLE_GIFTS",
            Name = "买2件A送2件赠品（商户利益最大化）",
            Priority = 100,
            IsEnabled = true,
            ApplicableProductIds = ["A"],
            GiftProductIds = ["B", "C"], // B价格30元，C价格20元
            MinQuantity = 2,
            GiftQuantity = 2, // 送2件
            GiftSelectionStrategy = BenefitSelectionStrategy.MerchantBenefit
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_010",
            (TestDataGenerator.CreateProductA(), 2), // 满足买条件
            (TestDataGenerator.CreateProductB(), 2), // 2件B
            (TestDataGenerator.CreateProductC(), 2)  // 2件C
        );

        LogCartDetails(cart, "商户利益最大化多赠品购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "商户利益最大化多赠品");
        LogPromotionResultDetails(result);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证选择了成本最低的组合（2件C）
        var productBItems = result.ProcessedCart.Items.Where(i => i.Product.Id == "B").ToList();
        var productCItems = result.ProcessedCart.Items.Where(i => i.Product.Id == "C").ToList();

        Assert.Single(productBItems);
        Assert.Single(productCItems);

        Assert.True(result.AppliedPromotions[0].GiftItems.All(x => x.ProductId == "C"), "C应该被选为赠品");
        //Assert.True(productCItems[0].ActualUnitPrice > 0, "B不应该全部被选为赠品");

        // 验证总优惠等于2件B的原价
        var expectedDiscount = TestDataGenerator.CreateProductC().Price * 2;
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应等于2件C的原价");
    }

    #endregion

    #region 按金额买赠测试

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Medium")]
    public void Apply_AmountBasedGift_ShouldApplyCorrectly()
    {
        // Arrange - 按金额买赠：满100元送1件B
        var rule = new UnifiedGiftRule
        {
            Id = "AMOUNT_BASED_UNIFIED_GIFT",
            Name = "满100元送1件B",
            Priority = 100,
            IsEnabled = true,
            ApplicableProductIds = ["A"],
            GiftProductIds = ["B"],
            MinAmount = 100m,
            GiftQuantity = 1,
            IsByAmount = true // 按金额计算
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_011",
            (TestDataGenerator.CreateProductA(), 10), // 10件A，总价100元
            (TestDataGenerator.CreateProductB(), 1)   // 1件B作为赠品
        );

        LogCartDetails(cart, "按金额买赠购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "按金额买赠");
        LogPromotionResultDetails(result);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证B变成赠品
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        AssertAmountEqual(0m, productBItem.ActualUnitPrice, "B应该是赠品");

        // 验证总优惠等于B的原价
        var expectedDiscount = TestDataGenerator.CreateProductB().Price; // 15元
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应等于B的原价");
    }

    #endregion

    #region 性能测试

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Low")]
    public void Apply_LargeCart_ShouldPerformWell()
    {
        // Arrange
        var rule = TestDataGenerator.CreateUnifiedGiftRule_Buy2A_Get1B();
        var cart = TestDataGenerator.PerformanceTestData.CreateLargeCart(100);

        TestPromotionRuleService.Rules = [rule];
        // Act & Assert - 应该在合理时间内完成
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;
        stopwatch.Stop();

        AssertPromotionResult(result, "大购物车性能测试");
        Assert.True(stopwatch.ElapsedMilliseconds < 1000, "大购物车处理应在1秒内完成");
        Output.WriteLine($"大购物车处理耗时: {stopwatch.ElapsedMilliseconds}ms");
    }

    #endregion

    #region 规则配置验证测试

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Low")]
    public void Apply_InvalidRuleConfiguration_ShouldHandleGracefully()
    {
        // Arrange - 无效的规则配置：没有购买条件商品
        var rule = new UnifiedGiftRule
        {
            Id = "INVALID_UNIFIED_GIFT",
            Name = "无效统一买赠",
            Priority = 100,
            IsEnabled = true,
            ApplicableProductIds = [], // 空的购买条件
            GiftProductIds = ["B"],
            MinQuantity = 2,
            GiftQuantity = 1
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_012",
            (TestDataGenerator.CreateProductA(), 2),
            (TestDataGenerator.CreateProductB(), 1)
        );

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert - 应该优雅地处理无效配置
        AssertPromotionResult(result, "无效规则配置");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertAmountEqual(0m, result.TotalDiscount, "无效配置应无优惠");
    }

    #endregion
}