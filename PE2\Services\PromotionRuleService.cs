using PE2.PromotionEngine.Rules;
using System.Text.Json;

namespace PE2.Services;

/// <summary>
/// 促销规则管理服务
/// </summary>
public class PromotionRuleService : IPromotionRuleService
{
    private readonly string _rulesFilePath;
    private readonly JsonSerializerOptions _jsonOptions;
    private List<PromotionRuleBase> _cachedRules;
    private DateTime _lastLoadTime;
    private readonly object _lockObject = new object();

    public PromotionRuleService(IConfiguration configuration)
    {
        _rulesFilePath = configuration.GetValue<string>("PromotionRules:FilePath") ?? "Configuration/promotion-rules.json";
        _cachedRules = new List<PromotionRuleBase>();
        _lastLoadTime = DateTime.MinValue;

        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true,
            PropertyNameCaseInsensitive = true
        };
    }

    /// <summary>
    /// 获取所有促销规则
    /// </summary>
    public async Task<List<PromotionRuleBase>> GetAllRulesAsync()
    {
        await EnsureRulesLoadedAsync();
        lock (_lockObject)
        {
            return new List<PromotionRuleBase>(_cachedRules);
        }
    }

    /// <summary>
    /// 根据ID获取促销规则
    /// </summary>
    public async Task<PromotionRuleBase?> GetRuleByIdAsync(string ruleId)
    {
        var rules = await GetAllRulesAsync();
        return rules.FirstOrDefault(r => r.Id == ruleId);
    }

    /// <summary>
    /// 获取启用的促销规则
    /// </summary>
    public async Task<List<PromotionRuleBase>> GetEnabledRulesAsync()
    {
        var rules = await GetAllRulesAsync();
        return rules.Where(r => r.IsEnabled).ToList();
    }

    /// <summary>
    /// 获取有效期内的促销规则
    /// </summary>
    public async Task<List<PromotionRuleBase>> GetValidRulesAsync(DateTime? checkTime = null)
    {
        var time = checkTime ?? DateTime.Now;
        var rules = await GetEnabledRulesAsync();
        return rules.Where(r => r.IsInValidPeriod(time)).ToList();
    }

    /// <summary>
    /// 添加促销规则
    /// </summary>
    public async Task<bool> AddRuleAsync(PromotionRuleBase rule)
    {
        try
        {
            var rules = await GetAllRulesAsync();

            // 检查ID是否已存在
            if (rules.Any(r => r.Id == rule.Id))
            {
                throw new InvalidOperationException($"规则ID '{rule.Id}' 已存在");
            }

            rules.Add(rule);
            await SaveRulesAsync(rules);

            lock (_lockObject)
            {
                _cachedRules = rules;
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 更新促销规则
    /// </summary>
    public async Task<bool> UpdateRuleAsync(PromotionRuleBase rule)
    {
        try
        {
            var rules = await GetAllRulesAsync();
            var existingRuleIndex = rules.FindIndex(r => r.Id == rule.Id);

            if (existingRuleIndex == -1)
            {
                throw new InvalidOperationException($"规则ID '{rule.Id}' 不存在");
            }

            rules[existingRuleIndex] = rule;
            await SaveRulesAsync(rules);

            lock (_lockObject)
            {
                _cachedRules = rules;
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 删除促销规则
    /// </summary>
    public async Task<bool> DeleteRuleAsync(string ruleId)
    {
        try
        {
            var rules = await GetAllRulesAsync();
            var ruleToRemove = rules.FirstOrDefault(r => r.Id == ruleId);

            if (ruleToRemove == null)
            {
                return false;
            }

            rules.Remove(ruleToRemove);
            await SaveRulesAsync(rules);

            lock (_lockObject)
            {
                _cachedRules = rules;
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 启用/禁用促销规则
    /// </summary>
    public async Task<bool> SetRuleEnabledAsync(string ruleId, bool enabled)
    {
        try
        {
            var rules = await GetAllRulesAsync();
            var rule = rules.FirstOrDefault(r => r.Id == ruleId);

            if (rule == null)
            {
                return false;
            }

            rule.IsEnabled = enabled;
            await SaveRulesAsync(rules);

            lock (_lockObject)
            {
                _cachedRules = rules;
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 重新加载规则文件
    /// </summary>
    public async Task ReloadRulesAsync()
    {
        lock (_lockObject)
        {
            _lastLoadTime = DateTime.MinValue;
        }
        await EnsureRulesLoadedAsync();
    }

    /// <summary>
    /// 验证规则文件格式
    /// </summary>
    public async Task<(bool IsValid, string ErrorMessage)> ValidateRulesFileAsync()
    {
        try
        {
            if (!File.Exists(_rulesFilePath))
            {
                return (false, "规则文件不存在");
            }

            var jsonContent = await File.ReadAllTextAsync(_rulesFilePath);
            var rules = JsonSerializer.Deserialize<List<PromotionRuleBase>>(jsonContent, _jsonOptions);

            if (rules == null)
            {
                return (false, "规则文件格式错误：无法解析为规则列表");
            }

            // 检查规则ID唯一性
            var duplicateIds = rules.GroupBy(r => r.Id)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key)
                .ToList();

            if (duplicateIds.Any())
            {
                return (false, $"发现重复的规则ID: {string.Join(", ", duplicateIds)}");
            }

            return (true, "规则文件格式正确");
        }
        catch (Exception ex)
        {
            return (false, $"验证规则文件时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 确保规则已加载
    /// </summary>
    private async Task EnsureRulesLoadedAsync()
    {
        var fileInfo = new FileInfo(_rulesFilePath);

        lock (_lockObject)
        {
            if (_cachedRules.Any() && fileInfo.Exists && fileInfo.LastWriteTime <= _lastLoadTime)
            {
                return; // 缓存仍然有效
            }
        }

        await LoadRulesFromFileAsync();
    }

    /// <summary>
    /// 从文件加载规则
    /// </summary>
    private async Task LoadRulesFromFileAsync()
    {
        try
        {
            if (!File.Exists(_rulesFilePath))
            {
                // 如果文件不存在，创建一个空的规则文件
                await CreateDefaultRulesFileAsync();
            }

            var jsonContent = await File.ReadAllTextAsync(_rulesFilePath);
            var rules = JsonSerializer.Deserialize<List<PromotionRuleBase>>(jsonContent, _jsonOptions) ?? new List<PromotionRuleBase>();

            lock (_lockObject)
            {
                _cachedRules = rules;
                _lastLoadTime = DateTime.Now;
            }
        }
        catch (Exception ex)
        {
            // 如果加载失败，使用空列表
            lock (_lockObject)
            {
                _cachedRules = new List<PromotionRuleBase>();
                _lastLoadTime = DateTime.Now;
            }

            // 这里可以记录日志
            Console.WriteLine($"加载促销规则文件失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 保存规则到文件
    /// </summary>
    private async Task SaveRulesAsync(List<PromotionRuleBase> rules)
    {
        var directory = Path.GetDirectoryName(_rulesFilePath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }

        var jsonContent = JsonSerializer.Serialize(rules, _jsonOptions);
        await File.WriteAllTextAsync(_rulesFilePath, jsonContent);

        lock (_lockObject)
        {
            _lastLoadTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 创建默认规则文件
    /// </summary>
    private async Task CreateDefaultRulesFileAsync()
    {
        var defaultRules = new List<PromotionRuleBase>();
        await SaveRulesAsync(defaultRules);
    }
}
