<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组合条件添加按钮测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
</head>
<body>
    <div id="app" style="padding: 20px;">
        <h1>组合条件添加按钮测试</h1>
        
        <el-button @click="testCombinationConditions" type="primary">
            测试 CombinationBuyFreeRule 组合条件
        </el-button>
        
        <div v-if="showForm" style="margin-top: 20px; border: 1px solid #ccc; padding: 20px;">
            <h3>组合条件字段 (combinationConditions):</h3>
            <p>当前值类型: {{ Array.isArray(formData.combinationConditions) ? 'Array' : typeof formData.combinationConditions }}</p>
            <p>当前值: {{ JSON.stringify(formData.combinationConditions) }}</p>
            
            <div style="border: 2px solid blue; padding: 10px; margin: 10px 0;">
                <complex-array-renderer
                    v-if="combinationField"
                    :field-name="'combinationConditions'"
                    :field-config="combinationField"
                    :model-value="formData.combinationConditions || []"
                    :disabled="false"
                    @update:model-value="value => formData.combinationConditions = value"
                />
                <div v-else style="color: red;">
                    combinationConditions 字段未找到
                </div>
            </div>
            
            <div style="margin-top: 20px;">
                <h4>当前表单数据:</h4>
                <pre style="background: #f5f5f5; padding: 10px;">{{ JSON.stringify(formData, null, 2) }}</pre>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="js/components/ProductSelector.js"></script>
    <script src="js/components/ComplexArrayRenderer.js"></script>

    <script>
        const { createApp, ref } = Vue;

        createApp({
            components: {
                ComplexArrayRenderer,
                ProductSelector
            },
            setup() {
                const showForm = ref(false);
                const formData = ref({});
                const combinationField = ref(null);

                const testCombinationConditions = async () => {
                    try {
                        // 获取 CombinationBuyFreeRule 的元数据
                        const response = await fetch('/api/promotion/metadata/types/CombinationBuyFreeRule');
                        const metadata = await response.json();
                        
                        // 找到 combinationConditions 字段
                        combinationField.value = metadata.fields.find(f => f.name === 'combinationConditions');
                        
                        // 初始化表单数据
                        formData.value = {
                            combinationConditions: [] // 显式设置为空数组
                        };
                        
                        showForm.value = true;
                        
                        ElMessage.success('元数据加载成功，请检查是否显示"添加"按钮');
                    } catch (error) {
                        ElMessage.error('加载失败: ' + error.message);
                    }
                };

                return {
                    showForm,
                    formData,
                    combinationField,
                    testCombinationConditions
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
