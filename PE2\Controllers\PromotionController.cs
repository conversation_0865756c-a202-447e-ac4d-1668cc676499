using Microsoft.AspNetCore.Mvc;
using PE2.Models;
using PE2.Services;

namespace PE2.Controllers;

/// <summary>
/// 促销控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class PromotionController : ControllerBase
{
    private readonly PromotionEngineService _promotionEngineService;
    private readonly PromotionRuleService _ruleService;
    private readonly ILogger<PromotionController> _logger;

    public PromotionController(
        PromotionEngineService promotionEngineService,
        PromotionRuleService ruleService,
        ILogger<PromotionController> logger)
    {
        _promotionEngineService = promotionEngineService;
        _ruleService = ruleService;
        _logger = logger;
    }

    /// <summary>
    /// 计算购物车的最优促销
    /// </summary>
    [HttpPost("calculate")]
    public async Task<IActionResult> CalculateOptimalPromotions([FromBody] ShoppingCart cart)
    {
        try
        {
            if (cart == null || !cart.Items.Any())
            {
                return BadRequest("购物车不能为空");
            }

            var _cart = new ShoppingCart
            {
                Id = cart.Id,
                CustomerId = cart.CustomerId
            };
            
            foreach (var item in cart.Items)
            {
                _cart.AddItem(new Product { Id = item.Product.Id, Name = item.Product.Name, Price = item.Product.Price, Category = item.Product.Category }, item.Quantity);
            }

            var result = await _promotionEngineService.CalculateOptimalPromotionsAsync(_cart);
            return Ok(new
            {
                result.AppliedPromotions,
                result.ProcessedCart,
                result.IgnoredPromotions,
                result.OriginalCart
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "计算最优促销时发生错误");
            return StatusCode(500, "计算促销时发生内部错误");
        }
    }

    /// <summary>
    /// 获取购物车的促销预览
    /// </summary>
    [HttpPost("preview")]
    public async Task<IActionResult> GetPromotionPreviews([FromBody] ShoppingCart cart)
    {
        try
        {
            if (cart == null || !cart.Items.Any())
            {
                return BadRequest("购物车不能为空");
            }

            var previews = await _promotionEngineService.GetPromotionPreviewsAsync(cart);
            return Ok(previews);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取促销预览时发生错误");
            return StatusCode(500, "获取促销预览时发生内部错误");
        }
    }

    /// <summary>
    /// 应用指定的促销规则
    /// </summary>
    [HttpPost("apply/{ruleId}")]
    public async Task<IActionResult> ApplySpecificPromotion(
        string ruleId,
        [FromBody] ShoppingCart cart,
        [FromQuery] int applicationCount = 1)
    {
        try
        {
            if (cart == null || !cart.Items.Any())
            {
                return BadRequest("购物车不能为空");
            }

            if (string.IsNullOrEmpty(ruleId))
            {
                return BadRequest("促销规则ID不能为空");
            }

            var result = await _promotionEngineService.ApplySpecificPromotionAsync(cart, ruleId, applicationCount);

            if (!result.IsSuccessful)
            {
                return BadRequest(result.ErrorMessage);
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "应用促销规则 {RuleId} 时发生错误", ruleId);
            return StatusCode(500, "应用促销时发生内部错误");
        }
    }

    /// <summary>
    /// 验证促销组合
    /// </summary>
    [HttpPost("validate")]
    public async Task<IActionResult> ValidatePromotionCombination([FromBody] PromotionValidationRequest request)
    {
        try
        {
            if (request?.Cart == null || !request.Cart.Items.Any())
            {
                return BadRequest("购物车不能为空");
            }

            if (request.RuleIds == null || !request.RuleIds.Any())
            {
                return BadRequest("促销规则ID列表不能为空");
            }

            var (isValid, errorMessage) = await _promotionEngineService.ValidatePromotionCombinationAsync(
                request.Cart, request.RuleIds);

            return Ok(new { IsValid = isValid, ErrorMessage = errorMessage });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证促销组合时发生错误");
            return StatusCode(500, "验证促销组合时发生内部错误");
        }
    }

    /// <summary>
    /// 获取促销统计信息
    /// </summary>
    [HttpGet("statistics")]
    public async Task<IActionResult> GetPromotionStatistics()
    {
        try
        {
            var statistics = await _promotionEngineService.GetPromotionStatisticsAsync();
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取促销统计信息时发生错误");
            return StatusCode(500, "获取促销统计信息时发生内部错误");
        }
    }
}

/// <summary>
/// 促销验证请求
/// </summary>
public class PromotionValidationRequest
{
    /// <summary>
    /// 购物车
    /// </summary>
    public ShoppingCart Cart { get; set; } = new();

    /// <summary>
    /// 促销规则ID列表
    /// </summary>
    public List<string> RuleIds { get; set; } = new();
}
