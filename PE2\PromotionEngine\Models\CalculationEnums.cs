namespace PE2.PromotionEngine.Models;

/// <summary>
/// 计算状态枚举
/// </summary>
public enum CalculationState
{
    /// <summary>
    /// 未开始
    /// </summary>
    NotStarted,

    /// <summary>
    /// 运行中
    /// </summary>
    Running,

    /// <summary>
    /// 已完成
    /// </summary>
    Completed,

    /// <summary>
    /// 已失败
    /// </summary>
    Failed,

    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled,

    /// <summary>
    /// 未找到
    /// </summary>
    NotFound
}

/// <summary>
/// 计算模式枚举
/// </summary>
public enum CalculationMode
{
    /// <summary>
    /// 自动模式 - 系统自动选择最优促销组合
    /// </summary>
    Automatic,

    /// <summary>
    /// 手动模式 - 用户手动选择促销
    /// </summary>
    Manual,

    /// <summary>
    /// 混合模式 - 部分自动部分手动
    /// </summary>
    Hybrid
}

/// <summary>
/// 优化目标枚举
/// </summary>
public enum OptimizationTarget
{
    /// <summary>
    /// 最大折扣 - 为客户争取最大优惠
    /// </summary>
    MaxDiscount,

    /// <summary>
    /// 最大利润 - 为商家争取最大利润
    /// </summary>
    MaxProfit,

    /// <summary>
    /// 平衡模式 - 平衡客户和商家利益
    /// </summary>
    Balanced,

    /// <summary>
    /// 最快计算 - 优先计算速度
    /// </summary>
    FastCalculation
}
