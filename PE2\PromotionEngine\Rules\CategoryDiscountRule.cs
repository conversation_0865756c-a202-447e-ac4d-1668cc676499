using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules;

/// <summary>
/// 分类折扣规则（如：服装类商品满3件8折）
/// </summary>
public class CategoryDiscountRule : PromotionRuleBase
{
    public override string RuleType => "CategoryDiscount";

    /// <summary>
    /// 适用的商品分类列表
    /// </summary>
    public List<string> ApplicableCategories { get; set; } = new();

    /// <summary>
    /// 最小购买数量
    /// </summary>
    public int MinQuantity { get; set; } = 1;

    /// <summary>
    /// 折扣百分比（0.8表示8折）
    /// </summary>
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// 最大优惠数量（0表示无限制）
    /// </summary>
    public int MaxDiscountQuantity { get; set; } = 0;

    /// <summary>
    /// 是否按分类分别计算（true：每个分类独立计算，false：所有分类合并计算）
    /// </summary>
    public bool CalculateByCategory { get; set; } = false;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (ApplicableCategories.Count == 0)
            return false;

        if (CalculateByCategory)
        {
            // 每个分类独立检查
            foreach (var category in ApplicableCategories)
            {
                var categoryQuantity = cart.GetAvailableCategoryQuantity(category);
                if (categoryQuantity >= MinQuantity)
                    return true;
            }
            return false;
        }
        else
        {
            // 所有分类合并计算
            var totalQuantity = ApplicableCategories
                .Sum(category => cart.GetAvailableCategoryQuantity(category));
            return totalQuantity >= MinQuantity;
        }
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = 0;

        if (CalculateByCategory)
        {
            // 每个分类独立计算
            foreach (var category in ApplicableCategories)
            {
                var categoryQuantity = cart.GetAvailableCategoryQuantity(category);
                if (categoryQuantity >= MinQuantity)
                {
                    var categoryMaxApplications = categoryQuantity / MinQuantity;
                    maxApplications += categoryMaxApplications;
                }
            }
        }
        else
        {
            // 所有分类合并计算
            var totalQuantity = ApplicableCategories
                .Sum(category => cart.GetAvailableCategoryQuantity(category));
            maxApplications = totalQuantity / MinQuantity;
        }

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var totalDiscountAmount = 0m;
            var totalConsumedQuantity = 0;
            var consumedItems = new List<ConsumedItem>();

            if (CalculateByCategory)
            {
                // 按分类分别处理
                foreach (var category in ApplicableCategories)
                {
                    var categoryApplications = applicationCount;
                    var categoryQuantity = cart.GetAvailableCategoryQuantity(category);
                    var maxCategoryApplications = categoryQuantity / MinQuantity;
                    categoryApplications = Math.Min(categoryApplications, maxCategoryApplications);

                    for (int app = 0; app < categoryApplications; app++)
                    {
                        var remainingQuantityNeeded = MinQuantity;
                        var categoryItems = cart.Items
                            .Where(x => x.Product.Category == category && x.AvailableQuantity > 0)
                            .OrderByDescending(x => x.UnitPrice)
                            .ToList();

                        foreach (var item in categoryItems)
                        {
                            if (remainingQuantityNeeded <= 0) break;

                            var consumeQuantity = Math.Min(item.AvailableQuantity, remainingQuantityNeeded);
                            
                            // 检查最大优惠数量限制
                            if (MaxDiscountQuantity > 0)
                            {
                                var remainingDiscountQuantity = MaxDiscountQuantity - totalConsumedQuantity;
                                consumeQuantity = Math.Min(consumeQuantity, remainingDiscountQuantity);
                            }

                            if (consumeQuantity > 0)
                            {
                                // 计算折扣金额
                                var discountAmount = consumeQuantity * item.UnitPrice * (1 - DiscountPercentage);
                                totalDiscountAmount += discountAmount;

                                // 记录消耗的商品
                                var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == item.Product.Id);
                                if (existingConsumed != null)
                                {
                                    existingConsumed.Quantity += consumeQuantity;
                                }
                                else
                                {
                                    consumedItems.Add(new ConsumedItem
                                    {
                                        ProductId = item.Product.Id,
                                        ProductName = item.Product.Name,
                                        Quantity = consumeQuantity,
                                        UnitPrice = item.UnitPrice
                                    });
                                }

                                item.Quantity -= consumeQuantity;
                                totalConsumedQuantity += consumeQuantity;
                                remainingQuantityNeeded -= consumeQuantity;
                            }
                        }

                        if (remainingQuantityNeeded > 0)
                            break; // 该分类商品不足，停止处理
                    }
                }
            }
            else
            {
                // 所有分类合并处理
                for (int app = 0; app < applicationCount; app++)
                {
                    var remainingQuantityNeeded = MinQuantity;
                    var applicableItems = cart.Items
                        .Where(x => ApplicableCategories.Contains(x.Product.Category) && x.AvailableQuantity > 0)
                        .OrderByDescending(x => x.UnitPrice)
                        .ToList();

                    foreach (var item in applicableItems)
                    {
                        if (remainingQuantityNeeded <= 0) break;

                        var consumeQuantity = Math.Min(item.AvailableQuantity, remainingQuantityNeeded);
                        
                        // 检查最大优惠数量限制
                        if (MaxDiscountQuantity > 0)
                        {
                            var remainingDiscountQuantity = MaxDiscountQuantity - totalConsumedQuantity;
                            consumeQuantity = Math.Min(consumeQuantity, remainingDiscountQuantity);
                        }

                        if (consumeQuantity > 0)
                        {
                            // 计算折扣金额
                            var discountAmount = consumeQuantity * item.UnitPrice * (1 - DiscountPercentage);
                            totalDiscountAmount += discountAmount;

                            // 记录消耗的商品
                            var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == item.Product.Id);
                            if (existingConsumed != null)
                            {
                                existingConsumed.Quantity += consumeQuantity;
                            }
                            else
                            {
                                consumedItems.Add(new ConsumedItem
                                {
                                    ProductId = item.Product.Id,
                                    ProductName = item.Product.Name,
                                    Quantity = consumeQuantity,
                                    UnitPrice = item.UnitPrice
                                });
                            }

                            item.Quantity -= consumeQuantity;
                            totalConsumedQuantity += consumeQuantity;
                            remainingQuantityNeeded -= consumeQuantity;
                        }
                    }

                    if (remainingQuantityNeeded > 0)
                        break; // 商品不足，停止处理
                }
            }

            // 清理数量为0的商品项
            cart.Items.RemoveAll(x => x.Quantity <= 0);

            application.DiscountAmount = totalDiscountAmount;
            application.ConsumedItems = consumedItems;
            application.IsSuccessful = totalDiscountAmount > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用分类折扣";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = $"应用分类折扣时发生错误: {ex.Message}";
        }

        return application;
    }
}
