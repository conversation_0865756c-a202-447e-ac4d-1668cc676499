using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.ExchangeRules;

/// <summary>
/// 组合打折换购规则测试
/// 测试场景：购买A、B商品各大于等于1件时，可以0.9折购买C商品
/// </summary>
public class CombinationDiscountExchangeRuleTests(ITestOutputHelper output) : TestBase(output)
{

    #region 基本功能测试

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "High")]
    public void Apply_BasicCombinationDiscountExchange_ShouldApplyCorrectly()
    {
        // Arrange - 创建基本组合打折换购规则：买A+B享C商品9折
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_BuyAB_90Percent_ExchangeC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            (TestDataGenerator.CreateProductA(), 1), // A商品50元
            (TestDataGenerator.CreateProductB(), 1), // B商品30元
            (TestDataGenerator.CreateProductC(), 1)  // C商品20元
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "应用前购物车");

        var expectedDiscount = 2.00m; // C商品20元*0.1=2元优惠

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "基本组合打折换购");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证总优惠金额
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应为C商品的折扣金额");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart, "基本组合打折换购");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "High")]
    public void Apply_DifferentDiscountRates_ShouldApplyCorrectly()
    {
        // Arrange - 测试不同折扣率
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_BuyAB_50Percent_ExchangeC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_002",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1),
            (TestDataGenerator.CreateProductC(), 1) // 20元商品
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "不同折扣率测试购物车");

        var expectedDiscount = 10.00m; // 20元*0.5=10元优惠

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "不同折扣率测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证总优惠金额
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应为C商品的50%折扣金额");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart, "不同折扣率测试");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_QuantityBasedCombination_ShouldApplyCorrectly()
    {
        // Arrange - 基于数量的组合条件
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_QuantityBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_003",
            (TestDataGenerator.CreateProductA(), 2), // 满足A商品数量条件
            (TestDataGenerator.CreateProductB(), 2), // 满足B商品数量条件
            (TestDataGenerator.CreateProductC(), 1)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "基于数量的组合条件测试购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "基于数量的组合条件测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证有优惠产生
        Assert.True(result.TotalDiscount > 0, "基于数量的组合条件应产生优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart, "基于数量的组合打折换购");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_AmountBasedCombination_ShouldApplyCorrectly()
    {
        // Arrange - 基于金额的组合条件
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_AmountBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_004",
            (TestDataGenerator.CreateProductA(), 3), // 150元，满足A商品金额条件
            (TestDataGenerator.CreateProductB(), 2), // 60元，满足B商品金额条件
            (TestDataGenerator.CreateProductC(), 1)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "基于金额的组合条件测试购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "基于金额的组合条件测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证有优惠产生
        Assert.True(result.TotalDiscount > 0, "基于金额的组合条件应产生优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart, "基于金额的组合打折换购");
    }

    #endregion

    #region 条件不满足测试

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "High")]
    public void Apply_MissingFirstProduct_ShouldNotApply()
    {
        // Arrange - 缺少第一个组合商品
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_BuyAB_90Percent_ExchangeC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_005",
            (TestDataGenerator.CreateProductB(), 1), // 只有B商品，没有A商品
            (TestDataGenerator.CreateProductC(), 1)
        );

        LogCartDetails(cart, "缺少第一个组合商品的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "缺少第一个组合商品场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "缺少组合商品时应无优惠");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "High")]
    public void Apply_MissingSecondProduct_ShouldNotApply()
    {
        // Arrange - 缺少第二个组合商品
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_BuyAB_90Percent_ExchangeC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_006",
            (TestDataGenerator.CreateProductA(), 1), // 只有A商品，没有B商品
            (TestDataGenerator.CreateProductC(), 1)
        );

        LogCartDetails(cart, "缺少第二个组合商品的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "缺少第二个组合商品场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "缺少组合商品时应无优惠");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "High")]
    public void Apply_NoExchangeProductInCart_ShouldNotApply()
    {
        // Arrange - 购物车中没有换购商品
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_BuyAB_90Percent_ExchangeC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_007",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1)
        // 没有C商品
        );

        LogCartDetails(cart, "没有换购商品的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "没有换购商品场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "没有换购商品时应无优惠");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_InsufficientCombinationQuantity_ShouldNotApply()
    {
        // Arrange - 组合商品数量不足
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_QuantityBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_008",
            (TestDataGenerator.CreateProductA(), 1), // 不满足数量条件（需要2件）
            (TestDataGenerator.CreateProductB(), 2),
            (TestDataGenerator.CreateProductC(), 1)
        );

        LogCartDetails(cart, "组合商品数量不足的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "组合商品数量不足场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "数量不足时应无优惠");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_InsufficientCombinationAmount_ShouldNotApply()
    {
        // Arrange - 组合商品金额不足
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_AmountBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_009",
            (TestDataGenerator.CreateProductA(), 1), // 50元，不满足金额条件
            (TestDataGenerator.CreateProductB(), 1), // 30元，不满足金额条件
            (TestDataGenerator.CreateProductC(), 1)
        );

        LogCartDetails(cart, "组合商品金额不足的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "组合商品金额不足场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "金额不足时应无优惠");
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Low")]
    public void Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_BuyAB_90Percent_ExchangeC();
        var cart = TestDataGenerator.CreateEmptyCart();

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "空购物车");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertAmountEqual(0m, result.TotalDiscount, "空购物车应无优惠");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Low")]
    public void Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_Disabled();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_010",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1),
            (TestDataGenerator.CreateProductC(), 1)
        );

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "禁用规则测试");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");
        AssertAmountEqual(0m, result.TotalDiscount, "禁用规则应无优惠");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Low")]
    public void Apply_ZeroDiscountRate_ShouldNotApply()
    {
        // Arrange - 0折扣率（无效配置）
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_ZeroDiscount();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_011",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1),
            (TestDataGenerator.CreateProductC(), 1)
        );

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "零折扣率测试");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertAmountEqual(0m, result.TotalDiscount, "零折扣率应无优惠");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Low")]
    public void Apply_FullDiscountRate_ShouldApplyAsFreeExchange()
    {
        // Arrange - 100%折扣（免费换购）
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_FreeExchange();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_012",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1),
            (TestDataGenerator.CreateProductC(), 1)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "全额折扣测试购物车");

        var expectedDiscount = 20.00m; // C商品20元全免

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "全额折扣测试");
        LogPromotionResultDetails(result);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证总优惠金额
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "全额折扣应等于C商品原价");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart, "全额折扣测试");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_ExactCombinationRequirements_ShouldApplyCorrectly()
    {
        // Arrange - 刚好满足组合条件
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_BuyAB_90Percent_ExchangeC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_013",
            (TestDataGenerator.CreateProductA(), 1), // 刚好1件
            (TestDataGenerator.CreateProductB(), 1), // 刚好1件
            (TestDataGenerator.CreateProductC(), 1)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "刚好满足组合条件的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "刚好满足组合条件");
        LogPromotionResultDetails(result);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证有优惠产生
        Assert.True(result.TotalDiscount > 0, "刚好满足条件应产生优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart, "刚好满足组合条件");
    }

    #endregion

    #region 可重复性测试

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_RepeatableRule_ShouldApplyMultipleTimes()
    {
        // Arrange - 可重复的组合打折换购规则
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_Repeatable();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_014",
            (TestDataGenerator.CreateProductA(), 3), // 3件A商品
            (TestDataGenerator.CreateProductB(), 3), // 3件B商品
            (TestDataGenerator.CreateProductC(), 3)  // 3件C商品可换购
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "可重复规则测试购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "可重复规则测试");
        LogPromotionResultDetails(result);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证有优惠产生
        Assert.True(result.TotalDiscount > 0, "可重复规则应产生优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart, "可重复规则测试");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_NonRepeatableRule_ShouldApplyOnlyOnce()
    {
        // Arrange - 不可重复的组合打折换购规则
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_NonRepeatable();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_015",
            (TestDataGenerator.CreateProductA(), 5), // 足够多的A商品
            (TestDataGenerator.CreateProductB(), 5), // 足够多的B商品
            (TestDataGenerator.CreateProductC(), 5)  // 足够多的C商品
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "不可重复规则测试购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "不可重复规则测试");
        LogPromotionResultDetails(result);

        // 验证规则应用情况（可能应用也可能不应用，取决于具体实现）
        if (result.AppliedPromotions.Any())
        {
            Assert.Single(result.AppliedPromotions);
            AssertRuleApplication(result.AppliedPromotions[0], rule.Id);
        }

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart, "不可重复规则测试");
    }

    #endregion

    #region 复杂场景测试

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "High")]
    public void Apply_MultipleExchangeOptions_ShouldSelectOptimal()
    {
        // Arrange - 多种换购选择
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_MultipleExchangeOptions();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_016",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1),
            (TestDataGenerator.CreateProductC(), 1), // 20元
            (TestDataGenerator.CreateProductD(), 1), // 100元
            (TestDataGenerator.CreateProductE(), 1)  // 15元
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "多种换购选择测试购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "多种换购选择测试");
        LogPromotionResultDetails(result);

        if (result.AppliedPromotions.Any())
        {
            Assert.Single(result.AppliedPromotions);
            AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

            // 验证有优惠产生
            Assert.True(result.TotalDiscount > 0, "多种换购选择应产生优惠");
        }

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart, "多种换购选择测试");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "High")]
    public void Apply_MerchantBenefitStrategy_ShouldSelectLowPriceProduct()
    {
        // Arrange - 商家利益最大化策略
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_MerchantBenefit();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_017",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1),
            (TestDataGenerator.CreateProductC(), 1), // 20元
            (TestDataGenerator.CreateProductD(), 1), // 100元
            (TestDataGenerator.CreateProductE(), 1)  // 15元
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "商家利益最大化策略测试购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "商家利益最大化策略测试");
        LogPromotionResultDetails(result);

        if (result.AppliedPromotions.Any())
        {
            Assert.Single(result.AppliedPromotions);
            AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

            // 验证有优惠产生
            Assert.True(result.TotalDiscount > 0, "商家利益最大化策略应产生优惠");
        }

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart, "商家利益最大化策略测试");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "High")]
    public void Apply_ComplexCombinationConditions_ShouldHandleCorrectly()
    {
        // Arrange - 复杂组合条件（数量+金额混合）
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_ComplexConditions();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_018",
            (TestDataGenerator.CreateProductA(), 2), // 满足数量条件
            (TestDataGenerator.CreateProductB(), 3), // 90元，满足金额条件
            (TestDataGenerator.CreateProductC(), 1)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "复杂组合条件测试购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "复杂组合条件测试");
        LogPromotionResultDetails(result);

        if (result.AppliedPromotions.Any())
        {
            Assert.Single(result.AppliedPromotions);
            AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

            // 验证有优惠产生
            Assert.True(result.TotalDiscount > 0, "复杂组合条件应产生优惠");
        }

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart, "复杂组合条件测试");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "High")]
    public void Apply_WithOtherPromotions_ShouldHandleCorrectly()
    {
        // Arrange - 与其他促销规则交互
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_BuyAB_90Percent_ExchangeC();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_019",
            (TestDataGenerator.CreateProductA(), 2),
            (TestDataGenerator.CreateProductB(), 2),
            (TestDataGenerator.CreateProductC(), 2)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "与其他促销交互测试购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "与其他促销交互测试");
        LogPromotionResultDetails(result);

        if (result.AppliedPromotions.Any())
        {
            Assert.Single(result.AppliedPromotions);
            AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

            // 验证有优惠产生
            Assert.True(result.TotalDiscount > 0, "与其他促销交互应产生优惠");
        }

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart, "与其他促销交互测试");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_ThreeProductCombination_ShouldApplyCorrectly()
    {
        // Arrange - 三商品组合
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_ThreeProductCombo();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_020",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1),
            (TestDataGenerator.CreateProductC(), 1),
            (TestDataGenerator.CreateProductD(), 1) // 换购商品
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "三商品组合测试购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "三商品组合测试");
        LogPromotionResultDetails(result);

        if (result.AppliedPromotions.Any())
        {
            Assert.Single(result.AppliedPromotions);
            AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

            // 验证有优惠产生
            Assert.True(result.TotalDiscount > 0, "三商品组合应产生优惠");
        }

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart, "三商品组合测试");
    }

    #endregion

    #region 性能测试

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Low")]
    public void Apply_LargeCart_ShouldPerformEfficiently()
    {
        // Arrange - 大型购物车性能测试
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_BuyAB_90Percent_ExchangeC();
        var cart = TestDataGenerator.CreateLargeTestCart(100);


        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "大型购物车性能测试");

        TestPromotionRuleService.Rules = [rule];

        // Act & Assert
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;
        stopwatch.Stop();

        AssertPromotionResult(result, "大型购物车性能测试");

        Assert.True(stopwatch.ElapsedMilliseconds < 1000, "大型购物车处理应在1秒内完成");
        Output.WriteLine($"大型购物车处理耗时: {stopwatch.ElapsedMilliseconds}ms");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart, "大型购物车性能测试");
    }

    #endregion

    #region 配置验证测试

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Low")]
    public void Apply_InvalidConfiguration_ShouldHandleGracefully()
    {
        // Arrange - 无效配置
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_InvalidConfig();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_021",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1),
            (TestDataGenerator.CreateProductC(), 1)
        );

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "无效配置测试");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertAmountEqual(0m, result.TotalDiscount, "无效配置应无优惠");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void CalculateMaxApplications_ValidConditions_ShouldReturnCorrectCount()
    {
        // Arrange
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_Repeatable();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_022",
            (TestDataGenerator.CreateProductA(), 5),
            (TestDataGenerator.CreateProductB(), 5),
            (TestDataGenerator.CreateProductC(), 3)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "最大应用次数测试购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "最大应用次数测试");
        LogPromotionResultDetails(result);

        if (result.AppliedPromotions.Any())
        {
            Assert.Single(result.AppliedPromotions);
            AssertRuleApplication(result.AppliedPromotions[0], rule.Id);
        }

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart, "最大应用次数测试");
        Output.WriteLine($"应用的促销数量: {result.AppliedPromotions.Count}");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Medium")]
    public void Apply_EdgeDiscountRates_ShouldHandleCorrectly()
    {
        // Arrange - 测试边界折扣率
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_EdgeDiscountRate();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_023",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1),
            (TestDataGenerator.CreateProductC(), 1)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "边界折扣率测试购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "边界折扣率测试");
        LogPromotionResultDetails(result);

        if (result.AppliedPromotions.Any())
        {
            Assert.Single(result.AppliedPromotions);
            AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

            // 验证优惠金额非负
            Assert.True(result.TotalDiscount >= 0, "优惠金额应非负");
        }

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart, "边界折扣率测试");
    }

    [Fact]
    [Trait("Category", "Exchange")]
    [Trait("Priority", "Low")]
    public void Apply_EmptyCombinationConditions_ShouldNotApply()
    {
        // Arrange - 空组合条件
        var rule = TestDataGenerator.CreateCombinationDiscountExchangeRule_EmptyConditions();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_024",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1),
            (TestDataGenerator.CreateProductC(), 1)
        );

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "空组合条件测试");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertAmountEqual(0m, result.TotalDiscount, "空组合条件应无优惠");
    }

    #endregion
}