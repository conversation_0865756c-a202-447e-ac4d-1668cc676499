using System.Diagnostics;
using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Rules;

namespace PE2.PromotionEngine.Calculators;

/// <summary>
/// 促销计算器 - 使用回溯算法寻找最优促销组合
/// </summary>
public class PromotionCalculator
{
    private readonly List<PromotionRuleBase> _rules;
    private readonly PromotionTracker _tracker;
    private int _maxSearchDepth = 1000;
    private int _currentSearchDepth = 0;

    public PromotionCalculator(List<PromotionRuleBase> rules)
    {
        _rules = rules ?? new List<PromotionRuleBase>();
        _tracker = new PromotionTracker();
    }

    /// <summary>
    /// 设置最大搜索深度（防止无限递归）
    /// </summary>
    public void SetMaxSearchDepth(int maxDepth)
    {
        _maxSearchDepth = maxDepth;
    }

    /// <summary>
    /// 计算最优促销组合
    /// </summary>
    public PromotionResult CalculateOptimalPromotions(ShoppingCart cart)
    {
        var stopwatch = Stopwatch.StartNew();
        _tracker.Reset();
        _currentSearchDepth = 0;

        var result = new PromotionResult
        {
            OriginalCart = cart.Clone(),
            CalculationSteps = new List<CalculationStep>()
        };

        _tracker.AddStep(StepType.Start, "开始计算最优促销组合", cart);

        try
        {
            // 1. 筛选可用的促销规则
            var applicableRules = FilterApplicableRules(cart);
            _tracker.AddStep(StepType.RuleFiltering, $"筛选出{applicableRules.Count}个可用促销规则", applicableRules.Select(r => r.Name));

            if (applicableRules.Count == 0)
            {
                result.ProcessedCart = cart.Clone();
                result.IgnoredPromotions = _rules.Select(r => new IgnoredPromotion
                {
                    RuleId = r.Id,
                    RuleName = r.Name,
                    Reason = "促销条件不满足",
                    ReasonType = IgnoreReason.ConditionNotMet
                }).ToList();
                
                _tracker.AddStep(StepType.Complete, "没有可用的促销规则", null);
                result.CalculationSteps = _tracker.GetSteps();
                return result;
            }

            // 2. 使用回溯算法寻找最优组合
            var bestSolution = FindOptimalCombination(cart.Clone(), applicableRules, 0);
            
            if (bestSolution != null)
            {
                result.ProcessedCart = bestSolution.Cart;
                result.AppliedPromotions = bestSolution.AppliedPromotions;
                result.IsOptimal = bestSolution.IsOptimal;
                result.AlgorithmInfo = $"搜索深度: {_currentSearchDepth}, 最大深度: {_maxSearchDepth}";
            }
            else
            {
                result.ProcessedCart = cart.Clone();
                result.IsOptimal = false;
                result.AlgorithmInfo = "未找到可行的促销组合";
            }

            // 3. 分析被忽略的促销
            result.IgnoredPromotions = AnalyzeIgnoredPromotions(cart, result.AppliedPromotions);

            _tracker.AddStep(StepType.Complete, $"计算完成，总优惠: {result.TotalDiscount:C}", result);
        }
        catch (Exception ex)
        {
            _tracker.AddStep(StepType.Complete, $"计算出错: {ex.Message}", ex);
            result.ProcessedCart = cart.Clone();
            result.IsOptimal = false;
            result.AlgorithmInfo = $"计算异常: {ex.Message}";
        }
        finally
        {
            stopwatch.Stop();
            result.CalculationTimeMs = stopwatch.ElapsedMilliseconds;
            result.CalculationSteps = _tracker.GetSteps();
        }

        return result;
    }

    /// <summary>
    /// 筛选可应用的促销规则
    /// </summary>
    private List<PromotionRuleBase> FilterApplicableRules(ShoppingCart cart)
    {
        var applicableRules = new List<PromotionRuleBase>();
        var checkTime = DateTime.Now;

        foreach (var rule in _rules.Where(r => r.IsEnabled).OrderByDescending(r => r.Priority))
        {
            if (rule.IsApplicable(cart, checkTime))
            {
                applicableRules.Add(rule);
                _tracker.AddStep(StepType.ConditionCheck, $"规则 {rule.Name} 条件满足", rule);
            }
            else
            {
                _tracker.AddStep(StepType.ConditionCheck, $"规则 {rule.Name} 条件不满足", rule);
            }
        }

        return applicableRules;
    }

    /// <summary>
    /// 使用回溯算法寻找最优促销组合
    /// </summary>
    private PromotionSolution? FindOptimalCombination(ShoppingCart cart, List<PromotionRuleBase> rules, int ruleIndex)
    {
        _currentSearchDepth++;
        
        // 防止搜索过深
        if (_currentSearchDepth > _maxSearchDepth)
        {
            _tracker.AddStep(StepType.OptimizationSearch, "达到最大搜索深度，停止搜索", _currentSearchDepth);
            return new PromotionSolution { Cart = cart, IsOptimal = false };
        }

        // 递归终止条件：所有规则都已考虑
        if (ruleIndex >= rules.Count)
        {
            return new PromotionSolution { Cart = cart, IsOptimal = true };
        }

        var currentRule = rules[ruleIndex];
        var bestSolution = new PromotionSolution { Cart = cart.Clone(), IsOptimal = true };

        _tracker.AddStep(StepType.OptimizationSearch, $"考虑规则: {currentRule.Name}", currentRule);

        // 选择1：不应用当前规则
        var solutionWithoutRule = FindOptimalCombination(cart.Clone(), rules, ruleIndex + 1);
        if (solutionWithoutRule != null && solutionWithoutRule.TotalDiscount > bestSolution.TotalDiscount)
        {
            bestSolution = solutionWithoutRule;
        }

        // 选择2：应用当前规则（可能多次）
        if (currentRule.IsApplicable(cart, DateTime.Now))
        {
            var maxApplications = currentRule.CalculateMaxApplications(cart);
            
            for (int appCount = 1; appCount <= maxApplications; appCount++)
            {
                var cartCopy = cart.Clone();
                var application = currentRule.ApplyPromotion(cartCopy, appCount);
                
                if (application.IsSuccessful && application.DiscountAmount > 0)
                {
                    _tracker.AddStep(StepType.PromotionApplication, 
                        $"应用规则 {currentRule.Name} {appCount}次，优惠 {application.DiscountAmount:C}", 
                        application);

                    // 递归处理剩余规则
                    var solutionWithRule = FindOptimalCombination(cartCopy, rules, ruleIndex + 1);
                    
                    if (solutionWithRule != null)
                    {
                        solutionWithRule.AppliedPromotions.Insert(0, new AppliedPromotion
                        {
                            RuleId = currentRule.Id,
                            RuleName = currentRule.Name,
                            PromotionType = currentRule.RuleType,
                            DiscountAmount = application.DiscountAmount,
                            ApplicationCount = appCount,
                            ConsumedItems = application.ConsumedItems,
                            GiftItems = application.GiftItems,
                            Description = $"应用{appCount}次，优惠{application.DiscountAmount:C}"
                        });

                        if (solutionWithRule.TotalDiscount > bestSolution.TotalDiscount)
                        {
                            bestSolution = solutionWithRule;
                            _tracker.AddStep(StepType.ResultComparison, 
                                $"找到更优解，总优惠: {bestSolution.TotalDiscount:C}", 
                                bestSolution);
                        }
                    }
                }
            }
        }

        _currentSearchDepth--;
        return bestSolution;
    }

    /// <summary>
    /// 分析被忽略的促销
    /// </summary>
    private List<IgnoredPromotion> AnalyzeIgnoredPromotions(ShoppingCart originalCart, List<AppliedPromotion> appliedPromotions)
    {
        var ignoredPromotions = new List<IgnoredPromotion>();
        var appliedRuleIds = appliedPromotions.Select(p => p.RuleId).ToHashSet();

        foreach (var rule in _rules)
        {
            if (appliedRuleIds.Contains(rule.Id))
                continue;

            var ignored = new IgnoredPromotion
            {
                RuleId = rule.Id,
                RuleName = rule.Name
            };

            if (!rule.IsEnabled)
            {
                ignored.Reason = "促销规则已禁用";
                ignored.ReasonType = IgnoreReason.Other;
            }
            else if (!rule.IsInValidPeriod(DateTime.Now))
            {
                if (rule.StartTime.HasValue && DateTime.Now < rule.StartTime.Value)
                {
                    ignored.Reason = $"促销未开始，开始时间: {rule.StartTime:yyyy-MM-dd HH:mm}";
                    ignored.ReasonType = IgnoreReason.NotStarted;
                }
                else
                {
                    ignored.Reason = $"促销已过期，结束时间: {rule.EndTime:yyyy-MM-dd HH:mm}";
                    ignored.ReasonType = IgnoreReason.Expired;
                }
            }
            else if (!rule.IsApplicable(originalCart, DateTime.Now))
            {
                ignored.Reason = "促销条件不满足";
                ignored.ReasonType = IgnoreReason.ConditionNotMet;
            }
            else if (rule.CalculateMaxApplications(originalCart) <= 0)
            {
                ignored.Reason = "商品数量不足";
                ignored.ReasonType = IgnoreReason.InsufficientQuantity;
            }
            else
            {
                ignored.Reason = "非最优选择";
                ignored.ReasonType = IgnoreReason.NotOptimal;
            }

            ignoredPromotions.Add(ignored);
        }

        return ignoredPromotions;
    }
}

/// <summary>
/// 促销解决方案
/// </summary>
internal class PromotionSolution
{
    public ShoppingCart Cart { get; set; } = new();
    public List<AppliedPromotion> AppliedPromotions { get; set; } = new();
    public bool IsOptimal { get; set; } = true;
    
    public decimal TotalDiscount => AppliedPromotions.Sum(p => p.DiscountAmount);
}
