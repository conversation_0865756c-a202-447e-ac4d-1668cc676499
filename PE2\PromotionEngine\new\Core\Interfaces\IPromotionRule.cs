using PE2.Models;
using PE2.PromotionEngine.Core;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.new.Core.Models;

namespace PE2.PromotionEngine.new.Core.Interfaces;

/// <summary>
/// 促销规则统一接口
/// 集成商品占用管理、条件执行分离和排他性管理功能
/// </summary>
public interface IPromotionRule
{
    #region 基本属性
    
    /// <summary>
    /// 规则ID
    /// </summary>
    string Id { get; }
    
    /// <summary>
    /// 规则名称
    /// </summary>
    string Name { get; }
    
    /// <summary>
    /// 规则描述
    /// </summary>
    string Description { get; }
    
    /// <summary>
    /// 规则类型
    /// </summary>
    string RuleType { get; }
    
    /// <summary>
    /// 优先级（数字越大优先级越高）
    /// </summary>
    int Priority { get; }
    
    /// <summary>
    /// 是否启用
    /// </summary>
    bool IsEnabled { get; }
    
    /// <summary>
    /// 生效时间
    /// </summary>
    DateTime? StartTime { get; }
    
    /// <summary>
    /// 失效时间
    /// </summary>
    DateTime? EndTime { get; }
    
    /// <summary>
    /// 是否可重复应用
    /// </summary>
    bool IsRepeatable { get; }
    
    /// <summary>
    /// 最大应用次数（0表示无限制）
    /// </summary>
    int MaxApplications { get; }
    
    /// <summary>
    /// 是否可与其他促销叠加
    /// </summary>
    bool CanStackWithOthers { get; }
    
    /// <summary>
    /// 商品互斥级别
    /// </summary>
    ProductExclusivityLevel ProductExclusivity { get; }
    
    /// <summary>
    /// 排斥的规则ID列表
    /// </summary>
    List<string> ExclusiveRuleIds { get; }
    
    #endregion
    
    #region 核心方法
    
    /// <summary>
    /// 检查规则是否适用于指定购物车
    /// </summary>
    /// <param name="cart">购物车</param>
    /// <param name="checkTime">检查时间</param>
    /// <returns>是否适用</returns>
    bool IsApplicable(ShoppingCart cart, DateTime checkTime);
    
    /// <summary>
    /// 检查规则是否在有效期内
    /// </summary>
    /// <param name="checkTime">检查时间</param>
    /// <returns>是否在有效期内</returns>
    bool IsInValidPeriod(DateTime checkTime);
    
    /// <summary>
    /// 计算可应用的最大次数
    /// </summary>
    /// <param name="cart">购物车</param>
    /// <returns>最大应用次数</returns>
    int CalculateMaxApplications(ShoppingCart cart);
    
    #endregion
    
    #region 商品占用管理集成
    
    /// <summary>
    /// 使用占用会话验证条件
    /// </summary>
    /// <param name="cart">购物车</param>
    /// <param name="session">占用会话</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateConditionsAsync(
        ShoppingCart cart, 
        IOccupationSession session, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 使用占用会话执行促销
    /// </summary>
    /// <param name="cart">购物车</param>
    /// <param name="session">占用会话</param>
    /// <param name="applicationCount">应用次数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行结果</returns>
    Task<ExecutionResult> ExecutePromotionAsync(
        ShoppingCart cart, 
        IOccupationSession session, 
        int applicationCount = 1,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取规则需要占用的商品信息
    /// </summary>
    /// <param name="cart">购物车</param>
    /// <returns>占用请求列表</returns>
    List<OccupationRequest> GetOccupationRequests(ShoppingCart cart);
    
    #endregion
    
    #region 兼容性方法
    
    /// <summary>
    /// 传统方式应用促销（向后兼容）
    /// </summary>
    /// <param name="cart">购物车</param>
    /// <param name="applicationCount">应用次数</param>
    /// <returns>促销应用结果</returns>
    PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1);
    
    #endregion
}

/// <summary>
/// 条件验证接口
/// </summary>
public interface IConditionValidator
{
    /// <summary>
    /// 验证促销条件
    /// </summary>
    /// <param name="cart">购物车</param>
    /// <param name="context">促销上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateAsync(
        ShoppingCart cart, 
        PromotionContext context, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 促销执行接口
/// </summary>
public interface IPromotionExecutor
{
    /// <summary>
    /// 执行促销
    /// </summary>
    /// <param name="cart">购物车</param>
    /// <param name="context">促销上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行结果</returns>
    Task<ExecutionResult> ExecuteAsync(
        ShoppingCart cart, 
        PromotionContext context, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 商品占用接口
/// </summary>
public interface IProductOccupation
{
    /// <summary>
    /// 请求商品占用
    /// </summary>
    /// <param name="session">占用会话</param>
    /// <param name="requests">占用请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>占用结果</returns>
    Task<bool> RequestOccupationAsync(
        IOccupationSession session, 
        List<OccupationRequest> requests, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 释放商品占用
    /// </summary>
    /// <param name="session">占用会话</param>
    /// <param name="productIds">商品ID列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>释放结果</returns>
    Task<bool> ReleaseOccupationAsync(
        IOccupationSession session, 
        List<string> productIds, 
        CancellationToken cancellationToken = default);
}
