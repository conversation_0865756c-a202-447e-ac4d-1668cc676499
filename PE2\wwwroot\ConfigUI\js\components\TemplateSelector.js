// 模板选择器组件
const TemplateSelector = {
  template: `
    <div class="template-selector">
      <div class="template-header">
        <h3>
          <i class="el-icon-files"></i>
          选择模板
        </h3>
        <div class="template-actions">
          <el-input
            v-model="searchQuery"
            placeholder="搜索模板..."
            size="small"
            style="width: 200px; margin-right: 10px;"
            clearable
          >
            <template #prefix>
              <i class="el-icon-search"></i>
            </template>
          </el-input>
          <el-button 
            size="small" 
            type="primary" 
            @click="showImportDialog = true"
          >
            导入模板
          </el-button>
        </div>
      </div>
      
      <div class="template-categories">
        <el-tabs v-model="activeCategory" @tab-change="onCategoryChange">
          <el-tab-pane label="买赠活动" name="buyGift">
            <div class="template-grid">
              <div 
                v-for="template in filteredTemplates.buyGift" 
                :key="template.id"
                class="template-card"
                :class="{ selected: selectedTemplate?.id === template.id }"
                @click="selectTemplate(template)"
              >
                <div class="template-icon">{{ template.icon }}</div>
                <div class="template-info">
                  <h4>{{ template.name }}</h4>
                  <p>{{ template.description }}</p>
                  <div class="template-meta">
                    <span class="template-tag" :class="template.isPredefined ? 'predefined' : 'custom'">
                      {{ template.isPredefined ? '系统模板' : '自定义' }}
                    </span>
                  </div>
                </div>
                <div class="template-actions" v-if="!template.isPredefined">
                  <el-button 
                    size="mini" 
                    type="text" 
                    @click.stop="exportTemplate(template.id)"
                  >
                    导出
                  </el-button>
                  <el-button 
                    size="mini" 
                    type="text" 
                    class="danger"
                    @click.stop="deleteTemplate(template.id)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="折扣活动" name="discount">
            <div class="template-grid">
              <div 
                v-for="template in filteredTemplates.discount" 
                :key="template.id"
                class="template-card"
                :class="{ selected: selectedTemplate?.id === template.id }"
                @click="selectTemplate(template)"
              >
                <div class="template-icon">{{ template.icon }}</div>
                <div class="template-info">
                  <h4>{{ template.name }}</h4>
                  <p>{{ template.description }}</p>
                  <div class="template-meta">
                    <span class="template-tag" :class="template.isPredefined ? 'predefined' : 'custom'">
                      {{ template.isPredefined ? '系统模板' : '自定义' }}
                    </span>
                  </div>
                </div>
                <div class="template-actions" v-if="!template.isPredefined">
                  <el-button 
                    size="mini" 
                    type="text" 
                    @click.stop="exportTemplate(template.id)"
                  >
                    导出
                  </el-button>
                  <el-button 
                    size="mini" 
                    type="text" 
                    class="danger"
                    @click.stop="deleteTemplate(template.id)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="套餐活动" name="bundle">
            <div class="template-grid">
              <div 
                v-for="template in filteredTemplates.bundle" 
                :key="template.id"
                class="template-card"
                :class="{ selected: selectedTemplate?.id === template.id }"
                @click="selectTemplate(template)"
              >
                <div class="template-icon">{{ template.icon }}</div>
                <div class="template-info">
                  <h4>{{ template.name }}</h4>
                  <p>{{ template.description }}</p>
                  <div class="template-meta">
                    <span class="template-tag" :class="template.isPredefined ? 'predefined' : 'custom'">
                      {{ template.isPredefined ? '系统模板' : '自定义' }}
                    </span>
                  </div>
                </div>
                <div class="template-actions" v-if="!template.isPredefined">
                  <el-button 
                    size="mini" 
                    type="text" 
                    @click.stop="exportTemplate(template.id)"
                  >
                    导出
                  </el-button>
                  <el-button 
                    size="mini" 
                    type="text" 
                    class="danger"
                    @click.stop="deleteTemplate(template.id)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="自定义模板" name="custom">
            <div class="template-grid">
              <div 
                v-for="template in filteredTemplates.custom" 
                :key="template.id"
                class="template-card"
                :class="{ selected: selectedTemplate?.id === template.id }"
                @click="selectTemplate(template)"
              >
                <div class="template-icon">{{ template.icon }}</div>
                <div class="template-info">
                  <h4>{{ template.name }}</h4>
                  <p>{{ template.description }}</p>
                  <div class="template-meta">
                    <span class="template-tag custom">自定义</span>
                    <span class="template-date">
                      {{ formatDate(template.createdAt) }}
                    </span>
                  </div>
                </div>
                <div class="template-actions">
                  <el-button 
                    size="mini" 
                    type="text" 
                    @click.stop="exportTemplate(template.id)"
                  >
                    导出
                  </el-button>
                  <el-button 
                    size="mini" 
                    type="text" 
                    @click.stop="editTemplate(template)"
                  >
                    编辑
                  </el-button>
                  <el-button 
                    size="mini" 
                    type="text" 
                    class="danger"
                    @click.stop="deleteTemplate(template.id)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <div class="template-footer" v-if="selectedTemplate">
        <div class="selected-template-info">
          <h4>已选择: {{ selectedTemplate.name }}</h4>
          <p>{{ selectedTemplate.description }}</p>
        </div>
        <div class="template-footer-actions">
          <el-button @click="clearSelection">取消选择</el-button>
          <el-button 
            type="primary" 
            @click="applyTemplate"
            :disabled="!selectedTemplate"
          >
            应用模板
          </el-button>
        </div>
      </div>
      
      <!-- 导入模板对话框 -->
      <el-dialog
        v-model="showImportDialog"
        title="导入模板"
        width="500px"
      >
        <div class="import-dialog-content">
          <p>选择要导入的模板文件（JSON格式）：</p>
          <input 
            type="file" 
            ref="templateFileInput"
            accept=".json"
            @change="onTemplateFileSelected"
            style="margin: 20px 0;"
          />
          <div class="import-tips">
            <h4>导入说明：</h4>
            <ul>
              <li>文件必须是有效的JSON格式</li>
              <li>模板将被添加到自定义模板中</li>
              <li>如果模板名称重复，将自动生成新名称</li>
            </ul>
          </div>
        </div>
        <template #footer>
          <el-button @click="showImportDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="importTemplate"
            :disabled="!selectedFile"
          >
            导入
          </el-button>
        </template>
      </el-dialog>
      
      <!-- 编辑模板对话框 -->
      <el-dialog
        v-model="showEditDialog"
        title="编辑模板"
        width="600px"
      >
        <div v-if="editingTemplate">
          <el-form :model="editingTemplate" label-width="100px">
            <el-form-item label="模板名称">
              <el-input v-model="editingTemplate.name" />
            </el-form-item>
            <el-form-item label="模板描述">
              <el-input 
                v-model="editingTemplate.description" 
                type="textarea"
                rows="3"
              />
            </el-form-item>
            <el-form-item label="模板图标">
              <el-input v-model="editingTemplate.icon" placeholder="输入emoji图标" />
            </el-form-item>
          </el-form>
        </div>
        <template #footer>
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="saveEditTemplate"
          >
            保存
          </el-button>
        </template>
      </el-dialog>
    </div>
  `,
  
  data() {
    return {
      activeCategory: 'buyGift',
      searchQuery: '',
      selectedTemplate: null,
      showImportDialog: false,
      showEditDialog: false,
      selectedFile: null,
      editingTemplate: null,
      allTemplates: []
    };
  },
  
  computed: {
    filteredTemplates() {
      const templates = {
        buyGift: [],
        discount: [],
        bundle: [],
        custom: []
      };
      
      this.allTemplates.forEach(template => {
        if (this.searchQuery) {
          const query = this.searchQuery.toLowerCase();
          if (!template.name.toLowerCase().includes(query) && 
              !template.description.toLowerCase().includes(query)) {
            return;
          }
        }
        
        if (template.category === 'custom') {
          templates.custom.push(template);
        } else {
          templates[template.category]?.push(template);
        }
      });
      
      return templates;
    }
  },
  
  mounted() {
    this.loadTemplates();
  },
  
  methods: {
    loadTemplates() {
      this.allTemplates = window.TemplateManager.getAllTemplates();
    },
    
    onCategoryChange(category) {
      this.activeCategory = category;
      this.clearSelection();
    },
    
    selectTemplate(template) {
      this.selectedTemplate = template;
    },
    
    clearSelection() {
      this.selectedTemplate = null;
    },
    
    applyTemplate() {
      if (this.selectedTemplate) {
        const templateData = window.TemplateManager.applyTemplate(this.selectedTemplate.id);
        if (templateData) {
          this.$emit('template-applied', templateData);
          this.clearSelection();
          this.$message.success('模板已应用');
        } else {
          this.$message.error('应用模板失败');
        }
      }
    },
    
    exportTemplate(templateId) {
      try {
        window.TemplateManager.exportTemplate(templateId);
        this.$message.success('模板已导出');
      } catch (error) {
        this.$message.error('导出失败: ' + error.message);
      }
    },
    
    deleteTemplate(templateId) {
      this.$confirm('确定要删除这个模板吗？', '确认删除', {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const deleted = window.TemplateManager.deleteCustomTemplate(templateId);
        if (deleted) {
          this.loadTemplates();
          if (this.selectedTemplate?.id === templateId) {
            this.clearSelection();
          }
          this.$message.success('模板已删除');
        } else {
          this.$message.error('删除失败');
        }
      }).catch(() => {
        // 用户取消删除
      });
    },
    
    editTemplate(template) {
      this.editingTemplate = { ...template };
      this.showEditDialog = true;
    },
    
    saveEditTemplate() {
      if (this.editingTemplate) {
        const updated = window.TemplateManager.updateCustomTemplate(
          this.editingTemplate.id,
          {
            name: this.editingTemplate.name,
            description: this.editingTemplate.description,
            icon: this.editingTemplate.icon
          }
        );
        
        if (updated) {
          this.loadTemplates();
          this.showEditDialog = false;
          this.$message.success('模板已更新');
        } else {
          this.$message.error('更新失败');
        }
      }
    },
    
    onTemplateFileSelected(event) {
      this.selectedFile = event.target.files[0];
    },
    
    async importTemplate() {
      if (!this.selectedFile) {
        this.$message.warning('请选择文件');
        return;
      }
      
      try {
        const imported = await window.TemplateManager.importTemplate(this.selectedFile);
        this.loadTemplates();
        this.showImportDialog = false;
        this.selectedFile = null;
        this.$refs.templateFileInput.value = '';
        this.$message.success('模板导入成功: ' + imported.name);
      } catch (error) {
        this.$message.error('导入失败: ' + error.message);
      }
    },
    
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN');
    }
  }
};

// 全局注册组件
if (window.Vue && window.Vue.createApp) {
  window.TemplateSelector = TemplateSelector;
}
