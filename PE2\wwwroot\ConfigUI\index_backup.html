<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>促销规则配置管理系统 - 专业版</title>
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3.3.8/dist/vue.global.js"></script>
    
    <!-- Element Plus UI Framework -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    
    <!-- Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.iife.min.js"></script>
      <!-- 自定义样式 -->    <style>
        /* Ant Design 设计变量 */
        :root {
            --ant-primary-color: #1890ff;
            --ant-primary-color-hover: #40a9ff;
            --ant-primary-color-active: #096dd9;
            --ant-success-color: #52c41a;
            --ant-warning-color: #faad14;
            --ant-error-color: #ff4d4f;
            --ant-text-color: rgba(0, 0, 0, 0.85);
            --ant-text-color-secondary: rgba(0, 0, 0, 0.65);
            --ant-text-color-disabled: rgba(0, 0, 0, 0.25);
            --ant-background-color-base: #f0f2f5;
            --ant-component-background: #ffffff;
            --ant-border-color-base: #d9d9d9;
            --ant-border-color-split: #f0f0f0;
            --ant-border-radius-base: 6px;
            --ant-box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
            --ant-padding-lg: 24px;
            --ant-padding-md: 16px;
            --ant-padding-sm: 12px;
            --ant-padding-xs: 8px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ant-background-color-base);
            color: var(--ant-text-color);
            font-size: 14px;
            line-height: 1.5715;
            margin: 0;
            padding: 0;
        }        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* 头部样式 */
        .header {
            background: var(--ant-component-background);
            border-bottom: 1px solid var(--ant-border-color-split);
            padding: var(--ant-padding-md) var(--ant-padding-lg);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: var(--ant-padding-sm);
        }

        .header-title h1 {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
            color: var(--ant-text-color);
        }

        .header-subtitle {
            font-size: 14px;
            color: var(--ant-text-color-secondary);
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: var(--ant-padding-xs);
        }        /* 布局样式 */
        .layout {
            display: flex;
            flex: 1;
            min-height: 0;
        }

        .sidebar {
            width: 280px;
            background: var(--ant-component-background);
            border-right: 1px solid var(--ant-border-color-split);
            overflow-y: auto;
        }

        .sidebar-section {
            padding: var(--ant-padding-md);
        }

        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--ant-text-color);
            margin-bottom: var(--ant-padding-sm);
            display: flex;
            align-items: center;
            gap: var(--ant-padding-xs);
        }        /* 促销类型卡片样式 */
        .promotion-type-grid {
            display: flex;
            flex-direction: column;
            gap: var(--ant-padding-xs);
        }

        .promotion-type-card {
            padding: var(--ant-padding-sm);
            border: 1px solid var(--ant-border-color-base);
            border-radius: var(--ant-border-radius-base);
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            background: var(--ant-component-background);
        }

        .promotion-type-card:hover {
            border-color: var(--ant-primary-color-hover);
        }

        .promotion-type-card.active {
            background: #e6f7ff;
            border-color: var(--ant-primary-color);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: var(--ant-padding-xs);
            margin-bottom: 4px;
        }        .card-icon {
            font-size: 16px;
            color: var(--ant-primary-color);
        }

        .card-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--ant-text-color);
        }

        .card-description {
            font-size: 12px;
            color: var(--ant-text-color-secondary);
            line-height: 1.4;
        }

        /* 主面板样式 */
        .main-panel {
            flex: 1;
            background: var(--ant-component-background);
            padding: var(--ant-padding-lg);
            overflow-y: auto;
        }        /* Ant Design 风格步骤指示器 */
        .step-indicator {
            display: flex;
            align-items: center;
            margin-bottom: var(--ant-padding-lg);
            padding: var(--ant-padding-lg) 0;
        }

        .step {
            flex: 1;
            display: flex;
            align-items: center;
            position: relative;
            cursor: pointer;
        }

        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 16px;
            right: -50%;
            width: 100%;
            height: 1px;
            background: var(--ant-border-color-split);
            z-index: 0;
        }

        .step.completed:not(:last-child)::after {
            background: var(--ant-primary-color);
        }

        .step-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 600;
            border: 2px solid var(--ant-border-color-base);
            background: var(--ant-component-background);
            color: var(--ant-text-color-secondary);
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            z-index: 1;
            position: relative;
        }

        .step-content {
            margin-left: var(--ant-padding-sm);
            flex: 1;
        }

        .step-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--ant-text-color-secondary);
            line-height: 1.4;
            transition: color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        }

        .step-description {
            font-size: 12px;
            color: var(--ant-text-color-disabled);
            margin-top: 2px;
            line-height: 1.3;
        }

        /* 激活状态 */
        .step.active .step-icon {
            background: var(--ant-primary-color);
            border-color: var(--ant-primary-color);
            color: white;
        }

        .step.active .step-title {
            color: var(--ant-primary-color);
            font-weight: 600;
        }

        .step.active .step-description {
            color: var(--ant-text-color-secondary);
        }

        /* 完成状态 */
        .step.completed .step-icon {
            background: var(--ant-primary-color);
            border-color: var(--ant-primary-color);
            color: white;
        }

        .step.completed .step-title {
            color: var(--ant-text-color);
        }

        .step.completed .step-description {
            color: var(--ant-text-color-secondary);
        }

        /* 悬浮效果 */
        .step:hover .step-icon {
            border-color: var(--ant-primary-color-hover);
        }

        .step:hover .step-title {
            color: var(--ant-primary-color-hover);
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .step-content {
                margin-left: var(--ant-padding-xs);
            }
            
            .step-icon {
                width: 28px;
                height: 28px;
                font-size: 14px;
            }
            
            .step-title {
                font-size: 13px;
            }
            
            .step-description {
                display: none;
            }
        }        /* Ant Design 风格表单区域 */
        .form-container {
            max-width: 800px;
        }

        .form-section {
            background: var(--ant-component-background);
            border: 1px solid var(--ant-border-color-split);
            border-radius: var(--ant-border-radius-base);
            padding: var(--ant-padding-lg);
            margin-bottom: var(--ant-padding-lg);
            transition: box-shadow 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        }

        .form-section:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: var(--ant-padding-sm);
            margin-bottom: var(--ant-padding-lg);
            padding-bottom: var(--ant-padding-md);
            border-bottom: 1px solid var(--ant-border-color-split);
        }

        .section-icon {
            width: 32px;
            height: 32px;
            border-radius: var(--ant-border-radius-base);
            background: #f0f9ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--ant-primary-color);
            font-size: 16px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--ant-text-color);
            margin: 0;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--ant-padding-lg);
        }/* 右侧预览面板样式 */
        .right-panel {
            width: 400px;
            background: var(--ant-background-color-base);
            border-left: 1px solid var(--ant-border-color-split);
            display: flex;
            flex-direction: column;
        }

        .preview-header {
            padding: var(--ant-padding-lg);
            border-bottom: 1px solid var(--ant-border-color-split);
            background: var(--ant-component-background);
        }

        .preview-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--ant-text-color);
            margin-bottom: var(--ant-padding-xs);
            display: flex;
            align-items: center;
            gap: var(--ant-padding-xs);
        }

        .preview-subtitle {
            font-size: 14px;
            color: var(--ant-text-color-secondary);
        }

        .preview-content {
            flex: 1;
            padding: var(--ant-padding-lg);
            overflow-y: auto;
        }

        .json-editor {
            background: #f6f8fa;
            color: #24292f;
            border-radius: var(--ant-border-radius-base);
            padding: var(--ant-padding-md);
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 12px;
            line-height: 1.45;
            overflow-x: auto;            border: 1px solid var(--ant-border-color-base);
            min-height: 300px;
            white-space: pre-wrap;
        }

        /* 验证状态样式 */
        .validation-status {
            margin-top: var(--ant-padding-md);
            padding: var(--ant-padding-sm);
            border-radius: var(--ant-border-radius-base);
            font-size: 14px;
        }

        .validation-status.valid {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: var(--ant-success-color);
        }

        .validation-status.invalid {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: var(--ant-error-color);
        }

        .action-buttons {
            padding: var(--ant-padding-lg);
            border-top: 1px solid var(--ant-border-color-split);
            background: var(--ant-component-background);
            display: flex;
            gap: var(--ant-padding-xs);
        }        /* 响应式设计 */
        @media (max-width: 1200px) {
            .right-panel {
                width: 350px;
            }
        }

        @media (max-width: 768px) {
            .layout {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                order: 2;
                max-height: 300px;
            }
            
            .main-panel {
                padding: var(--ant-padding-md);
                order: 1;
            }
            
            .right-panel {
                width: 100%;
                order: 3;
                max-height: 400px;
            }

            .header {
                padding: var(--ant-padding-sm) var(--ant-padding-md);
            }

            .header-title h1 {
                font-size: 18px;
            }

            .header-actions {
                flex-wrap: wrap;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }
        }/* Element Plus 组件样式覆写 */
        .el-button--primary {
            background-color: var(--ant-primary-color) !important;
            border-color: var(--ant-primary-color) !important;
        }

        .el-button--primary:hover {
            background-color: var(--ant-primary-color-hover) !important;
            border-color: var(--ant-primary-color-hover) !important;
        }

        .el-button--primary:active {
            background-color: var(--ant-primary-color-active) !important;
            border-color: var(--ant-primary-color-active) !important;
        }

        .el-input__wrapper {
            border-radius: var(--ant-border-radius-base) !important;
        }

        .el-select .el-input__wrapper {
            border-radius: var(--ant-border-radius-base) !important;
        }

        /* Element Plus 表单组件优化 */
        .el-form-item {
            margin-bottom: var(--ant-padding-lg) !important;
        }

        .el-form-item__label {
            font-size: 14px !important;
            font-weight: 500 !important;
            color: var(--ant-text-color) !important;
            line-height: 1.5715 !important;
            padding-bottom: var(--ant-padding-xs) !important;
        }

        .el-input__wrapper {
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
        }

        .el-input__wrapper:hover {
            border-color: var(--ant-primary-color-hover) !important;
        }

        .el-input__wrapper.is-focus {
            border-color: var(--ant-primary-color) !important;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
        }

        .el-textarea__inner {
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
        }

        .el-textarea__inner:hover {
            border-color: var(--ant-primary-color-hover) !important;
        }

        .el-textarea__inner:focus {
            border-color: var(--ant-primary-color) !important;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
        }

        .el-input-number {
            width: 100%;
        }

        .el-select .el-input__wrapper:hover {
            border-color: var(--ant-primary-color-hover) !important;
        }

        .el-select .el-input.is-focus .el-input__wrapper {
            border-color: var(--ant-primary-color) !important;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
        }

        /* 步骤导航按钮区域优化 */
        .step-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: var(--ant-padding-lg);
            padding-top: var(--ant-padding-lg);
            border-top: 1px solid var(--ant-border-color-split);
        }

        .step-navigation .el-button {
            min-width: 100px;
        }        /* 简洁的过渡动画 */
        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.2s ease;
        }

        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }/* 商品选择器样式 */
        .product-selector {
            border: 1px dashed var(--ant-border-color-base);
            border-radius: var(--ant-border-radius-base);
            padding: var(--ant-padding-md);
            text-align: center;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            background: var(--ant-component-background);
        }

        .product-selector:hover {
            border-color: var(--ant-primary-color);
            background: #f0f9ff;
        }

        .product-tags {
            display: flex;
            flex-wrap: wrap;
            gap: var(--ant-padding-xs);
            margin-bottom: var(--ant-padding-sm);
        }

        .empty-state {
            text-align: center;
            padding: 80px 20px;
            color: var(--ant-text-color-secondary);
        }        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 20px;
            color: var(--ant-text-color-disabled);
        }

        /* 浮动帮助按钮 */
        .floating-helper {
            position: fixed;
            bottom: var(--ant-padding-lg);
            right: var(--ant-padding-lg);
            width: 56px;
            height: 56px;
            background: var(--ant-primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: var(--ant-box-shadow-base);
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            z-index: 1000;
        }        .floating-helper:hover {
            background: var(--ant-primary-color-hover);
            transform: translateY(-2px);
        }        /* 预览模态框样式 */
        .preview-dialog .el-dialog {
            border-radius: var(--ant-border-radius-base);
        }

        .preview-container {
            max-height: 600px;
            overflow-y: auto;
        }

        .rule-overview {
            background: var(--ant-component-background);
            border: 1px solid var(--ant-border-color-split);
            border-radius: var(--ant-border-radius-base);
            padding: var(--ant-padding-lg);
            margin-bottom: var(--ant-padding-lg);
        }

        .rule-info h3 {
            font-size: 18px;
            font-weight: 600;
            color: var(--ant-text-color);
            margin-bottom: var(--ant-padding-sm);
        }

        .rule-meta {
            display: flex;
            gap: var(--ant-padding-xs);
            margin-bottom: var(--ant-padding-sm);
            flex-wrap: wrap;
        }

        .rule-description {
            color: var(--ant-text-color-secondary);
            font-size: 14px;
            line-height: 1.5;
            margin: 0;
        }

        .preview-scenarios h4,
        .benefit-summary h4,
        .calculation-steps h4 {
            font-size: 16px;
            font-weight: 600;
            color: var(--ant-text-color);
            margin-bottom: var(--ant-padding-md);
        }

        .scenario-card {
            background: var(--ant-component-background);
            border: 1px solid var(--ant-border-color-split);
            border-radius: var(--ant-border-radius-base);
            padding: var(--ant-padding-lg);
            margin-bottom: var(--ant-padding-md);
        }

        .scenario-header h5 {
            font-size: 16px;
            font-weight: 600;
            color: var(--ant-text-color);
            margin-bottom: 4px;
        }

        .scenario-header p {
            color: var(--ant-text-color-secondary);
            font-size: 14px;
            margin-bottom: var(--ant-padding-md);
        }

        .cart-preview h6 {
            font-size: 14px;
            font-weight: 600;
            color: var(--ant-text-color);
            margin-bottom: var(--ant-padding-sm);
        }

        .cart-items {
            background: var(--ant-background-color-base);
            border-radius: var(--ant-border-radius-base);
            padding: var(--ant-padding-sm);
            margin-bottom: var(--ant-padding-md);
        }

        .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--ant-padding-xs) 0;
            border-bottom: 1px solid var(--ant-border-color-split);
        }

        .cart-item:last-child {
            border-bottom: none;
        }

        .item-info {
            flex: 1;
        }

        .item-name {
            font-weight: 500;
            color: var(--ant-text-color);
            margin-right: var(--ant-padding-xs);
        }

        .item-category {
            color: var(--ant-text-color-secondary);
            font-size: 12px;
            background: var(--ant-border-color-split);
            padding: 2px 6px;
            border-radius: 3px;
        }

        .item-price {
            display: flex;
            align-items: center;
            gap: var(--ant-padding-xs);
        }

        .quantity {
            color: var(--ant-text-color-secondary);
            font-size: 12px;
        }

        .price {
            font-weight: 600;
            color: var(--ant-text-color);
        }

        .calculation-result {
            background: #f0f9ff;
            border-radius: var(--ant-border-radius-base);
            padding: var(--ant-padding-md);
        }

        .result-row {
            display: flex;
            justify-content: space-between;
            padding: 4px 0;
            font-size: 14px;
        }

        .result-row.discount {
            color: var(--ant-success-color);
            font-weight: 500;
        }

        .result-row.gift {
            color: var(--ant-warning-color);
            font-weight: 500;
        }

        .result-row.total {
            border-top: 1px solid var(--ant-border-color-split);
            margin-top: var(--ant-padding-xs);
            padding-top: var(--ant-padding-xs);
            font-weight: 600;
            font-size: 16px;
            color: var(--ant-primary-color);
        }

        .benefit-summary {
            margin-bottom: var(--ant-padding-lg);
        }

        .benefit-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--ant-padding-md);
        }

        .benefit-card {
            background: var(--ant-component-background);
            border: 1px solid var(--ant-border-color-split);
            border-radius: var(--ant-border-radius-base);
            padding: var(--ant-padding-md);
            display: flex;
            align-items: center;
            gap: var(--ant-padding-sm);
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        }

        .benefit-card:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
        }

        .benefit-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(24, 144, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .benefit-content {
            flex: 1;
        }

        .benefit-value {
            font-size: 18px;
            font-weight: 600;
            color: var(--ant-text-color);
        }

        .benefit-label {
            font-size: 12px;
            color: var(--ant-text-color-secondary);
        }

        .calculation-steps {
            margin-bottom: var(--ant-padding-lg);
        }

        .step-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--ant-primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
        }

        .preview-footer {
            display: flex;
            justify-content: flex-end;
            gap: var(--ant-padding-xs);
        }
    </style>
</head>
<body>    <div id="app">
        <!-- PromotionConfigSystem组件将在这里渲染 -->
    </div>    <!-- 加载配置和组件 -->
    <script src="./js/config/promotionTypes.js"></script>
    <script src="./js/services/apiService.js"></script>
    <script src="./js/services/metadataService.js"></script>
    <script src="./js/components/DynamicFormRenderer.js"></script>
    <script src="./js/components/RuleManager.js"></script>
    <script src="./js/components/PromotionConfigMainComponent.js"></script>
    <script>
        // 使用新的主组件
        const { createApp } = Vue;
        
        // 创建Vue应用
        const app = createApp(PromotionConfigMainComponent);
        
        // 注册Element Plus
        app.use(ElementPlus);
        
        // 注册Element Plus图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
        
        // 挂载应用
        app.mount('#app');
    </script>
            template: `
                <div class="app-container">
                    <div class="main-content">                        <!-- 顶部标题栏 -->                        <div class="header">
                            <div class="header-title">
                                <div style="width: 40px; height: 40px; background: var(--ant-primary-color); border-radius: var(--ant-border-radius-base); display: flex; align-items: center; justify-content: center; color: white;">
                                    <el-icon size="20"><Setting /></el-icon>
                                </div>
                                <div>
                                    <h1>促销规则配置管理系统</h1>
                                    <div class="header-subtitle">专业的促销活动配置平台</div>
                                </div>
                            </div>                            <div class="header-actions">
                                <el-button @click="showRuleManager = true">
                                    <el-icon><List /></el-icon>
                                    规则管理
                                </el-button>
                                <el-button @click="autoGenerateIds" type="info">
                                    <el-icon><Magic /></el-icon>
                                    自动生成ID
                                </el-button>
                                <el-button @click="importConfig">
                                    <el-icon><Upload /></el-icon>
                                    导入配置
                                </el-button>
                                <el-button @click="saveToBackend" type="success" :loading="saving">
                                    <el-icon><CloudUpload /></el-icon>
                                    保存到后端
                                </el-button>
                                <el-button type="primary" @click="exportConfig" :disabled="!isValidConfig">
                                    <el-icon><Download /></el-icon>
                                    导出配置
                                </el-button>
                            </div>
                        </div>

                        <!-- 主布局 -->
                        <div class="layout">
                            <!-- 左侧促销类型选择 -->
                            <div class="sidebar">
                                <div class="sidebar-section">
                                    <div class="sidebar-title">
                                        <el-icon><Grid /></el-icon>
                                        促销类型
                                    </div>
                                    <div class="promotion-type-grid">
                                        <div 
                                            v-for="(category, key) in promotionTypes" 
                                            :key="key"
                                            class="promotion-type-card"
                                            :class="{ active: selectedCategory === key }"
                                            @click="selectCategory(key, category)"
                                        >
                                            <div class="card-header">
                                                <div class="card-icon">{{ category.icon }}</div>
                                                <div class="card-title">{{ category.name }}</div>
                                            </div>
                                            <div class="card-description">
                                                {{ Object.keys(category.types).length }} 个配置方案
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div v-if="selectedCategory" class="sidebar-section">
                                    <div class="sidebar-title">
                                        <el-icon><Setting /></el-icon>
                                        配置方案
                                    </div>
                                    <div class="promotion-type-grid">
                                        <div 
                                            v-for="(type, typeKey) in selectedCategoryData.types" 
                                            :key="typeKey"
                                            class="promotion-type-card"
                                            :class="{ active: selectedType === typeKey }"
                                            @click="selectType(typeKey, type)"
                                            style="margin-bottom: 8px;"
                                        >
                                            <div class="card-title" style="font-size: 14px; margin-bottom: 4px;">
                                                {{ type.name }}
                                            </div>
                                            <div class="card-description" style="font-size: 12px;">
                                                {{ type.description }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 中间配置表单 -->
                            <div class="main-panel">                                <div v-if="!selectedType" class="empty-state">
                                    <div class="empty-state-icon">
                                        <el-icon><Grid /></el-icon>
                                    </div>
                                    <h3 style="margin-bottom: 12px;">选择促销类型开始配置</h3>
                                    <p>从左侧选择您需要的促销类型，开始创建专业的促销规则</p>
                                </div>

                                <div v-if="selectedType" class="form-container">                                    <!-- Ant Design 风格步骤指示器 -->
                                    <div class="step-indicator">
                                        <div 
                                            class="step" 
                                            :class="{ active: currentStep === 1, completed: currentStep > 1 }"
                                            @click="currentStep = 1"
                                        >
                                            <div class="step-icon">
                                                <el-icon v-if="currentStep > 1"><Check /></el-icon>
                                                <span v-else>1</span>
                                            </div>
                                            <div class="step-content">
                                                <div class="step-title">基础信息</div>
                                                <div class="step-description">配置规则基本信息</div>
                                            </div>
                                        </div>
                                        <div 
                                            class="step" 
                                            :class="{ active: currentStep === 2, completed: currentStep > 2 }"
                                            @click="currentStep = 2"
                                        >
                                            <div class="step-icon">
                                                <el-icon v-if="currentStep > 2"><Check /></el-icon>
                                                <span v-else>2</span>
                                            </div>
                                            <div class="step-content">
                                                <div class="step-title">条件配置</div>
                                                <div class="step-description">设置促销触发条件</div>
                                            </div>
                                        </div>
                                        <div 
                                            class="step" 
                                            :class="{ active: currentStep === 3 }"
                                            @click="currentStep = 3"
                                        >
                                            <div class="step-icon">
                                                <span>3</span>
                                            </div>
                                            <div class="step-content">
                                                <div class="step-title">高级设置</div>
                                                <div class="step-description">配置高级选项</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 表单内容 -->
                                    <transition name="fade" mode="out-in">
                                        <div v-if="currentStep === 1" key="step1">
                                            <div class="form-section">                                                <div class="section-header">
                                                    <div class="section-icon">
                                                        <el-icon><Edit /></el-icon>
                                                    </div>
                                                    <div class="section-title">基础信息配置</div>
                                                </div>
                                                
                                                <el-form :model="formData" label-width="120px" label-position="top">
                                                    <div class="form-grid">
                                                        <el-form-item label="规则ID" required>
                                                            <el-input 
                                                                v-model="formData.id" 
                                                                placeholder="输入唯一的规则标识符"
                                                                :class="{ 'is-error': errors.id }"
                                                            />
                                                            <div v-if="errors.id" style="color: var(--danger-color); font-size: 12px; margin-top: 4px;">
                                                                {{ errors.id }}
                                                            </div>
                                                        </el-form-item>
                                                        
                                                        <el-form-item label="规则名称" required>
                                                            <el-input 
                                                                v-model="formData.name" 
                                                                placeholder="输入规则显示名称"
                                                                :class="{ 'is-error': errors.name }"
                                                            />
                                                            <div v-if="errors.name" style="color: var(--danger-color); font-size: 12px; margin-top: 4px;">
                                                                {{ errors.name }}
                                                            </div>
                                                        </el-form-item>
                                                    </div>

                                                    <el-form-item label="规则描述">
                                                        <el-input 
                                                            type="textarea" 
                                                            v-model="formData.description" 
                                                            placeholder="详细描述这个促销规则的作用和适用场景"
                                                            :rows="3"
                                                        />
                                                    </el-form-item>

                                                    <div class="form-grid">
                                                        <el-form-item label="优先级">
                                                            <el-input-number 
                                                                v-model="formData.priority" 
                                                                :min="0" 
                                                                :max="999"
                                                                style="width: 100%;"
                                                            />
                                                            <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                                                                数字越大优先级越高
                                                            </div>
                                                        </el-form-item>
                                                        
                                                        <el-form-item label="规则状态">
                                                            <el-switch 
                                                                v-model="formData.isEnabled"
                                                                active-text="启用"
                                                                inactive-text="禁用"
                                                                style="--el-switch-on-color: var(--ant-primary-color);"
                                                            />
                                                        </el-form-item>
                                                    </div>

                                                    <div class="form-grid">
                                                        <el-form-item label="生效时间">
                                                            <el-date-picker
                                                                v-model="formData.startTime"
                                                                type="datetime"
                                                                placeholder="选择开始时间"
                                                                style="width: 100%;"
                                                            />
                                                        </el-form-item>
                                                        
                                                        <el-form-item label="失效时间">
                                                            <el-date-picker
                                                                v-model="formData.endTime"
                                                                type="datetime"
                                                                placeholder="选择结束时间"
                                                                style="width: 100%;"
                                                            />
                                                        </el-form-item>
                                                    </div>
                                                </el-form>
                                            </div>
                                        </div>

                                        <div v-else-if="currentStep === 2" key="step2">
                                            <div class="form-section">                                                <div class="section-header">
                                                    <div class="section-icon">
                                                        <el-icon><Setting /></el-icon>
                                                    </div>
                                                    <div class="section-title">促销条件配置</div>
                                                </div>
                                                
                                                <div v-if="selectedTypeData && selectedTypeData.fields">
                                                    <div v-for="field in conditionFields" :key="field.name">
                                                        <advanced-form-field 
                                                            :field="field"
                                                            :value="formData[field.name]"
                                                            @update:value="updateField(field.name, $event)"
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div v-else-if="currentStep === 3" key="step3">
                                            <div class="form-section">                                                <div class="section-header">
                                                    <div class="section-icon">
                                                        <el-icon><Tools /></el-icon>
                                                    </div>
                                                    <div class="section-title">高级设置</div>
                                                </div>
                                                
                                                <el-form :model="formData" label-width="120px" label-position="top">
                                                    <div class="form-grid">
                                                        <el-form-item label="最大应用次数">
                                                            <el-input-number 
                                                                v-model="formData.maxApplications" 
                                                                :min="0"
                                                                placeholder="0表示无限制"
                                                                style="width: 100%;"
                                                            />
                                                        </el-form-item>
                                                        
                                                        <el-form-item label="是否可重复应用">
                                                            <el-switch 
                                                                v-model="formData.isRepeatable"
                                                                active-text="是"
                                                                inactive-text="否"
                                                            />
                                                        </el-form-item>
                                                    </div>

                                                    <div class="form-grid">
                                                        <el-form-item label="是否可与其他促销叠加">
                                                            <el-switch 
                                                                v-model="formData.canStackWithOthers"
                                                                active-text="是"
                                                                inactive-text="否"
                                                            />
                                                        </el-form-item>
                                                        
                                                        <el-form-item label="商品互斥级别">
                                                            <el-select v-model="formData.productExclusivity" style="width: 100%;">
                                                                <el-option label="无互斥" value="None" />
                                                                <el-option label="同类型互斥" value="SameType" />
                                                                <el-option label="全部互斥" value="All" />
                                                            </el-select>
                                                        </el-form-item>
                                                    </div>

                                                    <el-form-item label="适用客户类型">
                                                        <el-select 
                                                            v-model="formData.applicableCustomerTypes" 
                                                            multiple 
                                                            filterable 
                                                            allow-create
                                                            placeholder="选择或输入客户类型"
                                                            style="width: 100%;"
                                                        >
                                                            <el-option label="VIP客户" value="VIP" />
                                                            <el-option label="普通会员" value="MEMBER" />
                                                            <el-option label="新用户" value="NEW_USER" />
                                                            <el-option label="企业客户" value="ENTERPRISE" />
                                                        </el-select>
                                                    </el-form-item>

                                                    <el-form-item label="互斥规则ID">
                                                        <el-select 
                                                            v-model="formData.exclusiveRuleIds" 
                                                            multiple 
                                                            filterable 
                                                            allow-create
                                                            placeholder="输入互斥的规则ID"
                                                            style="width: 100%;"
                                                        >
                                                        </el-select>
                                                    </el-form-item>
                                                </el-form>
                                            </div>
                                        </div>
                                    </transition>                                    <!-- 步骤导航按钮 -->
                                    <div class="step-navigation">
                                        <el-button 
                                            v-if="currentStep > 1" 
                                            @click="currentStep--"
                                            size="large"
                                        >
                                            <el-icon><ArrowLeft /></el-icon>
                                            上一步
                                        </el-button>
                                        <div v-else></div>
                                        
                                        <el-button 
                                            v-if="currentStep < 3" 
                                            type="primary" 
                                            @click="nextStep"
                                            size="large"
                                        >
                                            下一步
                                            <el-icon><ArrowRight /></el-icon>
                                        </el-button>
                                        <el-button 
                                            v-else 
                                            type="primary" 
                                            @click="validateAndFinish"
                                            size="large"
                                        >
                                            <el-icon><Check /></el-icon>
                                            完成配置
                                        </el-button>
                                    </div>
                                </div>
                            </div>

                            <!-- 右侧预览面板 -->
                            <div class="right-panel">
                                <div class="preview-header">
                                    <div class="preview-title">
                                        <el-icon><View /></el-icon>
                                        实时预览
                                    </div>
                                    <div class="preview-subtitle">配置将实时生成JSON格式</div>
                                </div>

                                <div class="preview-content">
                                    <div class="json-editor">{{ formattedJson }}</div>
                                    
                                    <div class="validation-status" :class="isValidConfig ? 'valid' : 'invalid'">
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <el-icon v-if="isValidConfig"><SuccessFilled /></el-icon>
                                            <el-icon v-else><WarningFilled /></el-icon>
                                            {{ isValidConfig ? '配置验证通过' : '配置存在问题' }}
                                        </div>
                                        <div v-if="!isValidConfig" style="font-size: 12px; margin-top: 4px;">
                                            {{ validationMessage }}
                                        </div>
                                    </div>
                                </div>

                                <div class="action-buttons">
                                    <el-button @click="copyToClipboard" size="small">
                                        <el-icon><DocumentCopy /></el-icon>
                                        复制
                                    </el-button>
                                    <el-button @click="previewRule" size="small">
                                        <el-icon><View /></el-icon>
                                        预览
                                    </el-button>
                                    <el-button type="primary" @click="exportConfig" :disabled="!isValidConfig" size="small">
                                        <el-icon><Download /></el-icon>
                                        导出
                                    </el-button>
                                </div>
                            </div>
                        </div>
                    </div>                    <!-- 浮动帮助按钮 -->
                    <div class="floating-helper" @click="showHelp">
                        <el-icon><QuestionFilled /></el-icon>
                    </div>

                    <!-- 预览模态框 -->
                    <el-dialog 
                        v-model="showPreviewModal" 
                        title="促销规则预览" 
                        width="900px"
                        :before-close="() => showPreviewModal = false"
                        class="preview-dialog"
                    >
                        <div class="preview-container">
                            <!-- 规则信息概览 -->
                            <div class="rule-overview">
                                <div class="rule-info">
                                    <h3>{{ formData.name }}</h3>
                                    <div class="rule-meta">
                                        <el-tag type="primary">{{ selectedTypeData?.name }}</el-tag>
                                        <el-tag :type="formData.isEnabled ? 'success' : 'danger'">
                                            {{ formData.isEnabled ? '已启用' : '已禁用' }}
                                        </el-tag>
                                        <el-tag>优先级: {{ formData.priority }}</el-tag>
                                    </div>
                                    <p class="rule-description">{{ formData.description || '暂无描述' }}</p>
                                </div>
                            </div>

                            <!-- 预览场景 -->
                            <div class="preview-scenarios">
                                <h4>场景预览</h4>
                                <div v-for="(scenario, index) in previewData.scenarios" :key="index" class="scenario-card">
                                    <div class="scenario-header">
                                        <h5>{{ scenario.title }}</h5>
                                        <p>{{ scenario.description }}</p>
                                    </div>
                                    
                                    <!-- 购物车商品 -->
                                    <div class="cart-preview">
                                        <h6>购物车商品</h6>
                                        <div class="cart-items">
                                            <div v-for="item in scenario.cart" :key="item.id" class="cart-item">
                                                <div class="item-info">
                                                    <span class="item-name">{{ item.name }}</span>
                                                    <span class="item-category">{{ item.category }}</span>
                                                </div>
                                                <div class="item-price">
                                                    <span class="quantity">×{{ item.quantity }}</span>
                                                    <span class="price">¥{{ item.price }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 计算结果 -->
                                    <div class="calculation-result">
                                        <div class="result-row">
                                            <span>商品总额:</span>
                                            <span>¥{{ scenario.result.originalTotal }}</span>
                                        </div>
                                        <div v-if="scenario.result.discount" class="result-row discount">
                                            <span>促销优惠:</span>
                                            <span>-¥{{ scenario.result.discount }}</span>
                                        </div>
                                        <div v-if="scenario.result.gifts" class="result-row gift">
                                            <span>获得赠品:</span>
                                            <span>{{ scenario.result.gifts.map(g => g.name).join(', ') }}</span>
                                        </div>
                                        <div class="result-row total">
                                            <span>应付金额:</span>
                                            <span>¥{{ scenario.result.finalTotal }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 优惠效果统计 -->
                            <div class="benefit-summary">
                                <h4>优惠效果</h4>
                                <div class="benefit-cards">
                                    <div v-for="benefit in previewData.benefits" :key="benefit.label" class="benefit-card">
                                        <div class="benefit-icon" :style="{ color: benefit.color }">
                                            <el-icon><component :is="benefit.icon" /></el-icon>
                                        </div>
                                        <div class="benefit-content">
                                            <div class="benefit-value">{{ benefit.value }}</div>
                                            <div class="benefit-label">{{ benefit.label }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 计算步骤 -->
                            <div class="calculation-steps">
                                <h4>计算步骤</h4>
                                <el-steps direction="vertical" :active="4">
                                    <el-step 
                                        v-for="step in previewData.calculation" 
                                        :key="step.step"
                                        :title="step.description"
                                        :description="step.calculation"
                                        status="finish"
                                    >
                                        <template #icon>
                                            <div class="step-number">{{ step.step }}</div>
                                        </template>
                                    </el-step>
                                </el-steps>
                            </div>
                        </div>
                          <template #footer>
                            <div class="preview-footer">
                                <el-button @click="showPreviewModal = false">关闭预览</el-button>
                                <el-button type="primary" @click="exportConfig" :disabled="!isValidConfig">
                                    <el-icon><Download /></el-icon>
                                    导出配置
                                </el-button>
                            </div>
                        </template>
                    </el-dialog>

                    <!-- 规则管理器弹窗 -->
                    <el-dialog 
                        v-model="showRuleManager" 
                        title="促销规则管理" 
                        width="1200px"
                        :before-close="() => showRuleManager = false"
                        fullscreen
                    >
                        <rule-manager
                            :visible="showRuleManager"
                            @edit-rule="editExistingRule"
                            @view-rule="viewExistingRule"
                            @create-rule="showRuleManager = false"
                            @rules-updated="onRulesUpdated"
                        />
                    </el-dialog>
                </div>
            `,
            data() {
                return {
                    promotionTypes: window.PROMOTION_TYPES || {},
                    selectedCategory: null,
                    selectedCategoryData: null,
                    selectedType: null,
                    selectedTypeData: null,
                    currentStep: 1,                    formData: {
                        id: '',
                        name: '',
                        description: '',
                        priority: 0,
                        isEnabled: true,
                        startTime: null,
                        endTime: null,
                        isRepeatable: true,
                        maxApplications: 0,
                        canStackWithOthers: true,
                        productExclusivity: 'None',
                        applicableCustomerTypes: [],
                        exclusiveRuleIds: []
                    },
                    errors: {},                    showPreviewModal: false,
                    showRuleManager: false,
                    saving: false,
                    previewData: {
                        scenarios: [],
                        calculation: null,
                        benefits: []
                    }
                };
            },
            computed: {
                conditionFields() {
                    if (!this.selectedTypeData || !this.selectedTypeData.fields) return [];
                    return this.selectedTypeData.fields.filter(field => 
                        field.type === 'array' || field.name.includes('Condition')
                    );
                },
                formattedJson() {
                    const config = { ...this.formData };
                    if (this.selectedType) {
                        config.ruleType = this.selectedType;
                    }
                    return JSON.stringify(config, null, 2);
                },
                isValidConfig() {
                    return this.formData.id && this.formData.name && this.selectedType;
                },
                validationMessage() {
                    if (!this.formData.id) return '请输入规则ID';
                    if (!this.formData.name) return '请输入规则名称';
                    if (!this.selectedType) return '请选择促销类型';
                    return '';
                }
            },
            methods: {
                selectCategory(key, category) {
                    this.selectedCategory = key;
                    this.selectedCategoryData = category;
                    this.selectedType = null;
                    this.selectedTypeData = null;
                },
                selectType(typeKey, type) {
                    this.selectedType = typeKey;
                    this.selectedTypeData = type;
                    this.currentStep = 1;
                    this.initFormData();
                },                initFormData() {
                    if (this.selectedTypeData && this.selectedTypeData.fields) {
                        this.selectedTypeData.fields.forEach(field => {
                            if (!this.formData.hasOwnProperty(field.name)) {
                                if (field.type === 'array') {
                                    this.formData[field.name] = [];
                                } else if (field.type === 'switch') {
                                    this.formData[field.name] = field.default || false;
                                } else if (field.type === 'number') {
                                    this.formData[field.name] = field.default || 0;
                                } else {
                                    this.formData[field.name] = field.default || '';
                                }
                            }
                        });
                    }
                },                updateField(name, value) {
                    this.formData[name] = value;
                    this.validateField(name);
                },                validateField(name) {
                    delete this.errors[name];
                    
                    if ((name === 'id' || name === 'name') && !this.formData[name]) {
                        this.errors[name] = '此字段为必填项';
                    }
                },
                nextStep() {
                    if (this.currentStep === 1) {
                        this.validateField('id');
                        this.validateField('name');
                        if (Object.keys(this.errors).length > 0) {
                            return;
                        }
                    }
                    this.currentStep++;
                },                validateAndFinish() {
                    if (this.isValidConfig) {
                        this.$message.success('配置验证通过！');
                        this.currentStep = 1;
                    }
                },                copyToClipboard() {
                    navigator.clipboard.writeText(this.formattedJson).then(() => {
                        this.$message.success('JSON配置已复制到剪贴板');
                    });
                },
                exportConfig() {
                    if (!this.isValidConfig) return;
                    
                    const config = { ...this.formData, ruleType: this.selectedType };
                    const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `promotion-rule-${this.formData.id}.json`;                    a.click();
                    URL.revokeObjectURL(url);
                    
                    this.$message.success('配置文件已导出成功');
                },
                importConfig() {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = '.json';
                    input.onchange = (e) => {
                        const file = e.target.files[0];
                        if (file) {
                            const reader = new FileReader();
                            reader.onload = (e) => {                                try {
                                    const config = JSON.parse(e.target.result);
                                    this.formData = { ...this.formData, ...config };
                                    this.$message.success('配置文件导入成功');
                                } catch (error) {
                                    this.$message.error('配置文件格式错误');
                                }
                            };
                            reader.readAsText(file);
                        }
                    };
                    input.click();
                },                saveTemplate() {
                    this.$message.info('模板保存功能开发中...');
                },                previewRule() {
                    if (!this.isValidConfig) {
                        this.$message.warning('请先完成基本配置');
                        return;
                    }
                    
                    this.showPreviewModal = true;
                    this.generatePreviewData();
                },showHelp() {
                    this.$alert(
                        '1. 选择促销类型\n2. 填写基础信息\n3. 配置促销条件\n4. 设置高级选项\n5. 导出配置文件',
                        '使用帮助',
                        { confirmButtonText: '确定' }
                    );                },

                // API集成方法
                async saveToBackend() {
                    if (!this.isValidConfig) {
                        this.$message.warning('请先完成基本配置');
                        return;
                    }

                    this.saving = true;
                    try {
                        const config = { ...this.formData, promotionType: this.selectedType };
                        const result = await window.apiService.addPromotionRule(config);
                        this.$message.success('规则已成功保存到后端');
                        console.log('保存结果:', result);
                    } catch (error) {
                        this.$message.error('保存失败: ' + error.message);
                        console.error('保存错误:', error);
                    } finally {
                        this.saving = false;
                    }
                },

                autoGenerateIds() {
                    if (this.selectedType) {
                        this.formData.id = window.apiService.generateRuleId(this.selectedType);
                        this.formData.name = window.apiService.generateRuleName(this.selectedType, this.selectedType);
                        this.$message.success('ID和名称已自动生成');
                    } else {
                        this.$message.warning('请先选择促销类型');
                    }
                },

                editExistingRule(rule) {
                    // 编辑已有规则
                    this.formData = { ...rule };
                    
                    // 根据规则类型选择对应的分类和类型
                    Object.keys(this.promotionTypes).forEach(categoryKey => {
                        const category = this.promotionTypes[categoryKey];
                        Object.keys(category.types).forEach(typeKey => {
                            if (typeKey === rule.promotionType) {
                                this.selectCategory(categoryKey, category);
                                this.selectType(typeKey, category.types[typeKey]);
                            }
                        });
                    });
                    
                    this.showRuleManager = false;
                    this.$message.info('规则已加载，可以进行编辑');
                },

                viewExistingRule(rule) {
                    // 查看规则详情
                    this.$alert(
                        JSON.stringify(rule, null, 2),
                        '规则详情',
                        {
                            confirmButtonText: '确定',
                            dangerouslyUseHTMLString: false
                        }
                    );
                },

                onRulesUpdated(rules) {
                    console.log('规则列表已更新:', rules);
                },
                
                generatePreviewData() {
                    // 根据促销类型生成预览场景
                    const ruleType = this.selectedType;
                    const scenarios = [];
                    
                    // 模拟购物车数据
                    const mockCart = [
                        { id: 'A001', name: '苹果iPhone 15 Pro', price: 8999, quantity: 1, category: '手机' },
                        { id: 'B001', name: 'MacBook Pro M3', price: 14999, quantity: 1, category: '电脑' },
                        { id: 'C001', name: 'AirPods Pro 2', price: 1999, quantity: 2, category: '耳机' }
                    ];
                    
                    // 根据不同规则类型生成不同场景
                    if (ruleType?.includes('FullReduction')) {
                        scenarios.push({
                            title: '满减促销场景',
                            description: '购买满指定金额享受立减优惠',
                            cart: mockCart,
                            conditions: {
                                minAmount: 10000,
                                discount: 1000
                            },
                            result: {
                                originalTotal: 25997,
                                discount: 1000,
                                finalTotal: 24997,
                                savings: 1000
                            }
                        });
                    } else if (ruleType?.includes('BuyGetGift')) {
                        scenarios.push({
                            title: '买赠促销场景',
                            description: '购买指定商品获得赠品',
                            cart: mockCart,
                            conditions: {
                                buyProducts: ['A001'],
                                giftProducts: ['C001']
                            },
                            result: {
                                originalTotal: 25997,
                                gifts: [{ name: 'AirPods Pro 2', value: 1999 }],
                                finalTotal: 25997,
                                giftValue: 1999
                            }
                        });
                    } else if (ruleType?.includes('Discount')) {
                        scenarios.push({
                            title: '折扣促销场景',
                            description: '指定商品享受折扣优惠',
                            cart: mockCart,
                            conditions: {
                                discountRate: 0.85,
                                applicableProducts: ['A001', 'B001']
                            },
                            result: {
                                originalTotal: 25997,
                                discount: 3600,
                                finalTotal: 22397,
                                savings: 3600
                            }
                        });
                    } else {
                        // 通用场景
                        scenarios.push({
                            title: '促销预览场景',
                            description: '根据当前配置的促销规则效果预览',
                            cart: mockCart,
                            conditions: {
                                ruleType: ruleType,
                                priority: this.formData.priority
                            },
                            result: {
                                originalTotal: 25997,
                                discount: 1500,
                                finalTotal: 24497,
                                savings: 1500
                            }
                        });
                    }
                    
                    this.previewData = {
                        scenarios: scenarios,
                        calculation: this.generateCalculationSteps(scenarios[0]),
                        benefits: this.generateBenefitSummary(scenarios[0])
                    };
                },
                
                generateCalculationSteps(scenario) {
                    return [
                        {
                            step: 1,
                            description: '计算购物车原始总金额',
                            calculation: '8999 + 14999 + (1999 × 2) = 25,997元',
                            result: '25,997元'
                        },
                        {
                            step: 2,
                            description: '检查促销规则适用条件',
                            calculation: '满足促销条件，可享受优惠',
                            result: '条件满足'
                        },
                        {
                            step: 3,
                            description: '计算促销优惠金额',
                            calculation: `优惠金额 = ${scenario.result.savings}元`,
                            result: `${scenario.result.savings}元`
                        },
                        {
                            step: 4,
                            description: '计算最终应付金额',
                            calculation: `${scenario.result.originalTotal} - ${scenario.result.savings} = ${scenario.result.finalTotal}元`,
                            result: `${scenario.result.finalTotal}元`
                        }
                    ];
                },
                
                generateBenefitSummary(scenario) {
                    return [
                        {
                            icon: 'Money',
                            label: '节省金额',
                            value: `¥${scenario.result.savings || 0}`,
                            color: '#52c41a'
                        },
                        {
                            icon: 'Discount',
                            label: '优惠幅度',
                            value: `${((scenario.result.savings / scenario.result.originalTotal) * 100).toFixed(1)}%`,
                            color: '#1890ff'
                        },
                        {
                            icon: 'ShoppingCart',
                            label: '适用商品',
                            value: `${scenario.cart.length}件`,
                            color: '#722ed1'
                        },
                        {
                            icon: 'Star',
                            label: '优惠等级',
                            value: this.formData.priority > 50 ? '高优先级' : '普通优惠',
                            color: '#faad14'
                        }
                    ];
                },
            }
        };

        // 高级表单字段组件
        const AdvancedFormField = {
            props: ['field', 'value'],
            emits: ['update:value'],
            template: `
                <div style="margin-bottom: 24px;">
                    <div style="font-size: 16px; font-weight: 600; margin-bottom: 16px; color: var(--text-primary);">
                        {{ field.label }}
                    </div>
                    
                    <!-- 数组类型字段 -->
                    <div v-if="field.type === 'array'" style="border: 1px solid var(--border-color); border-radius: 8px; padding: 20px; background: var(--bg-secondary);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <span style="font-weight: 500; color: var(--text-primary);">{{ field.label }}列表</span>
                            <el-button type="primary" size="small" @click="addArrayItem">
                                <el-icon><Plus /></el-icon>
                                添加{{ field.label }}
                            </el-button>
                        </div>
                        
                        <div v-if="!arrayValue.length" style="text-align: center; color: var(--text-secondary); padding: 40px 20px; border: 2px dashed var(--border-color); border-radius: 8px;">
                            <el-icon size="32"><Box /></el-icon>
                            <div style="margin-top: 12px;">暂无{{ field.label }}，点击上方按钮添加</div>
                        </div>
                        
                        <div v-for="(item, index) in arrayValue" :key="index" 
                             style="background: white; border-radius: 12px; padding: 20px; margin-bottom: 16px; border: 1px solid var(--border-color); box-shadow: var(--shadow-sm);">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                <span style="font-weight: 600; color: var(--text-primary);">{{ field.label }} #{{ index + 1 }}</span>
                                <el-button type="danger" size="small" @click="removeArrayItem(index)" text>
                                    <el-icon><Delete /></el-icon>
                                    删除
                                </el-button>
                            </div>
                            
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                                <div v-for="subField in field.fields" :key="subField.name">
                                    <label style="display: block; margin-bottom: 8px; font-weight: 500; color: var(--text-primary); font-size: 14px;">
                                        {{ subField.label }}
                                    </label>
                                    
                                    <!-- 商品选择器 -->
                                    <product-selector-advanced
                                        v-if="subField.type === 'product-selector'"
                                        :value="item[subField.name] || []"
                                        @update:value="updateArrayItemField(index, subField.name, $event)"
                                    />
                                    
                                    <!-- 数字输入 -->
                                    <el-input-number
                                        v-else-if="subField.type === 'number'"
                                        :model-value="item[subField.name] || 0"
                                        @update:model-value="updateArrayItemField(index, subField.name, $event)"
                                        style="width: 100%;"
                                        :step="subField.step || 1"
                                        :min="0"
                                    />
                                    
                                    <!-- 文本输入 -->
                                    <el-input
                                        v-else
                                        :model-value="item[subField.name] || ''"
                                        @update:model-value="updateArrayItemField(index, subField.name, $event)"
                                        :placeholder="subField.placeholder"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            computed: {
                arrayValue() {
                    return this.value || [];
                }
            },
            methods: {
                addArrayItem() {
                    const newItem = {};
                    this.field.fields.forEach(subField => {
                        if (subField.type === 'product-selector') {
                            newItem[subField.name] = [];
                        } else if (subField.type === 'number') {
                            newItem[subField.name] = subField.default || 0;
                        } else {
                            newItem[subField.name] = subField.default || '';
                        }
                    });
                    
                    const newArray = [...this.arrayValue, newItem];
                    this.$emit('update:value', newArray);
                },
                removeArrayItem(index) {
                    const newArray = this.arrayValue.filter((_, i) => i !== index);
                    this.$emit('update:value', newArray);
                },
                updateArrayItemField(index, fieldName, value) {
                    const newArray = [...this.arrayValue];
                    newArray[index] = { ...newArray[index], [fieldName]: value };
                    this.$emit('update:value', newArray);
                }
            }
        };

         // 高级商品选择器组件
        const ProductSelectorAdvanced = {
            props: ['value'],
            emits: ['update:value'],
            template: `
                <div>
                    <div v-if="selectedProducts.length" class="product-tags">
                        <el-tag 
                            v-for="product in selectedProducts" 
                            :key="product.id"
                            closable
                            @close="removeProduct(product.id)"
                            style="margin-right: 8px; margin-bottom: 4px;"
                            type="primary"
                        >
                            {{ product.name }} ({{ product.id }})
                        </el-tag>
                    </div>
                    
                    <div 
                        class="product-selector" 
                        @click="openModal"
                        :style="{ borderStyle: selectedProducts.length ? 'solid' : 'dashed' }"
                    >
                        <el-icon size="24" style="margin-bottom: 8px;"><ShoppingCart /></el-icon>
                        <div style="font-weight: 500, margin-bottom: 4px;">
                            {{ selectedProducts.length ? '修改商品选择' : '选择商品' }}
                        </div>
                        <div style="font-size: 12px; color: var(--text-secondary);">
                            已选择 {{ selectedProducts.length }} 个商品
                        </div>
                    </div>

                    <!-- 商品选择弹窗 -->
                    <el-dialog v-model="showModal" title="选择商品" width="700px" :before-close="handleClose">
                        <!-- 加载状态 -->
                        <div v-if="loading" style="text-align: center; padding: 40px;">
                            <el-icon class="is-loading" size="24"><Loading /></el-icon>
                            <div style="margin-top: 12px; color: var(--text-secondary);">正在加载商品数据...</div>
                        </div>
                        
                        <!-- 加载失败状态 -->
                        <div v-else-if="loadError" style="text-align: center; padding: 40px;">
                            <el-icon size="48" color="#F56565"><WarningFilled /></el-icon>
                            <div style="margin: 16px 0; font-weight: 500;">加载商品数据失败</div>
                            <div style="margin-bottom: 16px; color: var(--text-secondary);">{{ loadError }}</div>
                            <el-button type="primary" @click="loadProducts">重新加载</el-button>
                        </div>
                        
                        <!-- 正常内容 -->
                        <div v-else>
                            <div style="margin-bottom: 16px;">
                                <el-input
                                    v-model="searchKeyword"
                                    placeholder="搜索商品名称或ID..."
                                    style="margin-bottom: 16px;"
                                    clearable
                                >
                                    <template #prefix>
                                        <el-icon><Search /></el-icon>
                                    </template>
                                </el-input>
                                
                                <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                    <el-tag 
                                        v-for="category in productCategories" 
                                        :key="category"
                                        :type="selectedCategory === category ? 'primary' : ''"
                                        style="cursor: pointer;"
                                        @click="selectedCategory = selectedCategory === category ? '' : category"
                                    >
                                        {{ category }}
                                    </el-tag>
                                </div>
                            </div>
                            
                            <div style="max-height: 400px; overflow-y: auto;">
                                <div v-if="filteredProducts.length === 0" style="text-align: center; padding: 40px; color: var(--text-secondary);">
                                    <el-icon size="48"><DocumentRemove /></el-icon>
                                    <div style="margin-top: 12px;">没有找到匹配的商品</div>
                                </div>
                                
                                <div 
                                    v-for="product in filteredProducts" 
                                    :key="product.id"
                                    @click="toggleProduct(product)"
                                    style="padding: 16px; border: 1px solid var(--border-color); border-radius: 8px; margin-bottom: 8px; cursor: pointer; display: flex; justify-content: space-between; align-items: center; transition: all 0.3s ease;"
                                    :style="{ 
                                        background: isSelected(product.id) ? 'rgba(99, 102, 241, 0.1)' : 'white',
                                        borderColor: isSelected(product.id) ? 'var(--ant-primary-color)' : 'var(--ant-border-color-base)'
                                    }"
                                >
                                    <div style="flex: 1;">
                                        <div style="font-weight: 500; margin-bottom: 4px;">{{ product.name }}</div>
                                        <div style="font-size: 12px; color: var(--text-secondary); display: flex; gap: 16px;">
                                            <span>ID: {{ product.id }}</span>
                                            <span>价格: ¥{{ product.price }}</span>
                                            <span>分类: {{ product.category }}</span>
                                            <span v-if="product.brand">品牌: {{ product.brand }}</span>
                                        </div>
                                    </div>
                                    <el-checkbox :model-value="isSelected(product.id)" />
                                </div>
                            </div>
                        </div>
                        
                        <template #footer>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="color: var(--text-secondary);">
                                    已选择 {{ tempSelected.length }} 个商品
                                </div>
                                <div>
                                    <el-button @click="showModal = false">取消</el-button>
                                    <el-button 
                                        type="primary" 
                                        @click="confirmSelection"
                                        :disabled="loading || loadError"
                                    >
                                        确认选择
                                    </el-button>
                                </div>
                            </div>
                        </template>
                    </el-dialog>
                </div>
            `,
            data() {
                return {
                    showModal: false,
                    searchKeyword: '',
                    selectedCategory: '',
                    tempSelected: [],
                    products: [],
                    loading: false,
                    loadError: null
                };
            },
            computed: {
                selectedProducts() {
                    const selectedIds = this.value || [];
                    return this.products.filter(p => selectedIds.includes(p.id));
                },
                filteredProducts() {
                    let filtered = this.products;
                    
                    // 按分类筛选
                    if (this.selectedCategory) {
                        filtered = filtered.filter(product => product.category === this.selectedCategory);
                    }
                    
                    // 按关键词搜索
                    if (this.searchKeyword) {
                        const keyword = this.searchKeyword.toLowerCase();
                        filtered = filtered.filter(product => 
                            product.name.toLowerCase().includes(keyword) || 
                            product.id.toLowerCase().includes(keyword) ||
                            (product.brand && product.brand.toLowerCase().includes(keyword))
                        );
                    }
                    
                    return filtered;
                },
                productCategories() {
                    return [...new Set(this.products.map(p => p.category))].sort();
                }
            },
            watch: {
                showModal(val) {
                    if (val) {
                        this.tempSelected = [...(this.value || [])];
                    }
                }
            },
            methods: {
                async openModal() {
                    this.showModal = true;
                    if (this.products.length === 0) {
                        await this.loadProducts();
                    }
                },
                
                async loadProducts() {
                    this.loading = true;
                    this.loadError = null;
                    
                    try {
                        const response = await fetch('/api/product', {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP错误! 状态: ${response.status}`);
                        }

                        const data = await response.json();
                        
                        if (Array.isArray(data)) {
                            this.products = data.map(product => ({
                                id: product.id || product.Id,
                                name: product.name || product.Name,
                                price: product.price || product.Price,
                                category: product.category || product.Category,
                                brand: product.brand || product.Brand,
                                barcode: product.barcode || product.Barcode,
                                isActive: product.isActive !== false && product.IsActive !== false
                            })).filter(p => p.isActive !== false);
                            
                            console.log('商品数据加载成功:', this.products.length, '个商品');
                        } else {
                            throw new Error('返回的数据格式不正确');
                        }
                        
                    } catch (error) {
                        console.error('加载商品数据失败:', error);
                        this.loadError = error.message || '网络请求失败，请检查服务器连接';
                        this.products = [];
                    } finally {
                        this.loading = false;
                    }
                },
                
                isSelected(productId) {
                    return this.tempSelected.includes(productId);
                },
                
                toggleProduct(product) {
                    const index = this.tempSelected.indexOf(product.id);
                    if (index > -1) {
                        this.tempSelected.splice(index, 1);
                    } else {
                        this.tempSelected.push(product.id);
                    }
                },
                
                removeProduct(productId) {
                    const newValue = (this.value || []).filter(id => id !== productId);
                    this.$emit('update:value', newValue);
                },
                
                confirmSelection() {
                    this.$emit('update:value', [...this.tempSelected]);
                    this.showModal = false;
                },
                
                handleClose() {
                    this.showModal = false;
                }
            }
        };        // 创建Vue应用
        const { createApp } = Vue;
        const app = createApp(window.PromotionConfigMainComponent);

        // 使用Element Plus
        app.use(ElementPlus);

        // 注册图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }        // 注册组件
        app.component('AdvancedFormField', AdvancedFormField);
        app.component('ProductSelectorAdvanced', ProductSelectorAdvanced);
        app.component('RuleManager', window.RuleManager);
        app.component('DynamicFormRenderer', window.DynamicFormRenderer);

        // 挂载应用
        app.mount('#app');
    </script>
</body>
</html>
