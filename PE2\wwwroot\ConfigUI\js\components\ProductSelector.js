/**
 * 商品选择器组件
 * 支持单选和多选模式，集成远程搜索功能
 */

const ProductSelector = {
    name: 'ProductSelector',
    props: {
        modelValue: {
            type: [String, Array],
            default: () => null
        },
        multiple: {
            type: Boolean,
            default: false
        },
        placeholder: {
            type: String,
            default: '请选择商品'
        },
        disabled: {
            type: Boolean,
            default: false
        },
        clearable: {
            type: Boolean,
            default: true
        }
    },
    emits: ['update:modelValue', 'change'],
    setup(props, { emit }) {
        const { ref, computed, watch } = Vue;
        const { ElMessage } = ElementPlus;

        const loading = ref(false);
        const options = ref([]);
        const searchValue = ref('');
        const selectRef = ref(null);

        // 计算当前值
        const currentValue = computed({
            get() {
                return props.modelValue;
            },
            set(value) {
                emit('update:modelValue', value);
                emit('change', value);
            }
        });

        // 模拟商品数据（在实际应用中，这些数据应该来自API）
        const mockProducts = [
            { id: 'P001', name: '苹果 iPhone 15', category: '手机', price: 5999 },
            { id: 'P002', name: '华为 Mate 60', category: '手机', price: 6999 },
            { id: 'P003', name: '小米 14', category: '手机', price: 3999 },
            { id: 'P004', name: '三星 Galaxy S24', category: '手机', price: 7999 },
            { id: 'P005', name: 'iPad Air', category: '平板', price: 4599 },
            { id: 'P006', name: '华为 MatePad', category: '平板', price: 2999 },
            { id: 'P007', name: '联想 ThinkPad', category: '笔记本', price: 8999 },
            { id: 'P008', name: '戴尔 XPS', category: '笔记本', price: 12999 },
            { id: 'P009', name: '耐克运动鞋', category: '鞋类', price: 899 },
            { id: 'P010', name: '阿迪达斯T恤', category: '服装', price: 299 },
            { id: 'P011', name: '可口可乐', category: '饮料', price: 3.5 },
            { id: 'P012', name: '百事可乐', category: '饮料', price: 3.0 },
            { id: 'P013', name: '农夫山泉', category: '饮料', price: 2.0 },
            { id: 'P014', name: '星巴克咖啡豆', category: '食品', price: 89 },
            { id: 'P015', name: '哈根达斯冰淇淋', category: '食品', price: 45 }
        ];

        // 搜索商品
        const searchProducts = async (query) => {
            loading.value = true;
            try {
                // 模拟API延迟
                await new Promise(resolve => setTimeout(resolve, 300));
                
                let results = [];
                if (query && query.trim()) {
                    // 根据商品名称和ID进行模糊搜索
                    const searchTerm = query.toLowerCase();
                    results = mockProducts.filter(product => 
                        product.name.toLowerCase().includes(searchTerm) ||
                        product.id.toLowerCase().includes(searchTerm) ||
                        product.category.toLowerCase().includes(searchTerm)
                    );
                } else {
                    // 如果没有搜索词，返回前10个商品
                    results = mockProducts.slice(0, 10);
                }
                
                options.value = results.map(product => ({
                    value: product.id,
                    label: `${product.name} (${product.id})`,
                    product: product
                }));
            } catch (error) {
                console.error('搜索商品失败:', error);
                ElMessage.error('搜索商品失败');
                options.value = [];
            } finally {
                loading.value = false;
            }
        };

        // 远程搜索方法
        const remoteMethod = (query) => {
            searchValue.value = query;
            searchProducts(query);
        };

        // 处理值变化
        const handleChange = (value) => {
            currentValue.value = value;
        };

        // 初始化时加载默认选项
        const initOptions = async () => {
            await searchProducts('');
            
            // 如果有初始值，确保对应的选项存在
            if (props.modelValue) {
                const initialValues = Array.isArray(props.modelValue) ? props.modelValue : [props.modelValue];
                for (const value of initialValues) {
                    const existingOption = options.value.find(opt => opt.value === value);
                    if (!existingOption) {
                        // 为未找到的值添加占位选项
                        options.value.unshift({
                            value: value,
                            label: `商品 ${value}`,
                            product: { id: value, name: `商品 ${value}` }
                        });
                    }
                }
            }
        };

        // 组件挂载时初始化
        Vue.onMounted(() => {
            initOptions();
        });

        return {
            loading,
            options,
            currentValue,
            selectRef,
            remoteMethod,
            handleChange
        };
    },
    template: `
        <el-select
            ref="selectRef"
            v-model="currentValue"
            :multiple="multiple"
            :placeholder="placeholder"
            :disabled="disabled"
            :clearable="clearable"
            :loading="loading"
            filterable
            remote
            :remote-method="remoteMethod"
            style="width: 100%"
            @change="handleChange"
        >
            <el-option
                v-for="option in options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
            >
                <span style="float: left">{{ option.product.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                    {{ option.product.id }}
                </span>
            </el-option>
        </el-select>
    `
};

// 导出组件
if (typeof window !== 'undefined') {
    window.ProductSelector = ProductSelector;
}
