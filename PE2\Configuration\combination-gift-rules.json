[{"$type": "CombinationGift", "id": "COMBINATION_GIFT_001", "name": "组合送赠品 - A+B送C", "description": "购买A商品1件+B商品1件，赠送C商品1件", "priority": 30, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftSelectionStrategy": "CustomerBenefit", "combinationConditions": [{"productId": "A", "requiredQuantity": 1, "minAmount": 0}, {"productId": "B", "requiredQuantity": 1, "minAmount": 0}], "giftConditions": [{"productIds": ["C"], "giftQuantity": 1}]}, {"$type": "CombinationGift", "id": "COMBINATION_GIFT_002", "name": "组合送赠品 - 可翻倍", "description": "购买A商品1件+B商品1件，赠送C商品1件（可翻倍）", "priority": 30, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 3, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftSelectionStrategy": "CustomerBenefit", "combinationConditions": [{"productId": "A", "requiredQuantity": 1, "minAmount": 0}, {"productId": "B", "requiredQuantity": 1, "minAmount": 0}], "giftConditions": [{"productIds": ["C"], "giftQuantity": 1}]}, {"$type": "CombinationGift", "id": "COMBINATION_GIFT_003", "name": "组合送赠品 - 多选一商家利益最大化", "description": "购买A+B组合，从C、D、E中选择价值最低的赠送", "priority": 30, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftSelectionStrategy": "MerchantBenefit", "combinationConditions": [{"productId": "A", "requiredQuantity": 1, "minAmount": 0}, {"productId": "B", "requiredQuantity": 1, "minAmount": 0}], "giftConditions": [{"productIds": ["C", "D", "E"], "giftQuantity": 1}]}, {"$type": "CombinationGift", "id": "COMBINATION_GIFT_004", "name": "复杂组合送赠品", "description": "购买A商品2件+B商品1件+C商品1件，赠送D商品2件", "priority": 35, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftSelectionStrategy": "CustomerBenefit", "combinationConditions": [{"productId": "A", "requiredQuantity": 2, "minAmount": 0}, {"productId": "B", "requiredQuantity": 1, "minAmount": 0}, {"productId": "C", "requiredQuantity": 1, "minAmount": 0}], "giftConditions": [{"productIds": ["D"], "giftQuantity": 2}]}, {"$type": "CombinationGift", "id": "COMBINATION_GIFT_005", "name": "组合送赠品 - 金额条件", "description": "购买A商品满500元+B商品1件，赠送C商品1件", "priority": 30, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftSelectionStrategy": "CustomerBenefit", "combinationConditions": [{"productId": "A", "requiredQuantity": 1, "minAmount": 500}, {"productId": "B", "requiredQuantity": 1, "minAmount": 0}], "giftConditions": [{"productIds": ["C"], "giftQuantity": 1}]}]