using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.DiscountRules;

/// <summary>
/// 循环递增折扣规则测试类
/// 测试 CyclicDiscountRule 的循环折扣计算和应用逻辑
/// </summary>
public class CyclicDiscountRuleTests(ITestOutputHelper output) : TestBase(output)
{
    #region 基础循环折扣功能测试

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_ValidCyclicDiscountScenario_ShouldApplyCorrectly()
    {
        // Arrange - 循环折扣：第1件9折，第2件8折，第3件7折，第4件9折...
        var rule = TestDataGenerator.CreateCyclicDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            (TestDataGenerator.CreateProductA(), 4) // 4件A，应按循环折扣应用
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "循环折扣测试购物车");

        var originalPrice = TestDataGenerator.CreateProductA().Price; // 50元
        // 第1件9折(45元)，第2件8折(40元)，第3件7折(35元)，第4件9折(45元)
        var expectedTotalDiscount = (50 - 45) + (50 - 40) + (50 - 35) + (50 - 45); // 5+10+15+5 = 35元

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "循环折扣测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为循环折扣的累计金额");

        Output.WriteLine($"循环折扣详情: 第1件9折，第2件8折，第3件7折，第4件9折");
        Output.WriteLine($"总节省: {expectedTotalDiscount:C}");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_SingleItem_ShouldApplyFirstTierDiscount()
    {
        // Arrange - 只有1件商品，应享受第一个梯度的折扣
        var rule = TestDataGenerator.CreateCyclicDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_002",
            (TestDataGenerator.CreateProductA(), 1) // 1件A，应享受第1个梯度9折
        );

        LogCartDetails(cart, "单件商品循环折扣测试购物车");

        var originalPrice = TestDataGenerator.CreateProductA().Price; // 50元
        var expectedDiscountedPrice = originalPrice * 0.9m; // 45元（第1件9折）
        var expectedTotalDiscount = originalPrice - expectedDiscountedPrice; // 5元

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "单件商品循环折扣测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为第1件的9折折扣金额");

        Output.WriteLine($"单件折扣: {originalPrice:C} -> {expectedDiscountedPrice:C}, 节省: {expectedTotalDiscount:C}");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_MultipleCycles_ShouldRepeatCyclicPattern()
    {
        // Arrange - 6件商品，应完成2个完整循环
        var rule = TestDataGenerator.CreateCyclicDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_003",
            (TestDataGenerator.CreateProductA(), 6) // 6件A，完成2个循环
        );

        LogCartDetails(cart, "多循环折扣测试购物车");

        var originalPrice = TestDataGenerator.CreateProductA().Price; // 50元
        // 第1轮：9折+8折+7折，第2轮：9折+8折+7折
        var expectedTotalDiscount = 2 * ((50 - 45) + (50 - 40) + (50 - 35)); // 2 * (5+10+15) = 60元

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "多循环折扣测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为2个完整循环的累计折扣金额");

        Output.WriteLine($"2个完整循环: 第1轮(9折+8折+7折) + 第2轮(9折+8折+7折)");
        Output.WriteLine($"总节省: {expectedTotalDiscount:C}");
    }

    #endregion

    #region 多商品循环折扣测试

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Medium")]
    public void Apply_MultipleProducts_ShouldApplyCyclicDiscountOptimally()
    {
        // Arrange - 多种商品，应按优化策略应用循环折扣
        var rule = TestDataGenerator.CreateCyclicDiscountRule_MultiProduct();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_MULTI_001",
            (TestDataGenerator.CreateProductA(), 2), // 2件A (50元)
            (TestDataGenerator.CreateProductB(), 1)  // 1件B (30元)
        );

        LogCartDetails(cart, "多商品循环折扣测试购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "多商品循环折扣测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证有优惠产生
        Assert.True(result.TotalDiscount > 0, "多商品循环折扣应产生优惠");

        Output.WriteLine($"多商品循环折扣总节省: {result.TotalDiscount:C}");
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Low")]
    public void Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateCyclicDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateEmptyCart();

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "空购物车");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertAmountEqual(0m, result.TotalDiscount, "空购物车应无优惠");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Low")]
    public void Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateCyclicDiscountRule_Progressive();
        rule.IsEnabled = false; // 禁用规则

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_DISABLED",
            (TestDataGenerator.CreateProductA(), 3)
        );

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "禁用规则场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");
        AssertAmountEqual(0m, result.TotalDiscount, "禁用规则应无优惠");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Medium")]
    public void Apply_NonApplicableProducts_ShouldNotApply()
    {
        // Arrange - 购物车中没有适用的商品
        var rule = TestDataGenerator.CreateCyclicDiscountRule_Progressive(); // 适用于A商品
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_NON_APPLICABLE",
            (TestDataGenerator.CreateProductC(), 3) // 3件C，不在规则适用范围内
        );

        LogCartDetails(cart, "非适用商品购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "非适用商品场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");
        AssertAmountEqual(0m, result.TotalDiscount, "非适用商品应无优惠");

        // 验证商品价格未变
        var productCItem = result.ProcessedCart.Items.First(i => i.Product.Id == "C");
        AssertAmountEqual(
            TestDataGenerator.CreateProductC().Price,
            productCItem.ActualUnitPrice,
            "非适用商品价格应保持原价"
        );
    }

    #endregion

    #region 性能测试

    [Fact]
    [Trait("Category", "Performance")]
    [Trait("Priority", "Low")]
    public void Apply_LargeCart_ShouldPerformWell()
    {
        // Arrange
        var rule = TestDataGenerator.CreateCyclicDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateLargeTestCart(100); // 100个商品项
        
        // 添加适用的商品
        cart.Items.AddRange([
            new() { Product = TestDataGenerator.CreateProductA(), Quantity = 50, UnitPrice = TestDataGenerator.CreateProductA().Price }
        ]);
        cart.InitializeActualPrices();

        Output.WriteLine($"大型购物车测试: {cart.Items.Count} 个商品项");

        TestPromotionRuleService.Rules = [rule];
        // Act & Assert
        var executionTime = MeasureExecutionTime(() =>
        {
            var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;
            AssertPromotionResult(result, "大型购物车性能测试");
        });

        // 性能断言：大型购物车处理应在合理时间内完成
        AssertPerformance(executionTime, TimeSpan.FromMilliseconds(500), "大型购物车循环折扣规则计算");
    }

    #endregion
}