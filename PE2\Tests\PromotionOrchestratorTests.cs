using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PE2.Models;
using PE2.PromotionEngine.Core;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Extensions;
using PE2.PromotionEngine.Calculators;

namespace PE2.Tests;

/// <summary>
/// 促销编排器测试类
/// 验证PromotionOrchestrator的基本功能
/// </summary>
public class PromotionOrchestratorTests
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IPromotionOrchestrator _orchestrator;

    public PromotionOrchestratorTests()
    {
        var services = new ServiceCollection();
        
        // 添加日志服务
        services.AddLogging(builder => builder.AddConsole());
        
        // 添加促销引擎服务
        services.AddPromotionEngine();
        
        // 添加促销计算器（模拟）
        services.AddScoped<PromotionCalculator>();
        
        _serviceProvider = services.BuildServiceProvider();
        _orchestrator = _serviceProvider.GetRequiredService<IPromotionOrchestrator>();
    }

    /// <summary>
    /// 测试基本的促销计算流程
    /// </summary>
    public async Task TestBasicPromotionCalculation()
    {
        // 准备测试数据
        var cart = CreateTestCart();
        var rules = CreateTestRules();
        
        var request = new PromotionCalculationRequest
        {
            Id = Guid.NewGuid().ToString(),
            Cart = cart,
            AvailableRules = rules,
            Mode = CalculationMode.Automatic,
            Target = OptimizationTarget.MaxDiscount,
            ManuallySelectedPromotions = new List<string>()
        };

        // 执行计算
        var result = await _orchestrator.CalculatePromotionsAsync(request);

        // 验证结果
        Console.WriteLine($"计算结果: {(result.IsSuccessful ? "成功" : "失败")}");
        Console.WriteLine($"计算ID: {result.CalculationId}");
        Console.WriteLine($"计算耗时: {result.Statistics.TotalCalculationTimeMs}ms");
        
        if (result.OptimalResult != null)
        {
            Console.WriteLine($"原始金额: {result.OptimalResult.OriginalAmount:C}");
            Console.WriteLine($"最终金额: {result.OptimalResult.FinalAmount:C}");
            Console.WriteLine($"总优惠: {result.OptimalResult.TotalDiscount:C}");
            Console.WriteLine($"应用促销数: {result.OptimalResult.AppliedPromotions.Count}");
        }
    }

    /// <summary>
    /// 测试促销预分析功能
    /// </summary>
    public async Task TestPromotionPreAnalysis()
    {
        var cart = CreateTestCart();
        var rules = CreateTestRules();

        var analysis = await _orchestrator.PreAnalyzePromotionsAsync(cart, rules);

        Console.WriteLine($"预分析完成 - 购物车ID: {analysis.CartId}");
        Console.WriteLine($"适用规则数: {analysis.RuleApplicability.Count}");
        Console.WriteLine($"潜在冲突数: {analysis.PotentialConflicts.Count}");
        
        if (analysis.OptimizationPotential != null)
        {
            Console.WriteLine($"优化复杂度: {analysis.OptimizationPotential.OptimizationComplexity}");
            Console.WriteLine($"预估计算时间: {analysis.OptimizationPotential.EstimatedCalculationTime}ms");
        }
    }

    /// <summary>
    /// 测试计算状态查询
    /// </summary>
    public async Task TestCalculationStatusQuery()
    {
        var calculationId = Guid.NewGuid().ToString();
        
        // 查询不存在的计算ID
        var status = await _orchestrator.GetCalculationStatusAsync(calculationId);
        
        Console.WriteLine($"状态查询结果: {status.State}");
        Console.WriteLine($"当前阶段: {status.CurrentPhase}");
        Console.WriteLine($"进度: {status.ProgressPercentage}%");
    }

    /// <summary>
    /// 测试促销兼容性验证
    /// </summary>
    public async Task TestPromotionCompatibilityValidation()
    {
        var cart = CreateTestCart();
        var promotionIds = new List<string> { "RULE001", "RULE002", "RULE003" };

        var compatibility = await _orchestrator.ValidatePromotionCompatibilityAsync(promotionIds, cart);

        Console.WriteLine($"兼容性验证结果: {(compatibility.IsCompatible ? "兼容" : "不兼容")}");
        Console.WriteLine($"冲突促销数: {compatibility.ConflictingPromotions.Count}");
        Console.WriteLine($"兼容性问题数: {compatibility.CompatibilityIssues.Count}");
    }

    /// <summary>
    /// 运行所有测试
    /// </summary>
    public async Task RunAllTests()
    {
        Console.WriteLine("=== 促销编排器测试开始 ===\n");

        try
        {
            Console.WriteLine("1. 测试基本促销计算流程");
            await TestBasicPromotionCalculation();
            Console.WriteLine();

            Console.WriteLine("2. 测试促销预分析功能");
            await TestPromotionPreAnalysis();
            Console.WriteLine();

            Console.WriteLine("3. 测试计算状态查询");
            await TestCalculationStatusQuery();
            Console.WriteLine();

            Console.WriteLine("4. 测试促销兼容性验证");
            await TestPromotionCompatibilityValidation();
            Console.WriteLine();

            Console.WriteLine("=== 所有测试完成 ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"测试过程中发生异常: {ex.Message}");
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        }
    }

    /// <summary>
    /// 创建测试购物车
    /// </summary>
    private ShoppingCart CreateTestCart()
    {
        var cart = new ShoppingCart
        {
            Id = Guid.NewGuid().ToString(),
            CustomerId = "TEST_CUSTOMER",
            Items = new List<CartItem>
            {
                new CartItem
                {
                    Product = new Product
                    {
                        Id = "P001",
                        Name = "测试商品1",
                        Category = "电子产品",
                        Price = 100.00m
                    },
                    Quantity = 2,
                    UnitPrice = 100.00m
                },
                new CartItem
                {
                    Product = new Product
                    {
                        Id = "P002",
                        Name = "测试商品2",
                        Category = "服装",
                        Price = 50.00m
                    },
                    Quantity = 3,
                    UnitPrice = 50.00m
                },
                new CartItem
                {
                    Product = new Product
                    {
                        Id = "P003",
                        Name = "测试商品3",
                        Category = "食品",
                        Price = 25.00m
                    },
                    Quantity = 1,
                    UnitPrice = 25.00m
                }
            }
        };

        return cart;
    }

    /// <summary>
    /// 创建测试促销规则
    /// </summary>
    private List<PromotionRuleBase> CreateTestRules()
    {
        // 这里返回空列表，实际测试中需要创建具体的促销规则实例
        // 由于促销规则的具体实现较为复杂，这里简化处理
        return new List<PromotionRuleBase>();
    }
}

/// <summary>
/// 测试程序入口
/// </summary>
public class Program
{
    public static async Task Main(string[] args)
    {
        var tests = new PromotionOrchestratorTests();
        await tests.RunAllTests();
        
        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
}
