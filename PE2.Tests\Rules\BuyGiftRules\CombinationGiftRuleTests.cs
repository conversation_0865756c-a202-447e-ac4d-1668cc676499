using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Rules;
using PE2.PromotionEngine.Rules.BuyGiftRules;
using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.BuyGiftRules;

/// <summary>
/// 组合买赠规则测试类
/// 测试 CombinationGiftRule 的组合购买条件和赠品逻辑
/// </summary>
public class CombinationGiftRuleTests(ITestOutputHelper output) : TestBase(output)
{
    #region 基础组合买赠功能测试

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "High")]
    public void Apply_ValidCombinationGiftScenario_ShouldApplyCorrectly()
    {
        // Arrange - 买A+B各1件送1件C
        var rule = TestDataGenerator.CreateCombinationGiftRule_BuyAB_Get1C();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            (TestDataGenerator.CreateProductA(), 1), // 满足组合条件：1件A
            (TestDataGenerator.CreateProductB(), 1), // 满足组合条件：1件B
            (TestDataGenerator.CreateProductC(), 1) // 满足赠条件：有1件C可赠
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "应用前购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "买A+B各1件送1件C");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证赠品C的价格被调整为0
        //var productCItem = result.ProcessedCart.Items.First(i => i.Product.Id == "C");
        //AssertAmountEqual(0m, productCItem.ActualUnitPrice, "赠品C的实际单价应为0");

        // 验证总优惠金额等于赠品原价
        var expectedDiscount = TestDataGenerator.CreateProductC().Price; // 20元
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应等于赠品原价");
    }

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "High")]
    public void Apply_InsufficientCombinationProducts_ShouldNotApply()
    {
        // Arrange - 组合条件不满足：只有A，缺少B
        var rule = TestDataGenerator.CreateCombinationGiftRule_BuyAB_Get1C();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_002",
            (TestDataGenerator.CreateProductA(), 1), // 有A
            (TestDataGenerator.CreateProductC(), 1) // 有赠品C，但缺少B
        );

        LogCartDetails(cart, "组合条件不满足的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "组合条件不满足");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "组合条件不满足时应无优惠");
    }

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "High")]
    public void Apply_NoGiftProductInCart_ShouldNotApply()
    {
        // Arrange - 赠条件不满足：有A+B但没有赠品C
        var rule = TestDataGenerator.CreateCombinationGiftRule_BuyAB_Get1C();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_003",
            (TestDataGenerator.CreateProductA(), 1), // 满足组合条件：1件A
            (TestDataGenerator.CreateProductB(), 1) // 满足组合条件：1件B
        // 缺少赠品C
        );

        LogCartDetails(cart, "无赠品的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "无赠品场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "无赠品时应无优惠");
    }

    #endregion

    #region 多次应用测试

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Medium")]
    public void Apply_MultipleApplications_ShouldApplyCorrectly()
    {
        // Arrange - 可以应用多次：2件A + 2件B + 2件C，可以应用2次组合买赠
        var rule = TestDataGenerator.CreateCombinationGiftRule_BuyAB_Get1C();
        rule.IsRepeatable = true; // 允许重复应用
        rule.MaxApplications = 4;

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_004",
            (TestDataGenerator.CreateProductA(), 2), // 可以组合2次
            (TestDataGenerator.CreateProductB(), 2), // 可以组合2次
            (TestDataGenerator.CreateProductC(), 2) // 有2件C可以作为赠品
        );

        LogCartDetails(cart, "多次应用场景购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "多次应用场景");
        LogPromotionResultDetails(result);

        // 验证规则被应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证所有C都变成赠品（价格为0）
        var productCItems = result.AppliedPromotions.First().GiftItems;

        Assert.True(
            result.AppliedPromotions.First().GiftItems.Sum(x => x.Quantity) == 2,
            "所有C都应该是赠品"
        );

        Assert.True(result.AppliedPromotions.First().ApplicationCount == 2, "应用了2次");

        // 验证总优惠 = 2件C的原价
        var expectedDiscount = TestDataGenerator.CreateProductC().Price * 2; // 20 * 2 = 40元
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应等于2件C的原价");
    }

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Medium")]
    public void Apply_PartialGiftAvailable_ShouldApplyPartially()
    {
        // Arrange - 部分赠品可用：2套A+B（可组合2次），但只有1件C
        var rule = TestDataGenerator.CreateCombinationGiftRule_BuyAB_Get1C();
        rule.IsRepeatable = true;
        rule.MaxApplications = 4;

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_005",
            (TestDataGenerator.CreateProductA(), 2), // 理论上可以组合2次
            (TestDataGenerator.CreateProductB(), 2), // 理论上可以组合2次
            (TestDataGenerator.CreateProductC(), 1) // 但只有1件C
        );

        LogCartDetails(cart, "部分赠品可用购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "部分赠品可用");
        LogPromotionResultDetails(result);

        // 验证规则被应用（但只能应用1次）
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        Assert.True(result.AppliedPromotions.First().ApplicationCount == 1, "只能执行1次");
        // 验证只有1件C变成赠品

        Assert.True(result.AppliedPromotions.First().GiftItems.Count(x => x.ProductId == "C") == 1, "1件C应该是赠品");

        // 验证总优惠 = 1件C的原价
        var expectedDiscount = TestDataGenerator.CreateProductC().Price; // 20元
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应等于1件C的原价");
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Low")]
    public void Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateCombinationGiftRule_BuyAB_Get1C();
        var cart = TestDataGenerator.CreateEmptyCart();

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "空购物车");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertAmountEqual(0m, result.TotalDiscount, "空购物车应无优惠");
    }

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Low")]
    public void Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateCombinationGiftRule_BuyAB_Get1C();
        rule.IsEnabled = false; // 禁用规则

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_006",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1),
            (TestDataGenerator.CreateProductC(), 1)
        );

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "禁用规则");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");
        AssertAmountEqual(0m, result.TotalDiscount, "禁用规则应无优惠");
    }

    #endregion

    #region 复杂组合条件测试

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Medium")]
    public void Apply_ComplexCombinationConditions_ShouldApplyCorrectly()
    {
        // Arrange - 复杂组合条件：买2件A+1件B送1件C
        var rule = new CombinationGiftRule
        {
            Id = "COMPLEX_COMBINATION_GIFT",
            Name = "买2件A+1件B送1件C",
            Priority = 100,
            IsEnabled = true,
            CombinationConditions = new List<CombinationGiftCondition>
            {
                new() { ProductId = "A", RequiredQuantity = 2 },
                new() { ProductId = "B", RequiredQuantity = 1 }
            },
            GiftProductIds = ["C"],
            GiftQuantity = 1
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_007",
            (TestDataGenerator.CreateProductA(), 2), // 满足条件：2件A
            (TestDataGenerator.CreateProductB(), 1), // 满足条件：1件B
            (TestDataGenerator.CreateProductC(), 1) // 赠品C
        );

        LogCartDetails(cart, "复杂组合条件购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "复杂组合条件");
        LogPromotionResultDetails(result);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证C变成赠品
        var productCItem = result.ProcessedCart.Items.First(i => i.Product.Id == "C");
        //AssertAmountEqual(0m, productCItem.ActualUnitPrice, "C应该是赠品");

        var expectedDiscount = TestDataGenerator.CreateProductC().Price; // 20元
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应等于C的原价");
    }

    #endregion

    #region 客户利益最大化测试

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Medium")]
    public void Apply_CustomerBenefitStrategy_ShouldSelectHighestValueGifts()
    {
        // Arrange - 客户利益最大化：选择价值最高的赠品
        var rule = new CombinationGiftRule
        {
            Id = "CUSTOMER_BENEFIT_COMBINATION_GIFT",
            Name = "买A+B组合送赠品（客户利益最大化）",
            Priority = 100,
            IsEnabled = true,
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredAmount = 1 },
                new() { ProductId = "B", RequiredQuantity = 1 }
            ],
            GiftProductIds = ["B", "C"], // B价格30元，C价格20元
            GiftQuantity = 1,
            GiftSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit // 客户利益最大化
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_BENEFIT_001",
            (TestDataGenerator.CreateProductA(), 1), // 满足组合条件
            (TestDataGenerator.CreateProductB(), 2), // 1件用于组合，1件可作为赠品
            (TestDataGenerator.CreateProductC(), 1) // C价格20元，应该被选为赠品
        );

        LogCartDetails(cart, "客户利益最大化组合买赠购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "客户利益最大化组合买赠");
        LogPromotionResultDetails(result);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证选择了价值更高的C作为赠品
        var giftItems = result.AppliedPromotions.First().GiftItems;
        Assert.True(
            giftItems.First(x => x.ProductId == "B").Quantity == 1 && giftItems.Count == 1,
            "B 应该被选为赠品（价值更高）"
        );

        // 验证总优惠等于C的原价
        var expectedDiscount = TestDataGenerator.CreateProductB().Price;

        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应等于B 的原价");
    }

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Medium")]
    public void Apply_CustomerBenefitStrategy_MultipleGifts_ShouldSelectOptimal()
    {
        // Arrange - 客户利益最大化：多个赠品时选择最优组合
        var rule = new CombinationGiftRule
        {
            Id = "CUSTOMER_BENEFIT_MULTIPLE_COMBINATION_GIFTS",
            Name = "买A+B组合送2件赠品（客户利益最大化）",
            Priority = 100,
            IsEnabled = true,
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 1 },
                new() { ProductId = "B", RequiredQuantity = 1 }
            ],
            GiftProductIds = ["B", "C"], // B价格30元，C价格20元
            GiftQuantity = 2, // 送2件
            GiftSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_BENEFIT_002",
            (TestDataGenerator.CreateProductA(), 1), // 满足组合条件
            (TestDataGenerator.CreateProductB(), 3), // 1件用于组合，2件可作为赠品
            (TestDataGenerator.CreateProductC(), 2) // 2件C可作为赠品
        );

        LogCartDetails(cart, "客户利益最大化多赠品组合购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "客户利益最大化多赠品组合");
        LogPromotionResultDetails(result);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证选择了价值最高的组合（2件C）
        var giftItems = result.AppliedPromotions.First().GiftItems; //result.ProcessedCart.Items.Where(i => i.Product.Id == "C").ToList();
        Assert.Single(giftItems);
        Assert.True(giftItems.Sum(x => x.Quantity) == 2, "C应该被选为赠品");

        // 验证总优惠等于2件B的原价
        var expectedDiscount = TestDataGenerator.CreateProductB().Price * 2; // 30 * 2 = 60元
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应等于2件B的原价");
    }

    #endregion

    #region 商户利益最大化测试

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Medium")]
    public void Apply_MerchantBenefitStrategy_ShouldSelectLowestValueGifts()
    {
        // Arrange - 商户利益最大化：选择价值最低的赠品
        var rule = new CombinationGiftRule
        {
            Id = "MERCHANT_BENEFIT_COMBINATION_GIFT",
            Name = "买A+B组合送赠品（商户利益最大化）",
            Priority = 100,
            IsEnabled = true,
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 1 },
                new() { ProductId = "B", RequiredQuantity = 1 }
            ],
            GiftProductIds = ["B", "C"], // B价格30元，C价格20元
            GiftQuantity = 1,
            GiftSelectionStrategy = BenefitSelectionStrategy.MerchantBenefit // 商户利益最大化
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "MERCHANT_BENEFIT_001",
            (TestDataGenerator.CreateProductA(), 1), // 满足组合条件
            (TestDataGenerator.CreateProductB(), 2), // 1件用于组合，1件可作为赠品
            (TestDataGenerator.CreateProductC(), 1) // C价格20元
        );

        LogCartDetails(cart, "商户利益最大化组合买赠购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "商户利益最大化组合买赠");
        LogPromotionResultDetails(result);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 应该有一件B被选为赠品（价格为0）
        var giftBItem = result.AppliedPromotions.First().GiftItems;
        Assert.NotNull(giftBItem);
        Assert.True(giftBItem.First().ProductId == "C", "C 应该被选为赠品");

        // 验证总优惠等于B的原价
        var expectedDiscount = TestDataGenerator.CreateProductC().Price;
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应等于C的原价");
    }

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Medium")]
    public void Apply_MerchantBenefitStrategy_MultipleGifts_ShouldSelectOptimal()
    {
        // Arrange - 商户利益最大化：多个赠品时选择成本最低组合
        var rule = new CombinationGiftRule
        {
            Id = "MERCHANT_BENEFIT_MULTIPLE_COMBINATION_GIFTS",
            Name = "买A+B组合送2件赠品（商户利益最大化）",
            Priority = 100,
            IsEnabled = true,
            CombinationConditions =
            [
                new() { ProductId = "A", RequiredQuantity = 1 },
                new() { ProductId = "B", RequiredQuantity = 1 }
            ],
            GiftProductIds = ["B", "C"], // B价格15元，C价格20元
            GiftQuantity = 2, // 送2件
            GiftSelectionStrategy = BenefitSelectionStrategy.MerchantBenefit
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "MERCHANT_BENEFIT_002",
            (TestDataGenerator.CreateProductA(), 1), // 满足组合条件
            (TestDataGenerator.CreateProductB(), 3), // 1件用于组合，2件可作为赠品
            (TestDataGenerator.CreateProductC(), 2) // 2件C可作为赠品
        );

        LogCartDetails(cart, "商户利益最大化多赠品组合购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "商户利益最大化多赠品组合");
        LogPromotionResultDetails(result);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证选择了成本最低的组合（2件B）
        //var productBItems = result.ProcessedCart.Items.Where(i => i.Product.Id == "B").ToList();
        //var productCItems = result.ProcessedCart.Items.Where(i => i.Product.Id == "C").ToList();

        //Assert.Single(productBItems);
        //Assert.Single(productCItems);

        // 应该有2件B被选为赠品
        //AssertAmountEqual(0m, productBItems[0].ActualUnitPrice, "B应该被选为赠品");
        Assert.True(
            result.AppliedPromotions.First().GiftItems.First().ProductId == "C",
            "C 应该全部被选为赠品"
        );

        // 验证总优惠等于2件B的原价
        var expectedDiscount = TestDataGenerator.CreateProductC().Price * 2; // 20 * 2 = 40元
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠应等于2件B的原价");
    }

    #endregion

    #region 性能测试

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Low")]
    public void Apply_LargeCart_ShouldPerformWell()
    {
        // Arrange - 大购物车性能测试
        var rule = TestDataGenerator.CreateCombinationGiftRule_BuyAB_Get1C();
        rule.IsRepeatable = true;

        var cartItems = new List<(Product, int)>();
        // 添加大量商品
        for (int i = 0; i < 50; i++)
        {
            cartItems.Add((TestDataGenerator.CreateProductA(), 1));
            cartItems.Add((TestDataGenerator.CreateProductB(), 1));
            cartItems.Add((TestDataGenerator.CreateProductC(), 1));
        }

        var cart = TestDataGenerator.CreateCustomCart(
            "LARGE_CART",
            "CUSTOMER_PERF",
            cartItems.ToArray()
        );

        TestPromotionRuleService.Rules = [rule];
        // Act & Assert - 应该在合理时间内完成
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;
        stopwatch.Stop();

        Assert.True(stopwatch.ElapsedMilliseconds < 1000, "大购物车处理应在1秒内完成");
        Assert.Single(result.AppliedPromotions);
        Assert.True(result.TotalDiscount > 0, "应该有优惠");
    }

    #endregion

    #region 规则配置验证测试

    [Fact]
    [Trait("Category", "BuyGift")]
    [Trait("Priority", "Low")]
    public void Apply_InvalidRuleConfiguration_ShouldHandleGracefully()
    {
        // Arrange - 无效的规则配置：没有组合条件
        var rule = new CombinationGiftRule
        {
            Id = "INVALID_COMBINATION_GIFT",
            Name = "无效组合买赠",
            Priority = 100,
            IsEnabled = true,
            CombinationConditions = new List<CombinationGiftCondition>(), // 空的组合条件
            GiftProductIds = ["C"],
            GiftQuantity = 1
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_008",
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1),
            (TestDataGenerator.CreateProductC(), 1)
        );

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert - 应该优雅地处理无效配置
        AssertPromotionResult(result, "无效规则配置");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertAmountEqual(0m, result.TotalDiscount, "无效配置应无优惠");
    }

    #endregion
}
