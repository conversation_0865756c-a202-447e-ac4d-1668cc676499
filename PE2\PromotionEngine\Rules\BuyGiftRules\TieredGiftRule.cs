using PE2.Models;
using PE2.PromotionEngine.Models;
using Serilog;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Rules.BuyGiftRules;

/// <summary>
/// 梯度送赠品规则 [OK]
/// 针对某一类商品，满第一梯度送A商品，满第二梯度送B商品
/// 场景案例：购买A商品大于等于1件时，送1件B商品；购买A商品大于等于2件时，送2件C商品。
/// 促销设置为不翻倍且按照梯度送：A商品吊牌、零售价为1000元；购买A商品3件时，应收金额为3000元（送2件C商品）
/// 促销设置为不翻倍且按照全部送：A商品吊牌、零售价为1000元；购买A商品4件时，应收金额为4000元（送1件B商品和2件C商品）
/// 促销设置为翻2倍且按照梯度送：A商品吊牌、零售价为1000元；购买A商品4件时，应收金额为4000元（送4件C商品）
/// 促销设置为翻2倍且按照全部送：A商品吊牌、零售价为1000元；购买A商品4件时，应收金额为4000元（送2件B和4件C商品）
/// 备注：1.赠送商品需要在购物车中存在，且赠送商品的数量或价值需满足条件才触发发促销规则执行
///       2.赠送商品策略：客户利益最大化 vs 商家利益最大化 需要根据配置来决定
///       3.赠品策略：梯度送-只赠送符合梯度条件的数量的赠品，全部送-赠送所有符合梯度的赠品和数量，可能会重复赠送
///       4.可以修改赠送方式字段：1表示梯度送，2表示全部送，3表示同款送  同款送表示赠品只能是同款商品也即是从ApplicableProductIds 中筛选，而不用设置GiftProductIds
/// </summary>
//TODO:目前发现的问题，如果是买2送3，买1送1，如果赠品只有2件，那么目前 算法降级1-1，实际上这不是最优
//还要检测就是商品和赠品是否混合在一起，占位的问题
public class TieredGiftRule : BaseBuyGiftRule
{
    public override string RuleType => "TieredGift";

    /// <summary>
    /// 适用的商品ID列表（购买条件商品）
    /// </summary>
    public List<string> ApplicableProductIds { get; set; } = new();

    /// <summary>
    /// 梯度赠品列表（按梯度从低到高排序）
    /// </summary>
    public List<GiftTier> GiftTiers { get; set; } = [];

    /// <summary>
    /// 赠品策略：梯度送 vs 全部送
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public GiftStrategy GiftStrategy { get; set; } = GiftStrategy.ByTier;

    /// <summary>
    /// 是否按金额计算（如果为true，则使用金额条件；否则使用数量条件）
    /// </summary>
    public bool IsByAmount { get; set; } = false;

    /// <summary>
    /// 是否仅限会员（送券时必须为true）
    /// </summary>
    public bool MemberOnly { get; set; } = false;



    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var applicableTiers = GetApplicableTiers(cart);
        if (!applicableTiers.Any())
            return 0;

        var maxApplications = 0;
        var highestTier = applicableTiers.OrderByDescending(t => IsByAmount ? t.MinAmount : t.MinQuantity).First();

        if (IsByAmount)
        {
            var totalAmount = CalculateTotalAmount(cart, ApplicableProductIds);

            maxApplications = IsRepeatable
                ? (int)(totalAmount / highestTier.MinAmount)
                : (totalAmount >= highestTier.MinAmount ? 1 : 0);
        }
        else
        {
            var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);

            maxApplications = IsRepeatable
                ? totalQuantity / highestTier.MinQuantity
                : (totalQuantity >= highestTier.MinQuantity ? 1 : 0);
        }

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyTieredGift(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0 || result.Item3.Any();

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用梯度送赠品促销";
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "应用梯度送赠品促销失败，规则ID: {RuleId}, 购物车ID: {CartId}", Id, cart.Id);
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }



    /// <summary>
    /// 处理梯度券赠品
    /// </summary>
    private List<GiftItem> ProcessTierCouponGifts(ShoppingCart cart, GiftTier tier, int applicationIndex)
    {
        var giftItems = new List<GiftItem>();

        foreach (var couponGift in tier.CouponGifts)
        {
            // 发送券到会员账户（异步处理）
            _ = Task.Run(async () => await SendCouponToMemberAsync(cart.MemberId, couponGift));

            // 记录券赠品
            var giftStrategyDescription = GiftStrategy == GiftStrategy.ByTier ? "梯度送" : "全部送";

            giftItems.Add(new GiftItem
            {
                ProductId = couponGift.CouponId,
                ProductName = couponGift.CouponName,
                Quantity = couponGift.Quantity,
                Value = couponGift.CouponValue * couponGift.Quantity,
                Description = $"梯度送券：{tier.Description}，{couponGift.Description}，价值{couponGift.CouponValue * couponGift.Quantity:C}（第{applicationIndex + 1}次应用，{giftStrategyDescription}）"
            });
        }

        return giftItems;
    }

    // ...existing code...

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!ApplicableProductIds.Any() || !GiftTiers.Any())
            return false;

        // 验证购买条件商品是否在购物车中
        if (!ValidateBuyGiftProductsInCart(cart, ApplicableProductIds))
            return false;

        // 验证至少有一个梯度的赠品在购物车中（修复：增加赠品可用性检查）
        var hasValidTier = false;
        foreach (var tier in GiftTiers.OrderBy(t => IsByAmount ? t.MinAmount : t.MinQuantity))
        {
            // 检查是否满足购买条件
            bool meetsCondition;
            if (IsByAmount)
            {
                var totalAmount = CalculateTotalAmount(cart, ApplicableProductIds);
                meetsCondition = totalAmount >= tier.MinAmount;
            }
            else
            {
                var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);
                meetsCondition = totalQuantity >= tier.MinQuantity;
            }

            if (meetsCondition)
            {
                // 检查赠品是否可用
                if (tier.GiftProductIds.Any())
                {
                    var availableGiftQuantity = CalculateTotalQuantity(cart, tier.GiftProductIds);
                    if (availableGiftQuantity >= tier.GiftQuantity)
                    {
                        hasValidTier = true;
                        break; // 找到一个可用的梯度就足够了
                    }
                }
                else if (tier.CouponGifts.Any())
                {
                    // 券赠品不需要检查库存，但需要检查会员身份
                    if (!MemberOnly || !string.IsNullOrEmpty(cart.MemberId))
                    {
                        hasValidTier = true;
                        break;
                    }
                }
            }
        }

        if (!hasValidTier)
            return false;

        // 如果有券赠品且仅限会员，验证会员身份
        var hasCouponGifts = GiftTiers.Any(t => t.CouponGifts.Any());
        if (hasCouponGifts && MemberOnly && string.IsNullOrEmpty(cart.MemberId))
            return false;

        return true;
    }

    // ...existing code...

    /// <summary>
    /// 获取适用的梯度列表（修复：增加赠品库存验证）
    /// </summary>
    private List<GiftTier> GetApplicableTiers(ShoppingCart cart)
    {
        var applicableTiers = new List<GiftTier>();

        foreach (var tier in GiftTiers)
        {
            bool meetsCondition;
            if (IsByAmount)
            {
                var totalAmount = CalculateTotalAmount(cart, ApplicableProductIds);
                meetsCondition = totalAmount >= tier.MinAmount;
            }
            else
            {
                var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);
                meetsCondition = totalQuantity >= tier.MinQuantity;
            }

            if (meetsCondition)
            {
                // 检查赠品库存
                if (tier.GiftProductIds.Any())
                {
                    var availableGiftQuantity = CalculateTotalQuantity(cart, tier.GiftProductIds);
                    if (availableGiftQuantity >= tier.GiftQuantity)
                    {
                        applicableTiers.Add(tier);
                    }
                }
                else if (tier.CouponGifts.Any())
                {
                    // 券赠品始终可用（如果满足会员条件）
                    if (!MemberOnly || !string.IsNullOrEmpty(cart.MemberId))
                    {
                        applicableTiers.Add(tier);
                    }
                }
            }
        }

        return applicableTiers;
    }

    // ...existing code...

    /// <summary>
    /// 应用梯度送赠品促销（修复：增加赠品库存检查和梯度降级逻辑）
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyTieredGift(ShoppingCart cart, int applicationCount)
    {
        var totalGiftValue = 0m;
        var consumedItems = new List<ConsumedItem>();
        var giftItems = new List<GiftItem>();

        for (int app = 0; app < applicationCount; app++)
        {
            // 重新获取适用的梯度（考虑当前购物车状态）
            var applicableTiers = GetApplicableTiers(cart);
            if (!applicableTiers.Any())
                break;

            // 根据策略确定要应用的梯度
            var tiersToApply = GiftStrategy switch
            {
                GiftStrategy.ByTier => GetOptimalTierForApplication(applicableTiers), // 修复：选择最优梯度
                GiftStrategy.AllTiers => applicableTiers.ToList(),
                _ => GetOptimalTierForApplication(applicableTiers)
            };

            if (!tiersToApply.Any())
                break;

            // 消耗购买条件商品
            var highestTier = tiersToApply.OrderByDescending(t => IsByAmount ? t.MinAmount : t.MinQuantity).First();
            var requiredQuantity = IsByAmount ? 0 : highestTier.MinQuantity;
            if (requiredQuantity > 0)
            {
                var consumed = ConsumeConditionProducts(cart, ApplicableProductIds, requiredQuantity);
                foreach (var item in consumed)
                {
                    var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == item.ProductId);
                    if (existingConsumed != null)
                    {
                        existingConsumed.Quantity += item.Quantity;
                    }
                    else
                    {
                        consumedItems.Add(item);
                    }
                }
            }

            // 处理每个梯度的赠品
            foreach (var tier in tiersToApply)
            {
                // 处理实物赠品
                if (tier.GiftProductIds.Any())
                {
                    var giftProducts = ProcessTierPhysicalGifts(cart, tier, app);
                    totalGiftValue += giftProducts.Sum(g => g.Value);
                    giftItems.AddRange(giftProducts);
                }

                // 处理券赠品
                if (tier.CouponGifts.Any() && !string.IsNullOrEmpty(cart.MemberId))
                {
                    var couponGiftItems = ProcessTierCouponGifts(cart, tier, app);
                    giftItems.AddRange(couponGiftItems);
                }
            }
        }

        return (totalGiftValue, consumedItems, giftItems);
    }

    /// <summary>
    /// 获取最优梯度用于应用（修复：智能梯度选择和降级逻辑）
    /// </summary>
    private List<GiftTier> GetOptimalTierForApplication(List<GiftTier> applicableTiers)
    {
        if (!applicableTiers.Any())
            return new List<GiftTier>();

        // 按梯度从高到低排序
        var sortedTiers = applicableTiers.OrderByDescending(t => IsByAmount ? t.MinAmount : t.MinQuantity).ToList();

        // 返回最高的单个梯度
        return new List<GiftTier> { sortedTiers.First() };
    }

    /// <summary>
    /// 处理梯度实物赠品（修复：改进赠品选择和库存检查）
    /// </summary>
    private List<GiftItem> ProcessTierPhysicalGifts(ShoppingCart cart, GiftTier tier, int applicationIndex)
    {
        var giftItems = new List<GiftItem>();

        // 收集可用的赠品
        var availableGifts = new List<(string ProductId, int Quantity, decimal UnitPrice)>();
        foreach (var giftProductId in tier.GiftProductIds)
        {
            var cartItems = cart.Items.Where(x => x.Product.Id == giftProductId && x.Quantity > 0).ToList();
            foreach (var cartItem in cartItems)
            {
                availableGifts.Add((giftProductId, cartItem.Quantity, cartItem.UnitPrice));
            }
        }

        if (!availableGifts.Any())
            return giftItems;

        // 检查库存是否足够
        var totalAvailable = availableGifts.Sum(g => g.Quantity);
        var actualGiftQuantity = Math.Min(tier.GiftQuantity, totalAvailable);

        if (actualGiftQuantity <= 0)
            return giftItems;

        // 选择赠品
        var selectedGifts = SelectGiftProducts(availableGifts, actualGiftQuantity);
        if (!selectedGifts.Any())
            return giftItems;

        // 应用赠品效果到购物车
        var promotion = new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType
        };
        ApplyGiftEffectToCart(cart, selectedGifts, promotion);

        // 记录赠品
        foreach (var gift in selectedGifts)
        {
            var strategyDescription = GiftSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                ? "客户利益最大化"
                : "商家利益最大化";
            var giftStrategyDescription = GiftStrategy == GiftStrategy.ByTier ? "梯度送" : "全部送";

            var existingGift = giftItems.FirstOrDefault(x => x.ProductId == gift.ProductId);
            if (existingGift != null)
            {
                existingGift.Quantity += gift.Quantity;
                existingGift.Value += gift.Quantity * gift.UnitPrice;
            }
            else
            {
                var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == gift.ProductId);
                giftItems.Add(new GiftItem
                {
                    ProductId = gift.ProductId,
                    ProductName = cartItem?.Product.Name ?? gift.ProductId,
                    Quantity = gift.Quantity,
                    Value = gift.Quantity * gift.UnitPrice,
                    Description = $"梯度送赠品：{tier.Description}，赠送{gift.Quantity}件，价值{gift.Quantity * gift.UnitPrice:C}（第{applicationIndex + 1}次应用，{giftStrategyDescription}，{strategyDescription}）"
                });
            }
        }

        return giftItems;
    }
}

/// <summary>
/// 赠品梯度
/// </summary>
public class GiftTier
{
    /// <summary>
    /// 最小数量要求
    /// </summary>
    public int MinQuantity { get; set; }

    /// <summary>
    /// 最小金额要求
    /// </summary>
    public decimal MinAmount { get; set; }

    /// <summary>
    /// 赠品商品ID列表
    /// </summary>
    public List<string> GiftProductIds { get; set; } = new();

    /// <summary>
    /// 赠品数量
    /// </summary>
    public int GiftQuantity { get; set; }

    /// <summary>
    /// 券赠品列表
    /// </summary>
    public List<CouponGift> CouponGifts { get; set; } = new();

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 赠品策略枚举
/// </summary>
public enum GiftStrategy
{
    /// <summary>
    /// 梯度送：只送最高满足条件的梯度
    /// </summary>
    ByTier = 0,

    /// <summary>
    /// 全部送：送所有满足条件的梯度
    /// </summary>
    AllTiers = 1
}
