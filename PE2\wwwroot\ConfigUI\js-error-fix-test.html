<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript引用错误修复测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    <script src="https://unpkg.com/vue@3.3.8/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.iife.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-title {
            margin-bottom: 24px;
            color: #333;
            font-size: 24px;
            font-weight: 600;
        }
        .status-indicator {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .status-success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .status-error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .test-section {
            margin-bottom: 24px;
            padding: 16px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
        }
        .test-section h3 {
            margin: 0 0 12px 0;
            color: #333;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1 class="test-title">JavaScript引用错误修复测试</h1>
            
            <div class="test-section">
                <h3>1. 组件定义检查</h3>
                <div :class="['status-indicator', componentCheckStatus.class]">
                    {{ componentCheckStatus.message }}
                </div>
            </div>

            <div class="test-section">
                <h3>2. EnhancedConditionRenderer渲染测试</h3>
                <div :class="['status-indicator', rendererTestStatus.class]">
                    {{ rendererTestStatus.message }}
                </div>
                
                <div v-if="testFieldConfig">
                    <enhanced-condition-renderer
                        field-name="combinationConditions"
                        :field-config="testFieldConfig"
                        :model-value="testValue"
                        rule-type="CombinationSpecialPriceRule"
                        @update:model-value="handleUpdate">
                    </enhanced-condition-renderer>
                </div>
            </div>

            <div class="test-section">
                <h3>3. 当前测试值</h3>
                <el-card>
                    <pre>{{ JSON.stringify(testValue, null, 2) }}</pre>
                </el-card>
            </div>
        </div>
    </div>

    <!-- 按顺序加载组件 -->
    <script src="./js/components/ConditionConfigRenderer.js"></script>
    <script src="./js/components/EnhancedConditionRenderer.js"></script>
    <script>
        const { createApp, ref, computed, onMounted } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            components: {
                'enhanced-condition-renderer': typeof EnhancedConditionRenderer !== 'undefined' ? EnhancedConditionRenderer : null
            },
            setup() {
                const testValue = ref([]);
                const testFieldConfig = ref(null);

                const componentCheckStatus = computed(() => {
                    const checks = [
                        { name: 'ConditionConfigRenderer', exists: typeof ConditionConfigRenderer !== 'undefined' },
                        { name: 'EnhancedConditionRenderer', exists: typeof EnhancedConditionRenderer !== 'undefined' }
                    ];

                    const failedChecks = checks.filter(c => !c.exists);
                    
                    if (failedChecks.length === 0) {
                        return {
                            class: 'status-success',
                            message: '✅ 所有组件都已正确定义'
                        };
                    } else {
                        return {
                            class: 'status-error',
                            message: `❌ 缺失组件: ${failedChecks.map(c => c.name).join(', ')}`
                        };
                    }
                });

                const rendererTestStatus = computed(() => {
                    if (typeof EnhancedConditionRenderer === 'undefined') {
                        return {
                            class: 'status-error',
                            message: '❌ EnhancedConditionRenderer未定义，无法进行渲染测试'
                        };
                    }

                    return {
                        class: 'status-success',
                        message: '✅ EnhancedConditionRenderer可以正常渲染'
                    };
                });

                const initTestData = () => {
                    testFieldConfig.value = {
                        "name": "combinationConditions",
                        "label": "组合条件",
                        "type": "array-complex",
                        "required": false,
                        "group": "condition",
                        "metadata": {
                            "elementSchema": {
                                "typeName": "CombinationSpecialPriceCondition",
                                "displayName": "组合特价条件",
                                "fields": [
                                    {
                                        "name": "productId",
                                        "label": "商品ID",
                                        "type": "input",
                                        "required": false,
                                        "group": "condition"
                                    },
                                    {
                                        "name": "requiredQuantity",
                                        "label": "所需数量",
                                        "type": "number",
                                        "required": false,
                                        "group": "condition"
                                    },
                                    {
                                        "name": "requiredAmount",
                                        "label": "所需金额",
                                        "type": "number",
                                        "required": false,
                                        "group": "condition"
                                    }
                                ]
                            },
                            "defaultItem": {
                                "productId": "",
                                "requiredQuantity": 0,
                                "requiredAmount": 0
                            },
                            "operations": ["add", "remove", "edit", "reorder"]
                        }
                    };

                    testValue.value = [
                        {
                            productId: "PROD001",
                            requiredQuantity: 2,
                            requiredAmount: 100.0
                        }
                    ];
                };

                const handleUpdate = (newValue) => {
                    console.log('测试值更新:', newValue);
                    testValue.value = newValue;
                    ElMessage.success('测试值已更新');
                };

                onMounted(() => {
                    initTestData();
                    
                    // 输出调试信息
                    console.log('ConditionConfigRenderer:', typeof ConditionConfigRenderer);
                    console.log('EnhancedConditionRenderer:', typeof EnhancedConditionRenderer);
                });

                return {
                    testValue,
                    testFieldConfig,
                    componentCheckStatus,
                    rendererTestStatus,
                    handleUpdate
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
