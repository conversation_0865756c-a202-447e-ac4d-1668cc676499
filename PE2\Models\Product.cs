namespace PE2.Models;

/// <summary>
/// 商品信息
/// </summary>
public class Product
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 商品名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 商品价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 商品分类
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// 商品条码
    /// </summary>
    public string Barcode { get; set; } = string.Empty;

    /// <summary>
    /// 商品品牌
    /// </summary>
    public string Brand { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsActive { get; set; } = true;

    public override bool Equals(object? obj)
    {
        return obj is Product product && Id == product.Id;
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(Id);
    }
}
