using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Core;

/// <summary>
/// 商品占用会话实现 - 支持事务性的商品占用管理
/// </summary>
internal sealed class OccupationSession : IOccupationSession
{
    private readonly ProductOccupationEngine _engine;
    private readonly ILogger _logger;
    private readonly Dictionary<string, ProductSessionState> _sessionStates = new();
    private readonly List<string> _reservationIds = new();
    private bool _disposed;
    private bool _committed;

    public string SessionId { get; }
    public ProcessedCart Cart { get; }

    internal OccupationSession(
        string sessionId,
        ProcessedCart cart,
        ProductOccupationEngine engine,
        ILogger logger)
    {
        SessionId = sessionId;
        Cart = cart;
        _engine = engine;
        _logger = logger;
    }

    /// <summary>
    /// 初始化会话状态
    /// </summary>
    internal async Task InitializeAsync(CancellationToken cancellationToken = default)
    {
        await Task.Run(() =>
        {
            // 初始化每个商品的会话状态
            foreach (var item in Cart.Items)
            {
                if (!_sessionStates.ContainsKey(item.ProductId))
                {
                    _sessionStates[item.ProductId] = new ProductSessionState
                    {
                        ProductId = item.ProductId,
                        ProductName = item.ProductName,
                        TotalQuantity = item.Quantity,
                        AvailableQuantity = item.Quantity,
                        UnitPrice = item.OriginalUnitPrice
                    };
                }
                else
                {
                    // 如果同一商品有多行，累加数量
                    var state = _sessionStates[item.ProductId];
                    state.TotalQuantity += item.Quantity;
                    state.AvailableQuantity += item.Quantity;
                }
            }
        }, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 尝试预留商品用于条件验证
    /// </summary>
    public async Task<ReservationResult> TryReserveForConditionAsync(
        string ruleId,
        string ruleName,
        List<string> productIds,
        int requiredQuantity,
        ProductExclusivityLevel exclusivityLevel = ProductExclusivityLevel.None,
        bool canStackWithOthers = true,
        CancellationToken cancellationToken = default)
    {
        return await TryReserveInternalAsync(
            ruleId,
            ruleName,
            productIds,
            requiredQuantity,
            ReservationType.Condition,
            exclusivityLevel,
            canStackWithOthers,
            cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 尝试预留商品用于促销执行
    /// </summary>
    public async Task<ReservationResult> TryReserveForExecutionAsync(
        string ruleId,
        string ruleName,
        List<string> productIds,
        int requiredQuantity,
        ProductExclusivityLevel exclusivityLevel = ProductExclusivityLevel.None,
        bool canStackWithOthers = true,
        CancellationToken cancellationToken = default)
    {
        return await TryReserveInternalAsync(
            ruleId,
            ruleName,
            productIds,
            requiredQuantity,
            ReservationType.Execution,
            exclusivityLevel,
            canStackWithOthers,
            cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 内部预留实现
    /// </summary>
    private async Task<ReservationResult> TryReserveInternalAsync(
        string ruleId,
        string ruleName,
        List<string> productIds,
        int requiredQuantity,
        ReservationType reservationType,
        ProductExclusivityLevel exclusivityLevel,
        bool canStackWithOthers,
        CancellationToken cancellationToken)
    {
        return await Task.Run(() =>
        {
            var result = new ReservationResult();
            var conflicts = new List<ConflictInfo>();
            var reservedProducts = new List<ReservedProductDetail>();
            var tempReservations = new List<(string ProductId, int Quantity)>();

            try
            {
                // 1. 检查排他性冲突
                var exclusivityConflicts = CheckExclusivityConflicts(productIds, exclusivityLevel, canStackWithOthers);
                if (exclusivityConflicts.Any())
                {
                    return new ReservationResult
                    {
                        IsSuccessful = false,
                        ErrorMessage = "存在排他性冲突",
                        Conflicts = exclusivityConflicts
                    };
                }

                // 2. 计算可用数量
                var availableQuantities = productIds.ToDictionary(
                    id => id,
                    id => _sessionStates.GetValueOrDefault(id)?.AvailableQuantity ?? 0
                );

                var totalAvailable = availableQuantities.Values.Sum();
                if (totalAvailable < requiredQuantity)
                {
                    return new ReservationResult
                    {
                        IsSuccessful = false,
                        ErrorMessage = $"商品数量不足，需要{requiredQuantity}件，可用{totalAvailable}件",
                        Conflicts = [new ConflictInfo
                        {
                            ProductId = string.Join(",", productIds),
                            ConflictType = ConflictType.InsufficientQuantity,
                            Description = $"需要{requiredQuantity}件，可用{totalAvailable}件"
                        }]
                    };
                }

                // 3. 按策略分配数量
                var allocation = AllocateQuantity(productIds, requiredQuantity, availableQuantities);

                // 4. 执行预留
                foreach (var (productId, quantity) in allocation)
                {
                    if (quantity > 0)
                    {
                        var sessionState = _sessionStates[productId];
                        sessionState.AvailableQuantity -= quantity;

                        var reservation = new SessionReservation
                        {
                            ReservationId = Guid.NewGuid().ToString("N"),
                            RuleId = ruleId,
                            RuleName = ruleName,
                            ReservationType = reservationType,
                            Quantity = quantity,
                            ExclusivityLevel = exclusivityLevel,
                            CanStackWithOthers = canStackWithOthers
                        };

                        sessionState.Reservations.Add(reservation);
                        _reservationIds.Add(reservation.ReservationId);
                        tempReservations.Add((productId, quantity));

                        reservedProducts.Add(new ReservedProductDetail
                        {
                            ProductId = productId,
                            ProductName = sessionState.ProductName,
                            ReservedQuantity = quantity,
                            UnitPrice = sessionState.UnitPrice,
                            ReservationType = reservationType
                        });
                    }
                }

                return new ReservationResult
                {
                    IsSuccessful = true,
                    ReservationIds = _reservationIds.ToList(),
                    ReservedProducts = reservedProducts
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "预留商品时发生异常: SessionId={SessionId}, RuleId={RuleId}", SessionId, ruleId);

                // 回滚临时预留
                foreach (var (productId, quantity) in tempReservations)
                {
                    if (_sessionStates.TryGetValue(productId, out var state))
                    {
                        state.AvailableQuantity += quantity;
                        state.Reservations.RemoveAll(r => r.RuleId == ruleId);
                    }
                }

                return new ReservationResult
                {
                    IsSuccessful = false,
                    ErrorMessage = $"预留失败: {ex.Message}"
                };
            }
        }, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 检查排他性冲突
    /// </summary>
    private List<ConflictInfo> CheckExclusivityConflicts(
        List<string> productIds,
        ProductExclusivityLevel exclusivityLevel,
        bool canStackWithOthers)
    {
        var conflicts = new List<ConflictInfo>();

        foreach (var productId in productIds)
        {
            if (!_sessionStates.TryGetValue(productId, out var sessionState))
                continue;

            foreach (var existingReservation in sessionState.Reservations)
            {
                // 检查是否可以叠加
                if (!CanStackReservations(existingReservation, exclusivityLevel, canStackWithOthers))
                {
                    conflicts.Add(new ConflictInfo
                    {
                        ProductId = productId,
                        ConflictType = ConflictType.ExclusivityConflict,
                        Description = $"与规则 {existingReservation.RuleName} 存在排他性冲突",
                        ConflictingRuleId = existingReservation.RuleId
                    });
                }
            }
        }

        return conflicts;
    }

    /// <summary>
    /// 检查两个预留是否可以叠加
    /// </summary>
    private static bool CanStackReservations(
        SessionReservation existingReservation,
        ProductExclusivityLevel newExclusivityLevel,
        bool newCanStackWithOthers)
    {
        // 如果现有预留不允许叠加
        if (!existingReservation.CanStackWithOthers)
            return false;

        // 如果新预留不允许叠加
        if (!newCanStackWithOthers)
            return false;

        // 根据排他性级别判断
        return existingReservation.ExclusivityLevel switch
        {
            ProductExclusivityLevel.None => true,
            ProductExclusivityLevel.Partial => newExclusivityLevel <= ProductExclusivityLevel.Partial,
            ProductExclusivityLevel.Exclusive => false,
            _ => true
        };
    }

    /// <summary>
    /// 分配数量到商品
    /// </summary>
    private List<(string ProductId, int Quantity)> AllocateQuantity(
        List<string> productIds,
        int requiredQuantity,
        Dictionary<string, int> availableQuantities)
    {
        var allocation = new List<(string ProductId, int Quantity)>();
        var remaining = requiredQuantity;

        // 按可用数量降序排序，优先使用数量多的商品
        var sortedProducts = productIds
            .Where(id => availableQuantities[id] > 0)
            .OrderByDescending(id => availableQuantities[id])
            .ToList();

        foreach (var productId in sortedProducts)
        {
            if (remaining <= 0)
                break;

            var available = availableQuantities[productId];
            var toAllocate = Math.Min(remaining, available);

            allocation.Add((productId, toAllocate));
            remaining -= toAllocate;
        }

        return allocation;
    }

    /// <summary>
    /// 释放指定规则的所有预留
    /// </summary>
    public async Task ReleaseReservationsAsync(string ruleId, CancellationToken cancellationToken = default)
    {
        await Task.Run(() =>
        {
            foreach (var sessionState in _sessionStates.Values)
            {
                var reservationsToRemove = sessionState.Reservations
                    .Where(r => r.RuleId == ruleId)
                    .ToList();

                foreach (var reservation in reservationsToRemove)
                {
                    sessionState.AvailableQuantity += reservation.Quantity;
                    sessionState.Reservations.Remove(reservation);
                    _reservationIds.Remove(reservation.ReservationId);
                }
            }
        }, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 提交会话
    /// </summary>
    public async Task<CommitResult> CommitAsync(CancellationToken cancellationToken = default)
    {
        if (_committed)
            return new CommitResult { IsSuccessful = true };

        return await Task.Run(() =>
        {
            try
            {
                // 将会话状态同步到全局状态
                foreach (var kvp in _sessionStates)
                {
                    var productId = kvp.Key;
                    var sessionState = kvp.Value;

                    var globalState = _engine.GetOccupationState(productId);
                    globalState.TotalQuantity = sessionState.TotalQuantity;
                    globalState.AvailableQuantity = sessionState.AvailableQuantity;

                    // 转换会话预留为全局预留
                    foreach (var sessionReservation in sessionState.Reservations)
                    {
                        globalState.Reservations.Add(new OccupationReservation
                        {
                            SessionId = SessionId,
                            RuleId = sessionReservation.RuleId,
                            RuleName = sessionReservation.RuleName,
                            ReservationType = sessionReservation.ReservationType,
                            Quantity = sessionReservation.Quantity,
                            ExclusivityLevel = sessionReservation.ExclusivityLevel,
                            CanStackWithOthers = sessionReservation.CanStackWithOthers
                        });
                    }

                    _engine.UpdateOccupationState(productId, globalState);
                }

                _committed = true;
                return new CommitResult { IsSuccessful = true };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "提交会话时发生异常: SessionId={SessionId}", SessionId);
                return new CommitResult
                {
                    IsSuccessful = false,
                    ErrorMessage = ex.Message
                };
            }
        }, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 回滚会话
    /// </summary>
    public async Task RollbackAsync(CancellationToken cancellationToken = default)
    {
        await Task.Run(() =>
        {
            _sessionStates.Clear();
            _reservationIds.Clear();
        }, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 获取会话状态报告
    /// </summary>
    public SessionStateReport GetStateReport()
    {
        return new SessionStateReport
        {
            SessionId = SessionId,
            TotalProducts = _sessionStates.Count,
            TotalReservations = _sessionStates.Values.Sum(s => s.Reservations.Count),
            ProductStates = _sessionStates.Values.ToList(),
            IsCommitted = _committed
        };
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            if (!_committed)
            {
                _engine.CleanupSession(SessionId);
            }
            _disposed = true;
        }
    }
}

/// <summary>
/// 商品会话状态
/// </summary>
internal sealed class ProductSessionState
{
    public required string ProductId { get; init; }
    public string ProductName { get; init; } = string.Empty;
    public int TotalQuantity { get; set; }
    public int AvailableQuantity { get; set; }
    public decimal UnitPrice { get; init; }
    public List<SessionReservation> Reservations { get; init; } = [];
}

/// <summary>
/// 会话预留记录
/// </summary>
internal sealed class SessionReservation
{
    public required string ReservationId { get; init; }
    public required string RuleId { get; init; }
    public string RuleName { get; init; } = string.Empty;
    public required ReservationType ReservationType { get; init; }
    public int Quantity { get; set; }
    public ProductExclusivityLevel ExclusivityLevel { get; init; }
    public bool CanStackWithOthers { get; init; }
    public DateTime CreatedAt { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// 提交结果
/// </summary>
public sealed class CommitResult
{
    public bool IsSuccessful { get; init; }
    public string ErrorMessage { get; init; } = string.Empty;
}

/// <summary>
/// 会话状态报告
/// </summary>
public sealed class SessionStateReport
{
    public required string SessionId { get; init; }
    public int TotalProducts { get; init; }
    public int TotalReservations { get; init; }
    public List<ProductSessionState> ProductStates { get; init; } = [];
    public bool IsCommitted { get; init; }
}
