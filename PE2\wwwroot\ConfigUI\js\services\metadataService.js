/**
 * 促销元数据管理服务
 * 负责从后端API获取和缓存促销类型的配置元数据
 * 支持动态促销类型加载和缓存策略
 */

class PromotionMetadataService {
    constructor(apiService) {
        this.apiService = apiService;
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
        this.loading = new Set(); // 跟踪正在加载的请求
    }

    /**
     * 获取所有促销类型的元数据
     * @returns {Promise<Object>} 促销类型元数据
     */
    async getPromotionTypes() {
        const cacheKey = 'promotion-types';
        
        // 检查缓存
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
        }

        // 防止重复请求
        if (this.loading.has(cacheKey)) {
            return new Promise((resolve, reject) => {
                const checkInterval = setInterval(() => {
                    if (!this.loading.has(cacheKey)) {
                        clearInterval(checkInterval);
                        if (this.cache.has(cacheKey)) {
                            resolve(this.cache.get(cacheKey).data);
                        } else {
                            reject(new Error('加载促销类型元数据失败'));
                        }
                    }
                }, 100);
            });
        }

        this.loading.add(cacheKey);

        try {
            console.log('正在从API获取促销类型元数据...');
            const response = await this.apiService.getPromotionMetadata();
            
            if (!response || typeof response !== 'object') {
                throw new Error('返回的元数据格式无效');
            }

            // 处理和标准化元数据
            const processedData = this.processMetadata(response);
            
            // 缓存数据
            this.cache.set(cacheKey, {
                data: processedData,
                timestamp: Date.now()
            });

            console.log('促销类型元数据加载完成:', processedData);
            return processedData;

        } catch (error) {
            console.error('获取促销类型元数据失败:', error);
            
            // 如果有旧缓存数据，返回旧数据
            if (this.cache.has(cacheKey)) {
                console.warn('使用缓存的元数据');
                return this.cache.get(cacheKey).data;
            }
            
            // 返回空数据而不是抛出错误，确保界面可以正常显示
            console.warn('返回空的元数据，界面将显示空状态');
            return {};
            
        } finally {
            this.loading.delete(cacheKey);
        }
    }

    /**
     * 获取指定促销类型的详细信息
     * @param {string} ruleType 规则类型
     * @returns {Promise<Object>} 促销类型详细信息
     */
    async getPromotionTypeDetail(ruleType) {
        const cacheKey = `promotion-type-${ruleType}`;
        
        // 检查缓存
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
        }

        // 防止重复请求
        if (this.loading.has(cacheKey)) {
            return new Promise((resolve, reject) => {
                const checkInterval = setInterval(() => {
                    if (!this.loading.has(cacheKey)) {
                        clearInterval(checkInterval);
                        if (this.cache.has(cacheKey)) {
                            resolve(this.cache.get(cacheKey).data);
                        } else {
                            reject(new Error(`加载促销类型 ${ruleType} 详细信息失败`));
                        }
                    }
                }, 100);
            });
        }

        this.loading.add(cacheKey);

        try {
            console.log(`正在获取促销类型 ${ruleType} 的详细信息...`);
            const response = await this.apiService.getPromotionTypeDetail(ruleType);
            
            if (!response) {
                throw new Error(`促销类型 ${ruleType} 不存在`);
            }

            // 处理详细信息
            const processedDetail = this.processTypeDetail(response);
            
            // 缓存数据
            this.cache.set(cacheKey, {
                data: processedDetail,
                timestamp: Date.now()
            });

            console.log(`促销类型 ${ruleType} 详细信息加载完成:`, processedDetail);
            return processedDetail;

        } catch (error) {
            console.error(`获取促销类型 ${ruleType} 详细信息失败:`, error);
            throw error;
        } finally {
            this.loading.delete(cacheKey);
        }
    }

    /**
     * 处理和标准化元数据
     * @param {Object} rawData 原始元数据
     * @returns {Object} 处理后的元数据
     */
    processMetadata(rawData) {
        const processed = {};

        try {
            Object.keys(rawData).forEach(categoryKey => {
                const category = rawData[categoryKey];
                
                if (!category || typeof category !== 'object') {
                    console.warn(`跳过无效的分类: ${categoryKey}`);
                    return;
                }

                processed[categoryKey] = {
                    name: category.name || categoryKey,
                    icon: category.icon || 'Setting',
                    description: category.description || '',
                    types: {}
                };

                if (category.types && typeof category.types === 'object') {
                    Object.keys(category.types).forEach(typeKey => {
                        const type = category.types[typeKey];
                        
                        if (!type || typeof type !== 'object') {
                            console.warn(`跳过无效的类型: ${categoryKey}.${typeKey}`);
                            return;
                        }

                        processed[categoryKey].types[typeKey] = {
                            name: type.name || typeKey,
                            description: type.description || '',
                            ruleType: type.ruleType || typeKey,
                            fields: this.processFields(type.fields || [])
                        };
                    });
                }
            });

            return processed;

        } catch (error) {
            console.error('处理元数据时发生错误:', error);
            return {};
        }
    }

    /**
     * 处理类型详细信息
     * @param {Object} rawDetail 原始详细信息
     * @returns {Object} 处理后的详细信息
     */
    processTypeDetail(rawDetail) {
        return {
            ruleType: rawDetail.ruleType,
            name: rawDetail.name || rawDetail.ruleType,
            description: rawDetail.description || '',
            fields: this.processFields(rawDetail.fields || []),
            category: rawDetail.category || {},
            examples: rawDetail.examples || []
        };
    }

    /**
     * 处理字段配置
     * @param {Array} rawFields 原始字段配置
     * @returns {Array} 处理后的字段配置
     */
    processFields(rawFields) {
        if (!Array.isArray(rawFields)) {
            console.warn('字段配置不是数组，返回空数组');
            return [];
        }

        return rawFields.map(field => {
            try {
                const processed = {
                    name: field.name,
                    displayName: field.displayName || field.name,
                    type: field.type || 'string',
                    required: !!field.required,
                    description: field.description || '',
                    group: field.group || '基本信息',
                    span: field.span || 24,
                    placeholder: field.placeholder,
                    readonly: !!field.readonly,
                    defaultValue: field.defaultValue
                };

                // 类型特定属性
                switch (field.type) {
                    case 'number':
                    case 'decimal':
                    case 'integer':
                        processed.min = field.min;
                        processed.max = field.max;
                        processed.step = field.step;
                        processed.precision = field.precision;
                        break;

                    case 'string':
                    case 'text':
                        processed.minLength = field.minLength;
                        processed.maxLength = field.maxLength;
                        processed.pattern = field.pattern;
                        processed.patternMessage = field.patternMessage;
                        processed.subType = field.subType; // textarea
                        processed.rows = field.rows;
                        break;

                    case 'boolean':
                        processed.activeText = field.activeText;
                        processed.inactiveText = field.inactiveText;
                        break;

                    case 'select':
                    case 'enum':
                        processed.options = field.options || [];
                        processed.filterable = field.filterable;
                        processed.multiple = field.multiple;
                        break;

                    case 'array':
                        processed.itemType = field.itemType || 'string';
                        processed.itemSchema = field.itemSchema;
                        processed.minItems = field.minItems;
                        processed.maxItems = field.maxItems;
                        break;

                    case 'object':
                        processed.schema = field.schema;
                        processed.properties = field.properties;
                        break;
                }

                // 扩展属性
                if (field.props && typeof field.props === 'object') {
                    processed.props = field.props;
                }

                return processed;

            } catch (error) {
                console.error(`处理字段 ${field?.name || '未知'} 时发生错误:`, error);
                return {
                    name: field?.name || 'unknown',
                    displayName: field?.displayName || field?.name || 'Unknown',
                    type: 'string',
                    required: false,
                    description: '字段处理异常',
                    group: '基本信息'
                };
            }
        });
    }

    /**
     * 清除缓存
     * @param {string} key 缓存键，不提供则清除所有缓存
     */
    clearCache(key = null) {
        if (key) {
            this.cache.delete(key);
            console.log(`缓存 ${key} 已清除`);
        } else {
            this.cache.clear();
            console.log('所有缓存已清除');
        }
    }

    /**
     * 获取缓存状态
     * @returns {Object} 缓存状态信息
     */
    getCacheStatus() {
        const status = {
            size: this.cache.size,
            loading: Array.from(this.loading),
            keys: Array.from(this.cache.keys()),
            details: {}
        };

        this.cache.forEach((value, key) => {
            status.details[key] = {
                timestamp: value.timestamp,
                age: Date.now() - value.timestamp,
                expired: Date.now() - value.timestamp > this.cacheTimeout
            };
        });

        return status;
    }

    /**
     * 预加载所有促销类型数据
     * @returns {Promise<void>}
     */
    async preloadAll() {
        try {
            console.log('开始预加载促销类型数据...');
            const types = await this.getPromotionTypes();
            
            // 预加载所有类型的详细信息
            const promises = [];
            Object.keys(types).forEach(categoryKey => {
                const category = types[categoryKey];
                if (category.types) {
                    Object.keys(category.types).forEach(typeKey => {
                        const type = category.types[typeKey];
                        if (type.ruleType) {
                            promises.push(this.getPromotionTypeDetail(type.ruleType));
                        }
                    });
                }
            });

            await Promise.allSettled(promises);
            console.log('促销类型数据预加载完成');
            
        } catch (error) {
            console.error('预加载促销类型数据失败:', error);
        }
    }

    /**
     * 检查API连接状态
     * @returns {Promise<boolean>} 连接状态
     */
    async checkApiConnection() {
        try {
            await this.getPromotionTypes();
            return true;
        } catch (error) {
            console.error('API连接检查失败:', error);
            return false;
        }
    }
}

// 创建单例实例
let metadataServiceInstance = null;

/**
 * 获取元数据服务实例
 * @param {Object} apiService API服务实例
 * @returns {PromotionMetadataService} 元数据服务实例
 */
function getMetadataService(apiService) {
    if (!metadataServiceInstance && apiService) {
        metadataServiceInstance = new PromotionMetadataService(apiService);
    }
    return metadataServiceInstance;
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { PromotionMetadataService, getMetadataService };
} else if (typeof window !== 'undefined') {
    window.PromotionMetadataService = PromotionMetadataService;
    window.getMetadataService = getMetadataService;
}
