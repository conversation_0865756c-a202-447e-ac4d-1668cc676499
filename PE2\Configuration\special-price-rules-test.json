[{"$type": "UnifiedSpecialPrice", "id": "UNIFIED_SPECIAL_PRICE_001", "name": "统一特价测试 - A商品特价100元（不翻倍）", "description": "A商品满1件时，特价100元（不翻倍）", "priority": 70, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "specialPriceSelectionStrategy": "CustomerBenefit", "applicableProductIds": ["A"], "minQuantity": 1, "minAmount": 0, "specialPrice": 100.0, "isByAmount": false}, {"$type": "UnifiedSpecialPrice", "id": "UNIFIED_SPECIAL_PRICE_002", "name": "统一特价测试 - B商品特价100元（最多翻2倍）", "description": "B商品满1件时，特价100元（最多翻2倍）", "priority": 75, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 2, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "specialPriceSelectionStrategy": "CustomerBenefit", "applicableProductIds": ["B"], "minQuantity": 1, "minAmount": 0, "specialPrice": 100.0, "isByAmount": false}, {"$type": "TieredSpecialPrice", "id": "TIERED_SPECIAL_PRICE_001", "name": "梯度特价测试 - A商品梯度特价（不翻倍）", "description": "当购买商品数量大于等于1件时，特价100元；当商品数量大于等于2件时，特价50元；当商品数量大于等于三件时，特价10元", "priority": 80, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "specialPriceSelectionStrategy": "CustomerBenefit", "applicableProductIds": ["A"], "isByAmount": false, "specialPriceTiers": [{"minQuantity": 1, "minAmount": 0, "specialPrice": 100.0, "description": "满1件特价100元"}, {"minQuantity": 2, "minAmount": 0, "specialPrice": 50.0, "description": "满2件特价50元"}, {"minQuantity": 3, "minAmount": 0, "specialPrice": 10.0, "description": "满3件特价10元"}]}, {"$type": "TieredSpecialPrice", "id": "TIERED_SPECIAL_PRICE_002", "name": "梯度特价测试 - A商品梯度特价（翻倍）", "description": "当购买商品数量大于等于3件时，特价10元，支持翻倍", "priority": 85, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 2, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "specialPriceSelectionStrategy": "CustomerBenefit", "applicableProductIds": ["A"], "isByAmount": false, "specialPriceTiers": [{"minQuantity": 3, "minAmount": 0, "specialPrice": 10.0, "description": "满3件特价10元"}]}, {"$type": "CombinationSpecialPrice", "id": "COMBINATION_SPECIAL_PRICE_001", "name": "组合特价测试 - 买A+B特价600元（不翻倍）", "description": "A、B商品各购买数量大于等于1件时，特价600元", "priority": 90, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "specialPriceSelectionStrategy": "CustomerBenefit", "specialPrice": 600.0, "combinationConditions": [{"productId": "A", "requiredQuantity": 1, "requiredAmount": 0}, {"productId": "B", "requiredQuantity": 1, "requiredAmount": 0}]}, {"$type": "CombinationSpecialPrice", "id": "COMBINATION_SPECIAL_PRICE_002", "name": "组合特价测试 - 买A+B特价600元（翻倍）", "description": "A、B商品各购买数量大于等于1件时，特价600元，支持翻倍", "priority": 95, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 3, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "specialPriceSelectionStrategy": "CustomerBenefit", "specialPrice": 600.0, "combinationConditions": [{"productId": "A", "requiredQuantity": 1, "requiredAmount": 0}, {"productId": "B", "requiredQuantity": 1, "requiredAmount": 0}]}, {"$type": "IndividualSpecialPrice", "id": "INDIVIDUAL_SPECIAL_PRICE_001", "name": "单品特价测试 - A、B商品独立特价", "description": "A商品特价600元，B商品特价500元", "priority": 60, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "specialPriceSelectionStrategy": "CustomerBenefit", "productConfigs": [{"productId": "A", "specialPrice": 600.0, "description": "A商品特价600元"}, {"productId": "B", "specialPrice": 500.0, "description": "B商品特价500元"}]}, {"$type": "UnifiedSpecialPrice", "id": "UNIFIED_SPECIAL_PRICE_AMOUNT_001", "name": "按金额统一特价测试 - 满1000元特价500元", "description": "A商品满1000元时，特价500元", "priority": 65, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "specialPriceSelectionStrategy": "CustomerBenefit", "applicableProductIds": ["A"], "minQuantity": 0, "minAmount": 1000.0, "specialPrice": 500.0, "isByAmount": true}]