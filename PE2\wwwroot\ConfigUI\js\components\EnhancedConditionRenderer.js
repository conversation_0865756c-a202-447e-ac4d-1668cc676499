/**
 * 增强版条件配置渲染器
 * 支持复杂类型、集合类型和嵌套对象的智能渲染
 */

const EnhancedConditionRenderer = {
    name: 'EnhancedConditionRenderer',
    props: {
        fieldName: {
            type: String,
            required: true
        },
        fieldConfig: {
            type: Object,
            required: true
        },
        modelValue: {
            type: [Object, String, Array],
            default: () => null
        },
        ruleType: {
            type: String,
            required: true
        },
        disabled: {
            type: Boolean,
            default: false
        }
    },
    emits: ['update:modelValue'],
    setup(props, { emit }) {
        const { ref, computed, watch } = Vue;
        const { ElMessage } = ElementPlus;

        const currentValue = ref(null);
        const editMode = ref('form'); // 'form', 'json', 'visual'

        // 初始化值
        const initValue = () => {
            if (props.modelValue) {
                currentValue.value = typeof props.modelValue === 'string' 
                    ? JSON.parse(props.modelValue) 
                    : { ...props.modelValue };
            } else {
                currentValue.value = getDefaultValue();
            }
        };

        // 获取默认值
        const getDefaultValue = () => {
            const metadata = props.fieldConfig.metadata;
            if (metadata && metadata.defaultValue) {
                return metadata.defaultValue;
            }
            
            const type = props.fieldConfig.type;
            if (type === 'array-complex' || type === 'array-simple') {
                return [];
            } else if (type === 'object-complex') {
                return {};
            }
            
            return null;
        };

        // 获取配置模板
        const getConfigTemplate = computed(() => {
            const fieldName = props.fieldName.toLowerCase();
            const ruleType = props.ruleType.toLowerCase();

            // 根据字段名称和规则类型确定配置模板
            if (fieldName.includes('combination')) {
                return getCombinationTemplate();
            } else if (fieldName.includes('exchange')) {
                return getExchangeTemplate();
            } else if (fieldName.includes('buy')) {
                return getBuyTemplate();
            } else if (fieldName.includes('tier')) {
                return getTierTemplate();
            } else {
                return getGeneralTemplate();
            }
        });

        // 组合条件模板
        const getCombinationTemplate = () => ({
            title: '组合购买条件',
            description: '设置商品组合的购买条件',
            icon: 'Grid',
            fields: [
                {
                    name: 'requiredProducts',
                    label: '必需商品',
                    type: 'product-list',
                    required: true,
                    description: '客户必须购买的商品列表',
                    defaultValue: []
                },
                {
                    name: 'totalMinQuantity',
                    label: '总最小数量',
                    type: 'number',
                    min: 1,
                    description: '所有商品的总最小购买数量'
                },
                {
                    name: 'totalMinAmount',
                    label: '总最小金额',
                    type: 'number',
                    min: 0,
                    step: 0.01,
                    description: '所有商品的总最小购买金额'
                },
                {
                    name: 'mustBuyAll',
                    label: '必须购买所有商品',
                    type: 'switch',
                    defaultValue: false,
                    description: '是否必须购买列表中的所有商品'
                },
                {
                    name: 'allowPartialMatch',
                    label: '允许部分匹配',
                    type: 'switch',
                    defaultValue: true,
                    description: '是否允许部分匹配条件'
                }
            ]
        });

        // 兑换条件模板
        const getExchangeTemplate = () => ({
            title: '兑换条件配置',
            description: '设置商品兑换的条件和规则',
            icon: 'Refresh',
            fields: [
                {
                    name: 'exchangeItems',
                    label: '兑换商品',
                    type: 'product-list',
                    required: true,
                    description: '可以兑换的商品列表',
                    defaultValue: []
                },
                {
                    name: 'exchangeRate',
                    label: '兑换比例',
                    type: 'number',
                    min: 0,
                    max: 1,
                    step: 0.01,
                    description: '兑换折扣比例 (0-1)'
                },
                {
                    name: 'minExchangeAmount',
                    label: '最小兑换金额',
                    type: 'number',
                    min: 0,
                    step: 0.01,
                    description: '启动兑换的最小金额'
                },
                {
                    name: 'maxExchangeQuantity',
                    label: '最大兑换数量',
                    type: 'number',
                    min: 1,
                    description: '单次最大兑换数量'
                }
            ]
        });

        // 购买条件模板
        const getBuyTemplate = () => ({
            title: '购买条件设置',
            description: '配置触发优惠的购买条件',
            icon: 'ShoppingCart',
            fields: [
                {
                    name: 'targetProducts',
                    label: '目标商品',
                    type: 'product-list',
                    required: true,
                    description: '参与优惠的商品列表',
                    defaultValue: []
                },
                {
                    name: 'minQuantity',
                    label: '最小购买数量',
                    type: 'number',
                    min: 1,
                    description: '每个商品的最小购买数量'
                },
                {
                    name: 'minAmount',
                    label: '最小购买金额',
                    type: 'number',
                    min: 0,
                    step: 0.01,
                    description: '触发优惠的最小购买金额'
                },
                {
                    name: 'calculateBy',
                    label: '计算方式',
                    type: 'select',
                    options: [
                        { value: 'quantity', label: '按数量计算' },
                        { value: 'amount', label: '按金额计算' },
                        { value: 'both', label: '数量和金额都满足' }
                    ],
                    defaultValue: 'quantity'
                }
            ]
        });

        // 阶梯条件模板
        const getTierTemplate = () => ({
            title: '阶梯条件配置',
            description: '设置阶梯式优惠条件',
            icon: 'TrendCharts',
            fields: [
                {
                    name: 'tiers',
                    label: '阶梯配置',
                    type: 'tier-list',
                    required: true,
                    description: '阶梯式条件配置',
                    defaultValue: []
                },
                {
                    name: 'tierMode',
                    label: '阶梯模式',
                    type: 'select',
                    options: [
                        { value: 'quantity', label: '按数量阶梯' },
                        { value: 'amount', label: '按金额阶梯' },
                        { value: 'mixed', label: '混合阶梯' }
                    ],
                    defaultValue: 'quantity'
                },
                {
                    name: 'cumulative',
                    label: '累计计算',
                    type: 'switch',
                    defaultValue: true,
                    description: '是否累计计算优惠'
                }
            ]
        });

        // 通用模板
        const getGeneralTemplate = () => ({
            title: '通用条件配置',
            description: '通用的条件设置',
            icon: 'Setting',
            fields: [
                {
                    name: 'conditions',
                    label: '条件配置',
                    type: 'json',
                    description: '请输入JSON格式的条件配置'
                }
            ]
        });

        // 更新值
        const updateValue = (value) => {
            currentValue.value = value;
            emit('update:modelValue', value);
        };

        // 切换编辑模式
        const switchEditMode = (mode) => {
            editMode.value = mode;
        };

        // 验证配置
        const validateConfig = () => {
            const template = getConfigTemplate.value;
            const errors = [];

            template.fields.forEach(field => {
                if (field.required && !currentValue.value?.[field.name]) {
                    errors.push(`${field.label}为必填项`);
                }
            });

            return errors;
        };        // 重置配置
        const resetConfig = () => {
            currentValue.value = getDefaultValue();
            emit('update:modelValue', currentValue.value);
        };

        // 数组操作方法
        const addArrayItem = () => {
            if (!Array.isArray(currentValue.value)) {
                currentValue.value = [];
            }
            const defaultItem = props.fieldConfig.metadata?.defaultItem || {};
            currentValue.value.push({ ...defaultItem });
            emit('update:modelValue', currentValue.value);
        };

        const removeArrayItem = (index) => {
            if (Array.isArray(currentValue.value)) {
                currentValue.value.splice(index, 1);
                emit('update:modelValue', currentValue.value);
            }
        };

        const updateArrayItem = (index, fieldName, value) => {
            if (Array.isArray(currentValue.value) && currentValue.value[index]) {
                currentValue.value[index][fieldName] = value;
                emit('update:modelValue', currentValue.value);
            }
        };

        const updateField = (fieldName, value) => {
            if (typeof currentValue.value === 'object' && currentValue.value !== null) {
                currentValue.value[fieldName] = value;
                emit('update:modelValue', currentValue.value);
            }
        };

        // 计算属性
        const isArrayType = computed(() => {
            return props.fieldConfig.type === 'array-complex' || props.fieldConfig.type === 'array-simple';
        });

        const getElementFields = computed(() => {
            if (props.fieldConfig.metadata?.elementSchema?.fields) {
                return props.fieldConfig.metadata.elementSchema.fields;
            }
            return [];
        });

        // 监听属性变化
        watch(() => props.modelValue, initValue, { immediate: true });        return {
            currentValue,
            editMode,
            getConfigTemplate,
            updateValue,
            switchEditMode,
            validateConfig,
            resetConfig,
            addArrayItem,
            removeArrayItem,
            updateArrayItem,
            updateField,
            isArrayType,
            getElementFields
        };
    },
    template: `
        <div class="enhanced-condition-renderer">
            <!-- 头部工具栏 -->
            <div class="condition-header">
                <div class="condition-title">
                    <el-icon><component :is="getConfigTemplate.icon" /></el-icon>
                    <span>{{ getConfigTemplate.title }}</span>
                </div>
                <div class="condition-actions">
                    <el-button-group>
                        <el-button 
                            :type="editMode === 'form' ? 'primary' : ''"
                            size="small"
                            @click="switchEditMode('form')"
                        >
                            表单模式
                        </el-button>
                        <el-button 
                            :type="editMode === 'json' ? 'primary' : ''"
                            size="small"
                            @click="switchEditMode('json')"
                        >
                            JSON模式
                        </el-button>
                        <el-button 
                            :type="editMode === 'visual' ? 'primary' : ''"
                            size="small"
                            @click="switchEditMode('visual')"
                        >
                            可视化模式
                        </el-button>
                    </el-button-group>
                    <el-button size="small" @click="resetConfig">
                        <el-icon><RefreshLeft /></el-icon>
                        重置
                    </el-button>
                </div>
            </div>

            <!-- 描述 -->
            <div class="condition-description">
                {{ getConfigTemplate.description }}
            </div>            <!-- 表单模式 -->
            <div v-if="editMode === 'form'" class="form-mode">
                <div v-if="isArrayType" class="array-form">
                    <div v-for="(item, index) in currentValue" :key="index" class="array-item">
                        <el-card>
                            <template #header>
                                <div class="array-item-header">
                                    <span>{{ fieldConfig.label || fieldConfig.name }} {{ index + 1 }}</span>
                                    <el-button size="small" type="danger" @click="removeArrayItem(index)" :disabled="disabled">删除</el-button>
                                </div>
                            </template>
                            <div v-for="field in getElementFields" :key="field.name" class="form-field">
                                <el-form-item :label="field.label || field.name">
                                    <el-input 
                                        v-if="field.type === 'input' || field.type?.type === 'input'"
                                        v-model="item[field.name]"
                                        @input="updateArrayItem(index, field.name, $event)"
                                        :disabled="disabled"
                                    />
                                    <el-input-number
                                        v-else-if="field.type === 'number' || field.type?.type === 'number'"
                                        v-model="item[field.name]"
                                        @change="updateArrayItem(index, field.name, $event)"
                                        :disabled="disabled"
                                        style="width: 100%"
                                    />
                                </el-form-item>
                            </div>
                        </el-card>
                    </div>
                    <el-button @click="addArrayItem" type="primary" :disabled="disabled" style="width: 100%; margin-top: 16px;">
                        添加{{ fieldConfig.label || fieldConfig.name }}
                    </el-button>
                </div>
                <div v-else class="object-form">
                    <div v-for="field in getElementFields" :key="field.name" class="form-field">
                        <el-form-item :label="field.label || field.name">
                            <el-input 
                                v-if="field.type === 'input' || field.type?.type === 'input'"
                                v-model="currentValue[field.name]"
                                @input="updateField(field.name, $event)"
                                :disabled="disabled"
                            />
                            <el-input-number
                                v-else-if="field.type === 'number' || field.type?.type === 'number'"
                                v-model="currentValue[field.name]"
                                @change="updateField(field.name, $event)"
                                :disabled="disabled"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </div>
                </div>
            </div>

            <!-- JSON模式 -->
            <div v-else-if="editMode === 'json'" class="json-mode">
                <el-input
                    type="textarea"
                    :model-value="JSON.stringify(currentValue, null, 2)"
                    @update:model-value="value => { try { updateValue(JSON.parse(value)); } catch {} }"
                    :rows="10"
                    placeholder="请输入有效的JSON配置"
                    style="font-family: monospace;"
                    :disabled="disabled"
                />
            </div>            <!-- 可视化模式 -->
            <div v-else-if="editMode === 'visual'" class="visual-mode">
                <el-result icon="info" title="可视化模式" sub-title="可视化编辑器正在开发中，请使用表单模式或JSON模式">
                    <template #extra>
                        <el-button type="primary" @click="editMode = 'form'">切换到表单模式</el-button>
                    </template>
                </el-result>
            </div>

            <!-- 配置预览 -->
            <div class="condition-preview">
                <el-divider>配置预览</el-divider>
                <div class="preview-content">
                    <pre>{{ JSON.stringify(currentValue, null, 2) }}</pre>
                </div>
            </div>        </div>
    `
};

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedConditionRenderer;
} else if (typeof window !== 'undefined') {
    window.EnhancedConditionRenderer = EnhancedConditionRenderer;
}
