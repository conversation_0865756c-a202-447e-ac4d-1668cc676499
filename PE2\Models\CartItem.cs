namespace PE2.Models;

/// <summary>
/// 购物车商品项
/// </summary>
public class CartItem
{
    /// <summary>
    /// 商品信息
    /// </summary>
    public Product Product { get; set; } = new();

    /// <summary>
    /// 商品数量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 单价（可能与商品原价不同，比如会员价）
    /// </summary>
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// 实际支付单价（应用促销后的单价）
    /// </summary>
    public decimal ActualUnitPrice { get; set; }

    /// <summary>
    /// 小计金额（数量 * 单价）
    /// </summary>
    public decimal SubTotal => Quantity * UnitPrice;

    /// <summary>
    /// 实际支付小计（数量 * 实际支付单价）
    /// </summary>
    public decimal ActualSubTotal => Quantity * ActualUnitPrice;

    /// <summary>
    /// 是否为赠品
    /// </summary>
    public bool IsGift { get; set; }

    /// <summary>
    /// 是否已被促销规则消耗
    /// </summary>
    public bool IsConsumed { get; set; }

    /// <summary>
    /// 应用的促销规则ID列表
    /// </summary>
    public List<string> AppliedPromotionRuleIds { get; set; } = new();

    /// <summary>
    /// 促销详情列表
    /// </summary>
    public List<ItemPromotionDetail> PromotionDetails { get; set; } = new();

    /// <summary>
    /// 剩余可用数量（用于促销计算）
    /// </summary>
    public int AvailableQuantity => IsConsumed ? 0 : Quantity;

    public CartItem Clone()
    {
        return new CartItem
        {
            Product = Product,
            Quantity = Quantity,
            UnitPrice = UnitPrice,
            ActualUnitPrice = ActualUnitPrice,
            IsGift = IsGift,
            IsConsumed = IsConsumed,
            AppliedPromotionRuleIds = new List<string>(AppliedPromotionRuleIds),
            PromotionDetails = PromotionDetails.Select(p => p.Clone()).ToList()
        };
    }

    /// <summary>
    /// 初始化实际支付单价（如果未设置）
    /// </summary>
    public void InitializeActualUnitPrice()
    {
        if (ActualUnitPrice == 0)
        {
            ActualUnitPrice = UnitPrice;
        }
    }

    /// <summary>
    /// 添加促销详情
    /// </summary>
    public void AddPromotionDetail(ItemPromotionDetail detail)
    {
        PromotionDetails.Add(detail);
        if (!AppliedPromotionRuleIds.Contains(detail.RuleId))
        {
            AppliedPromotionRuleIds.Add(detail.RuleId);
        }
    }

    /// <summary>
    /// 获取总优惠金额
    /// </summary>
    public decimal TotalDiscountAmount => PromotionDetails.Sum(p => p.DiscountAmount);

    public override bool Equals(object? obj)
    {
        return obj is CartItem item &&
               Product.Id == item.Product.Id &&
               UnitPrice == item.UnitPrice &&
               IsGift == item.IsGift;
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(Product.Id, UnitPrice, IsGift);
    }
}

/// <summary>
/// 商品促销详情
/// </summary>
public class ItemPromotionDetail
{
    /// <summary>
    /// 促销规则ID
    /// </summary>
    public string RuleId { get; set; } = string.Empty;

    /// <summary>
    /// 促销规则名称
    /// </summary>
    public string RuleName { get; set; } = string.Empty;

    /// <summary>
    /// 促销类型
    /// </summary>
    public string PromotionType { get; set; } = string.Empty;

    /// <summary>
    /// 优惠金额（分摊到该商品的优惠金额）
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// 优惠描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否为赠品相关的促销
    /// </summary>
    public bool IsGiftRelated { get; set; }

    public ItemPromotionDetail Clone()
    {
        return new ItemPromotionDetail
        {
            RuleId = RuleId,
            RuleName = RuleName,
            PromotionType = PromotionType,
            DiscountAmount = DiscountAmount,
            Description = Description,
            IsGiftRelated = IsGiftRelated
        };
    }
}
