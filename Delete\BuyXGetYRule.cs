using PE2.Models;
using PE2.PromotionEngine.Models;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Rules;

/// <summary>
/// 买X送Y规则（如：买2送1）
/// </summary>
[Obsolete("老版本促销，已通用")]
public class BuyXGetYRule : PromotionRuleBase
{
    public override string RuleType => "BuyXGetY";

    /// <summary>
    /// 购买商品的条件
    /// </summary>
    public List<BuyCondition> BuyConditions { get; set; } = new();

    /// <summary>
    /// 赠送商品的条件
    /// </summary>
    public List<GiftCondition> GiftConditions { get; set; } = new();

    /// <summary>
    /// 是否赠送相同商品
    /// </summary>
    public bool GiftSameProduct { get; set; } = true;

    /// <summary>
    /// 买赠类型
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public BuyGetGiftType GiftType { get; set; } = BuyGetGiftType.UnifiedGift;

    /// <summary>
    /// 梯度赠送策略（仅在梯度送赠品时有效）
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public GradientGiftStrategy GradientStrategy { get; set; } = GradientGiftStrategy.ByGradient;

    /// <summary>
    /// 是否按金额条件（而非数量条件）
    /// </summary>
    public bool IsByAmount { get; set; } = false;

    /// <summary>
    /// 最小金额要求（当IsByAmount为true时使用）
    /// </summary>
    public decimal MinAmount { get; set; } = 0;

    /// <summary>
    /// 组合购买条件（用于组合送赠品）
    /// </summary>
    public List<CombinationCondition> CombinationConditions { get; set; } = new();

    /// <summary>
    /// 梯度赠品条件（用于梯度送赠品）
    /// </summary>
    public List<GradientGiftCondition> GradientGiftConditions { get; set; } = new();

    /// <summary>
    /// 赠品选择策略
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public GiftSelectionStrategy GiftSelectionStrategy { get; set; } = GiftSelectionStrategy.CustomerBenefit;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        return GiftType switch
        {
            BuyGetGiftType.UnifiedGift => CheckUnifiedGiftConditions(cart),
            BuyGetGiftType.GradientGift => CheckGradientGiftConditions(cart),
            BuyGetGiftType.CombinationGift => CheckCombinationGiftConditions(cart),
            _ => false
        };
    }

    /// <summary>
    /// 检查统一送赠品条件
    /// </summary>
    private bool CheckUnifiedGiftConditions(ShoppingCart cart)
    {
        // 检查购买条件
        foreach (var condition in BuyConditions)
        {
            if (IsByAmount)
            {
                var totalAmount = condition.ProductIds.Sum(id =>
                    cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));
                if (totalAmount < MinAmount)
                    return false;
            }
            else
            {
                var availableQuantity = condition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                if (availableQuantity < condition.RequiredQuantity)
                    return false;
            }
        }

        // 如果赠送相同商品，需要确保有足够的商品可以赠送
        if (GiftSameProduct)
        {
            foreach (var buyCondition in BuyConditions)
            {
                var totalRequired = buyCondition.RequiredQuantity;
                foreach (var giftCondition in GiftConditions)
                {
                    totalRequired += giftCondition.GiftQuantity;
                }

                var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                if (availableQuantity < totalRequired)
                    return false;
            }
        }
        else
        {
            // 检查赠品是否有库存（这里简化处理，实际应该检查库存系统）
            foreach (var giftCondition in GiftConditions)
            {
                var availableQuantity = giftCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                if (availableQuantity < giftCondition.GiftQuantity)
                    return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 检查梯度送赠品条件
    /// 需要同时检查购买条件和赠品库存，确保能够完整执行梯度赠送
    /// </summary>
    private bool CheckGradientGiftConditions(ShoppingCart cart)
    {
        if (!GradientGiftConditions.Any())
            return false;

        // 检查是否至少满足一个完整的梯度条件（包括赠品库存）
        foreach (var buyCondition in BuyConditions)
        {
            var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
            var totalAmount = buyCondition.ProductIds.Sum(id =>
                cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));

            // 找到所有满足购买条件的梯度
            var applicableGradients = GradientGiftConditions
                .Where(g => availableQuantity >= g.RequiredQuantity &&
                           (g.RequiredAmount <= 0 || totalAmount >= g.RequiredAmount))
                .OrderByDescending(g => g.GradientLevel)
                .ToList();

            if (!applicableGradients.Any()) continue;

            // 根据策略确定要应用的梯度
            var gradientsToCheck = GradientStrategy == GradientGiftStrategy.ByGradient
                ? new List<GradientGiftCondition> { applicableGradients.First() } // 只检查最高梯度
                : applicableGradients; // 检查所有达到的梯度

            // 检查所有要应用的梯度是否都有足够的赠品库存
            bool allGradientsHaveStock = true;
            foreach (var gradient in gradientsToCheck)
            {
                if (!CheckGradientGiftStock(cart, gradient))
                {
                    allGradientsHaveStock = false;
                    break;
                }
            }

            if (allGradientsHaveStock)
                return true;
        }

        return false;
    }

    /// <summary>
    /// 检查特定梯度的赠品库存是否充足
    /// </summary>
    private bool CheckGradientGiftStock(ShoppingCart cart, GradientGiftCondition gradient)
    {
        // 检查赠品库存
        foreach (var giftProductId in gradient.GiftProductIds)
        {
            var availableGiftQuantity = cart.Items
                .Where(x => x.Product.Id == giftProductId)
                .Sum(x => x.Quantity);

            // 如果是多选一的情况，只要有一个商品有库存就可以
            if (gradient.GiftProductIds.Count > 1)
            {
                if (availableGiftQuantity > 0)
                    return true; // 至少有一个赠品有库存
            }
            else
            {
                // 单一赠品，必须有足够库存
                if (availableGiftQuantity < gradient.GiftQuantity)
                    return false;
            }
        }

        // 如果是多选一且没有任何赠品有库存，返回false
        if (gradient.GiftProductIds.Count > 1)
        {
            return gradient.GiftProductIds.Any(id =>
                cart.Items.Where(x => x.Product.Id == id).Sum(x => x.Quantity) > 0);
        }

        return true;
    }

    /// <summary>
    /// 检查组合送赠品条件
    /// </summary>
    private bool CheckCombinationGiftConditions(ShoppingCart cart)
    {
        if (!CombinationConditions.Any())
            return false;

        // 检查所有组合条件是否都满足
        foreach (var combination in CombinationConditions)
        {
            var availableQuantity = cart.GetAvailableProductQuantity(combination.ProductId);
            if (availableQuantity < combination.RequiredQuantity)
                return false;

            if (combination.MinAmount > 0)
            {
                var totalAmount = cart.Items
                    .Where(x => x.Product.Id == combination.ProductId)
                    .Sum(x => x.SubTotal);
                if (totalAmount < combination.MinAmount)
                    return false;
            }
        }

        return true;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = GiftType switch
        {
            BuyGetGiftType.UnifiedGift => CalculateUnifiedGiftMaxApplications(cart),
            BuyGetGiftType.GradientGift => CalculateGradientGiftMaxApplications(cart),
            BuyGetGiftType.CombinationGift => CalculateCombinationGiftMaxApplications(cart),
            _ => 0
        };

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    /// <summary>
    /// 计算统一送赠品的最大应用次数
    /// </summary>
    private int CalculateUnifiedGiftMaxApplications(ShoppingCart cart)
    {
        var maxApplications = int.MaxValue;

        // 根据购买条件计算最大应用次数
        foreach (var condition in BuyConditions)
        {
            if (IsByAmount)
            {
                var totalAmount = condition.ProductIds.Sum(id =>
                    cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));
                var maxByAmount = (int)(totalAmount / MinAmount);
                maxApplications = Math.Min(maxApplications, maxByAmount);
            }
            else
            {
                var availableQuantity = condition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                var maxByCondition = availableQuantity / condition.RequiredQuantity;
                maxApplications = Math.Min(maxApplications, maxByCondition);
            }
        }

        // 如果赠送相同商品，需要考虑赠品数量
        if (GiftSameProduct)
        {
            foreach (var buyCondition in BuyConditions)
            {
                var totalRequired = buyCondition.RequiredQuantity;
                foreach (var giftCondition in GiftConditions)
                {
                    totalRequired += giftCondition.GiftQuantity;
                }

                var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                var maxByGift = availableQuantity / totalRequired;
                maxApplications = Math.Min(maxApplications, maxByGift);
            }
        }

        return maxApplications == int.MaxValue ? 0 : maxApplications;
    }

    /// <summary>
    /// 计算梯度送赠品的最大应用次数
    /// 需要同时考虑购买条件和赠品库存限制
    /// </summary>
    private int CalculateGradientGiftMaxApplications(ShoppingCart cart)
    {
        var maxApplications = 0;

        // 根据购买条件计算可能的应用次数
        foreach (var buyCondition in BuyConditions)
        {
            var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
            var totalAmount = buyCondition.ProductIds.Sum(id =>
                cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));

            // 找到所有满足购买条件的梯度
            var applicableGradients = GradientGiftConditions
                .Where(g => availableQuantity >= g.RequiredQuantity &&
                           (g.RequiredAmount <= 0 || totalAmount >= g.RequiredAmount))
                .OrderByDescending(g => g.GradientLevel)
                .ToList();

            if (!applicableGradients.Any()) continue;

            // 根据策略确定要应用的梯度
            var gradientsToApply = GradientStrategy == GradientGiftStrategy.ByGradient
                ? new List<GradientGiftCondition> { applicableGradients.First() } // 只应用最高梯度
                : applicableGradients; // 应用所有达到的梯度

            // 计算基于购买条件的最大应用次数
            var maxByPurchase = IsRepeatable && applicableGradients.Any()
                ? availableQuantity / applicableGradients.First().RequiredQuantity
                : 1;

            // 计算基于赠品库存的最大应用次数
            var maxByGiftStock = CalculateMaxApplicationsByGiftStock(cart, gradientsToApply);

            // 取两者的最小值
            var currentMaxApplications = Math.Min(maxByPurchase, maxByGiftStock);
            maxApplications = Math.Max(maxApplications, currentMaxApplications);
        }

        return maxApplications;
    }

    /// <summary>
    /// 根据赠品库存计算最大应用次数
    /// </summary>
    private int CalculateMaxApplicationsByGiftStock(ShoppingCart cart, List<GradientGiftCondition> gradients)
    {
        var minApplications = int.MaxValue;

        foreach (var gradient in gradients)
        {
            var maxForThisGradient = CalculateMaxApplicationsForSingleGradient(cart, gradient);
            minApplications = Math.Min(minApplications, maxForThisGradient);
        }

        return minApplications == int.MaxValue ? 0 : minApplications;
    }

    /// <summary>
    /// 计算单个梯度的最大应用次数（基于赠品库存）
    /// </summary>
    private int CalculateMaxApplicationsForSingleGradient(ShoppingCart cart, GradientGiftCondition gradient)
    {
        if (gradient.GiftProductIds.Count > 1)
        {
            // 多选一的情况：只要有一个赠品有库存就可以应用
            var hasAnyStock = gradient.GiftProductIds.Any(id =>
                cart.Items.Where(x => x.Product.Id == id).Sum(x => x.Quantity) > 0);
            return hasAnyStock ? int.MaxValue : 0; // 多选一通常不受库存限制
        }
        else
        {
            // 单一赠品：根据库存数量计算
            var giftProductId = gradient.GiftProductIds.FirstOrDefault();
            if (string.IsNullOrEmpty(giftProductId)) return 0;

            var availableStock = cart.Items
                .Where(x => x.Product.Id == giftProductId)
                .Sum(x => x.Quantity);

            return gradient.GiftQuantity > 0 ? availableStock / gradient.GiftQuantity : 0;
        }
    }

    /// <summary>
    /// 计算组合送赠品的最大应用次数
    /// </summary>
    private int CalculateCombinationGiftMaxApplications(ShoppingCart cart)
    {
        var maxApplications = int.MaxValue;

        // 根据每个组合条件计算最大应用次数
        foreach (var combination in CombinationConditions)
        {
            var availableQuantity = cart.GetAvailableProductQuantity(combination.ProductId);
            var maxByProduct = availableQuantity / combination.RequiredQuantity;
            maxApplications = Math.Min(maxApplications, maxByProduct);
        }

        return maxApplications == int.MaxValue ? 0 : maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = GiftType switch
            {
                BuyGetGiftType.UnifiedGift => ApplyUnifiedGiftPromotion(cart, applicationCount),
                BuyGetGiftType.GradientGift => ApplyGradientGiftPromotion(cart, applicationCount),
                BuyGetGiftType.CombinationGift => ApplyCombinationGiftPromotion(cart, applicationCount),
                _ => (0m, new List<ConsumedItem>(), new List<GiftItem>())
            };

            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用买赠促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = $"应用买赠促销时发生错误: {ex.Message}";
        }

        return application;
    }

    /// <summary>
    /// 根据策略选择最优的赠品商品
    /// </summary>
    private List<string> SelectOptimalGiftProducts(List<string> candidateProductIds, ShoppingCart cart, int requiredQuantity)
    {
        if (!candidateProductIds.Any())
            return new List<string>();

        // 获取候选商品的价格信息
        var productPrices = new List<(string ProductId, decimal Price)>();

        foreach (var productId in candidateProductIds)
        {
            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
            if (cartItem != null)
            {
                productPrices.Add((productId, cartItem.Product.Price));
            }
        }

        if (!productPrices.Any())
            return new List<string>();

        // 根据策略排序
        var sortedProducts = GiftSelectionStrategy switch
        {
            GiftSelectionStrategy.CustomerBenefit => productPrices.OrderByDescending(p => p.Price), // 价值最高的优先
            GiftSelectionStrategy.MerchantBenefit => productPrices.OrderBy(p => p.Price), // 价值最低的优先
            _ => productPrices.OrderByDescending(p => p.Price)
        };

        // 选择足够数量的商品
        var selectedProducts = new List<string>();
        var remainingQuantity = requiredQuantity;

        foreach (var (productId, _) in sortedProducts)
        {
            if (remainingQuantity <= 0) break;

            var availableQuantity = cart.Items
                .Where(x => x.Product.Id == productId)
                .Sum(x => x.Quantity);

            if (availableQuantity > 0)
            {
                selectedProducts.Add(productId);
                remainingQuantity--;
            }
        }

        return selectedProducts;
    }

    /// <summary>
    /// 根据策略选择单个最优赠品商品
    /// </summary>
    private string SelectOptimalSingleGiftProduct(List<string> candidateProductIds, ShoppingCart cart)
    {
        var selectedProducts = SelectOptimalGiftProducts(candidateProductIds, cart, 1);
        return selectedProducts.FirstOrDefault() ?? candidateProductIds.FirstOrDefault() ?? string.Empty;
    }

    /// <summary>
    /// 应用统一送赠品促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyUnifiedGiftPromotion(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var giftItems = new List<GiftItem>();

        for (int app = 0; app < applicationCount; app++)
        {
            // 消耗购买条件中的商品
            foreach (var buyCondition in BuyConditions)
            {
                var remainingQuantity = buyCondition.RequiredQuantity;

                foreach (var productId in buyCondition.ProductIds)
                {
                    if (remainingQuantity <= 0) break;

                    var availableItems = cart.Items
                        .Where(x => x.Product.Id == productId && x.AvailableQuantity > 0)
                        .ToList();

                    foreach (var item in availableItems)
                    {
                        if (remainingQuantity <= 0) break;

                        var consumeQuantity = Math.Min(item.AvailableQuantity, remainingQuantity);

                        // 记录消耗的商品
                        var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == productId);
                        if (existingConsumed != null)
                        {
                            existingConsumed.Quantity += consumeQuantity;
                        }
                        else
                        {
                            consumedItems.Add(new ConsumedItem
                            {
                                ProductId = productId,
                                ProductName = item.Product.Name,
                                Quantity = consumeQuantity,
                                UnitPrice = item.UnitPrice
                            });
                        }

                        item.Quantity -= consumeQuantity;
                        remainingQuantity -= consumeQuantity;
                    }
                }
            }

            // 处理赠品 - 只记录赠品信息，不从购物车中扣除
            foreach (var giftCondition in GiftConditions)
            {
                if (GiftSameProduct)
                {
                    // 赠送相同商品
                    foreach (var buyCondition in BuyConditions)
                    {
                        var remainingGiftQuantity = giftCondition.GiftQuantity;

                        foreach (var productId in buyCondition.ProductIds)
                        {
                            if (remainingGiftQuantity <= 0) break;

                            // 找到对应的商品信息
                            var productItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
                            if (productItem != null)
                            {
                                var giftQuantity = Math.Min(remainingGiftQuantity, giftCondition.GiftQuantity);
                                var giftValue = giftQuantity * productItem.UnitPrice;

                                totalDiscountAmount += giftValue;

                                // 记录赠品信息
                                var existingGift = giftItems.FirstOrDefault(x => x.ProductId == productId);
                                if (existingGift != null)
                                {
                                    existingGift.Quantity += giftQuantity;
                                    existingGift.Value += giftValue;
                                }
                                else
                                {
                                    giftItems.Add(new GiftItem
                                    {
                                        ProductId = productId,
                                        ProductName = productItem.Product.Name,
                                        Quantity = giftQuantity,
                                        Value = giftValue
                                    });
                                }

                                remainingGiftQuantity -= giftQuantity;
                            }
                        }
                    }
                }
                else
                {
                    // 赠送指定商品 - 根据策略选择最优商品
                    var remainingGiftQuantity = giftCondition.GiftQuantity;

                    // 如果有多个候选商品，根据策略选择最优的
                    if (giftCondition.ProductIds.Count > 1)
                    {
                        var selectedProductIds = SelectOptimalGiftProducts(giftCondition.ProductIds, cart, remainingGiftQuantity);

                        foreach (var productId in selectedProductIds)
                        {
                            if (remainingGiftQuantity <= 0) break;

                            var productItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
                            if (productItem != null)
                            {
                                var giftQuantity = Math.Min(remainingGiftQuantity, 1); // 每个选中的商品送1件
                                var giftValue = giftQuantity * productItem.UnitPrice;

                                totalDiscountAmount += giftValue;

                                // 记录赠品信息
                                var existingGift = giftItems.FirstOrDefault(x => x.ProductId == productId);
                                if (existingGift != null)
                                {
                                    existingGift.Quantity += giftQuantity;
                                    existingGift.Value += giftValue;
                                }
                                else
                                {
                                    var strategyDescription = GiftSelectionStrategy == GiftSelectionStrategy.CustomerBenefit
                                        ? "客户利益最大化选择"
                                        : "商家利益最大化选择";

                                    giftItems.Add(new GiftItem
                                    {
                                        ProductId = productId,
                                        ProductName = productItem.Product.Name,
                                        Quantity = giftQuantity,
                                        Value = giftValue,
                                        Description = $"统一赠品 - {strategyDescription}"
                                    });
                                }

                                remainingGiftQuantity -= giftQuantity;
                            }
                        }
                    }
                    else
                    {
                        // 只有一个候选商品，直接赠送
                        foreach (var productId in giftCondition.ProductIds)
                        {
                            if (remainingGiftQuantity <= 0) break;

                            var productItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
                            if (productItem != null)
                            {
                                var giftQuantity = Math.Min(remainingGiftQuantity, giftCondition.GiftQuantity);
                                var giftValue = giftQuantity * productItem.UnitPrice;

                                totalDiscountAmount += giftValue;

                                // 记录赠品信息
                                var existingGift = giftItems.FirstOrDefault(x => x.ProductId == productId);
                                if (existingGift != null)
                                {
                                    existingGift.Quantity += giftQuantity;
                                    existingGift.Value += giftValue;
                                }
                                else
                                {
                                    giftItems.Add(new GiftItem
                                    {
                                        ProductId = productId,
                                        ProductName = productItem.Product.Name,
                                        Quantity = giftQuantity,
                                        Value = giftValue,
                                        Description = "统一赠品"
                                    });
                                }

                                remainingGiftQuantity -= giftQuantity;
                            }
                        }
                    }
                }
            }
        }

        // 清理数量为0的商品项
        cart.Items.RemoveAll(x => x.Quantity <= 0);

        return (totalDiscountAmount, consumedItems, giftItems);
    }

    /// <summary>
    /// 应用梯度送赠品促销
    /// 优化后的算法：先验证完整性，再执行应用，确保不会出现条件满足但赠品不足的情况
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyGradientGiftPromotion(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var giftItems = new List<GiftItem>();

        for (int app = 0; app < applicationCount; app++)
        {
            // 消耗购买条件中的商品
            foreach (var buyCondition in BuyConditions)
            {
                var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                var totalAmount = buyCondition.ProductIds.Sum(id =>
                    cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));

                // 找到所有满足购买条件的梯度
                var applicableGradients = GradientGiftConditions
                    .Where(g => availableQuantity >= g.RequiredQuantity &&
                               (g.RequiredAmount <= 0 || totalAmount >= g.RequiredAmount))
                    .OrderByDescending(g => g.GradientLevel)
                    .ToList();

                if (!applicableGradients.Any()) continue;

                // 根据策略决定赠送哪些梯度的赠品
                var gradientsToApply = GradientStrategy == GradientGiftStrategy.ByGradient
                    ? new List<GradientGiftCondition> { applicableGradients.First() } // 只送最高梯度
                    : applicableGradients; // 送所有达到的梯度

                // 验证所有要应用的梯度是否都有足够的赠品库存
                bool canApplyAllGradients = true;
                foreach (var gradient in gradientsToApply)
                {
                    if (!CheckGradientGiftStock(cart, gradient))
                    {
                        canApplyAllGradients = false;
                        break;
                    }
                }

                // 如果赠品库存不足，跳过这次应用
                if (!canApplyAllGradients) continue;

                // 消耗购买商品
                var totalRequiredQuantity = gradientsToApply.Max(g => g.RequiredQuantity);
                var remainingQuantity = totalRequiredQuantity;

                foreach (var productId in buyCondition.ProductIds)
                {
                    if (remainingQuantity <= 0) break;

                    var availableItems = cart.Items
                        .Where(x => x.Product.Id == productId && x.AvailableQuantity > 0)
                        .ToList();

                    foreach (var item in availableItems)
                    {
                        if (remainingQuantity <= 0) break;

                        var consumeQuantity = Math.Min(item.AvailableQuantity, remainingQuantity);

                        // 记录消耗的商品
                        var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == productId);
                        if (existingConsumed != null)
                        {
                            existingConsumed.Quantity += consumeQuantity;
                        }
                        else
                        {
                            consumedItems.Add(new ConsumedItem
                            {
                                ProductId = productId,
                                ProductName = item.Product.Name,
                                Quantity = consumeQuantity,
                                UnitPrice = item.UnitPrice
                            });
                        }

                        item.Quantity -= consumeQuantity;
                        remainingQuantity -= consumeQuantity;
                    }
                }

                // 处理梯度赠品
                foreach (var gradient in gradientsToApply)
                {
                    // 如果有多个候选赠品，根据策略选择最优的
                    if (gradient.GiftProductIds.Count > 1)
                    {
                        var selectedProductIds = SelectOptimalGiftProducts(gradient.GiftProductIds, cart, gradient.GiftQuantity);

                        var distributedQuantity = 0;
                        foreach (var giftProductId in selectedProductIds)
                        {
                            if (distributedQuantity >= gradient.GiftQuantity) break;

                            var productItem = cart.Items.FirstOrDefault(x => x.Product.Id == giftProductId);
                            if (productItem != null)
                            {
                                var giftQuantity = Math.Min(gradient.GiftQuantity - distributedQuantity, 1);
                                var giftValue = giftQuantity * productItem.UnitPrice;

                                totalDiscountAmount += giftValue;

                                // 记录赠品信息
                                var existingGift = giftItems.FirstOrDefault(x => x.ProductId == giftProductId);
                                if (existingGift != null)
                                {
                                    existingGift.Quantity += giftQuantity;
                                    existingGift.Value += giftValue;
                                }
                                else
                                {
                                    var strategyDescription = GiftSelectionStrategy == GiftSelectionStrategy.CustomerBenefit
                                        ? "客户利益最大化选择"
                                        : "商家利益最大化选择";

                                    giftItems.Add(new GiftItem
                                    {
                                        ProductId = giftProductId,
                                        ProductName = productItem.Product.Name,
                                        Quantity = giftQuantity,
                                        Value = giftValue,
                                        Description = $"梯度{gradient.GradientLevel}: {gradient.Description} - {strategyDescription}"
                                    });
                                }

                                distributedQuantity += giftQuantity;
                            }
                        }
                    }
                    else
                    {
                        // 只有一个候选商品，直接赠送
                        foreach (var giftProductId in gradient.GiftProductIds)
                        {
                            var productItem = cart.Items.FirstOrDefault(x => x.Product.Id == giftProductId);
                            if (productItem != null)
                            {
                                var giftQuantity = gradient.GiftQuantity;
                                var giftValue = giftQuantity * productItem.UnitPrice;

                                totalDiscountAmount += giftValue;

                                // 记录赠品信息
                                var existingGift = giftItems.FirstOrDefault(x => x.ProductId == giftProductId);
                                if (existingGift != null)
                                {
                                    existingGift.Quantity += giftQuantity;
                                    existingGift.Value += giftValue;
                                }
                                else
                                {
                                    giftItems.Add(new GiftItem
                                    {
                                        ProductId = giftProductId,
                                        ProductName = productItem.Product.Name,
                                        Quantity = giftQuantity,
                                        Value = giftValue,
                                        Description = $"梯度{gradient.GradientLevel}: {gradient.Description}"
                                    });
                                }
                            }
                        }
                    }
                }
            }
        }

        // 清理数量为0的商品项
        cart.Items.RemoveAll(x => x.Quantity <= 0);

        return (totalDiscountAmount, consumedItems, giftItems);
    }

    /// <summary>
    /// 应用组合送赠品促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyCombinationGiftPromotion(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var giftItems = new List<GiftItem>();

        for (int app = 0; app < applicationCount; app++)
        {
            // 消耗组合条件中的商品
            foreach (var combination in CombinationConditions)
            {
                var remainingQuantity = combination.RequiredQuantity;

                var availableItems = cart.Items
                    .Where(x => x.Product.Id == combination.ProductId && x.AvailableQuantity > 0)
                    .ToList();

                foreach (var item in availableItems)
                {
                    if (remainingQuantity <= 0) break;

                    var consumeQuantity = Math.Min(item.AvailableQuantity, remainingQuantity);

                    // 记录消耗的商品
                    var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == combination.ProductId);
                    if (existingConsumed != null)
                    {
                        existingConsumed.Quantity += consumeQuantity;
                    }
                    else
                    {
                        consumedItems.Add(new ConsumedItem
                        {
                            ProductId = combination.ProductId,
                            ProductName = item.Product.Name,
                            Quantity = consumeQuantity,
                            UnitPrice = item.UnitPrice
                        });
                    }

                    item.Quantity -= consumeQuantity;
                    remainingQuantity -= consumeQuantity;
                }
            }

            // 处理赠品
            foreach (var giftCondition in GiftConditions)
            {
                // 如果有多个候选赠品，根据策略选择最优的
                if (giftCondition.ProductIds.Count > 1)
                {
                    var selectedProductIds = SelectOptimalGiftProducts(giftCondition.ProductIds, cart, giftCondition.GiftQuantity);

                    var distributedQuantity = 0;
                    foreach (var productId in selectedProductIds)
                    {
                        if (distributedQuantity >= giftCondition.GiftQuantity) break;

                        var productItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
                        if (productItem != null)
                        {
                            var giftQuantity = Math.Min(giftCondition.GiftQuantity - distributedQuantity, 1);
                            var giftValue = giftQuantity * productItem.UnitPrice;

                            totalDiscountAmount += giftValue;

                            // 记录赠品信息
                            var existingGift = giftItems.FirstOrDefault(x => x.ProductId == productId);
                            if (existingGift != null)
                            {
                                existingGift.Quantity += giftQuantity;
                                existingGift.Value += giftValue;
                            }
                            else
                            {
                                var strategyDescription = GiftSelectionStrategy == GiftSelectionStrategy.CustomerBenefit
                                    ? "客户利益最大化选择"
                                    : "商家利益最大化选择";

                                giftItems.Add(new GiftItem
                                {
                                    ProductId = productId,
                                    ProductName = productItem.Product.Name,
                                    Quantity = giftQuantity,
                                    Value = giftValue,
                                    Description = $"组合赠品 - {strategyDescription}"
                                });
                            }

                            distributedQuantity += giftQuantity;
                        }
                    }
                }
                else
                {
                    // 只有一个候选商品，直接赠送
                    foreach (var productId in giftCondition.ProductIds)
                    {
                        var productItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
                        if (productItem != null)
                        {
                            var giftQuantity = giftCondition.GiftQuantity;
                            var giftValue = giftQuantity * productItem.UnitPrice;

                            totalDiscountAmount += giftValue;

                            // 记录赠品信息
                            var existingGift = giftItems.FirstOrDefault(x => x.ProductId == productId);
                            if (existingGift != null)
                            {
                                existingGift.Quantity += giftQuantity;
                                existingGift.Value += giftValue;
                            }
                            else
                            {
                                giftItems.Add(new GiftItem
                                {
                                    ProductId = productId,
                                    ProductName = productItem.Product.Name,
                                    Quantity = giftQuantity,
                                    Value = giftValue,
                                    Description = "组合赠品"
                                });
                            }
                        }
                    }
                }
            }
        }

        // 清理数量为0的商品项
        cart.Items.RemoveAll(x => x.Quantity <= 0);

        return (totalDiscountAmount, consumedItems, giftItems);
    }
}

/// <summary>
/// 购买条件
/// </summary>
public class BuyCondition
{
    /// <summary>
    /// 商品ID列表
    /// </summary>
    public List<string> ProductIds { get; set; } = new();

    /// <summary>
    /// 需要购买的数量
    /// </summary>
    public int RequiredQuantity { get; set; }
}

/// <summary>
/// 赠品条件
/// </summary>
public class GiftCondition
{
    /// <summary>
    /// 赠品商品ID列表
    /// </summary>
    public List<string> ProductIds { get; set; } = new();

    /// <summary>
    /// 赠送数量
    /// </summary>
    public int GiftQuantity { get; set; }
}

/// <summary>
/// 买赠类型
/// </summary>
public enum BuyGetGiftType
{
    /// <summary>
    /// 统一送赠品：满X件或X元，赠送某类商品Z件
    /// </summary>
    UnifiedGift = 0,

    /// <summary>
    /// 梯度送赠品：针对某一类商品，满第一梯度送A商品，满第二梯度送B商品
    /// </summary>
    GradientGift = 1,

    /// <summary>
    /// 组合送赠品：针对某些组合商品，必须购买A+B+C等商品且满足数量或金额条件才能赠送
    /// </summary>
    CombinationGift = 2
}

/// <summary>
/// 梯度赠送策略
/// </summary>
public enum GradientGiftStrategy
{
    /// <summary>
    /// 按梯度送：只送达到的最高梯度对应的赠品
    /// </summary>
    ByGradient = 0,

    /// <summary>
    /// 全部送：送所有达到梯度的赠品
    /// </summary>
    SendAll = 1
}

/// <summary>
/// 赠品选择策略
/// </summary>
public enum GiftSelectionStrategy
{
    /// <summary>
    /// 客户利益最大化：选择价值最高的商品作为赠品
    /// </summary>
    CustomerBenefit = 0,

    /// <summary>
    /// 商家利益最大化：选择价值最低的商品作为赠品
    /// </summary>
    MerchantBenefit = 1
}

/// <summary>
/// 组合购买条件
/// </summary>
public class CombinationCondition
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 需要购买的数量
    /// </summary>
    public int RequiredQuantity { get; set; }

    /// <summary>
    /// 最小金额要求（可选）
    /// </summary>
    public decimal MinAmount { get; set; } = 0;
}

/// <summary>
/// 梯度赠品条件
/// </summary>
public class GradientGiftCondition
{
    /// <summary>
    /// 梯度级别（数字越大级别越高）
    /// </summary>
    public int GradientLevel { get; set; }

    /// <summary>
    /// 触发条件：需要购买的数量
    /// </summary>
    public int RequiredQuantity { get; set; }

    /// <summary>
    /// 触发条件：需要购买的金额（可选）
    /// </summary>
    public decimal RequiredAmount { get; set; } = 0;

    /// <summary>
    /// 赠品商品ID列表
    /// </summary>
    public List<string> GiftProductIds { get; set; } = new();

    /// <summary>
    /// 赠送数量
    /// </summary>
    public int GiftQuantity { get; set; }

    /// <summary>
    /// 梯度描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}
