FreeFormDiscountRule.cs 是 促销引擎的一种规则 主要实现：【 自由组合:针对多类商品，满X件或X元，享受对应的各自的折扣；场景案例：A商品吊牌、零售价为1000，买1件0.8折，2件0.7折，3件0.6折；B商品吊
牌、零售价为1000，买1件0.9折 ，2件0.8折，3件0.7折；C商品吊牌、零售价为99，买1件
第 65 页
Intelligent cashier system
智能收银系统
0.9折，2件0.85折，3件0.8折。上述三种商品自由组合，只要数量满足2件，就能各自享受
满2件时对应的折扣；只要满足数量为3件，则能各自享受满3件时对应的折扣。
购买1件A，1件B，1件C；由于数量满足3件，则A、B、C都满足买3件对应的折扣，应
收金额为1000×0.6+1000×0.7+99×0.8=1379.2；购买1件A，2件B；由于数量满足3件，则A、B都满足买3件对应的折扣，应收金额为
1000×0.6+1000×0.7+1000×0.7=2000；购买1件A，1件C；由于数量满足2件，则A、C都满足买2件对应的折扣，应收金额为
1000×0.7+99×0.85=784.15； 购买1件A，1件B，2件C；由于数量满足3件，则A、B、C都满足买3件对应的折扣，应
收金额为1000×0.6+1000×0.7+99×2×0.8=1458.4。】 但是在 CheckConditions 和 ApplyPromotion 方法中 都只有按照数量判断的 逻辑 需要 增加 按金额 MinAmount 判断的逻辑 我给你按金额使用的json 【 {
"$type": "FreeFormDiscount",
"id": "FREEFORM_DISCOUNT_001",
"name": "自由组合折扣测试",
"description": "A、B、C商品自由组合，只要数量满足2件，就能各自享受满2件时对应的折扣；只要满足数量为3件，则能各自享受满3件时对应的折扣",
"priority": 90,
"isEnabled": true,
"startTime": "2024-01-01T00:00:00",
"endTime": "2025-12-31T23:59:59",
"isRepeatable": false,
"maxApplications": 1,
"applicableCustomerTypes": [],
"exclusiveRuleIds": [],
"canStackWithOthers": true,
"productExclusivity": "None",
"discountSelectionStrategy": "CustomerBenefit",
"productConfigs": [
{
"productIds": [ "A" ],
"discountTiers": [
{
"minQuantity": 0,
"minAmount": 100,
"discountRate": 0.8,
"description": "买1件8折"
},
{
"minQuantity": 0,
"minAmount": 200,
"discountRate": 0.7,
"description": "买2件7折"
},
{
"minQuantity": 0,
"minAmount": 300,
"discountRate": 0.6,
"description": "买3件6折"
}
]
},
{
"productIds": [ "B" ],
"discountTiers": [
{
"minQuantity": 0,
"minAmount": 100,
"discountRate": 0.9,
"description": "买1件9折"
},
{
"minQuantity": 0,
"minAmount": 200,
"discountRate": 0.8,
"description": "买2件8折"
},
{
"minQuantity": 0,
"minAmount": 300,
"discountRate": 0.7,
"description": "买3件7折"
}
]
},
{
"productIds": [ "C" ],
"discountTiers": [
{
"minQuantity": 0,
"minAmount": 100,
"discountRate": 0.9,
"description": "买1件9折"
},
{
"minQuantity": 0,
"minAmount": 200,
"discountRate": 0.85,
"description": "买2件85折"
},
{
"minQuantity": 0,
"minAmount": 300,
"discountRate": 0.8,
"description": "买3件8折"
}
]
}
]
}】 ，请尝试修复