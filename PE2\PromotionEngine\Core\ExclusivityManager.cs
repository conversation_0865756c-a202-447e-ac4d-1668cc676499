using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Rules;
using System.Collections.Concurrent;

namespace PE2.PromotionEngine.Core;

/// <summary>
/// 排他性管理器 - 处理促销规则间的互斥关系和商品占用冲突
/// 核心策略：层级排他 + 动态冲突检测 + 智能优化选择
/// </summary>
public sealed class ExclusivityManager
{
    private readonly ILogger<ExclusivityManager> _logger;
    private readonly ConcurrentDictionary<string, ExclusivityGroup> _exclusivityGroups = new();
    private readonly ConcurrentDictionary<string, List<string>> _ruleConflicts = new();

    public ExclusivityManager(ILogger<ExclusivityManager> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 分析促销规则的排他性冲突
    /// </summary>
    public async Task<ExclusivityAnalysisResult> AnalyzeExclusivityAsync(
        List<PromotionRuleBase> rules,
        ProcessedCart cart,
        CancellationToken cancellationToken = default)
    {
        return await Task.Run(() =>
        {
            var result = new ExclusivityAnalysisResult();
            var productOccupations = new Dictionary<string, List<RuleOccupation>>();

            // 1. 分析每个规则的商品占用需求
            foreach (var rule in rules)
            {
                var occupations = AnalyzeRuleOccupations(rule, cart);
                foreach (var occupation in occupations)
                {
                    if (!productOccupations.ContainsKey(occupation.ProductId))
                    {
                        productOccupations[occupation.ProductId] = [];
                    }
                    productOccupations[occupation.ProductId].Add(occupation);
                }
            }

            // 2. 检测冲突
            var conflicts = DetectConflicts(productOccupations);
            result.Conflicts = conflicts;

            // 3. 生成排他性组
            var exclusivityGroups = GenerateExclusivityGroups(rules, conflicts);
            result.ExclusivityGroups = exclusivityGroups;

            // 4. 计算兼容性矩阵
            var compatibilityMatrix = BuildCompatibilityMatrix(rules);
            result.CompatibilityMatrix = compatibilityMatrix;

            // 5. 推荐最优组合
            var recommendations = GenerateOptimalCombinations(rules, conflicts, cart);
            result.OptimalCombinations = recommendations;

            return result;
        }, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 检查两个规则是否可以同时应用
    /// </summary>
    public bool CanApplyTogether(PromotionRuleBase rule1, PromotionRuleBase rule2, ProcessedCart cart)
    {
        // 1. 检查规则级别的排他性
        if (!rule1.CanStackWithOthers || !rule2.CanStackWithOthers)
        {
            return false;
        }

        // 2. 检查显式排他规则
        if (rule1.ExclusiveRuleIds.Contains(rule2.Id) || rule2.ExclusiveRuleIds.Contains(rule1.Id))
        {
            return false;
        }

        // 3. 检查商品级别的排他性
        var rule1Occupations = AnalyzeRuleOccupations(rule1, cart);
        var rule2Occupations = AnalyzeRuleOccupations(rule2, cart);

        return !HasProductConflicts(rule1Occupations, rule2Occupations);
    }

    /// <summary>
    /// 选择最优的促销规则组合
    /// </summary>
    public async Task<OptimalSelectionResult> SelectOptimalCombinationAsync(
        List<PromotionRuleBase> availableRules,
        ProcessedCart cart,
        OptimizationStrategy strategy = OptimizationStrategy.MaxCustomerBenefit,
        CancellationToken cancellationToken = default)
    {
        return await Task.Run(() =>
        {
            var result = new OptimalSelectionResult();
            var allCombinations = GenerateValidCombinations(availableRules, cart);

            if (!allCombinations.Any())
            {
                result.SelectedRules = [];
                result.Reason = "没有找到有效的促销组合";
                return result;
            }

            // 根据策略评估组合
            var evaluatedCombinations = allCombinations
                .Select(combo => new
                {
                    Combination = combo,
                    Score = EvaluateCombination(combo, cart, strategy)
                })
                .OrderByDescending(x => x.Score)
                .ToList();

            var bestCombination = evaluatedCombinations.First();
            
            result.SelectedRules = bestCombination.Combination;
            result.Score = bestCombination.Score;
            result.Strategy = strategy;
            result.AlternativeCombinations = evaluatedCombinations
                .Skip(1)
                .Take(5)
                .Select(x => new AlternativeCombination
                {
                    Rules = x.Combination,
                    Score = x.Score,
                    Description = GenerateCombinationDescription(x.Combination)
                })
                .ToList();

            return result;
        }, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 分析规则的商品占用需求
    /// </summary>
    private List<RuleOccupation> AnalyzeRuleOccupations(PromotionRuleBase rule, ProcessedCart cart)
    {
        var occupations = new List<RuleOccupation>();

        // 根据规则类型分析占用需求
        switch (rule)
        {
            case IDiscountRule discountRule:
                foreach (var productId in discountRule.ApplicableProductIds)
                {
                    var cartItem = cart.Items.FirstOrDefault(i => i.ProductId == productId);
                    if (cartItem != null)
                    {
                        occupations.Add(new RuleOccupation
                        {
                            RuleId = rule.Id,
                            RuleName = rule.Name,
                            ProductId = productId,
                            RequiredQuantity = Math.Min(discountRule.MinQuantity, cartItem.Quantity),
                            OccupationType = OccupationType.Execution,
                            ExclusivityLevel = rule.ProductExclusivity,
                            CanStackWithOthers = rule.CanStackWithOthers
                        });
                    }
                }
                break;

            case IBuyXGetYRule buyXGetY:
                // 条件占用
                foreach (var productId in buyXGetY.ApplicableProductIds)
                {
                    var cartItem = cart.Items.FirstOrDefault(i => i.ProductId == productId);
                    if (cartItem != null)
                    {
                        occupations.Add(new RuleOccupation
                        {
                            RuleId = rule.Id,
                            RuleName = rule.Name,
                            ProductId = productId,
                            RequiredQuantity = buyXGetY.BuyQuantity,
                            OccupationType = OccupationType.Condition,
                            ExclusivityLevel = rule.ProductExclusivity,
                            CanStackWithOthers = rule.CanStackWithOthers
                        });

                        // 执行占用
                        occupations.Add(new RuleOccupation
                        {
                            RuleId = rule.Id,
                            RuleName = rule.Name,
                            ProductId = productId,
                            RequiredQuantity = buyXGetY.FreeQuantity,
                            OccupationType = OccupationType.Execution,
                            ExclusivityLevel = rule.ProductExclusivity,
                            CanStackWithOthers = rule.CanStackWithOthers
                        });
                    }
                }
                break;
        }

        return occupations;
    }

    /// <summary>
    /// 检测商品占用冲突
    /// </summary>
    private List<ExclusivityConflict> DetectConflicts(Dictionary<string, List<RuleOccupation>> productOccupations)
    {
        var conflicts = new List<ExclusivityConflict>();

        foreach (var kvp in productOccupations)
        {
            var productId = kvp.Key;
            var occupations = kvp.Value;

            if (occupations.Count <= 1)
                continue;

            // 检查每对占用之间的冲突
            for (int i = 0; i < occupations.Count; i++)
            {
                for (int j = i + 1; j < occupations.Count; j++)
                {
                    var occupation1 = occupations[i];
                    var occupation2 = occupations[j];

                    if (HasConflict(occupation1, occupation2))
                    {
                        conflicts.Add(new ExclusivityConflict
                        {
                            ProductId = productId,
                            Rule1Id = occupation1.RuleId,
                            Rule1Name = occupation1.RuleName,
                            Rule2Id = occupation2.RuleId,
                            Rule2Name = occupation2.RuleName,
                            ConflictType = DetermineConflictType(occupation1, occupation2),
                            Description = GenerateConflictDescription(occupation1, occupation2)
                        });
                    }
                }
            }
        }

        return conflicts;
    }

    /// <summary>
    /// 检查两个占用是否冲突
    /// </summary>
    private bool HasConflict(RuleOccupation occupation1, RuleOccupation occupation2)
    {
        // 如果任一规则不允许叠加
        if (!occupation1.CanStackWithOthers || !occupation2.CanStackWithOthers)
        {
            return true;
        }

        // 根据排他性级别判断
        return (occupation1.ExclusivityLevel, occupation2.ExclusivityLevel) switch
        {
            (ProductExclusivityLevel.Exclusive, _) => true,
            (_, ProductExclusivityLevel.Exclusive) => true,
            (ProductExclusivityLevel.Partial, ProductExclusivityLevel.Partial) => false,
            (ProductExclusivityLevel.None, _) => false,
            (_, ProductExclusivityLevel.None) => false,
            _ => false
        };
    }

    /// <summary>
    /// 检查规则占用之间是否有商品冲突
    /// </summary>
    private bool HasProductConflicts(List<RuleOccupation> occupations1, List<RuleOccupation> occupations2)
    {
        foreach (var occ1 in occupations1)
        {
            foreach (var occ2 in occupations2.Where(o => o.ProductId == occ1.ProductId))
            {
                if (HasConflict(occ1, occ2))
                {
                    return true;
                }
            }
        }
        return false;
    }

    /// <summary>
    /// 生成有效的规则组合
    /// </summary>
    private List<List<PromotionRuleBase>> GenerateValidCombinations(List<PromotionRuleBase> rules, ProcessedCart cart)
    {
        var validCombinations = new List<List<PromotionRuleBase>>();

        // 生成所有可能的组合（从单个规则到所有规则）
        for (int size = 1; size <= rules.Count; size++)
        {
            var combinations = GetCombinations(rules, size);
            foreach (var combination in combinations)
            {
                if (IsValidCombination(combination, cart))
                {
                    validCombinations.Add(combination);
                }
            }
        }

        return validCombinations;
    }

    /// <summary>
    /// 检查规则组合是否有效
    /// </summary>
    private bool IsValidCombination(List<PromotionRuleBase> rules, ProcessedCart cart)
    {
        // 检查每对规则是否可以同时应用
        for (int i = 0; i < rules.Count; i++)
        {
            for (int j = i + 1; j < rules.Count; j++)
            {
                if (!CanApplyTogether(rules[i], rules[j], cart))
                {
                    return false;
                }
            }
        }
        return true;
    }

    /// <summary>
    /// 评估组合的得分
    /// </summary>
    private decimal EvaluateCombination(List<PromotionRuleBase> rules, ProcessedCart cart, OptimizationStrategy strategy)
    {
        // 这里应该实际计算促销效果，简化实现
        decimal totalDiscount = 0;
        int totalRules = rules.Count;

        foreach (var rule in rules)
        {
            // 根据规则类型估算折扣
            totalDiscount += EstimateRuleDiscount(rule, cart);
        }

        return strategy switch
        {
            OptimizationStrategy.MaxCustomerBenefit => totalDiscount,
            OptimizationStrategy.MaxMerchantBenefit => -totalDiscount, // 商家希望折扣最小
            OptimizationStrategy.MaxRuleCount => totalRules * 100, // 优先规则数量
            OptimizationStrategy.Balanced => totalDiscount * 0.7m + totalRules * 30, // 平衡策略
            _ => totalDiscount
        };
    }

    /// <summary>
    /// 估算规则的折扣金额
    /// </summary>
    private decimal EstimateRuleDiscount(PromotionRuleBase rule, ProcessedCart cart)
    {
        return rule switch
        {
            IDiscountRule discount => cart.Items
                .Where(i => discount.ApplicableProductIds.Contains(i.ProductId))
                .Sum(i => i.OriginalUnitPrice * i.Quantity * (1 - discount.DiscountRate)),
            
            IBuyXGetYRule buyXGetY => cart.Items
                .Where(i => buyXGetY.ApplicableProductIds.Contains(i.ProductId))
                .Sum(i => i.OriginalUnitPrice * Math.Min(buyXGetY.FreeQuantity, i.Quantity)),
            
            _ => 0
        };
    }

    /// <summary>
    /// 生成组合
    /// </summary>
    private IEnumerable<List<T>> GetCombinations<T>(List<T> items, int size)
    {
        if (size == 0)
            yield return [];
        else if (size == items.Count)
            yield return items.ToList();
        else if (size == 1)
        {
            foreach (var item in items)
                yield return [item];
        }
        else
        {
            for (int i = 0; i <= items.Count - size; i++)
            {
                foreach (var combination in GetCombinations(items.Skip(i + 1).ToList(), size - 1))
                {
                    yield return [items[i], .. combination];
                }
            }
        }
    }

    /// <summary>
    /// 生成排他性组
    /// </summary>
    private List<ExclusivityGroup> GenerateExclusivityGroups(List<PromotionRuleBase> rules, List<ExclusivityConflict> conflicts)
    {
        var groups = new List<ExclusivityGroup>();
        var processedRules = new HashSet<string>();

        foreach (var conflict in conflicts)
        {
            if (!processedRules.Contains(conflict.Rule1Id) && !processedRules.Contains(conflict.Rule2Id))
            {
                var group = new ExclusivityGroup
                {
                    GroupId = Guid.NewGuid().ToString("N"),
                    RuleIds = [conflict.Rule1Id, conflict.Rule2Id],
                    ConflictType = conflict.ConflictType,
                    Description = $"互斥组: {conflict.Rule1Name} 与 {conflict.Rule2Name}"
                };
                groups.Add(group);
                processedRules.Add(conflict.Rule1Id);
                processedRules.Add(conflict.Rule2Id);
            }
        }

        return groups;
    }

    /// <summary>
    /// 构建兼容性矩阵
    /// </summary>
    private Dictionary<string, Dictionary<string, bool>> BuildCompatibilityMatrix(List<PromotionRuleBase> rules)
    {
        var matrix = new Dictionary<string, Dictionary<string, bool>>();

        foreach (var rule1 in rules)
        {
            matrix[rule1.Id] = new Dictionary<string, bool>();
            foreach (var rule2 in rules)
            {
                matrix[rule1.Id][rule2.Id] = rule1.Id == rule2.Id || CanApplyTogether(rule1, rule2, new ProcessedCart());
            }
        }

        return matrix;
    }

    /// <summary>
    /// 确定冲突类型
    /// </summary>
    private ExclusivityConflictType DetermineConflictType(RuleOccupation occupation1, RuleOccupation occupation2)
    {
        if (!occupation1.CanStackWithOthers || !occupation2.CanStackWithOthers)
        {
            return ExclusivityConflictType.StackingNotAllowed;
        }

        if (occupation1.ExclusivityLevel == ProductExclusivityLevel.Exclusive || 
            occupation2.ExclusivityLevel == ProductExclusivityLevel.Exclusive)
        {
            return ExclusivityConflictType.ExclusiveLevel;
        }

        return ExclusivityConflictType.PartialConflict;
    }

    /// <summary>
    /// 生成冲突描述
    /// </summary>
    private string GenerateConflictDescription(RuleOccupation occupation1, RuleOccupation occupation2)
    {
        return $"规则 {occupation1.RuleName} 与 {occupation2.RuleName} 在商品占用上存在冲突";
    }

    /// <summary>
    /// 生成组合描述
    /// </summary>
    private string GenerateCombinationDescription(List<PromotionRuleBase> rules)
    {
        return string.Join(" + ", rules.Select(r => r.Name));
    }
}

/// <summary>
/// 排他性分析结果
/// </summary>
public sealed class ExclusivityAnalysisResult
{
    public List<ExclusivityConflict> Conflicts { get; set; } = [];
    public List<ExclusivityGroup> ExclusivityGroups { get; set; } = [];
    public Dictionary<string, Dictionary<string, bool>> CompatibilityMatrix { get; set; } = new();
    public List<List<PromotionRuleBase>> OptimalCombinations { get; set; } = [];
}

/// <summary>
/// 最优选择结果
/// </summary>
public sealed class OptimalSelectionResult
{
    public List<PromotionRuleBase> SelectedRules { get; set; } = [];
    public decimal Score { get; set; }
    public OptimizationStrategy Strategy { get; set; }
    public string Reason { get; set; } = string.Empty;
    public List<AlternativeCombination> AlternativeCombinations { get; set; } = [];
}

/// <summary>
/// 替代组合
/// </summary>
public sealed class AlternativeCombination
{
    public required List<PromotionRuleBase> Rules { get; init; }
    public decimal Score { get; init; }
    public string Description { get; init; } = string.Empty;
}

/// <summary>
/// 规则占用信息
/// </summary>
public sealed class RuleOccupation
{
    public required string RuleId { get; init; }
    public string RuleName { get; init; } = string.Empty;
    public required string ProductId { get; init; }
    public int RequiredQuantity { get; init; }
    public OccupationType OccupationType { get; init; }
    public ProductExclusivityLevel ExclusivityLevel { get; init; }
    public bool CanStackWithOthers { get; init; }
}

/// <summary>
/// 占用类型
/// </summary>
public enum OccupationType
{
    Condition,
    Execution
}

/// <summary>
/// 排他性冲突
/// </summary>
public sealed class ExclusivityConflict
{
    public required string ProductId { get; init; }
    public required string Rule1Id { get; init; }
    public string Rule1Name { get; init; } = string.Empty;
    public required string Rule2Id { get; init; }
    public string Rule2Name { get; init; } = string.Empty;
    public ExclusivityConflictType ConflictType { get; init; }
    public string Description { get; init; } = string.Empty;
}

/// <summary>
/// 排他性冲突类型
/// </summary>
public enum ExclusivityConflictType
{
    StackingNotAllowed,
    ExclusiveLevel,
    PartialConflict
}

/// <summary>
/// 排他性组
/// </summary>
public sealed class ExclusivityGroup
{
    public required string GroupId { get; init; }
    public List<string> RuleIds { get; init; } = [];
    public ExclusivityConflictType ConflictType { get; init; }
    public string Description { get; init; } = string.Empty;
}

/// <summary>
/// 优化策略
/// </summary>
public enum OptimizationStrategy
{
    MaxCustomerBenefit,
    MaxMerchantBenefit,
    MaxRuleCount,
    Balanced
}
