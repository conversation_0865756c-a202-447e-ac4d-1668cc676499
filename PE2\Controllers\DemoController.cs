using Microsoft.AspNetCore.Mvc;
using PE2.Models;
using PE2.Services;

namespace PE2.Controllers;

/// <summary>
/// 演示控制器 - 用于展示促销引擎功能
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class DemoController : ControllerBase
{
    private readonly PromotionEngineService _promotionEngineService;
    private readonly ILogger<DemoController> _logger;

    public DemoController(PromotionEngineService promotionEngineService, ILogger<DemoController> logger)
    {
        _promotionEngineService = promotionEngineService;
        _logger = logger;
    }

    /// <summary>
    /// 创建示例购物车（A:1, B:2, C:5）
    /// </summary>
    [HttpGet("sample-cart")]
    public IActionResult CreateSampleCart()
    {
        var cart = new ShoppingCart
        {
            Id = "DEMO_CART_001",
            CustomerId = "CUSTOMER_001"
        };

        // 添加商品A：1件，单价50元
        cart.AddItem(new Product
        {
            Id = "A",
            Name = "商品A",
            Price = 50.00m,
            Category = "电子产品",
            Barcode = "1234567890001",
            Brand = "品牌A"
        }, 1);

        // 添加商品B：2件，单价30元
        cart.AddItem(new Product
        {
            Id = "B",
            Name = "商品B",
            Price = 30.00m,
            Category = "服装",
            Barcode = "1234567890002",
            Brand = "品牌B"
        }, 2);

        // 添加商品C：5件，单价20元
        cart.AddItem(new Product
        {
            Id = "C",
            Name = "商品C",
            Price = 20.00m,
            Category = "家居",
            Barcode = "1234567890003",
            Brand = "品牌C"
        }, 5);

        return Ok(cart);
    }

    /// <summary>
    /// 演示完整的促销计算流程
    /// </summary>
    [HttpPost("full-demo")]
    public async Task<IActionResult> FullDemo()
    {
        try
        {
            // 1. 创建示例购物车
            var cart = new ShoppingCart
            {
                Id = "DEMO_CART_002",
                CustomerId = "CUSTOMER_002"
            };

            cart.AddItem(new Product { Id = "A", Name = "商品A", Price = 50.00m, Category = "电子产品" }, 1);
            cart.AddItem(new Product { Id = "B", Name = "商品B", Price = 30.00m, Category = "服装" }, 2);
            cart.AddItem(new Product { Id = "C", Name = "商品C", Price = 20.00m, Category = "家居" }, 5);

            var demoResult = new
            {
                Step1_OriginalCart = new
                {
                    Cart = cart,
                    TotalAmount = cart.TotalAmount,
                    TotalQuantity = cart.TotalQuantity,
                    Description = "原始购物车：A商品1件(50元)，B商品2件(30元/件)，C商品5件(20元/件)，总计210元"
                }
            };

            // 2. 获取促销预览
            var previews = await _promotionEngineService.GetPromotionPreviewsAsync(cart);

            var step2 = new
            {
                Step2_PromotionPreviews = new
                {
                    AvailablePromotions = previews,
                    Description = "系统分析了所有可用的促销规则，显示哪些可以应用以及预计优惠金额"
                }
            };

            // 3. 计算最优促销组合
            var result = await _promotionEngineService.CalculateOptimalPromotionsAsync(cart.Clone());

            var step3 = new
            {
                Step3_OptimalCalculation = new
                {
                    Result = result,
                    Description = "使用回溯算法找到最优促销组合，确保客户获得最大优惠"
                }
            };

            // 4. 分析计算过程
            var analysisResult = AnalyzeCalculationProcess(result);

            var step4 = new
            {
                Step4_ProcessAnalysis = new
                {
                    Analysis = analysisResult,
                    Description = "详细分析计算过程，展示每个步骤的决策原因"
                }
            };

            return Ok(new
            {
                DemoTitle = "POSPE2 促销引擎完整演示",
                DemoDescription = "展示从购物车创建到最优促销计算的完整流程，包括溯源分析",
                demoResult.Step1_OriginalCart,
                step2.Step2_PromotionPreviews,
                //step3.Step3_OptimalCalculation,
                step4.Step4_ProcessAnalysis,
                result.ProcessedCart,
                Summary = new
                {
                    OriginalAmount = result.OriginalAmount,
                    TotalDiscount = result.TotalDiscount,
                    FinalAmount = result.FinalAmount,
                    DiscountRate = result.DiscountRate,
                    CalculationTime = $"{result.CalculationTimeMs}ms",
                    IsOptimal = result.IsOptimal
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "演示过程中发生错误");
            return StatusCode(500, $"演示过程中发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 分析计算过程
    /// </summary>
    private object AnalyzeCalculationProcess(PE2.PromotionEngine.Models.PromotionResult result)
    {
        var analysis = new
        {
            AppliedPromotionsAnalysis = result.AppliedPromotions.Select(p => new
            {
                p.RuleName,
                p.PromotionType,
                p.DiscountAmount,
                p.ApplicationCount,
                p.Description,
                ConsumedItems = p.ConsumedItems.Select(c => $"{c.ProductName} x{c.Quantity}"),
                GiftItems = p.GiftItems.Select(g => $"{g.ProductName} x{g.Quantity} (价值{g.Value:C})")
            }),

            IgnoredPromotionsAnalysis = result.IgnoredPromotions.Select(i => new
            {
                i.RuleName,
                i.Reason,
                i.ReasonType,
                Impact = GetIgnoreReasonImpact(i.ReasonType)
            }),

            CalculationStepsAnalysis = new
            {
                TotalSteps = result.CalculationSteps.Count,
                StepsByType = result.CalculationSteps.GroupBy(s => s.Type)
                    .ToDictionary(g => g.Key.ToString(), g => g.Count()),
                KeyDecisions = result.CalculationSteps
                    .Where(s => s.Type == PE2.PromotionEngine.Models.StepType.PromotionApplication ||
                               s.Type == PE2.PromotionEngine.Models.StepType.ResultComparison)
                    .Select(s => new { s.StepNumber, s.Description, s.Type })
            },

            OptimizationAnalysis = new
            {
                result.IsOptimal,
                result.AlgorithmInfo,
                PerformanceMetrics = new
                {
                    CalculationTimeMs = result.CalculationTimeMs,
                    StepsPerSecond = result.CalculationSteps.Count / Math.Max(result.CalculationTimeMs / 1000.0, 0.001)
                }
            }
        };

        return analysis;
    }

    /// <summary>
    /// 获取忽略原因的影响说明
    /// </summary>
    private string GetIgnoreReasonImpact(PE2.PromotionEngine.Models.IgnoreReason reasonType)
    {
        return reasonType switch
        {
            PE2.PromotionEngine.Models.IgnoreReason.ConditionNotMet => "促销条件不满足，无法应用",
            PE2.PromotionEngine.Models.IgnoreReason.InsufficientQuantity => "商品数量不足，无法满足促销要求",
            PE2.PromotionEngine.Models.IgnoreReason.Expired => "促销已过期，不在有效期内",
            PE2.PromotionEngine.Models.IgnoreReason.NotStarted => "促销尚未开始",
            PE2.PromotionEngine.Models.IgnoreReason.Conflict => "与其他促销冲突，无法同时应用",
            PE2.PromotionEngine.Models.IgnoreReason.NotOptimal => "虽然可以应用，但不是最优选择",
            _ => "其他原因"
        };
    }

    /// <summary>
    /// 创建复杂场景的购物车
    /// </summary>
    [HttpGet("complex-cart")]
    public IActionResult CreateComplexCart()
    {
        var cart = new ShoppingCart
        {
            Id = "COMPLEX_CART_001",
            CustomerId = "CUSTOMER_003"
        };

        // 添加多种商品，测试复杂促销场景
        cart.AddItem(new Product { Id = "A", Name = "高端手机", Price = 3000.00m, Category = "电子产品" }, 2);
        cart.AddItem(new Product { Id = "B", Name = "时尚T恤", Price = 150.00m, Category = "服装" }, 4);
        cart.AddItem(new Product { Id = "C", Name = "家用电器", Price = 800.00m, Category = "家居" }, 3);
        cart.AddItem(new Product { Id = "D", Name = "运动鞋", Price = 500.00m, Category = "服装" }, 2);
        cart.AddItem(new Product { Id = "E", Name = "书籍", Price = 50.00m, Category = "图书" }, 10);

        return Ok(new
        {
            Cart = cart,
            Description = "复杂购物车场景：包含多种商品和分类，总价值超过10000元，适合测试多种促销规则的组合优化",
            TotalAmount = cart.TotalAmount,
            TotalQuantity = cart.TotalQuantity,
            CategoryBreakdown = cart.Items.GroupBy(i => i.Product.Category)
                .ToDictionary(g => g.Key, g => new
                {
                    Quantity = g.Sum(i => i.Quantity),
                    Amount = g.Sum(i => i.SubTotal)
                })
        });
    }
}
