using PE2.PromotionEngine.Rules.SpecialPriceRules;
using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.SpecialPriceRules;

/// <summary>
/// 梯度特价规则测试类
/// 测试 TieredSpecialPriceRule 的梯度特价计算和应用逻辑
/// </summary>
public class TieredSpecialPriceRuleTests(ITestOutputHelper output) : TestBase(output)
{
    #region 基础梯度特价功能测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_SingleTierSpecialPrice_ShouldApplyCorrectly()
    {
        // Arrange - A商品1件特价100元
        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_001",
            Name = "梯度特价测试 - A商品梯度特价",
            Description = "当购买商品数量大于等于1件时，特价100元",
            Priority = 80,
            IsEnabled = true,
            ApplicableProductIds = ["A"],
            IsByAmount = false,
            SpecialPriceTiers =
            [
                new SpecialPriceTier
                {
                    MinQuantity = 1,
                    MinAmount = 0,
                    SpecialPrice = 100.00m,
                    Description = "满1件特价100元"
                }
            ]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            (TestDataGenerator.CreateProductA(), 1) // 满足1件条件
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "应用前购物车");

        var originalPrice = TestDataGenerator.CreateProductA().Price; // 200元
        var expectedSpecialPrice = 100m; // 特价100元
        var expectedTotalDiscount = originalPrice - expectedSpecialPrice; // 200-100 = 100元

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "A商品1件特价100元");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证商品A的实际单价被调整为特价
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(expectedSpecialPrice, productAItem.ActualUnitPrice, "商品A的实际单价应为特价100元");

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为特价优惠金额");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_MultiTierSpecialPrice_ShouldApplyHighestTier()
    {
        // Arrange - A商品多梯度特价：1件100元，2件50元，3件10元
        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_001",
            Name = "梯度特价测试 - A商品梯度特价",
            ApplicableProductIds = ["A"],
            IsByAmount = false,
            IsEnabled = true,
            SpecialPriceTiers =
            [
                new SpecialPriceTier { MinQuantity = 1, SpecialPrice = 100.00m },
                new SpecialPriceTier { MinQuantity = 2, SpecialPrice = 50.00m },
                new SpecialPriceTier { MinQuantity = 3, SpecialPrice = 10.00m }
            ]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_002",
            (TestDataGenerator.CreateProductA(), 3) // 满足3件条件，应用最高梯度
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "多梯度特价购物车");

        var expectedSpecialPrice = 10m; // 最高梯度特价10元
        var expectedTotalDiscount = (200m - 10m) * 3; // (200-10)*3 = 570元

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "A商品3件应用最高梯度特价10元");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证商品A的实际单价被调整为最高梯度特价
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(
            expectedSpecialPrice,
            productAItem.ActualUnitPrice,
            "商品A的实际单价应为最高梯度特价10元"
        );

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为3件商品的特价优惠金额");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_MiddleTierQuantity_ShouldApplyCorrectTier()
    {
        // Arrange - A商品2件，应用中间梯度
        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_001",
            Name = "梯度特价测试 - A商品梯度特价",
            ApplicableProductIds = ["A"],
            IsByAmount = false,
            IsEnabled = true,
            SpecialPriceTiers =
            [
                new SpecialPriceTier { MinQuantity = 1, SpecialPrice = 100.00m },
                new SpecialPriceTier { MinQuantity = 2, SpecialPrice = 50.00m },
                new SpecialPriceTier { MinQuantity = 3, SpecialPrice = 10.00m }
            ]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_003",
            (TestDataGenerator.CreateProductA(), 2) // 满足2件条件，应用中间梯度
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "中间梯度特价购物车");

        var expectedSpecialPrice = 50m; // 中间梯度特价50元
        var expectedTotalDiscount = (200m - 50m) * 2; // (200-50)*2 = 300元

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "A商品2件应用中间梯度特价50元");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证商品A的实际单价被调整为中间梯度特价
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(
            expectedSpecialPrice,
            productAItem.ActualUnitPrice,
            "商品A的实际单价应为中间梯度特价50元"
        );

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为2件商品的特价优惠金额");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 按金额梯度特价测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_AmountBasedTieredSpecialPrice_ShouldApplyCorrectly()
    {
        // Arrange - 按金额梯度特价
        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_AMOUNT_001",
            Name = "按金额梯度特价测试",
            ApplicableProductIds = ["A"],
            IsByAmount = true,
            IsEnabled = true,
            SpecialPriceTiers =
            [
                new SpecialPriceTier { MinAmount = 500m, SpecialPrice = 150.00m },
                new SpecialPriceTier { MinAmount = 1000m, SpecialPrice = 100.00m },
                new SpecialPriceTier { MinAmount = 1500m, SpecialPrice = 50.00m }
            ]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_004",
            (TestDataGenerator.CreateProductA(), 8) // 8件A，总价1600元，满足最高梯度
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "按金额梯度特价购物车");

        var expectedSpecialPrice = 50m; // 最高梯度特价50元
        var expectedTotalDiscount = (200m - 50m) * 8; // (200-50)*8 = 1200元

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "按金额梯度特价场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证商品A的实际单价被调整为最高梯度特价
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(
            expectedSpecialPrice,
            productAItem.ActualUnitPrice,
            "商品A的实际单价应为最高梯度特价50元"
        );

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_InsufficientQuantityForAnyTier_ShouldNotApply()
    {
        // Arrange - 数量不足：没有A商品
        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_001",
            Name = "梯度特价测试 - A商品梯度特价",
            ApplicableProductIds = ["A"],
            IsByAmount = false,
            IsEnabled = true,
            SpecialPriceTiers = [new SpecialPriceTier { MinQuantity = 1, SpecialPrice = 100.00m }]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_005",
            (TestDataGenerator.CreateProductB(), 2) // 只有B，没有A
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "数量不足的购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "数量不足场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "数量不足时应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange - 禁用的规则
        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_001",
            Name = "梯度特价测试 - A商品梯度特价",
            ApplicableProductIds = ["A"],
            IsByAmount = false,
            IsEnabled = false, // 禁用规则
            SpecialPriceTiers = [new SpecialPriceTier { MinQuantity = 1, SpecialPrice = 100.00m }]
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_007",
            (TestDataGenerator.CreateProductA(), 2)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "禁用规则测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "禁用规则场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "规则已禁用");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "禁用规则应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_ExactTierBoundary_ShouldApplyCorrectTier()
    {
        // Arrange - 恰好满足梯度边界
        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_BOUNDARY_001",
            Name = "梯度特价测试 - 边界条件",
            ApplicableProductIds = ["A"],
            SpecialPriceTiers =
            [
                new SpecialPriceTier { MinQuantity = 3, SpecialPrice = 150.00m },
                new SpecialPriceTier { MinQuantity = 5, SpecialPrice = 120.00m },
                new SpecialPriceTier { MinQuantity = 10, SpecialPrice = 100.00m }
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_008",
            (TestDataGenerator.CreateProductA(), 5) // 恰好满足第二梯度
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "梯度边界测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "梯度边界场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证应用了正确的梯度特价（120元）
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(120m, productAItem.ActualUnitPrice, "应应用第二梯度特价120元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public async Task Apply_JustBelowTierBoundary_ShouldApplyLowerTier()
    {
        // Arrange - 刚好低于梯度边界
        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_BOUNDARY_002",
            Name = "梯度特价测试 - 低于边界",
            ApplicableProductIds = ["A"],
            SpecialPriceTiers =
            [
                new SpecialPriceTier { MinQuantity = 3, SpecialPrice = 150.00m },
                new SpecialPriceTier { MinQuantity = 5, SpecialPrice = 120.00m },
                new SpecialPriceTier { MinQuantity = 10, SpecialPrice = 100.00m }
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_009",
            (TestDataGenerator.CreateProductA(), 4) // 低于第二梯度，应用第一梯度
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "低于梯度边界测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "低于梯度边界场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证应用了第一梯度特价（150元）
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(150m, productAItem.ActualUnitPrice, "应应用第一梯度特价150元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_ZeroTierPrice_ShouldApplyCorrectly()
    {
        // Arrange - 零价格梯度（免费）
        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_ZERO_001",
            Name = "梯度特价测试 - 免费梯度",
            ApplicableProductIds = ["A"],
            SpecialPriceTiers =
            [
                new SpecialPriceTier { MinQuantity = 1, SpecialPrice = 150.00m },
                new SpecialPriceTier { MinQuantity = 5, SpecialPrice = 0.00m } // 免费
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_010",
            (TestDataGenerator.CreateProductA(), 6)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "免费梯度测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "免费梯度场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证应用了免费梯度
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(0m, productAItem.ActualUnitPrice, "应应用免费梯度");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 可重复性测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_MultipleApplications_ShouldBeConsistent()
    {
        // Arrange - 多次应用同一规则应该得到一致结果
        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_REPEAT_001",
            Name = "梯度特价测试 - 可重复性",
            ApplicableProductIds = ["A"],
            SpecialPriceTiers =
            [
                new SpecialPriceTier { MinQuantity = 2, SpecialPrice = 150.00m },
                new SpecialPriceTier { MinQuantity = 5, SpecialPrice = 120.00m }
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_011",
            (TestDataGenerator.CreateProductA(), 6)
        );

        ValidateTestData(cart, [rule]);
        TestPromotionRuleService.Rules = [rule];

        // Act - 多次执行
        var result1 = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;
        var result2 = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;
        var result3 = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert - 结果应该完全一致
        AssertAmountEqual(result1.TotalDiscount, result2.TotalDiscount, "第一次和第二次结果应一致");
        AssertAmountEqual(result2.TotalDiscount, result3.TotalDiscount, "第二次和第三次结果应一致");

        Assert.Equal(result1.AppliedPromotions.Count, result2.AppliedPromotions.Count);
        Assert.Equal(result2.AppliedPromotions.Count, result3.AppliedPromotions.Count);

        // 验证应用的梯度一致
        var productA1 = result1.ProcessedCart.Items.First(i => i.Product.Id == "A");
        var productA2 = result2.ProcessedCart.Items.First(i => i.Product.Id == "A");
        var productA3 = result3.ProcessedCart.Items.First(i => i.Product.Id == "A");

        AssertAmountEqual(productA1.ActualUnitPrice, productA2.ActualUnitPrice, "商品A特价应一致");
        AssertAmountEqual(productA2.ActualUnitPrice, productA3.ActualUnitPrice, "商品A特价应一致");

        // 验证购物车一致性
        AssertCartConsistency(result1.ProcessedCart);
        AssertCartConsistency(result2.ProcessedCart);
        AssertCartConsistency(result3.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_SameRuleMultipleProducts_ShouldApplyToAll()
    {
        // Arrange - 同一规则应用于多个符合条件的商品
        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_MULTI_001",
            Name = "梯度特价测试 - 多商品应用",
            ApplicableProductIds = ["A", "B"], // 适用于A和B商品
            SpecialPriceTiers =
            [
                new SpecialPriceTier { MinQuantity = 1, SpecialPrice = 80.00m },
                new SpecialPriceTier { MinQuantity = 3, SpecialPrice = 60.00m }
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_012",
            (TestDataGenerator.CreateProductA(), 4), // 满足第二梯度
            (TestDataGenerator.CreateProductB(), 2) // 满足第一梯度
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "多商品梯度应用测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "多商品梯度应用场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证A和B商品都被调整为对应梯度的特价
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        AssertAmountEqual(60m, productAItem.ActualUnitPrice, "商品A应为第二梯度特价60元");
        AssertAmountEqual(80m, productBItem.ActualUnitPrice, "商品B应为第一梯度特价80元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 复杂场景测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_MultipleRulesConflict_ShouldApplyOptimal()
    {
        // Arrange - 多个梯度规则冲突，应选择最优的
        var rule1 = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_CONFLICT_001",
            Name = "梯度特价测试 - 规则1",
            ApplicableProductIds = ["A"],
            SpecialPriceTiers =
            [
                new SpecialPriceTier { MinQuantity = 2, SpecialPrice = 140.00m },
                new SpecialPriceTier { MinQuantity = 5, SpecialPrice = 110.00m }
            ],
            Priority = 50,
            IsEnabled = true
        };

        var rule2 = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_CONFLICT_002",
            Name = "梯度特价测试 - 规则2",
            ApplicableProductIds = ["A"],
            SpecialPriceTiers =
            [
                new SpecialPriceTier { MinQuantity = 2, SpecialPrice = 120.00m }, // 更优
                new SpecialPriceTier { MinQuantity = 5, SpecialPrice = 90.00m } // 更优
            ],
            Priority = 60,
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_013",
            (TestDataGenerator.CreateProductA(), 6)
        );

        ValidateTestData(cart, [rule1, rule2]);
        LogCartDetails(cart, "多梯度规则冲突测试购物车");

        TestPromotionRuleService.Rules = [rule1, rule2];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "多梯度规则冲突场景");
        LogPromotionResultDetails(result);

        // 验证应用了更优的规则
        Assert.Single(result.AppliedPromotions);

        // 验证商品A的实际单价（应该是更优的90元）
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(90m, productAItem.ActualUnitPrice, "应应用更优的梯度特价90元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_ComplexTierStructure_ShouldApplyCorrectly()
    {
        // Arrange - 复杂梯度结构测试
        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_COMPLEX_001",
            Name = "梯度特价测试 - 复杂结构",
            ApplicableProductIds = ["A", "B", "C"],
            SpecialPriceTiers =
            [
                new SpecialPriceTier { MinQuantity = 1, SpecialPrice = 180.00m },
                new SpecialPriceTier { MinQuantity = 3, SpecialPrice = 150.00m },
                new SpecialPriceTier { MinQuantity = 5, SpecialPrice = 120.00m },
                new SpecialPriceTier { MinQuantity = 10, SpecialPrice = 100.00m },
                new SpecialPriceTier { MinQuantity = 20, SpecialPrice = 80.00m }
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_014",
            (TestDataGenerator.CreateProductA(), 12), // 第四梯度
            (TestDataGenerator.CreateProductB(), 4), // 第二梯度
            (TestDataGenerator.CreateProductC(), 1) // 第一梯度
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "复杂梯度结构测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "复杂梯度结构场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证每个商品都应用了正确的梯度特价
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        var productCItem = result.ProcessedCart.Items.First(i => i.Product.Id == "C");

        AssertAmountEqual(100m, productAItem.ActualUnitPrice, "商品A应为第四梯度特价100元");
        AssertAmountEqual(150m, productBItem.ActualUnitPrice, "商品B应为第二梯度特价150元");
        AssertAmountEqual(180m, productCItem.ActualUnitPrice, "商品C应为第一梯度特价180元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 配置验证测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_NullTiers_ShouldNotApply()
    {
        // Arrange - 空梯度配置
        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_NULL_001",
            Name = "梯度特价测试 - 空梯度",
            ApplicableProductIds = ["A"],
            SpecialPriceTiers = null, // 空梯度
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_015",
            (TestDataGenerator.CreateProductA(), 5)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "空梯度测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "空梯度场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_EmptyTiers_ShouldNotApply()
    {
        // Arrange - 空梯度列表
        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_EMPTY_001",
            Name = "梯度特价测试 - 空梯度列表",
            ApplicableProductIds = ["A"],
            SpecialPriceTiers = [], // 空列表
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_016",
            (TestDataGenerator.CreateProductA(), 5)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "空梯度列表测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "空梯度列表场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_NegativeTierQuantity_ShouldHandleCorrectly()
    {
        // Arrange - 负数梯度数量
        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_NEGATIVE_001",
            Name = "梯度特价测试 - 负数梯度数量",
            ApplicableProductIds = ["A"],
            SpecialPriceTiers =
            [
                new SpecialPriceTier { MinQuantity = -1, SpecialPrice = 150.00m }, // 负数
                new SpecialPriceTier { MinQuantity = 3, SpecialPrice = 120.00m }
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_017",
            (TestDataGenerator.CreateProductA(), 5)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "负数梯度数量测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "负数梯度数量场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确处理（可能忽略负数梯度或应用有效梯度）
        // 具体行为取决于业务逻辑实现

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_NegativeTierPrice_ShouldHandleCorrectly()
    {
        // Arrange - 负数梯度价格
        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_NEGATIVE_PRICE_001",
            Name = "梯度特价测试 - 负数梯度价格",
            ApplicableProductIds = ["A"],
            SpecialPriceTiers =
            [
                new SpecialPriceTier { MinQuantity = 1, SpecialPrice = 150.00m },
                new SpecialPriceTier { MinQuantity = 3, SpecialPrice = -50.00m } // 负数价格
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_018",
            (TestDataGenerator.CreateProductA(), 5)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "负数梯度价格测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "负数梯度价格场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确处理（可能忽略负数价格梯度或应用有效梯度）
        // 具体行为取决于业务逻辑实现

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 性能测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_LargeQuantityCart_ShouldPerformWell()
    {
        // Arrange - 大数量购物车性能测试
        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_PERFORMANCE_001",
            Name = "梯度特价测试 - 性能测试",
            ApplicableProductIds = ["A"],
            SpecialPriceTiers =
            [
                new SpecialPriceTier { MinQuantity = 1, SpecialPrice = 180.00m },
                new SpecialPriceTier { MinQuantity = 100, SpecialPrice = 150.00m },
                new SpecialPriceTier { MinQuantity = 500, SpecialPrice = 120.00m },
                new SpecialPriceTier { MinQuantity = 1000, SpecialPrice = 100.00m }
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_019",
            (TestDataGenerator.CreateProductA(), 1500) // 大数量
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "大数量梯度性能测试购物车");

        TestPromotionRuleService.Rules = [rule];

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        stopwatch.Stop();

        // Assert
        AssertPromotionResult(result, "大数量梯度性能测试场景");
        LogPromotionResultDetails(result);

        // 验证性能（应在合理时间内完成，如1秒）
        Assert.True(
            stopwatch.ElapsedMilliseconds < 1000,
            $"性能测试超时：{stopwatch.ElapsedMilliseconds}ms"
        );

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证应用了最高梯度特价
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(100m, productAItem.ActualUnitPrice, "应应用最高梯度特价100元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Low")]
    public void Apply_ManyTiers_ShouldPerformWell()
    {
        // Arrange - 大量梯度配置性能测试
        var tiers = new List<SpecialPriceTier>();
        for (int i = 1; i <= 100; i++)
        {
            tiers.Add(
                new SpecialPriceTier
                {
                    MinQuantity = i,
                    SpecialPrice = 200m - i // 递减价格
                }
            );
        }

        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_MANY_TIERS_001",
            Name = "梯度特价测试 - 大量梯度",
            ApplicableProductIds = ["A"],
            SpecialPriceTiers = tiers,
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_020",
            (TestDataGenerator.CreateProductA(), 50)
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "大量梯度性能测试购物车");

        TestPromotionRuleService.Rules = [rule];

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        stopwatch.Stop();

        // Assert
        AssertPromotionResult(result, "大量梯度性能测试场景");
        LogPromotionResultDetails(result);

        // 验证性能（应在合理时间内完成，如1秒）
        Assert.True(
            stopwatch.ElapsedMilliseconds < 1000,
            $"大量梯度性能测试超时：{stopwatch.ElapsedMilliseconds}ms"
        );

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证应用了正确的梯度特价（第50梯度：200-50=150元）
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(150m, productAItem.ActualUnitPrice, "应应用第50梯度特价150元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 利益最大化策略测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_CustomerBenefitMaximization_ShouldChooseOptimal()
    {
        // Arrange - 客户利益最大化：选择对客户最有利的梯度规则
        var rule1 = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_BENEFIT_001",
            Name = "梯度特价测试 - 利益最大化1",
            ApplicableProductIds = ["A"],
            SpecialPriceTiers =
            [
                new SpecialPriceTier { MinQuantity = 1, SpecialPrice = 160.00m },
                new SpecialPriceTier { MinQuantity = 3, SpecialPrice = 130.00m }
            ],
            Priority = 70,
            IsEnabled = true
        };

        var rule2 = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_BENEFIT_002",
            Name = "梯度特价测试 - 利益最大化2",
            ApplicableProductIds = ["A"],
            SpecialPriceTiers =
            [
                new SpecialPriceTier { MinQuantity = 1, SpecialPrice = 140.00m }, // 更优
                new SpecialPriceTier { MinQuantity = 3, SpecialPrice = 110.00m } // 更优
            ],
            Priority = 60,
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_021",
            (TestDataGenerator.CreateProductA(), 4)
        );

        ValidateTestData(cart, [rule1, rule2]);
        LogCartDetails(cart, "梯度利益最大化测试购物车");

        TestPromotionRuleService.Rules = [rule1, rule2];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "梯度利益最大化场景");
        LogPromotionResultDetails(result);

        // 验证选择了对客户最有利的规则（更低的特价）
        Assert.Single(result.AppliedPromotions);

        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(110m, productAItem.ActualUnitPrice, "应选择对客户最有利的梯度特价110元");

        // 验证总优惠最大化
        var expectedDiscount = (200m - 110m) * 4; // (200-110)*4 = 360元
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应实现总优惠最大化");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "High")]
    public void Apply_TierOptimization_ShouldChooseBestTier()
    {
        // Arrange - 梯度优化：在同一规则内选择最佳梯度
        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_OPTIMIZATION_001",
            Name = "梯度特价测试 - 梯度优化",
            ApplicableProductIds = ["A"],
            SpecialPriceTiers =
            [
                new SpecialPriceTier { MinQuantity = 1, SpecialPrice = 180.00m },
                new SpecialPriceTier { MinQuantity = 3, SpecialPrice = 150.00m },
                new SpecialPriceTier { MinQuantity = 5, SpecialPrice = 120.00m },
                new SpecialPriceTier { MinQuantity = 8, SpecialPrice = 100.00m },
                new SpecialPriceTier { MinQuantity = 12, SpecialPrice = 80.00m }
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_022",
            (TestDataGenerator.CreateProductA(), 10) // 满足第四梯度
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "梯度优化测试购物车");

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "梯度优化场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证应用了最佳可用梯度（第四梯度100元）
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(100m, productAItem.ActualUnitPrice, "应应用最佳可用梯度特价100元");

        // 验证总优惠
        var expectedDiscount = (200m - 100m) * 10; // (200-100)*10 = 1000元
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应实现梯度优化的总优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 数据完整性测试

    [Fact]
    [Trait("Category", "SpecialPrice")]
    [Trait("Priority", "Medium")]
    public void Apply_TierCalculationAccuracy_ShouldMaintainPrecision()
    {
        // Arrange - 梯度计算精度测试
        var rule = new TieredSpecialPriceRule
        {
            Id = "TIERED_SPECIAL_PRICE_PRECISION_001",
            Name = "梯度特价测试 - 计算精度",
            ApplicableProductIds = ["A", "B"],
            SpecialPriceTiers =
            [
                new SpecialPriceTier { MinQuantity = 1, SpecialPrice = 99.99m },
                new SpecialPriceTier { MinQuantity = 3, SpecialPrice = 66.66m },
                new SpecialPriceTier { MinQuantity = 5, SpecialPrice = 33.33m }
            ],
            IsEnabled = true
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_023",
            (TestDataGenerator.CreateProductA(), 6), // 第三梯度
            (TestDataGenerator.CreateProductB(), 4) // 第二梯度
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "梯度计算精度测试购物车");

        var originalTotal = cart.Items.Sum(i => i.Product.Price * i.Quantity);

        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "梯度计算精度场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证精确的梯度特价
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        AssertAmountEqual(33.33m, productAItem.ActualUnitPrice, "商品A应为精确的第三梯度特价33.33元");
        AssertAmountEqual(66.66m, productBItem.ActualUnitPrice, "商品B应为精确的第二梯度特价66.66元");

        // 验证购物车总额计算精度
        var processedTotal = result.ProcessedCart.Items.Sum(i => i.ActualUnitPrice * i.Quantity);
        var expectedTotal = originalTotal - result.TotalDiscount;

        AssertAmountEqual(expectedTotal, processedTotal, "处理后购物车总额应精确");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion
}
