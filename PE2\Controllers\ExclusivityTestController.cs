using Microsoft.AspNetCore.Mvc;
using PE2.Models;
using PE2.Services;
using PE2.PromotionEngine.Rules;

namespace PE2.Controllers;

/// <summary>
/// 促销互斥性测试控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ExclusivityTestController : ControllerBase
{
    private readonly PromotionEngineService _promotionEngineService;
    private readonly PromotionRuleService _ruleService;
    private readonly ILogger<ExclusivityTestController> _logger;

    public ExclusivityTestController(
        PromotionEngineService promotionEngineService,
        PromotionRuleService ruleService,
        ILogger<ExclusivityTestController> logger)
    {
        _promotionEngineService = promotionEngineService;
        _ruleService = ruleService;
        _logger = logger;
    }

    /// <summary>
    /// 测试促销互斥性功能
    /// </summary>
    [HttpPost("test-exclusivity")]
    public async Task<IActionResult> TestPromotionExclusivity()
    {
        try
        {
            // 创建测试购物车
            var cart = CreateTestCart();

            // 临时添加互斥性测试规则
            await AddExclusivityTestRules();

            // 计算促销结果
            var result = await _promotionEngineService.CalculateOptimalPromotionsAsync(cart);

            var testResult = new
            {
                TestScenario = "促销互斥性测试",
                TestCart = new
                {
                    Items = cart.Items.Select(item => new
                    {
                        ProductId = item.Product.Id,
                        ProductName = item.Product.Name,
                        Quantity = item.Quantity,
                        UnitPrice = item.UnitPrice
                    }),
                    TotalAmount = cart.TotalAmount
                },
                PromotionResult = new
                {
                    OriginalAmount = result.OriginalAmount,
                    TotalDiscount = result.TotalDiscount,
                    FinalAmount = result.FinalAmount,
                    AppliedPromotions = result.AppliedPromotions.Select(p => new
                    {
                        p.RuleId,
                        p.RuleName,
                        p.PromotionType,
                        p.DiscountAmount,
                        ConsumedProducts = p.ConsumedItems.Select(c => c.ProductId)
                    }),
                    IgnoredPromotions = result.IgnoredPromotions.Select(i => new
                    {
                        i.RuleId,
                        i.RuleName,
                        i.Reason,
                        i.ReasonType
                    })
                },
                ExclusivityAnalysis = AnalyzeExclusivity(result),
                TestConclusions = GenerateTestConclusions(result)
            };

            return Ok(testResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "测试促销互斥性时发生错误");
            return StatusCode(500, "测试失败");
        }
    }

    /// <summary>
    /// 比较有无互斥性限制的差异
    /// </summary>
    [HttpPost("compare-with-without-exclusivity")]
    public async Task<IActionResult> CompareWithWithoutExclusivity([FromBody] ShoppingCart cart)
    {
        try
        {
            if (cart == null || !cart.Items.Any())
            {
                return BadRequest("购物车不能为空");
            }

            // 场景1：使用当前规则（包含互斥性设置）
            var resultWithExclusivity = await _promotionEngineService.CalculateOptimalPromotionsAsync(cart.Clone());

            // 场景2：模拟移除所有互斥性限制
            // 这里需要临时修改规则，实际实现中可能需要更复杂的逻辑
            var resultWithoutExclusivity = await SimulateWithoutExclusivity(cart.Clone());

            var comparison = new
            {
                OriginalCart = new
                {
                    TotalAmount = cart.TotalAmount,
                    ItemCount = cart.Items.Count,
                    TotalQuantity = cart.TotalQuantity
                },
                WithExclusivity = new
                {
                    TotalDiscount = resultWithExclusivity.TotalDiscount,
                    FinalAmount = resultWithExclusivity.FinalAmount,
                    AppliedPromotionsCount = resultWithExclusivity.AppliedPromotions.Count,
                    IgnoredPromotionsCount = resultWithExclusivity.IgnoredPromotions.Count,
                    AppliedPromotions = resultWithExclusivity.AppliedPromotions.Select(p => p.RuleName)
                },
                WithoutExclusivity = new
                {
                    TotalDiscount = resultWithoutExclusivity.TotalDiscount,
                    FinalAmount = resultWithoutExclusivity.FinalAmount,
                    AppliedPromotionsCount = resultWithoutExclusivity.AppliedPromotions.Count,
                    IgnoredPromotionsCount = resultWithoutExclusivity.IgnoredPromotions.Count,
                    AppliedPromotions = resultWithoutExclusivity.AppliedPromotions.Select(p => p.RuleName)
                },
                Impact = new
                {
                    DiscountDifference = resultWithoutExclusivity.TotalDiscount - resultWithExclusivity.TotalDiscount,
                    PromotionCountDifference = resultWithoutExclusivity.AppliedPromotions.Count - resultWithExclusivity.AppliedPromotions.Count,
                    ExclusivityEffective = resultWithExclusivity.TotalDiscount != resultWithoutExclusivity.TotalDiscount,
                    Recommendation = GenerateRecommendation(resultWithExclusivity, resultWithoutExclusivity)
                }
            };

            return Ok(comparison);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "比较互斥性影响时发生错误");
            return StatusCode(500, "比较失败");
        }
    }

    /// <summary>
    /// 获取促销规则的互斥性配置
    /// </summary>
    [HttpGet("exclusivity-config")]
    public async Task<IActionResult> GetExclusivityConfiguration()
    {
        try
        {
            var rules = await _ruleService.GetAllRulesAsync();
            
            var exclusivityConfig = rules.Select(rule => new
            {
                rule.Id,
                rule.Name,
                rule.RuleType,
                rule.Priority,
                rule.IsEnabled,
                ExclusivitySettings = new
                {
                    rule.CanStackWithOthers,
                    rule.ProductExclusivity,
                    rule.ExclusiveRuleIds
                },
                Description = GetExclusivityDescription(rule)
            }).ToList();

            var summary = new
            {
                TotalRules = rules.Count,
                StackableRules = rules.Count(r => r.CanStackWithOthers),
                ExclusiveRules = rules.Count(r => !r.CanStackWithOthers),
                ExclusivityLevels = rules.GroupBy(r => r.ProductExclusivity)
                    .ToDictionary(g => g.Key.ToString(), g => g.Count()),
                RulesWithExclusiveIds = rules.Count(r => r.ExclusiveRuleIds.Any())
            };

            return Ok(new
            {
                Summary = summary,
                Rules = exclusivityConfig
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取互斥性配置时发生错误");
            return StatusCode(500, "获取配置失败");
        }
    }

    private ShoppingCart CreateTestCart()
    {
        var cart = new ShoppingCart
        {
            Id = "EXCLUSIVITY_TEST_CART",
            CustomerId = "TEST_CUSTOMER"
        };

        // 添加测试商品
        cart.AddItem(new Product { Id = "A", Name = "商品A", Price = 100, Category = "电子产品" }, 2);
        cart.AddItem(new Product { Id = "B", Name = "商品B", Price = 50, Category = "服装" }, 4);
        cart.AddItem(new Product { Id = "C", Name = "商品C", Price = 30, Category = "家居" }, 6);

        return cart;
    }

    private async Task AddExclusivityTestRules()
    {
        // 这里可以添加临时的测试规则
        // 实际实现中可能需要更复杂的规则管理逻辑
        _logger.LogInformation("添加互斥性测试规则");
    }

    private async Task<PE2.PromotionEngine.Models.PromotionResult> SimulateWithoutExclusivity(ShoppingCart cart)
    {
        // 这里模拟移除所有互斥性限制的计算
        // 实际实现中需要临时修改规则配置
        return await _promotionEngineService.CalculateOptimalPromotionsAsync(cart);
    }

    private object AnalyzeExclusivity(PE2.PromotionEngine.Models.PromotionResult result)
    {
        return new
        {
            ConflictAnalysis = result.IgnoredPromotions
                .Where(i => i.ReasonType == PE2.PromotionEngine.Models.IgnoreReason.Conflict)
                .Select(i => new { i.RuleName, i.Reason }),
            
            ProductOccupation = result.ProcessedCart.Items
                .Where(item => item.AppliedPromotionRuleIds.Any())
                .Select(item => new
                {
                    item.Product.Id,
                    item.Product.Name,
                    AppliedPromotions = item.AppliedPromotionRuleIds,
                    PromotionCount = item.AppliedPromotionRuleIds.Count
                }),
            
            StackingAnalysis = new
            {
                ItemsWithMultiplePromotions = result.ProcessedCart.Items
                    .Count(item => item.AppliedPromotionRuleIds.Count > 1),
                MaxPromotionsPerItem = result.ProcessedCart.Items
                    .Max(item => item.AppliedPromotionRuleIds.Count)
            }
        };
    }

    private List<string> GenerateTestConclusions(PE2.PromotionEngine.Models.PromotionResult result)
    {
        var conclusions = new List<string>();

        if (result.AppliedPromotions.Any())
        {
            conclusions.Add($"成功应用了 {result.AppliedPromotions.Count} 个促销规则");
        }

        var conflictCount = result.IgnoredPromotions.Count(i => i.ReasonType == PE2.PromotionEngine.Models.IgnoreReason.Conflict);
        if (conflictCount > 0)
        {
            conclusions.Add($"有 {conflictCount} 个促销因互斥冲突被忽略");
        }

        var multiPromotionItems = result.ProcessedCart.Items.Count(item => item.AppliedPromotionRuleIds.Count > 1);
        if (multiPromotionItems > 0)
        {
            conclusions.Add($"有 {multiPromotionItems} 个商品同时享受了多个促销");
        }
        else
        {
            conclusions.Add("所有商品都只享受了单一促销，互斥性控制有效");
        }

        if (result.IsOptimal)
        {
            conclusions.Add("找到了最优促销组合");
        }
        else
        {
            conclusions.Add("未能找到最优解，可能受到互斥性限制影响");
        }

        return conclusions;
    }

    private string GenerateRecommendation(
        PE2.PromotionEngine.Models.PromotionResult withExclusivity,
        PE2.PromotionEngine.Models.PromotionResult withoutExclusivity)
    {
        var discountDiff = withoutExclusivity.TotalDiscount - withExclusivity.TotalDiscount;
        
        if (Math.Abs(discountDiff) < 0.01m)
        {
            return "互斥性设置对此购物车没有影响";
        }
        else if (discountDiff > 0)
        {
            return $"互斥性限制减少了 {discountDiff:C} 的优惠，但确保了促销规则的正确执行";
        }
        else
        {
            return "互斥性设置优化了促销组合选择";
        }
    }

    private string GetExclusivityDescription(PromotionRuleBase rule)
    {
        if (!rule.CanStackWithOthers)
        {
            return "不可与其他促销叠加";
        }

        return rule.ProductExclusivity switch
        {
            ProductExclusivityLevel.None => "可与任何促销叠加",
            ProductExclusivityLevel.Partial => "只能与同级或更低级促销叠加",
            ProductExclusivityLevel.Exclusive => "完全互斥，不能与其他促销叠加",
            _ => "未知互斥级别"
        };
    }
}
