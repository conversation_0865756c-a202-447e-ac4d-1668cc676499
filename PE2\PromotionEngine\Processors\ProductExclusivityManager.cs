using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Rules;

namespace PE2.PromotionEngine.Processors;

/// <summary>
/// 商品互斥性管理器
/// 负责管理和检查商品级别的促销互斥性
/// </summary>
public class ProductExclusivityManager
{
    /// <summary>
    /// 商品占用状态跟踪
    /// Key: ProductId, Value: 占用该商品的促销规则信息
    /// </summary>
    private readonly Dictionary<string, List<ProductOccupation>> _productOccupations;

    public ProductExclusivityManager()
    {
        _productOccupations = new Dictionary<string, List<ProductOccupation>>();
    }

    /// <summary>
    /// 重置所有商品占用状态
    /// </summary>
    public void Reset()
    {
        _productOccupations.Clear();
    }

    /// <summary>
    /// 检查促销规则是否可以应用到指定商品上
    /// </summary>
    public bool CanApplyToProducts(PromotionRuleBase rule, List<ConsumedItem> consumedItems, out string conflictReason)
    {
        conflictReason = string.Empty;

        foreach (var consumedItem in consumedItems)
        {
            if (!CanApplyToProduct(rule, consumedItem.ProductId, consumedItem.Quantity, out var productConflictReason))
            {
                conflictReason = $"商品 {consumedItem.ProductName} {productConflictReason}";
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 检查促销规则是否可以应用到指定商品上
    /// </summary>
    public bool CanApplyToProduct(PromotionRuleBase rule, string productId, int requiredQuantity, out string conflictReason)
    {
        conflictReason = string.Empty;

        if (!_productOccupations.ContainsKey(productId))
        {
            return true; // 商品未被任何促销占用
        }

        var occupations = _productOccupations[productId];
        var totalOccupiedQuantity = occupations.Sum(o => o.Quantity);

        // 检查数量是否足够
        // 这里需要从购物车中获取商品的实际数量，暂时简化处理
        // 实际实现中应该传入购物车信息

        foreach (var occupation in occupations)
        {
            // 检查规则级别的互斥
            if (rule.ExclusiveRuleIds.Contains(occupation.RuleId))
            {
                conflictReason = $"与促销规则 {occupation.RuleName} 互斥";
                return false;
            }

            // 检查商品级别的互斥
            if (!CanStackPromotions(rule, occupation))
            {
                conflictReason = $"与促销规则 {occupation.RuleName} 在商品级别互斥";
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 占用商品（记录促销规则对商品的使用）
    /// </summary>
    public void OccupyProducts(PromotionRuleBase rule, List<ConsumedItem> consumedItems)
    {
        foreach (var consumedItem in consumedItems)
        {
            OccupyProduct(rule, consumedItem.ProductId, consumedItem.ProductName, consumedItem.Quantity);
        }
    }

    /// <summary>
    /// 占用单个商品
    /// </summary>
    public void OccupyProduct(PromotionRuleBase rule, string productId, string productName, int quantity)
    {
        if (!_productOccupations.ContainsKey(productId))
        {
            _productOccupations[productId] = new List<ProductOccupation>();
        }

        _productOccupations[productId].Add(new ProductOccupation
        {
            RuleId = rule.Id,
            RuleName = rule.Name,
            RuleType = rule.RuleType,
            ProductId = productId,
            ProductName = productName,
            Quantity = quantity,
            ExclusivityLevel = rule.ProductExclusivity,
            CanStackWithOthers = rule.CanStackWithOthers,
            OccupiedAt = DateTime.Now
        });
    }

    /// <summary>
    /// 释放商品占用
    /// </summary>
    public void ReleaseProducts(string ruleId)
    {
        foreach (var productId in _productOccupations.Keys.ToList())
        {
            var occupations = _productOccupations[productId];
            occupations.RemoveAll(o => o.RuleId == ruleId);
            
            if (occupations.Count == 0)
            {
                _productOccupations.Remove(productId);
            }
        }
    }

    /// <summary>
    /// 获取商品的占用状态
    /// </summary>
    public List<ProductOccupation> GetProductOccupations(string productId)
    {
        return _productOccupations.ContainsKey(productId) 
            ? new List<ProductOccupation>(_productOccupations[productId])
            : new List<ProductOccupation>();
    }

    /// <summary>
    /// 获取所有商品的占用状态
    /// </summary>
    public Dictionary<string, List<ProductOccupation>> GetAllOccupations()
    {
        return new Dictionary<string, List<ProductOccupation>>(_productOccupations);
    }

    /// <summary>
    /// 检查两个促销规则是否可以在商品级别叠加
    /// </summary>
    private bool CanStackPromotions(PromotionRuleBase newRule, ProductOccupation existingOccupation)
    {
        // 如果现有占用不允许叠加，则不能应用新规则
        if (!existingOccupation.CanStackWithOthers)
        {
            return false;
        }

        // 如果新规则不允许叠加，则不能应用
        if (!newRule.CanStackWithOthers)
        {
            return false;
        }

        // 根据互斥级别判断
        return existingOccupation.ExclusivityLevel switch
        {
            ProductExclusivityLevel.None => true, // 无限制，可以叠加
            ProductExclusivityLevel.Partial => newRule.ProductExclusivity <= ProductExclusivityLevel.Partial, // 只能与同级或更低级叠加
            ProductExclusivityLevel.Exclusive => false, // 完全互斥，不能叠加
            _ => true
        };
    }

    /// <summary>
    /// 生成互斥性分析报告
    /// </summary>
    public ExclusivityAnalysisReport GenerateAnalysisReport()
    {
        var report = new ExclusivityAnalysisReport
        {
            TotalOccupiedProducts = _productOccupations.Count,
            OccupationDetails = new List<ProductOccupationDetail>()
        };

        foreach (var kvp in _productOccupations)
        {
            var productId = kvp.Key;
            var occupations = kvp.Value;

            var detail = new ProductOccupationDetail
            {
                ProductId = productId,
                ProductName = occupations.FirstOrDefault()?.ProductName ?? productId,
                TotalOccupations = occupations.Count,
                TotalOccupiedQuantity = occupations.Sum(o => o.Quantity),
                Occupations = occupations.ToList(),
                HasConflicts = occupations.Any(o => !o.CanStackWithOthers || o.ExclusivityLevel == ProductExclusivityLevel.Exclusive)
            };

            report.OccupationDetails.Add(detail);
        }

        report.ConflictingProducts = report.OccupationDetails.Count(d => d.HasConflicts);
        
        return report;
    }
}

/// <summary>
/// 商品占用信息
/// </summary>
public class ProductOccupation
{
    /// <summary>
    /// 促销规则ID
    /// </summary>
    public string RuleId { get; set; } = string.Empty;

    /// <summary>
    /// 促销规则名称
    /// </summary>
    public string RuleName { get; set; } = string.Empty;

    /// <summary>
    /// 促销规则类型
    /// </summary>
    public string RuleType { get; set; } = string.Empty;

    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 商品名称
    /// </summary>
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// 占用数量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 互斥级别
    /// </summary>
    public ProductExclusivityLevel ExclusivityLevel { get; set; }

    /// <summary>
    /// 是否可与其他促销叠加
    /// </summary>
    public bool CanStackWithOthers { get; set; }

    /// <summary>
    /// 占用时间
    /// </summary>
    public DateTime OccupiedAt { get; set; }
}

/// <summary>
/// 互斥性分析报告
/// </summary>
public class ExclusivityAnalysisReport
{
    /// <summary>
    /// 被占用的商品总数
    /// </summary>
    public int TotalOccupiedProducts { get; set; }

    /// <summary>
    /// 有冲突的商品数量
    /// </summary>
    public int ConflictingProducts { get; set; }

    /// <summary>
    /// 商品占用详情
    /// </summary>
    public List<ProductOccupationDetail> OccupationDetails { get; set; } = new();
}

/// <summary>
/// 商品占用详情
/// </summary>
public class ProductOccupationDetail
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 商品名称
    /// </summary>
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// 占用次数
    /// </summary>
    public int TotalOccupations { get; set; }

    /// <summary>
    /// 总占用数量
    /// </summary>
    public int TotalOccupiedQuantity { get; set; }

    /// <summary>
    /// 是否有冲突
    /// </summary>
    public bool HasConflicts { get; set; }

    /// <summary>
    /// 占用详情列表
    /// </summary>
    public List<ProductOccupation> Occupations { get; set; } = new();
}
