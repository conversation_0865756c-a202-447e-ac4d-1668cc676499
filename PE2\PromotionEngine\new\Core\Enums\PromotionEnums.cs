using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.new.Core.Enums;

/// <summary>
/// 促销类型
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum PromotionType
{
    /// <summary>
    /// 换购促销
    /// </summary>
    Exchange = 1,
    
    /// <summary>
    /// 买免促销
    /// </summary>
    BuyFree = 2,
    
    /// <summary>
    /// 减现促销
    /// </summary>
    CashDiscount = 3,
    
    /// <summary>
    /// 打折促销
    /// </summary>
    Discount = 4,
    
    /// <summary>
    /// 特价促销
    /// </summary>
    SpecialPrice = 5,
    
    /// <summary>
    /// 买赠促销
    /// </summary>
    BuyGift = 6
}

/// <summary>
/// 占用级别
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum OccupationLevel
{
    /// <summary>
    /// 无占用
    /// </summary>
    None = 0,
    
    /// <summary>
    /// 标准占用
    /// </summary>
    Standard = 1,
    
    /// <summary>
    /// 部分占用
    /// </summary>
    Partial = 2,
    
    /// <summary>
    /// 完全占用
    /// </summary>
    Exclusive = 3
}

/// <summary>
/// 执行策略
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum ExecutionStrategy
{
    /// <summary>
    /// 标准执行
    /// </summary>
    Standard = 0,
    
    /// <summary>
    /// 客户利益最大化
    /// </summary>
    MaxCustomerBenefit = 1,
    
    /// <summary>
    /// 商家利益最大化
    /// </summary>
    MaxMerchantBenefit = 2,
    
    /// <summary>
    /// 平衡策略
    /// </summary>
    Balanced = 3,
    
    /// <summary>
    /// 快速执行（性能优先）
    /// </summary>
    FastExecution = 4
}

/// <summary>
/// 验证阶段
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum ValidationStage
{
    /// <summary>
    /// 基础验证
    /// </summary>
    Basic = 1,
    
    /// <summary>
    /// 条件验证
    /// </summary>
    Condition = 2,
    
    /// <summary>
    /// 占用验证
    /// </summary>
    Occupation = 3,
    
    /// <summary>
    /// 排他性验证
    /// </summary>
    Exclusivity = 4,
    
    /// <summary>
    /// 完整验证
    /// </summary>
    Complete = 5
}

/// <summary>
/// 执行阶段
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum ExecutionStage
{
    /// <summary>
    /// 准备阶段
    /// </summary>
    Preparation = 1,
    
    /// <summary>
    /// 条件验证
    /// </summary>
    ConditionValidation = 2,
    
    /// <summary>
    /// 商品占用
    /// </summary>
    ProductOccupation = 3,
    
    /// <summary>
    /// 促销计算
    /// </summary>
    PromotionCalculation = 4,
    
    /// <summary>
    /// 效果应用
    /// </summary>
    EffectApplication = 5,
    
    /// <summary>
    /// 结果提交
    /// </summary>
    ResultCommit = 6,
    
    /// <summary>
    /// 清理阶段
    /// </summary>
    Cleanup = 7
}

/// <summary>
/// 规则状态
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum RuleStatus
{
    /// <summary>
    /// 草稿
    /// </summary>
    Draft = 0,
    
    /// <summary>
    /// 启用
    /// </summary>
    Enabled = 1,
    
    /// <summary>
    /// 禁用
    /// </summary>
    Disabled = 2,
    
    /// <summary>
    /// 已过期
    /// </summary>
    Expired = 3,
    
    /// <summary>
    /// 已删除
    /// </summary>
    Deleted = 4
}

/// <summary>
/// 计算模式
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum CalculationMode
{
    /// <summary>
    /// 按数量计算
    /// </summary>
    ByQuantity = 1,
    
    /// <summary>
    /// 按金额计算
    /// </summary>
    ByAmount = 2,
    
    /// <summary>
    /// 混合计算
    /// </summary>
    Mixed = 3
}

/// <summary>
/// 赠品策略
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum GiftStrategy
{
    /// <summary>
    /// 按梯度送
    /// </summary>
    ByTier = 1,
    
    /// <summary>
    /// 全部送
    /// </summary>
    AllGifts = 2,
    
    /// <summary>
    /// 最优选择
    /// </summary>
    OptimalSelection = 3
}

/// <summary>
/// 换购策略
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum ExchangeStrategy
{
    /// <summary>
    /// 梯度换购
    /// </summary>
    ByGradient = 1,
    
    /// <summary>
    /// 全部换购
    /// </summary>
    AllExchange = 2,
    
    /// <summary>
    /// 最优换购
    /// </summary>
    OptimalExchange = 3
}

/// <summary>
/// 利益选择策略
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum BenefitSelectionStrategy
{
    /// <summary>
    /// 客户利益最大化
    /// </summary>
    CustomerBenefit = 1,
    
    /// <summary>
    /// 商家利益最大化
    /// </summary>
    MerchantBenefit = 2,
    
    /// <summary>
    /// 平衡策略
    /// </summary>
    Balanced = 3
}
