[{"$type": "UnifiedGift", "id": "UNIFIED_GIFT_001", "name": "购买A商品送B商品", "description": "购买A商品1件，赠送B商品1件", "priority": 20, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "giftSelectionStrategy": "CustomerBenefit", "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1}], "giftConditions": [{"productIds": ["B"], "giftQuantity": 1}]}, {"$type": "UnifiedGift", "id": "UNIFIED_GIFT_002", "name": "同商品买2赠1", "description": "购买A商品2件，赠送A商品1件", "priority": 25, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 3, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "isByAmount": false, "minAmount": 0, "giftSameProduct": true, "giftSelectionStrategy": "CustomerBenefit", "buyConditions": [{"productIds": ["A"], "requiredQuantity": 2}], "giftConditions": [{"productIds": ["A"], "giftQuantity": 1}]}, {"$type": "UnifiedGift", "id": "UNIFIED_GIFT_003", "name": "多选一赠品 - 商家利益最大化", "description": "购买A商品1件，从B、C、D中选择价值最低的赠送", "priority": 20, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "giftSelectionStrategy": "MerchantBenefit", "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1}], "giftConditions": [{"productIds": ["B", "C", "D"], "giftQuantity": 1}]}, {"$type": "UnifiedGift", "id": "UNIFIED_GIFT_004", "name": "按金额送赠品", "description": "购买A商品满1000元，赠送B商品1件", "priority": 15, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "isByAmount": true, "minAmount": 1000, "giftSameProduct": false, "giftSelectionStrategy": "CustomerBenefit", "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1}], "giftConditions": [{"productIds": ["B"], "giftQuantity": 1}]}]