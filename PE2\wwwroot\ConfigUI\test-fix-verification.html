<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组合条件字段修复验证</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        .fix-info {
            background: #f0f9ff;
            border: 1px solid #7dd3fc;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 20px;
        }
        .fix-title {
            font-weight: bold;
            color: #0369a1;
            margin-bottom: 8px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
        }
        .debug-info {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 16px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>🛠️ 组合条件字段修复验证</h1>
            
            <!-- 修复说明 -->
            <div class="fix-info">
                <div class="fix-title">🔧 已修复问题</div>
                <ul>
                    <li>✅ DynamicFormRenderer 中的 getFieldDefaultValue 方法现在正确处理 array-complex 类型</li>
                    <li>✅ array-complex 字段现在初始化为空数组 [] 而不是 null</li>
                    <li>✅ ComplexArrayRenderer 组件已在主页面正确注册</li>
                    <li>✅ 当字段为空数组时，用户可以看到"添加"按钮</li>
                </ul>
            </div>
            
            <!-- 测试步骤 -->
            <div class="test-section">
                <h3>🧪 测试步骤</h3>
                <ol>
                    <li>选择 "CombinationBuyFreeRule" 规则类型</li>
                    <li>获取元数据并渲染表单</li>
                    <li>检查 "combinationConditions" 字段是否显示"添加"按钮</li>
                    <li>点击"添加组合条件"按钮，验证对话框是否正常打开</li>
                    <li>在对话框中填写条件，保存并验证</li>
                </ol>
                
                <el-button @click="runTest" type="primary" style="margin-top: 16px;">
                    🚀 运行测试
                </el-button>
                
                <div class="debug-info" v-if="testResults.length > 0">
                    <div><strong>测试结果:</strong></div>
                    <div v-for="(result, index) in testResults" :key="index" :style="{ color: result.success ? 'green' : 'red' }">
                        {{ result.success ? '✅' : '❌' }} {{ result.message }}
                    </div>
                </div>
            </div>
            
            <!-- 实际测试区域 -->
            <div class="test-section">
                <h3>📝 实际表单测试</h3>
                <el-select v-model="selectedRuleType" placeholder="选择规则类型" style="width: 300px; margin-right: 16px;">
                    <el-option value="CombinationBuyFreeRule" label="组合买赠规则" />
                    <el-option value="CombinationGiftRule" label="组合买赠规则" />
                    <el-option value="UnifiedSpecialPriceExchangeRule" label="统一特价换购规则" />
                </el-select>
                <el-button @click="loadAndRender" type="success">加载并渲染</el-button>
                
                <div v-if="formMetadata" style="margin-top: 20px;">
                    <dynamic-form-renderer
                        :fields="formMetadata.fields"
                        :model-value="formData"
                        @update:model-value="formData = $event"
                        mode="edit"
                    />
                </div>
                
                <div v-if="formData.combinationConditions" class="debug-info" style="margin-top: 20px;">
                    <div><strong>combinationConditions 字段值:</strong></div>
                    <div>类型: {{ Array.isArray(formData.combinationConditions) ? 'Array' : typeof formData.combinationConditions }}</div>
                    <div>长度: {{ formData.combinationConditions?.length || 0 }}</div>
                    <div>内容: {{ JSON.stringify(formData.combinationConditions, null, 2) }}</div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="js/components/ProductSelector.js"></script>
    <script src="js/components/ComplexArrayRenderer.js"></script>
    <script src="js/components/DynamicFormRenderer_template.js"></script>

    <script>
        const { createApp, ref } = Vue;

        createApp({
            components: {
                ProductSelector,
                ComplexArrayRenderer,
                DynamicFormRenderer: DynamicFormRenderer_template
            },
            setup() {
                const selectedRuleType = ref('CombinationBuyFreeRule');
                const formMetadata = ref(null);
                const formData = ref({});
                const testResults = ref([]);

                const runTest = async () => {
                    testResults.value = [];
                    
                    try {
                        // 测试 1: 检查组件是否正确定义
                        testResults.value.push({
                            success: typeof ComplexArrayRenderer !== 'undefined',
                            message: 'ComplexArrayRenderer 组件已定义'
                        });

                        testResults.value.push({
                            success: typeof DynamicFormRenderer_template !== 'undefined',
                            message: 'DynamicFormRenderer 组件已定义'
                        });

                        // 测试 2: 获取元数据
                        const response = await fetch('/api/promotion/metadata/types/CombinationBuyFreeRule');
                        const success = response.ok;
                        testResults.value.push({
                            success: success,
                            message: success ? 'API 元数据获取成功' : 'API 元数据获取失败'
                        });

                        if (success) {
                            const metadata = await response.json();
                            
                            // 测试 3: 检查 combinationConditions 字段
                            const combinationField = metadata.fields?.find(f => f.name === 'combinationConditions');
                            testResults.value.push({
                                success: !!combinationField,
                                message: combinationField ? 'combinationConditions 字段存在' : 'combinationConditions 字段不存在'
                            });

                            if (combinationField) {
                                testResults.value.push({
                                    success: combinationField.type === 'array-complex',
                                    message: `combinationConditions 字段类型: ${combinationField.type}`
                                });

                                testResults.value.push({
                                    success: !!combinationField.metadata,
                                    message: combinationField.metadata ? '字段元数据存在' : '字段元数据缺失'
                                });
                            }
                        }

                    } catch (error) {
                        testResults.value.push({
                            success: false,
                            message: `测试错误: ${error.message}`
                        });
                    }
                };

                const loadAndRender = async () => {
                    if (!selectedRuleType.value) {
                        ElMessage.warning('请先选择规则类型');
                        return;
                    }

                    try {
                        const response = await fetch(`/api/promotion/metadata/types/${selectedRuleType.value}`);
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        
                        formMetadata.value = await response.json();
                        formData.value = {}; // 重置表单数据，触发默认值初始化
                        
                        // 手动验证 combinationConditions 初始化
                        setTimeout(() => {
                            const combinationField = formMetadata.value.fields?.find(f => f.name === 'combinationConditions');
                            if (combinationField) {
                                // 如果初始值不是数组，手动设置为空数组
                                if (!Array.isArray(formData.value.combinationConditions)) {
                                    formData.value.combinationConditions = [];
                                }
                            }
                        }, 100);
                        
                        ElMessage.success('表单渲染成功');
                    } catch (error) {
                        ElMessage.error(`加载失败: ${error.message}`);
                    }
                };

                return {
                    selectedRuleType,
                    formMetadata,
                    formData,
                    testResults,
                    runTest,
                    loadAndRender
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
