﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>促销活动编辑器</title>
    <!-- Vue.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/vue@3.4.27/dist/vue.global.prod.js"></script>
    <!-- Ant Design Vue CSS CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/ant-design-vue@4.2.1/dist/reset.css">
    <!-- Ant Design Vue JS CDN -->
    <script src="https://cdn.jsdelivr.net/npm/ant-design-vue@4.2.1/dist/antd.min.js"></script>
    <!-- Dayjs for Ant Design Vue DatePicker -->
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/dayjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/plugin/customParseFormat.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/plugin/localeData.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/plugin/weekday.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/plugin/isSameOrBefore.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/plugin/isSameOrAfter.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/locale/zh-cn.js"></script>
    <!-- Ant Design Vue Icons -->
    <script src="https://cdn.jsdelivr.net/npm/@ant-design/icons-vue@7.0.1/dist/index.umd.min.js"></script>


    <style>
        body {
            padding: 20px;
            background-color: #f0f2f5;
        }

        #editorApp {
            max-width: 1000px;
            margin: 20px auto;
            padding: 24px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section {
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #1890ff;
            border-bottom: 1px solid #e8e8e8;
            padding-bottom: 10px;
        }

        .action-buttons {
            margin-top: 24px;
            text-align: right;
        }

            .action-buttons .a-button {
                margin-left: 8px;
            }
    </style>
</head>
<body>
    <div id="editorApp">
        <a-config-provider :locale="antLocale">
            <a-steps :current="currentStep.value" type="navigation" @change="handleStepChange">
                <a-step title="基础信息与范围" description="设置活动基本资料和适用范围"></a-step>
                <a-step title="设置促销方案" description="选择并配置具体促销规则"></a-step>
                <a-step title="试算与完成" description="配置细则,预览效果并保存"></a-step>
            </a-steps>

            <div style="margin-top: 32px; padding: 24px; border: 1px solid #e8e8e8; border-radius: 8px;">
                <!-- Step 1 Content: Basic Information & Activity Scope -->
                <div v-show="currentStep.value === 0">
                    <!-- Section 1: Basic Information -->
                    <div class="section">
                        <div class="section-title">基础信息</div>
                        <a-form :model="formState.basicInfo" layout="vertical">
                            <a-row :gutter="16">
                                <a-col :span="12">
                                    <a-form-item label="活动名称" name="activityName" :rules="[{ required: true, message: '请输入活动名称!' }]">
                                        <a-input v-model:value="formState.basicInfo.activityName" placeholder="例如：夏季大促销" />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="12">
                                    <a-form-item label="活动日期" name="activityDates" :rules="[{ required: true, message: '请选择活动日期!' }]">
                                        <a-range-picker v-model:value="formState.basicInfo.activityDates"
                                                        style="width: 100%;"
                                                        show-time
                                                        format="YYYY-MM-DD HH:mm:ss"
                                                        :placeholder="['开始日期时间', '结束日期时间']" />
                                    </a-form-item>
                                </a-col>
                            </a-row>
                            <a-row :gutter="16">
                                <a-col :span="12">
                                    <a-form-item label="活动频次" name="activityFrequency">
                                        <a-select v-model:value="formState.basicInfo.activityFrequency" placeholder="请选择">
                                            <a-select-option value="daily">每天</a-select-option>
                                            <a-select-option value="weekly">每周</a-select-option>
                                            <a-select-option value="monthly">每月</a-select-option>
                                            <a-select-option value="custom">自定义</a-select-option>
                                        </a-select>
                                    </a-form-item>
                                </a-col>
                                <a-col :span="12">
                                    <a-form-item label="预售天数" name="presaleDays">
                                        <a-input-number v-model:value="formState.basicInfo.presaleDays" :min="0" style="width: 100%;" placeholder="例如：7" />
                                    </a-form-item>
                                </a-col>
                            </a-row>
                        </a-form>
                    </div>

                    <!-- Section 2: Activity Scope -->
                    <div class="section">
                        <div class="section-title">活动范围</div>
                        <a-form :model="formState.activityScope" layout="vertical">
                            <a-row :gutter="16">
                                <a-col :span="12">
                                    <a-form-item label="参与门店" name="participatingStores" :rules="[{ required: true, message: '请选择参与门店!' }]">
                                        <a-tree-select v-model:value="formState.activityScope.participatingStores"
                                                       style="width: 100%"
                                                       :tree-data="storeData"
                                                       tree-checkable
                                                       allow-clear
                                                       :show-checked-strategy="TreeSelect.SHOW_PARENT"
                                                       placeholder="请选择参与门店"
                                                       tree-default-expand-all />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="12">
                                    <a-form-item label="是否启用积分" name="enablePointsIntegration">
                                        <a-checkbox v-model:checked="formState.activityScope.enablePoints">是否积分</a-checkbox>
                                    </a-form-item>
                                </a-col>
                            </a-row>
                            <a-row :gutter="16">
                                <a-col :span="12">
                                    <a-form-item label="会员限制" name="memberRestriction">
                                        <a-checkbox v-model:checked="formState.activityScope.membersOnly">仅限会员参与</a-checkbox>
                                    </a-form-item>
                                </a-col>
                                <a-col :span="12" v-if="formState.activityScope.membersOnly">
                                    <a-form-item label="会员等级" name="membershipLevels">
                                        <a-select v-model:value="formState.activityScope.membershipLevels"
                                                  mode="multiple"
                                                  placeholder="请选择适用的会员等级"
                                                  style="width: 100%;"
                                                  allow-clear>
                                            <a-select-option value="bronze">普通会员</a-select-option>
                                            <a-select-option value="silver">白银会员</a-select-option>
                                            <a-select-option value="gold">黄金会员</a-select-option>
                                            <a-select-option value="platinum">铂金会员</a-select-option>
                                        </a-select>
                                    </a-form-item>
                                </a-col>
                            </a-row>
                        </a-form>
                    </div>
                </div>

                <!-- Step 2 Content: Activity Scheme -->
                <div v-show="currentStep.value === 1">
                    <div class="section">
                        <div class="section-title">活动方案选择</div>
                        <a-form :model="formState.activityScheme" layout="vertical">
                            <a-form-item label="选择促销类型" name="promotionType" :rules="[{ required: true, message: '请选择一个促销类型!' }]">
                                <a-radio-group v-model:value="formState.activityScheme.promotionType" button-style="solid">
                                    <a-radio-button value="uniformDiscount">统一折扣</a-radio-button>
                                    <a-radio-button value="incrementalDiscount" disabled>递增折扣</a-radio-button>
                                    <a-radio-button value="cyclicIncrementalDiscount" disabled>循环递增折扣</a-radio-button>
                                    <a-radio-button value="combinationDiscount" disabled>组合折扣</a-radio-button>
                                    <a-radio-button value="multipleDiscount" disabled>多种折扣</a-radio-button>
                                    <a-radio-button value="freeCombinationDiscount" disabled>自由组合折扣</a-radio-button>
                                </a-radio-group>
                            </a-form-item>
                        </a-form>
                    </div>

                    <div class="section" v-if="formState.activityScheme.promotionType === 'uniformDiscount'">
                        <div class="section-title">统一折扣配置</div>
                        <a-form :model="formState.activityScheme.uniformDiscount" layout="vertical">
                            <a-row :gutter="16">
                                <a-col :span="12">
                                    <a-form-item label="条件类型" name="conditionType" :rules="[{ required: true, message: '请选择条件类型!' }]">
                                        <a-radio-group v-model:value="formState.activityScheme.uniformDiscount.conditionType">
                                            <a-radio value="items">满件数</a-radio>
                                            <a-radio value="amount">满金额</a-radio>
                                        </a-radio-group>
                                    </a-form-item>
                                </a-col>
                                <a-col :span="12">
                                    <a-form-item :label="formState.activityScheme.uniformDiscount.conditionType === 'items' ? '满足件数' : '满足金额 (元)'"
                                                 name="conditionValue"
                                                 :rules="[{ required: true, message: '请输入条件值!' }]">
                                        <a-input-number v-model:value="formState.activityScheme.uniformDiscount.conditionValue"
                                                        :min="1"
                                                        style="width: 100%;"
                                                        :placeholder="formState.activityScheme.uniformDiscount.conditionType === 'items' ? '例如：3' : '例如：100'" />
                                    </a-form-item>
                                </a-col>
                            </a-row>
                            <a-row :gutter="16">
                                <a-col :span="12">
                                    <a-form-item label="折扣率 (%)" name="discountRate" :rules="[{ required: true, message: '请输入折扣率!' }]">
                                        <a-input-number v-model:value="formState.activityScheme.uniformDiscount.discountRate"
                                                        :min="1"
                                                        :max="99.99"
                                                        style="width: 100%;"
                                                        placeholder="例如: 80 (表示8折)">
                                            <template #addonAfter>
                                                %
                                            </template>
                                        </a-input-number>
                                    </a-form-item>
                                    <small>提示: 输入75代表7.5折 (即原价的75%)。</small>
                                </a-col>
                                <a-col :span="12">
                                    <a-form-item label="活动商品 (必填)" name="selectedProducts" :rules="[{ required: true, message: '请选择活动商品!', validator: () => formState.activityScheme.uniformDiscount.selectedProducts.length > 0 ? Promise.resolve() : Promise.reject('请选择活动商品!') }]">
                                        <a-button @click="openProductSelectorModal">
                                            <template #icon>
                                                <search-outlined />
                                            </template>
                                            选择商品 ({{ formState.activityScheme.uniformDiscount.selectedProducts.length }} 项已选)
                                        </a-button>
                                    </a-form-item>
                                </a-col>
                            </a-row>
                        </a-form>
                    </div>
                </div>

                <!-- Step 3 Content: Activity Rules & Trial Calculation -->
                <div v-show="currentStep.value === 2">
                    <div class="section">
                        <div class="section-title">活动细则设置</div>
                        <a-form :model="formState.activityRules" layout="vertical">
                            <a-row :gutter="24">
                                <a-col :span="12">
                                    <a-form-item label="与其他促销活动叠加">
                                        <a-checkbox v-model:checked="formState.activityRules.allowWithOtherProductPromotions">
                                            可与其他商品活动同时执行
                                        </a-checkbox>
                                        <br />
                                        <a-checkbox v-model:checked="formState.activityRules.allowWithStoreWidePromotions">
                                            可与全场活动同时执行
                                        </a-checkbox>
                                    </a-form-item>
                                </a-col>
                                <a-col :span="12">
                                    <a-form-item label="优惠方式" :rules="[{ required: true, message: '请选择优惠方式!' }]">
                                        <a-radio-group v-model:value="formState.activityRules.discountApplicationMethod">
                                            <a-radio value="merchantPriority">商家最优</a-radio>
                                            <a-radio value="customerPriority">顾客最优</a-radio>
                                        </a-radio-group>
                                    </a-form-item>
                                </a-col>
                            </a-row>
                            <a-row :gutter="24">
                                <a-col :span="12">
                                    <a-form-item label="VIP 折上折">
                                        <a-checkbox v-model:checked="formState.activityRules.enableVipDoubleDiscount">
                                            允许享受VIP折上折
                                        </a-checkbox>
                                    </a-form-item>
                                </a-col>
                            </a-row>
                            <a-divider>其他配置 (待详细化)</a-divider>
                            <a-row :gutter="24">
                                <a-col :span="8">
                                    <a-form-item label="优惠商品来源">
                                        <a-input v-model:value="formState.activityRules.discountedGoodsSource" placeholder="例如：供应商A承担" />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item label="商品来源类型">
                                        <a-input v-model:value="formState.activityRules.goodsSourceType" placeholder="例如：采购" />
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item label="活动扣点 (%)">
                                        <a-input-number v-model:value="formState.activityRules.activityDeductionRate"
                                                        :min="0"
                                                        :max="100"
                                                        style="width: 100%;"
                                                        placeholder="例如：5">
                                            <template #addonAfter>
                                                %
                                            </template>
                                        </a-input-number>
                                    </a-form-item>
                                </a-col>
                            </a-row>
                        </a-form>
                    </div>

                    <div class="section">
                        <div class="section-title">试算与完成</div>
                        <p>试算功能将在这里集成。</p>
                        <a-button type="dashed" @click="runTrialCalculation">执行试算 (功能待定)</a-button>
                    </div>
                </div>

                <div class="action-buttons">
                    <a-button @click="prevStep" v-if="currentStep.value > 0">上一步</a-button>
                    <a-button type="primary" @click="nextStep" v-if="currentStep.value < 2">下一步</a-button>
                    <a-button type="primary" @click="saveDraft" v-if="currentStep.value === 2" :loading="isSaving.value">
                        保存草稿
                    </a-button>
                    <a-button style="margin-left: 8px" @click="clearForm">清空</a-button>
                    <a-button style="margin-left: 8px">返回列表</a-button>
                </div>
            </div>

            <a-modal v-model:open="isProductSelectorModalVisible" title="选择商品" @ok="handleProductSelectionOk" @cancel="handleProductSelectionCancel" width="800px">
                <p>商品选择列表和筛选器将在这里...</p>
                <p>模拟选择: 点击确定后会添加3个模拟商品。</p>
            </a-modal>

        </a-config-provider>
    </div>

    <script>
        const { createApp, ref, reactive, computed } = Vue;
        const { ConfigProvider, Steps, Input, DatePicker, Select, Checkbox, Radio, Button, Form, Row, Col, TreeSelect, InputNumber, message, Modal, Divider } = antd;
        const { SearchOutlined } = antdIcons;

        dayjs.extend(window.dayjs_plugin_customParseFormat);
        dayjs.extend(window.dayjs_plugin_localeData);
        dayjs.extend(window.dayjs_plugin_weekday);
        dayjs.extend(window.dayjs_plugin_isSameOrBefore);
        dayjs.extend(window.dayjs_plugin_isSameOrAfter);
        dayjs.locale('zh-cn');

        const editorApp = createApp({
            setup() {
                const antLocale = ref(antd.locales.zh_CN);
                const currentStep = ref(0);
                const isSaving = ref(false);

                const initialActivityRulesState = {
                    allowWithOtherProductPromotions: false,
                    allowWithStoreWidePromotions: false,
                    discountApplicationMethod: 'customerPriority',
                    enableVipDoubleDiscount: false,
                    discountedGoodsSource: '',
                    goodsSourceType: '',
                    activityDeductionRate: null,
                };

                const formState = reactive({
                    basicInfo: {
                        activityName: '',
                        activityDates: null,
                        activityFrequency: 'daily',
                        presaleDays: 0,
                    },
                    activityScope: {
                        participatingStores: [],
                        membersOnly: false,
                        membershipLevels: [],
                        enablePoints: false,
                    },
                    activityScheme: {
                        promotionType: null,
                        uniformDiscount: {
                            conditionType: 'items',
                            conditionValue: null,
                            discountRate: null,
                            selectedProducts: [],
                        },
                    },
                    activityRules: { ...initialActivityRulesState },
                });

                const storeData = ref([
                    { title: '所有门店', value: 'all', key: 'all' },
                    {
                        title: '华东区', value: 'east-china', key: 'east-china', children: [
                            { title: '上海总店', value: 'sh-001', key: 'sh-001' },
                            { title: '南京分店', value: 'nj-001', key: 'nj-001' },
                        ]
                    },
                    {
                        title: '华北区', value: 'north-china', key: 'north-china', children: [
                            { title: '北京总店', value: 'bj-001', key: 'bj-001' },
                            { title: '天津分店', value: 'tj-001', key: 'tj-001' },
                        ]
                    },
                ]);

                const isProductSelectorModalVisible = ref(false);
                const openProductSelectorModal = () => {
                    isProductSelectorModalVisible.value = true;
                };
                const handleProductSelectionOk = () => {
                    formState.activityScheme.uniformDiscount.selectedProducts = ['prod1', 'prod2', 'prod3'];
                    isProductSelectorModalVisible.value = false;
                    message.success('模拟选择了3个商品');
                };
                const handleProductSelectionCancel = () => {
                    isProductSelectorModalVisible.value = false;
                };

                const runTrialCalculation = () => {
                    message.info('执行试算功能待实现!');
                };

                const validateStep0 = () => {
                    if (!formState.basicInfo.activityName || !formState.basicInfo.activityDates || formState.activityScope.participatingStores.length === 0) {
                        message.error('基础信息与范围: 请填写活动名称、活动日期和选择参与门店!');
                        return false;
                    }
                    return true;
                };

                const validateStep1 = () => {
                    if (!formState.activityScheme.promotionType) {
                        message.error('活动方案: 请选择一个促销类型!');
                        return false;
                    }
                    if (formState.activityScheme.promotionType === 'uniformDiscount') {
                        const ud = formState.activityScheme.uniformDiscount;
                        if (ud.conditionValue == null || ud.discountRate == null) {
                            message.error('活动方案: 请填写统一折扣的条件值和折扣率!');
                            return false;
                        }
                        if (ud.selectedProducts.length === 0) {
                            message.error('活动方案: 请为统一折扣活动选择商品!');
                            return false;
                        }
                    }
                    return true;
                };

                const validateStep2 = () => {
                    if (!formState.activityRules.discountApplicationMethod) {
                        message.error('活动细则: 请选择优惠方式!');
                        return false;
                    }
                    return true;
                }

                const handleStepChange = (step) => {
                    if (step > currentStep.value) {
                        if (currentStep.value === 0 && !validateStep0()) return;
                        if (currentStep.value === 1 && !validateStep1()) return;
                    }
                    currentStep.value = step;
                };

                const nextStep = () => {
                    if (currentStep.value === 0) {
                        if (!validateStep0()) return;
                    } else if (currentStep.value === 1) {
                        if (!validateStep1()) return;
                    }

                    if (currentStep.value < 2) {
                        currentStep.value++;
                    }
                };

                const prevStep = () => {
                    if (currentStep.value > 0) {
                        currentStep.value--;
                    }
                };

                const saveDraft = async () => {
                    if (!validateStep0() || !validateStep1() || !validateStep2()) {
                        message.error('请确保所有步骤的必填项都已正确填写!');
                        if (!validateStep0()) currentStep.value = 0;
                        else if (!validateStep1()) currentStep.value = 1;
                        else if (!validateStep2()) currentStep.value = 2;
                        return;
                    }

                    isSaving.value = true;

                    const payload = {
                        basicInfo: {
                            ...formState.basicInfo,
                            activityDates: formState.basicInfo.activityDates
                                ? [
                                    formState.basicInfo.activityDates[0]?.toISOString(),
                                    formState.basicInfo.activityDates[1]?.toISOString()
                                ]
                                : null,
                        },
                        activityScope: formState.activityScope,
                        activityScheme: formState.activityScheme,
                        activityRules: formState.activityRules,
                    };

                    console.log('Sending payload to /api/promotionadmin/save:', JSON.stringify(payload, null, 2));

                    try {
                        const response = await fetch('/api/promotionadmin/save', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(payload),
                        });

                        const responseData = await response.json();

                        if (response.ok) {
                            message.success(responseData.message || '促销活动保存成功!');
                        } else {
                            if (response.status === 400 && responseData.errors) {
                                let errorMessages = '保存失败，请检查以下错误：\n';
                                for (const key in responseData.errors) {
                                    if (responseData.errors.hasOwnProperty(key)) {
                                        // Make field names more readable if possible (e.g. BasicInfo.ActivityName -> 活动名称)
                                        // This is a simple version for now.
                                        errorMessages += `${key}: ${responseData.errors[key].join(', ')}\n`;
                                    }
                                }
                                message.error({ content: errorMessages, style: { whiteSpace: 'pre-line' }, duration: 7 });
                            } else if (response.status === 400 && responseData.title && responseData.status) { // ProblemDetails format
                                let errorMessages = `保存失败: ${responseData.title}\n`;
                                if (responseData.errors) {
                                    for (const key in responseData.errors) {
                                        if (responseData.errors.hasOwnProperty(key)) {
                                            errorMessages += `${key}: ${responseData.errors[key].join(', ')}\n`;
                                        }
                                    }
                                }
                                message.error({ content: errorMessages, style: { whiteSpace: 'pre-line' }, duration: 7 });
                            }
                            else {
                                message.error(responseData.message || `保存失败: ${response.status} ${response.statusText}`);
                            }
                        }
                    } catch (error) {
                        console.error('Error saving promotion:', error);
                        message.error('保存促销活动时发生网络或客户端错误。');
                    } finally {
                        isSaving.value = false;
                    }
                };

                const clearForm = () => {
                    formState.basicInfo = { activityName: '', activityDates: null, activityFrequency: 'daily', presaleDays: 0 };
                    formState.activityScope = { participatingStores: [], membersOnly: false, membershipLevels: [], enablePoints: false };
                    formState.activityScheme = {
                        promotionType: null,
                        uniformDiscount: { conditionType: 'items', conditionValue: null, discountRate: null, selectedProducts: [] },
                    };
                    formState.activityRules = { ...initialActivityRulesState };
                    currentStep.value = 0;
                    message.success('表单已清空');
                };

                return {
                    antLocale,
                    currentStep,
                    isSaving,
                    formState,
                    storeData,
                    nextStep,
                    prevStep,
                    clearForm,
                    saveDraft,
                    handleStepChange,
                    runTrialCalculation,
                    dayjs,
                    TreeSelect,
                    isProductSelectorModalVisible,
                    openProductSelectorModal,
                    handleProductSelectionOk,
                    handleProductSelectionCancel,
                };
            },
            components: {
                'a-config-provider': ConfigProvider,
                'a-steps': Steps,
                'a-step': Steps.Step,
                'a-input': Input,
                'a-date-picker': DatePicker,
                'a-range-picker': DatePicker.RangePicker,
                'a-select': Select,
                'a-select-option': Select.Option,
                'a-checkbox': Checkbox,
                'a-radio': Radio,
                'a-radio-group': Radio.Group,
                'a-radio-button': Radio.Button,
                'a-button': Button,
                'a-form': Form,
                'a-form-item': Form.Item,
                'a-row': Row,
                'a-col': Col,
                'a-tree-select': TreeSelect,
                'a-input-number': InputNumber,
                'a-modal': Modal,
                'a-divider': Divider,
                'search-outlined': SearchOutlined,
            }
        });

        editorApp.use(antd);
        editorApp.mount('#editorApp');
    </script>
</body>
</html>
