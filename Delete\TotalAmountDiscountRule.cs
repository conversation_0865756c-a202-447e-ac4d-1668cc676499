using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules;

/// <summary>
/// 订单总额折扣规则（如：满500元打9折）
/// </summary>
[Obsolete("老版本促销，已通用")]
public class TotalAmountDiscountRule : PromotionRuleBase
{
    public override string RuleType => "TotalAmountDiscount";

    /// <summary>
    /// 最小订单金额
    /// </summary>
    public decimal MinOrderAmount { get; set; }

    /// <summary>
    /// 折扣百分比（0.9表示9折）
    /// </summary>
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// 最大折扣金额（0表示无限制）
    /// </summary>
    public decimal MaxDiscountAmount { get; set; } = 0;

    /// <summary>
    /// 排除的商品ID列表（这些商品不参与折扣计算）
    /// </summary>
    public List<string> ExcludedProductIds { get; set; } = new();

    /// <summary>
    /// 排除的商品分类列表
    /// </summary>
    public List<string> ExcludedCategories { get; set; } = new();

    protected override bool CheckConditions(ShoppingCart cart)
    {
        var applicableAmount = GetApplicableAmount(cart);
        return applicableAmount >= MinOrderAmount;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        // 订单总额折扣通常只能应用一次
        return 1;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = 1 // 订单总额折扣只能应用一次
        };

        try
        {
            var applicableItems = GetApplicableItems(cart);
            var totalApplicableAmount = applicableItems.Sum(x => x.AvailableQuantity * x.UnitPrice);

            if (totalApplicableAmount < MinOrderAmount)
            {
                application.IsSuccessful = false;
                application.ErrorMessage = $"订单金额{totalApplicableAmount:C}未达到最低要求{MinOrderAmount:C}";
                return application;
            }

            // 计算折扣金额
            var discountAmount = totalApplicableAmount * (1 - DiscountPercentage);

            // 应用最大折扣限制
            if (MaxDiscountAmount > 0)
            {
                discountAmount = Math.Min(discountAmount, MaxDiscountAmount);
            }

            var consumedItems = new List<ConsumedItem>();

            // 按比例消耗所有适用商品
            foreach (var item in applicableItems)
            {
                if (item.AvailableQuantity > 0)
                {
                    consumedItems.Add(new ConsumedItem
                    {
                        ProductId = item.Product.Id,
                        ProductName = item.Product.Name,
                        Quantity = item.AvailableQuantity,
                        UnitPrice = item.UnitPrice
                    });

                    // 将商品标记为已消耗（用于防止重复应用其他促销）
                    item.IsConsumed = true;
                }
            }

            application.DiscountAmount = discountAmount;
            application.ConsumedItems = consumedItems;
            application.IsSuccessful = discountAmount > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "计算的折扣金额为0";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = $"应用订单总额折扣时发生错误: {ex.Message}";
        }

        return application;
    }

    private decimal GetApplicableAmount(ShoppingCart cart)
    {
        var applicableItems = GetApplicableItems(cart);
        return applicableItems.Sum(x => x.AvailableQuantity * x.UnitPrice);
    }

    private List<CartItem> GetApplicableItems(ShoppingCart cart)
    {
        return cart.Items.Where(x =>
            x.AvailableQuantity > 0 &&
            !ExcludedProductIds.Contains(x.Product.Id) &&
            !ExcludedCategories.Contains(x.Product.Category)
        ).ToList();
    }

    public override PromotionPreview GetPromotionPreview(ShoppingCart cart)
    {
        var applicableAmount = GetApplicableAmount(cart);

        if (applicableAmount < MinOrderAmount)
        {
            return new PromotionPreview
            {
                RuleId = Id,
                RuleName = Name,
                IsApplicable = false,
                Reason = $"订单金额{applicableAmount:C}未达到最低要求{MinOrderAmount:C}"
            };
        }

        var estimatedDiscount = applicableAmount * (1 - DiscountPercentage);
        if (MaxDiscountAmount > 0)
        {
            estimatedDiscount = Math.Min(estimatedDiscount, MaxDiscountAmount);
        }

        return new PromotionPreview
        {
            RuleId = Id,
            RuleName = Name,
            IsApplicable = true,
            MaxApplications = 1,
            EstimatedDiscount = estimatedDiscount,
            Description = $"订单{DiscountPercentage:P0}折扣，预计优惠: {estimatedDiscount:C}"
        };
    }
}
