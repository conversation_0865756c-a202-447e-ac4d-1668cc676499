<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>促销规则配置管理系统 - 增强专业版</title>
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3.3.8/dist/vue.global.js"></script>
    
    <!-- Element Plus UI Framework -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    
    <!-- Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.iife.min.js"></script>
    
    <!-- 自定义样式 -->
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-light: #a5b4fc;
            --primary-dark: #4338ca;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --border-color: #e5e7eb;
            --border-radius: 12px;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }

        .app-container {
            min-height: 100vh;
            padding: 20px;
        }

        .main-content {
            max-width: 1800px;
            margin: 0 auto;
            background: var(--bg-primary);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 24px 32px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .header-title h1 {
            font-size: 28px;
            font-weight: 700;
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        .layout {
            display: grid;
            grid-template-columns: 380px 1fr 420px;
            min-height: calc(100vh - 120px);
        }

        .sidebar {
            background: var(--bg-secondary);
            border-right: 1px solid var(--border-color);
            overflow-y: auto;
        }

        .content-area {
            display: flex;
            flex-direction: column;
        }

        .content-header {
            padding: 24px 32px;
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-primary);
        }

        .content-body {
            flex: 1;
            padding: 32px;
            overflow-y: auto;
        }

        .right-panel {
            background: var(--bg-secondary);
            border-left: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
        }

        .panel-tabs {
            border-bottom: 1px solid var(--border-color);
        }

        .panel-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        /* 模板选择器样式 */
        .template-selector {
            height: 100%;
        }

        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .template-header h3 {
            margin: 0;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .template-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .template-grid {
            display: grid;
            gap: 12px;
            max-height: 400px;
            overflow-y: auto;
        }

        .template-card {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
        }

        .template-card:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-sm);
        }

        .template-card.selected {
            border-color: var(--primary-color);
            background: #f0f9ff;
            box-shadow: var(--shadow-md);
        }

        .template-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .template-info h4 {
            margin: 0 0 4px 0;
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 600;
        }

        .template-info p {
            margin: 0 0 8px 0;
            color: var(--text-secondary);
            font-size: 12px;
            line-height: 1.4;
        }

        .template-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .template-tag {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
        }

        .template-tag.predefined {
            background: #e0f2fe;
            color: #0369a1;
        }

        .template-tag.custom {
            background: #f3e8ff;
            color: #7c3aed;
        }

        .template-date {
            font-size: 10px;
            color: var(--text-secondary);
        }

        .template-actions {
            display: flex;
            gap: 4px;
            margin-top: 8px;
        }

        .template-footer {
            margin-top: 20px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .selected-template-info h4 {
            margin: 0 0 4px 0;
            color: var(--text-primary);
        }

        .selected-template-info p {
            margin: 0 0 12px 0;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .template-footer-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        /* 验证面板样式 */
        .rule-validation-panel {
            height: 100%;
        }

        .validation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .validation-header h3 {
            margin: 0;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .validation-summary {
            margin-bottom: 24px;
        }

        .score-display {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
            padding: 16px;
            background: white;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .score-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            position: relative;
        }

        .score-circle.excellent {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .score-circle.good {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
        }

        .score-circle.fair {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
        }

        .score-circle.poor {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        .score-number {
            font-size: 18px;
            line-height: 1;
        }

        .score-label {
            font-size: 10px;
            line-height: 1;
        }

        .score-text h4 {
            margin: 0 0 4px 0;
            color: var(--text-primary);
        }

        .score-text p {
            margin: 0;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .validation-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 8px;
        }

        .stat-item {
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .stat-item.error {
            background: #fee2e2;
            color: #991b1b;
        }

        .stat-item.warning {
            background: #fef3c7;
            color: #92400e;
        }

        .stat-item.suggestion {
            background: #dbeafe;
            color: #1e40af;
        }

        .stat-item.success {
            background: #d1fae5;
            color: #065f46;
        }

        .validation-details {
            max-height: 400px;
            overflow-y: auto;
        }

        .validation-section {
            margin-bottom: 20px;
        }

        .section-title {
            margin: 0 0 12px 0;
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-title.error {
            color: #dc2626;
        }

        .section-title.warning {
            color: #d97706;
        }

        .section-title.suggestion {
            color: #2563eb;
        }

        .section-title.optimization {
            color: #7c3aed;
        }

        .validation-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .validation-item {
            padding: 10px 12px;
            border-radius: 6px;
            font-size: 13px;
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }

        .validation-item.error {
            background: #fee2e2;
            color: #991b1b;
            border-left: 3px solid #dc2626;
        }

        .validation-item.warning {
            background: #fef3c7;
            color: #92400e;
            border-left: 3px solid #d97706;
        }

        .validation-item.suggestion {
            background: #dbeafe;
            color: #1e40af;
            border-left: 3px solid #2563eb;
        }

        .validation-item.optimization {
            background: #f3e8ff;
            color: #7c3aed;
            border-left: 3px solid #7c3aed;
        }

        .suggestion-content {
            flex: 1;
        }

        .suggestion-message {
            display: block;
            margin-bottom: 4px;
        }

        .suggestion-type {
            font-size: 11px;
            opacity: 0.8;
            font-weight: 500;
        }

        .validation-empty, .validation-loading {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }

        .validation-empty i, .validation-loading i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .validation-help {
            margin-top: 20px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .validation-help h4 {
            margin: 0 0 12px 0;
            color: var(--text-primary);
        }

        .validation-help ul {
            margin: 0 0 16px 20px;
            color: var(--text-secondary);
            font-size: 13px;
        }

        .validation-help li {
            margin-bottom: 4px;
        }

        .score-levels {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        .score-level {
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
        }

        .score-level.excellent {
            background: #d1fae5;
            color: #065f46;
        }

        .score-level.good {
            background: #dbeafe;
            color: #1e40af;
        }

        .score-level.fair {
            background: #fef3c7;
            color: #92400e;
        }

        .score-level.poor {
            background: #fee2e2;
            color: #991b1b;
        }

        /* 响应式适配 */
        @media (max-width: 1400px) {
            .layout {
                grid-template-columns: 320px 1fr 380px;
            }
        }

        @media (max-width: 1200px) {
            .layout {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr;
            }
            
            .sidebar, .right-panel {
                border: none;
                border-top: 1px solid var(--border-color);
                border-bottom: 1px solid var(--border-color);
            }
            
            .right-panel {
                max-height: 600px;
            }
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }

        /* 动画效果 */
        .template-card, .validation-item {
            transition: all 0.2s ease;
        }

        .score-circle {
            transition: all 0.3s ease;
        }

        /* Element Plus 定制样式 */
        .el-tabs__header {
            margin: 0;
            background: var(--bg-secondary);
        }

        .el-tabs__nav-wrap {
            padding: 0 20px;
        }

        .el-button--small {
            font-size: 12px;
            padding: 6px 12px;
        }

        .el-input--small .el-input__inner {
            height: 28px;
            line-height: 28px;
        }

        .import-dialog-content {
            text-align: center;
        }

        .import-tips {
            text-align: left;
            margin-top: 20px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .import-tips h4 {
            margin: 0 0 12px 0;
            color: var(--text-primary);
        }

        .import-tips ul {
            margin: 0 0 0 20px;
            color: var(--text-secondary);
            font-size: 13px;
        }

        .import-tips li {
            margin-bottom: 4px;
        }

        .danger {
            color: var(--danger-color) !important;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <div class="main-content">
                <!-- 顶部导航 -->
                <div class="header">
                    <div class="header-title">
                        <i class="el-icon-setting" style="font-size: 32px;"></i>
                        <h1>促销规则配置管理系统</h1>
                        <span style="font-size: 14px; opacity: 0.9; margin-left: 10px;">增强专业版</span>
                    </div>
                    <div class="header-actions">
                        <el-button type="primary" size="small" @click="saveAsTemplate" :disabled="!hasValidData">
                            <i class="el-icon-collection"></i>
                            保存为模板
                        </el-button>
                        <el-button size="small" @click="exportRule" :disabled="!hasValidData">
                            <i class="el-icon-download"></i>
                            导出规则
                        </el-button>
                        <el-button size="small" @click="importRule">
                            <i class="el-icon-upload"></i>
                            导入规则
                        </el-button>
                        <el-button size="small" @click="clearAll">
                            <i class="el-icon-refresh-left"></i>
                            重置
                        </el-button>
                    </div>
                </div>

                <!-- 主要布局 -->
                <div class="layout">
                    <!-- 左侧边栏：促销类型选择 -->
                    <div class="sidebar">
                        <promotion-type-selector 
                            :selectedType="selectedType"
                            :selectedCategory="selectedCategory"
                            @type-selected="onTypeSelected"
                        />
                    </div>

                    <!-- 中间内容区：表单配置 -->
                    <div class="content-area">
                        <div class="content-header">
                            <h2 v-if="currentTypeData">{{ currentTypeData.name }} - 规则配置</h2>
                            <h2 v-else>请选择促销类型</h2>
                            <p v-if="currentTypeData" style="margin: 8px 0 0 0; color: var(--text-secondary);">
                                {{ currentTypeData.description || '配置该类型的促销规则' }}
                            </p>
                        </div>
                        
                        <div class="content-body">
                            <div v-if="!selectedType" class="validation-empty">
                                <i class="el-icon-s-marketing"></i>
                                <h3>开始配置促销规则</h3>
                                <p>从左侧选择一种促销类型，或从右侧选择模板快速开始</p>
                            </div>
                            
                            <dynamic-form 
                                v-else
                                :fields="currentTypeData.fields"
                                :ruleType="selectedType"
                                v-model="formData"
                                @form-changed="onFormChanged"
                            />
                        </div>
                    </div>

                    <!-- 右侧面板：模板选择和验证 -->
                    <div class="right-panel">
                        <el-tabs v-model="activeTab" class="panel-tabs">
                            <el-tab-pane label="模板库" name="templates">
                                <div class="panel-content">
                                    <template-selector @template-applied="onTemplateApplied" />
                                </div>
                            </el-tab-pane>
                            <el-tab-pane label="规则验证" name="validation">
                                <div class="panel-content">
                                    <rule-validation-panel 
                                        :ruleData="formData"
                                        :autoValidate="true"
                                        @validation-result="onValidationResult"
                                    />
                                </div>
                            </el-tab-pane>
                            <el-tab-pane label="JSON预览" name="preview">
                                <div class="panel-content">
                                    <json-preview 
                                        :data="formData"
                                        :title="selectedType ? currentTypeData.name + ' JSON' : 'JSON预览'"
                                    />
                                </div>
                            </el-tab-pane>
                        </el-tabs>
                    </div>
                </div>
            </div>
        </div>

        <!-- 保存模板对话框 -->
        <el-dialog
            v-model="showSaveTemplateDialog"
            title="保存为模板"
            width="500px"
        >
            <el-form :model="templateForm" label-width="100px">
                <el-form-item label="模板名称" required>
                    <el-input v-model="templateForm.name" placeholder="输入模板名称" />
                </el-form-item>
                <el-form-item label="模板描述">
                    <el-input 
                        v-model="templateForm.description" 
                        type="textarea"
                        rows="3"
                        placeholder="描述该模板的用途和特点"
                    />
                </el-form-item>
                <el-form-item label="模板图标">
                    <el-input v-model="templateForm.icon" placeholder="输入emoji图标，如🎁" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="showSaveTemplateDialog = false">取消</el-button>
                <el-button type="primary" @click="saveTemplate" :disabled="!templateForm.name">保存</el-button>
            </template>
        </el-dialog>

        <!-- 隐藏的文件上传输入框 -->
        <input 
            type="file" 
            ref="fileInput" 
            accept=".json"
            style="display: none"
            @change="onFileSelected"
        />
    </div>

    <!-- 工具类脚本 -->
    <script src="./js/utils/ruleValidator.js"></script>
    <script src="./js/utils/templateManager.js"></script>
    
    <!-- 配置文件 -->
    <script src="./js/config/promotionTypes.js"></script>
    
    <!-- 组件文件 -->
    <script src="./js/components/ProductSelector.js"></script>
    <script src="./js/components/DynamicForm.js"></script>
    <script src="./js/components/PromotionTypeSelector.js"></script>
    <script src="./js/components/JsonPreview.js"></script>
    <script src="./js/components/TemplateSelector.js"></script>    <script src="./js/components/RuleValidationPanel.js"></script>
    <script src="./js/components/PromotionConfigApp.js"></script>
    
    <!-- 应用逻辑 -->
    <script>
        const { createApp } = Vue;

        const app = createApp({
            data() {
                return {
                    selectedType: '',
                    selectedCategory: '',
                    currentTypeData: null,
                    formData: {},
                    activeTab: 'templates',
                    validationResult: null,
                    showSaveTemplateDialog: false,
                    templateForm: {
                        name: '',
                        description: '',
                        icon: '⚙️'
                    }
                };
            },
            
            computed: {
                hasValidData() {
                    return this.formData && 
                           this.formData.$type && 
                           this.formData.id && 
                           this.formData.name;
                }
            },
            
            methods: {
                onTypeSelected(selection) {
                    this.selectedCategory = selection.category;
                    this.selectedType = selection.type;
                    this.currentTypeData = selection.data;
                    
                    // 如果是新选择的类型，创建新的表单数据
                    if (!this.formData.$type || this.formData.$type !== selection.type) {
                        this.createNew();
                    }
                },
                
                onFormChanged(newData) {
                    this.formData = { ...newData };
                },
                
                onTemplateApplied(templateData) {
                    this.formData = { ...templateData };
                    
                    // 根据模板数据自动选择类型
                    if (templateData.$type) {
                        // 找到对应的类型数据
                        const typeInfo = this.findTypeByValue(templateData.$type);
                        if (typeInfo) {
                            this.selectedType = templateData.$type;
                            this.selectedCategory = typeInfo.category;
                            this.currentTypeData = typeInfo.data;
                        }
                    }
                    
                    // 切换到验证面板查看结果
                    this.activeTab = 'validation';
                    
                    this.$message.success('模板已应用');
                },
                
                onValidationResult(result) {
                    this.validationResult = result;
                    
                    // 如果有错误，显示通知
                    if (result.errors.length > 0) {
                        this.$message.warning(`发现 ${result.errors.length} 个错误需要修复`);
                    } else if (result.warnings.length > 0) {
                        this.$message.info(`有 ${result.warnings.length} 个警告建议处理`);
                    }
                },
                
                findTypeByValue(typeValue) {
                    for (const [category, types] of Object.entries(window.PROMOTION_TYPES)) {
                        if (types.types && types.types[typeValue]) {
                            return {
                                category,
                                data: types.types[typeValue]
                            };
                        }
                    }
                    return null;
                },
                
                createNew() {
                    if (!this.currentTypeData) return;
                    
                    // 创建新的表单数据，设置默认值
                    const newData = {
                        $type: this.selectedType,
                        id: this.generateRuleId(),
                        name: '',
                        description: '',
                        priority: 0,
                        isEnabled: true,
                        startTime: '',
                        endTime: '',
                        isRepeatable: true,
                        maxApplications: 0,
                        canStackWithOthers: true,
                        productExclusivity: 'None'
                    };
                    
                    this.formData = newData;
                },
                
                generateRuleId() {
                    const timestamp = Date.now();
                    const random = Math.random().toString(36).substr(2, 6);
                    return `rule_${timestamp}_${random}`;
                },
                
                saveAsTemplate() {
                    if (!this.hasValidData) {
                        this.$message.warning('请先完善规则配置');
                        return;
                    }
                    
                    // 预填充模板信息
                    this.templateForm = {
                        name: this.formData.name + ' - 模板',
                        description: this.formData.description || '',
                        icon: this.getTypeIcon(this.selectedType)
                    };
                    
                    this.showSaveTemplateDialog = true;
                },
                
                saveTemplate() {
                    try {
                        const templateData = {
                            name: this.templateForm.name,
                            description: this.templateForm.description,
                            icon: this.templateForm.icon,
                            template: { ...this.formData }
                        };
                        
                        window.TemplateManager.saveCustomTemplate(templateData);
                        
                        this.showSaveTemplateDialog = false;
                        this.templateForm = { name: '', description: '', icon: '⚙️' };
                        
                        this.$message.success('模板已保存');
                    } catch (error) {
                        this.$message.error('保存模板失败: ' + error.message);
                    }
                },
                
                getTypeIcon(type) {
                    const icons = {
                        'UnifiedGift': '🎁',
                        'BundleOffer': '📦',
                        'VolumeDiscount': '📊',
                        'AmountDiscount': '💰',
                        'PercentageDiscount': '🎯'
                    };
                    return icons[type] || '⚙️';
                },
                
                exportRule() {
                    if (!this.hasValidData) {
                        this.$message.warning('请先完善规则配置');
                        return;
                    }
                    
                    try {
                        const blob = new Blob([JSON.stringify(this.formData, null, 2)], { 
                            type: 'application/json' 
                        });
                        
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `promotion_rule_${this.formData.id}_${Date.now()}.json`;
                        a.click();
                        URL.revokeObjectURL(url);
                        
                        this.$message.success('规则已导出');
                    } catch (error) {
                        this.$message.error('导出失败: ' + error.message);
                    }
                },
                
                importRule() {
                    this.$refs.fileInput.click();
                },
                
                async onFileSelected(event) {
                    const file = event.target.files[0];
                    if (!file) return;
                    
                    try {
                        const text = await file.text();
                        const ruleData = JSON.parse(text);
                        
                        // 验证规则格式
                        if (!ruleData.$type || !ruleData.id) {
                            throw new Error('无效的规则文件格式');
                        }
                        
                        this.formData = ruleData;
                        
                        // 自动选择对应类型
                        const typeInfo = this.findTypeByValue(ruleData.$type);
                        if (typeInfo) {
                            this.selectedType = ruleData.$type;
                            this.selectedCategory = typeInfo.category;
                            this.currentTypeData = typeInfo.data;
                        }
                        
                        // 切换到验证面板
                        this.activeTab = 'validation';
                        
                        this.$message.success('规则已导入');
                    } catch (error) {
                        this.$message.error('导入失败: ' + error.message);
                    }
                    
                    // 清空文件输入框
                    event.target.value = '';
                },
                
                clearAll() {
                    this.$confirm('确定要重置所有配置吗？', '确认重置', {
                        confirmButtonText: '重置',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.selectedType = '';
                        this.selectedCategory = '';
                        this.currentTypeData = null;
                        this.formData = {};
                        this.validationResult = null;
                        this.activeTab = 'templates';
                        
                        this.$message.success('已重置');
                    }).catch(() => {
                        // 用户取消
                    });
                }
            }
        });        // 注册组件
        app.component('PromotionTypeSelector', window.PromotionTypeSelector);
        app.component('DynamicForm', window.DynamicForm);
        app.component('FormField', window.FormField);
        app.component('ArrayField', window.ArrayField);
        app.component('TagsInput', window.TagsInput);
        app.component('ProductSelector', window.ProductSelector);
        app.component('JsonPreview', window.JsonPreview);
        app.component('TemplateSelector', window.TemplateSelector);
        app.component('RuleValidationPanel', window.RuleValidationPanel);

        // 挂载应用
        app.use(ElementPlus);
        app.mount('#app');
    </script>
</body>
</html>
