using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules;

/// <summary>
/// 百分比折扣规则（如：指定商品8折）
/// </summary>
public class PercentageDiscountRule : PromotionRuleBase
{
    public override string RuleType => "PercentageDiscount";

    /// <summary>
    /// 适用的商品ID列表
    /// </summary>
    public List<string> ApplicableProductIds { get; set; } = new();

    /// <summary>
    /// 折扣百分比（0.8表示8折）
    /// </summary>
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// 最小购买数量
    /// </summary>
    public int MinQuantity { get; set; } = 1;

    /// <summary>
    /// 最大优惠数量（0表示无限制）
    /// </summary>
    public int MaxDiscountQuantity { get; set; } = 0;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (ApplicableProductIds.Count == 0)
            return false;

        var totalQuantity = ApplicableProductIds
            .Sum(productId => cart.GetAvailableProductQuantity(productId));

        return totalQuantity >= MinQuantity;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var totalQuantity = ApplicableProductIds
            .Sum(productId => cart.GetAvailableProductQuantity(productId));

        if (!IsRepeatable)
            return 1;

        var maxByQuantity = totalQuantity / MinQuantity;
        
        if (MaxApplications > 0)
            maxByQuantity = Math.Min(maxByQuantity, MaxApplications);

        return maxByQuantity;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var totalDiscountAmount = 0m;
            var totalConsumedQuantity = 0;
            var consumedItems = new List<ConsumedItem>();

            for (int app = 0; app < applicationCount; app++)
            {
                var remainingQuantityNeeded = MinQuantity;
                var appConsumedItems = new List<ConsumedItem>();

                // 按商品ID顺序消耗商品
                foreach (var productId in ApplicableProductIds)
                {
                    if (remainingQuantityNeeded <= 0) break;

                    var availableItems = cart.Items
                        .Where(x => x.Product.Id == productId && x.AvailableQuantity > 0)
                        .ToList();

                    foreach (var item in availableItems)
                    {
                        if (remainingQuantityNeeded <= 0) break;

                        var consumeQuantity = Math.Min(item.AvailableQuantity, remainingQuantityNeeded);
                        
                        // 检查最大优惠数量限制
                        if (MaxDiscountQuantity > 0)
                        {
                            var remainingDiscountQuantity = MaxDiscountQuantity - totalConsumedQuantity;
                            consumeQuantity = Math.Min(consumeQuantity, remainingDiscountQuantity);
                        }

                        if (consumeQuantity > 0)
                        {
                            // 计算折扣金额
                            var discountAmount = consumeQuantity * item.UnitPrice * (1 - DiscountPercentage);
                            totalDiscountAmount += discountAmount;

                            // 记录消耗的商品
                            var existingConsumed = appConsumedItems.FirstOrDefault(x => x.ProductId == productId);
                            if (existingConsumed != null)
                            {
                                existingConsumed.Quantity += consumeQuantity;
                            }
                            else
                            {
                                appConsumedItems.Add(new ConsumedItem
                                {
                                    ProductId = productId,
                                    ProductName = item.Product.Name,
                                    Quantity = consumeQuantity,
                                    UnitPrice = item.UnitPrice
                                });
                            }

                            // 更新购物车状态
                            item.Quantity -= consumeQuantity;
                            totalConsumedQuantity += consumeQuantity;
                            remainingQuantityNeeded -= consumeQuantity;
                        }
                    }
                }

                // 如果这轮应用没有满足最小数量要求，则停止
                if (remainingQuantityNeeded > 0)
                {
                    // 回滚这轮的修改
                    foreach (var consumed in appConsumedItems)
                    {
                        var items = cart.Items.Where(x => x.Product.Id == consumed.ProductId).ToList();
                        var restoreQuantity = consumed.Quantity;
                        
                        foreach (var item in items)
                        {
                            if (restoreQuantity <= 0) break;
                            var restore = Math.Min(restoreQuantity, consumed.Quantity);
                            item.Quantity += restore;
                            restoreQuantity -= restore;
                        }
                    }
                    break;
                }

                consumedItems.AddRange(appConsumedItems);
            }

            // 清理数量为0的商品项
            cart.Items.RemoveAll(x => x.Quantity <= 0);

            application.DiscountAmount = totalDiscountAmount;
            application.ConsumedItems = consumedItems;
            application.IsSuccessful = totalDiscountAmount > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用折扣";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = $"应用促销时发生错误: {ex.Message}";
        }

        return application;
    }
}
