using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules;

/// <summary>
/// 统一送赠品促销规则
/// 满X件或X元，赠送某类商品Z件
/// </summary>
[Obsolete("老版本促销，已通用")]
public class UnifiedGiftRule_OLD : PromotionRuleBase
{
    public override string RuleType => "UnifiedGift";

    /// <summary>
    /// 购买条件列表
    /// </summary>
    public List<BuyCondition> BuyConditions { get; set; } = new();

    /// <summary>
    /// 赠品条件列表
    /// </summary>
    public List<GiftCondition> GiftConditions { get; set; } = new();

    /// <summary>
    /// 是否按金额条件（而非数量条件）
    /// </summary>
    public bool IsByAmount { get; set; } = false;

    /// <summary>
    /// 最小金额要求（当IsByAmount为true时使用）
    /// </summary>
    public decimal MinAmount { get; set; } = 0;

    /// <summary>
    /// 是否赠送相同商品
    /// </summary>
    public bool GiftSameProduct { get; set; } = false;

    /// <summary>
    /// 赠品选择策略
    /// </summary>
    public GiftSelectionStrategy GiftSelectionStrategy { get; set; } = GiftSelectionStrategy.CustomerBenefit;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!BuyConditions.Any() || !GiftConditions.Any())
            return false;

        // 使用择优算法检查是否能满足条件
        var optimalPlan = FindOptimalPromotionPlan(cart);
        return optimalPlan != null && optimalPlan.MaxApplications > 0;
    }

    /// <summary>
    /// 促销计划
    /// </summary>
    private class PromotionPlan
    {
        public int MaxApplications { get; set; }
        public List<ProductAllocation> ConsumedAllocations { get; set; } = new();
        public List<ProductAllocation> GiftAllocations { get; set; } = new();
        public decimal TotalGiftValue { get; set; }
    }

    /// <summary>
    /// 商品分配
    /// </summary>
    private class ProductAllocation
    {
        public string ProductId { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalValue => Quantity * UnitPrice;
    }

    /// <summary>
    /// 找到最优的促销计划
    /// 这是POS系统的核心择优算法，考虑所有可能的商品组合
    /// </summary>
    private PromotionPlan? FindOptimalPromotionPlan(ShoppingCart cart)
    {
        var allProducts = GetAllRelevantProducts(cart);
        if (!allProducts.Any()) return null;

        var bestPlan = new PromotionPlan();
        var maxApplications = IsRepeatable ? (MaxApplications > 0 ? MaxApplications : 10) : 1;

        // 尝试不同的应用次数，找到最优方案
        for (int appCount = 1; appCount <= maxApplications; appCount++)
        {
            var plan = TryCreatePromotionPlan(cart, allProducts, appCount);
            if (plan != null && plan.MaxApplications > bestPlan.MaxApplications)
            {
                bestPlan = plan;
            }
        }

        return bestPlan.MaxApplications > 0 ? bestPlan : null;
    }

    /// <summary>
    /// 获取所有相关的商品（购买条件和赠品条件中的商品）
    /// </summary>
    private List<ProductAllocation> GetAllRelevantProducts(ShoppingCart cart)
    {
        var allProductIds = new HashSet<string>();

        // 添加购买条件中的商品
        foreach (var condition in BuyConditions)
        {
            foreach (var productId in condition.ProductIds)
            {
                allProductIds.Add(productId);
            }
        }

        // 添加赠品条件中的商品
        foreach (var condition in GiftConditions)
        {
            foreach (var productId in condition.ProductIds)
            {
                allProductIds.Add(productId);
            }
        }

        var products = new List<ProductAllocation>();
        foreach (var productId in allProductIds)
        {
            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
            if (cartItem != null && cartItem.AvailableQuantity > 0)
            {
                products.Add(new ProductAllocation
                {
                    ProductId = productId,
                    Quantity = cartItem.AvailableQuantity,
                    UnitPrice = cartItem.UnitPrice
                });
            }
        }

        return products;
    }

    /// <summary>
    /// 尝试创建指定应用次数的促销计划
    /// </summary>
    private PromotionPlan? TryCreatePromotionPlan(ShoppingCart cart, List<ProductAllocation> allProducts, int targetApplications)
    {
        var plan = new PromotionPlan();
        var remainingProducts = allProducts.Select(p => new ProductAllocation
        {
            ProductId = p.ProductId,
            Quantity = p.Quantity,
            UnitPrice = p.UnitPrice
        }).ToList();

        int actualApplications = 0;

        for (int app = 0; app < targetApplications; app++)
        {
            // 尝试满足一次购买条件
            if (!TryConsumeBuyConditions(remainingProducts, plan))
                break;

            // 尝试分配赠品
            if (!TryAllocateGifts(remainingProducts, plan))
                break;

            actualApplications++;
        }

        plan.MaxApplications = actualApplications;
        return plan.MaxApplications > 0 ? plan : null;
    }

    /// <summary>
    /// 尝试消耗购买条件中的商品
    /// </summary>
    private bool TryConsumeBuyConditions(List<ProductAllocation> remainingProducts, PromotionPlan plan)
    {
        foreach (var buyCondition in BuyConditions)
        {
            var requiredQuantity = buyCondition.RequiredQuantity;
            var consumedThisCondition = new List<ProductAllocation>();

            // 按价格排序：商家利益最大化时优先消耗高价商品作为购买条件
            var candidateProducts = remainingProducts
                .Where(p => buyCondition.ProductIds.Contains(p.ProductId) && p.Quantity > 0)
                .OrderBy(p => GiftSelectionStrategy == GiftSelectionStrategy.MerchantBenefit ? -p.UnitPrice : p.UnitPrice)
                .ToList();

            foreach (var product in candidateProducts)
            {
                if (requiredQuantity <= 0) break;

                var consumeQuantity = Math.Min(product.Quantity, requiredQuantity);

                consumedThisCondition.Add(new ProductAllocation
                {
                    ProductId = product.ProductId,
                    Quantity = consumeQuantity,
                    UnitPrice = product.UnitPrice
                });

                product.Quantity -= consumeQuantity;
                requiredQuantity -= consumeQuantity;
            }

            if (requiredQuantity > 0)
                return false; // 无法满足购买条件

            plan.ConsumedAllocations.AddRange(consumedThisCondition);
        }

        return true;
    }

    /// <summary>
    /// 尝试分配赠品
    /// 关键修复：从剩余商品中选择赠品，确保不会选择已被购买条件消耗的商品
    /// </summary>
    private bool TryAllocateGifts(List<ProductAllocation> remainingProducts, PromotionPlan plan)
    {
        foreach (var giftCondition in GiftConditions)
        {
            var requiredGiftQuantity = giftCondition.GiftQuantity;
            var giftAllocations = new List<ProductAllocation>();

            // 关键修复：从剩余商品中选择最优赠品
            var selectedGiftProducts = SelectOptimalGiftProductsFromRemaining(
                giftCondition.ProductIds,
                remainingProducts,
                requiredGiftQuantity);

            foreach (var productId in selectedGiftProducts)
            {
                if (requiredGiftQuantity <= 0) break;

                var product = remainingProducts.FirstOrDefault(p => p.ProductId == productId && p.Quantity > 0);
                if (product == null) continue;

                var giftQuantity = Math.Min(product.Quantity, requiredGiftQuantity);

                giftAllocations.Add(new ProductAllocation
                {
                    ProductId = product.ProductId,
                    Quantity = giftQuantity,
                    UnitPrice = product.UnitPrice
                });

                product.Quantity -= giftQuantity;
                requiredGiftQuantity -= giftQuantity;
                plan.TotalGiftValue += giftQuantity * product.UnitPrice;
            }

            if (requiredGiftQuantity > 0)
                return false; // 无法满足赠品条件

            plan.GiftAllocations.AddRange(giftAllocations);
        }

        return true;
    }

    /// <summary>
    /// 从剩余商品中选择最优的赠品商品
    /// 这是修复后的核心方法：只从未被消耗的剩余商品中选择赠品
    /// </summary>
    private List<string> SelectOptimalGiftProductsFromRemaining(List<string> candidateProductIds, List<ProductAllocation> remainingProducts, int requiredQuantity)
    {
        if (!candidateProductIds.Any())
            return new List<string>();

        // 只考虑剩余商品中的候选商品
        var availableGiftProducts = remainingProducts
            .Where(p => candidateProductIds.Contains(p.ProductId) && p.Quantity > 0)
            .ToList();

        if (!availableGiftProducts.Any())
            return new List<string>();

        // 根据策略排序
        var sortedProducts = GiftSelectionStrategy switch
        {
            GiftSelectionStrategy.CustomerBenefit => availableGiftProducts.OrderByDescending(p => p.UnitPrice),
            GiftSelectionStrategy.MerchantBenefit => availableGiftProducts.OrderBy(p => p.UnitPrice),
            _ => availableGiftProducts.OrderByDescending(p => p.UnitPrice)
        };

        var selectedProducts = new List<string>();
        var remainingQuantity = requiredQuantity;

        foreach (var product in sortedProducts)
        {
            if (remainingQuantity <= 0) break;

            if (product.Quantity > 0)
            {
                selectedProducts.Add(product.ProductId);
                remainingQuantity--;
            }
        }

        return selectedProducts;
    }

    /// <summary>
    /// 检查赠品库存是否充足（已废弃，由择优算法替代）
    /// </summary>
    private bool CheckGiftStock(ShoppingCart cart)
    {
        if (GiftSameProduct)
        {
            // 同商品买赠：需要确保有足够的商品可以赠送
            foreach (var buyCondition in BuyConditions)
            {
                var totalRequired = buyCondition.RequiredQuantity;
                foreach (var giftCondition in GiftConditions)
                {
                    totalRequired += giftCondition.GiftQuantity;
                }

                var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                if (availableQuantity < totalRequired)
                    return false;
            }
        }
        else
        {
            // 不同商品赠品：检查赠品库存
            foreach (var giftCondition in GiftConditions)
            {
                if (giftCondition.ProductIds.Count > 1)
                {
                    // 多选一：只要有一个有库存即可
                    var hasStock = giftCondition.ProductIds.Any(id => cart.GetAvailableProductQuantity(id) > 0);
                    if (!hasStock) return false;
                }
                else
                {
                    // 单一赠品：必须有足够库存
                    var availableQuantity = giftCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                    if (availableQuantity < giftCondition.GiftQuantity)
                        return false;
                }
            }
        }

        return true;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        var optimalPlan = FindOptimalPromotionPlan(cart);
        return optimalPlan?.MaxApplications ?? 0;
    }


    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyUnifiedGiftPromotion(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用统一送赠品促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = $"应用统一送赠品促销时发生错误: {ex.Message}";
        }

        return application;
    }

    /// <summary>
    /// 应用统一送赠品促销
    /// 使用择优算法确保最优的商品分配
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyUnifiedGiftPromotion(ShoppingCart cart, int applicationCount)
    {
        // 使用择优算法获取最优促销计划
        var optimalPlan = FindOptimalPromotionPlan(cart);
        if (optimalPlan == null || optimalPlan.MaxApplications == 0)
        {
            return (0m, new List<ConsumedItem>(), new List<GiftItem>());
        }

        var actualApplications = Math.Min(applicationCount, optimalPlan.MaxApplications);
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var giftItems = new List<GiftItem>();

        // 重新计算指定应用次数的促销计划
        var allProducts = GetAllRelevantProducts(cart);
        var finalPlan = TryCreatePromotionPlan(cart, allProducts, actualApplications);

        if (finalPlan == null)
        {
            return (0m, new List<ConsumedItem>(), new List<GiftItem>());
        }

        // 应用消耗的商品
        foreach (var allocation in finalPlan.ConsumedAllocations)
        {
            var cartItems = cart.Items
                .Where(x => x.Product.Id == allocation.ProductId && x.AvailableQuantity > 0)
                .ToList();

            var remainingQuantity = allocation.Quantity;
            foreach (var cartItem in cartItems)
            {
                if (remainingQuantity <= 0) break;

                var consumeQuantity = Math.Min(cartItem.AvailableQuantity, remainingQuantity);
                cartItem.Quantity -= consumeQuantity;
                remainingQuantity -= consumeQuantity;

                // 记录消耗的商品
                var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == allocation.ProductId);
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += consumeQuantity;
                }
                else
                {
                    consumedItems.Add(new ConsumedItem
                    {
                        ProductId = allocation.ProductId,
                        ProductName = cartItem.Product.Name,
                        Quantity = consumeQuantity,
                        UnitPrice = allocation.UnitPrice
                    });
                }
            }
        }

        // 应用赠品
        foreach (var allocation in finalPlan.GiftAllocations)
        {
            var cartItems = cart.Items
                .Where(x => x.Product.Id == allocation.ProductId && x.AvailableQuantity > 0)
                .ToList();

            var remainingQuantity = allocation.Quantity;
            foreach (var cartItem in cartItems)
            {
                if (remainingQuantity <= 0) break;

                var giftQuantity = Math.Min(cartItem.AvailableQuantity, remainingQuantity);
                cartItem.Quantity -= giftQuantity;
                remainingQuantity -= giftQuantity;

                var giftValue = giftQuantity * allocation.UnitPrice;
                totalDiscountAmount += giftValue;

                // 记录赠品
                var existingGift = giftItems.FirstOrDefault(x => x.ProductId == allocation.ProductId);
                if (existingGift != null)
                {
                    existingGift.Quantity += giftQuantity;
                    existingGift.Value += giftValue;
                }
                else
                {
                    var strategyDescription = GiftSelectionStrategy == GiftSelectionStrategy.CustomerBenefit
                        ? "客户利益最大化选择"
                        : "商家利益最大化选择";

                    giftItems.Add(new GiftItem
                    {
                        ProductId = allocation.ProductId,
                        ProductName = cartItem.Product.Name,
                        Quantity = giftQuantity,
                        Value = giftValue,
                        Description = $"统一赠品 - {strategyDescription}"
                    });
                }
            }
        }

        // 清理数量为0的商品项
        cart.Items.RemoveAll(x => x.Quantity <= 0);

        return (totalDiscountAmount, consumedItems, giftItems);
    }


}
