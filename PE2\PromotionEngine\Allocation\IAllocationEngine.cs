using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Allocation;

/// <summary>
/// 分摊引擎接口 - 优惠金额精确分摊算法
/// </summary>
public interface IAllocationEngine
{
    /// <summary>
    /// 执行优惠分摊
    /// </summary>
    Task<AllocationResult> AllocateDiscountAsync(AllocationRequest request);

    /// <summary>
    /// 批量执行优惠分摊
    /// </summary>
    Task<Dictionary<string, AllocationResult>> AllocateBatchAsync(List<AllocationRequest> requests);

    /// <summary>
    /// 验证分摊结果
    /// </summary>
    AllocationValidationResult ValidateAllocation(AllocationResult result);

    /// <summary>
    /// 处理舍入问题
    /// </summary>
    RoundingResult HandleRounding(AllocationResult result, RoundingStrategy strategy);

    /// <summary>
    /// 计算分摊预览
    /// </summary>
    AllocationPreview CalculateAllocationPreview(AllocationRequest request);

    /// <summary>
    /// 获取支持的分摊策略
    /// </summary>
    List<AllocationStrategy> GetSupportedStrategies();

    /// <summary>
    /// 分析分摊复杂度
    /// </summary>
    AllocationComplexityAnalysis AnalyzeComplexity(AllocationRequest request);
}

/// <summary>
/// 分摊请求
/// </summary>
public class AllocationRequest
{
    /// <summary>
    /// 请求ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 促销应用结果
    /// </summary>
    public AppliedPromotion Promotion { get; set; } = new();

    /// <summary>
    /// 参与分摊的商品
    /// </summary>
    public List<AllocationItem> Items { get; set; } = new();

    /// <summary>
    /// 分摊策略
    /// </summary>
    public AllocationStrategy Strategy { get; set; } = AllocationStrategy.ProportionalByAmount;

    /// <summary>
    /// 舍入策略
    /// </summary>
    public RoundingStrategy RoundingStrategy { get; set; } = RoundingStrategy.RoundToNearestCent;

    /// <summary>
    /// 尾差处理策略
    /// </summary>
    public TailDifferenceStrategy TailStrategy { get; set; } = TailDifferenceStrategy.AllocateToLargestItem;

    /// <summary>
    /// 精度要求（小数位数）
    /// </summary>
    public int Precision { get; set; } = 2;

    /// <summary>
    /// 是否允许负分摊
    /// </summary>
    public bool AllowNegativeAllocation { get; set; } = false;

    /// <summary>
    /// 自定义权重（当策略为CustomWeight时使用）
    /// </summary>
    public Dictionary<string, decimal> CustomWeights { get; set; } = new();
}

/// <summary>
/// 分摊商品
/// </summary>
public class AllocationItem
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 商品名称
    /// </summary>
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// 数量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// 总价
    /// </summary>
    public decimal TotalPrice => Quantity * UnitPrice;

    /// <summary>
    /// 权重（用于自定义权重分摊）
    /// </summary>
    public decimal Weight { get; set; } = 1m;

    /// <summary>
    /// 是否参与分摊
    /// </summary>
    public bool IsEligibleForAllocation { get; set; } = true;

    /// <summary>
    /// 最大可分摊金额
    /// </summary>
    public decimal MaxAllowedDiscount { get; set; }

    /// <summary>
    /// 最小可分摊金额
    /// </summary>
    public decimal MinAllowedDiscount { get; set; } = 0m;
}

/// <summary>
/// 分摊策略
/// </summary>
public enum AllocationStrategy
{
    /// <summary>按金额比例分摊</summary>
    ProportionalByAmount,
    /// <summary>按数量比例分摊</summary>
    ProportionalByQuantity,
    /// <summary>平均分摊</summary>
    EqualDistribution,
    /// <summary>按自定义权重分摊</summary>
    CustomWeight,
    /// <summary>优先分摊给高价商品</summary>
    HighValueFirst,
    /// <summary>优先分摊给低价商品</summary>
    LowValueFirst,
    /// <summary>按商品类别分摊</summary>
    ByCategory,
    /// <summary>按利润率分摊</summary>
    ByProfitMargin
}

/// <summary>
/// 舍入策略
/// </summary>
public enum RoundingStrategy
{
    /// <summary>四舍五入到最近分</summary>
    RoundToNearestCent,
    /// <summary>向下舍入</summary>
    RoundDown,
    /// <summary>向上舍入</summary>
    RoundUp,
    /// <summary>银行家舍入</summary>
    BankersRounding,
    /// <summary>不舍入（保持原精度）</summary>
    NoRounding
}

/// <summary>
/// 尾差处理策略
/// </summary>
public enum TailDifferenceStrategy
{
    /// <summary>分配给最大金额商品</summary>
    AllocateToLargestItem,
    /// <summary>分配给最小金额商品</summary>
    AllocateToSmallestItem,
    /// <summary>分配给第一个商品</summary>
    AllocateToFirstItem,
    /// <summary>分配给最后一个商品</summary>
    AllocateToLastItem,
    /// <summary>随机分配</summary>
    RandomAllocation,
    /// <summary>忽略尾差</summary>
    IgnoreDifference
}

/// <summary>
/// 分摊结果
/// </summary>
public class AllocationResult
{
    /// <summary>
    /// 请求ID
    /// </summary>
    public string RequestId { get; set; } = string.Empty;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// 分摊详情
    /// </summary>
    public List<AllocationDetail> AllocationDetails { get; set; } = new();

    /// <summary>
    /// 总分摊金额
    /// </summary>
    public decimal TotalAllocatedAmount { get; set; }

    /// <summary>
    /// 原始优惠金额
    /// </summary>
    public decimal OriginalDiscountAmount { get; set; }

    /// <summary>
    /// 分摊差异
    /// </summary>
    public decimal AllocationDifference => OriginalDiscountAmount - TotalAllocatedAmount;

    /// <summary>
    /// 舍入调整金额
    /// </summary>
    public decimal RoundingAdjustment { get; set; }

    /// <summary>
    /// 尾差调整金额
    /// </summary>
    public decimal TailDifferenceAdjustment { get; set; }

    /// <summary>
    /// 分摊耗时（毫秒）
    /// </summary>
    public long AllocationTimeMs { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 警告信息
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 分摊统计
    /// </summary>
    public AllocationStatistics Statistics { get; set; } = new();
}

/// <summary>
/// 分摊详情
/// </summary>
public class AllocationDetail
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 商品名称
    /// </summary>
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// 原始金额
    /// </summary>
    public decimal OriginalAmount { get; set; }

    /// <summary>
    /// 分摊金额
    /// </summary>
    public decimal AllocatedAmount { get; set; }

    /// <summary>
    /// 分摊比例
    /// </summary>
    public decimal AllocationRatio { get; set; }

    /// <summary>
    /// 单位分摊金额
    /// </summary>
    public decimal UnitAllocationAmount { get; set; }

    /// <summary>
    /// 舍入前金额
    /// </summary>
    public decimal PreRoundingAmount { get; set; }

    /// <summary>
    /// 舍入调整
    /// </summary>
    public decimal RoundingAdjustment { get; set; }

    /// <summary>
    /// 是否接受尾差调整
    /// </summary>
    public bool ReceivedTailAdjustment { get; set; }

    /// <summary>
    /// 尾差调整金额
    /// </summary>
    public decimal TailAdjustmentAmount { get; set; }

    /// <summary>
    /// 最终单价
    /// </summary>
    public decimal FinalUnitPrice { get; set; }
}

/// <summary>
/// 分摊统计
/// </summary>
public class AllocationStatistics
{
    /// <summary>
    /// 参与分摊的商品数量
    /// </summary>
    public int ParticipatingItemsCount { get; set; }

    /// <summary>
    /// 平均分摊金额
    /// </summary>
    public decimal AverageAllocationAmount { get; set; }

    /// <summary>
    /// 最大分摊金额
    /// </summary>
    public decimal MaxAllocationAmount { get; set; }

    /// <summary>
    /// 最小分摊金额
    /// </summary>
    public decimal MinAllocationAmount { get; set; }

    /// <summary>
    /// 分摊金额标准差
    /// </summary>
    public decimal AllocationStandardDeviation { get; set; }

    /// <summary>
    /// 分摊精度损失
    /// </summary>
    public decimal PrecisionLoss { get; set; }

    /// <summary>
    /// 舍入次数
    /// </summary>
    public int RoundingOperationsCount { get; set; }
}

/// <summary>
/// 分摊验证结果
/// </summary>
public class AllocationValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 验证错误
    /// </summary>
    public List<ValidationError> Errors { get; set; } = new();

    /// <summary>
    /// 验证警告
    /// </summary>
    public List<ValidationWarning> Warnings { get; set; } = new();

    /// <summary>
    /// 金额一致性检查
    /// </summary>
    public bool AmountConsistencyCheck { get; set; }

    /// <summary>
    /// 精度检查
    /// </summary>
    public bool PrecisionCheck { get; set; }

    /// <summary>
    /// 业务规则检查
    /// </summary>
    public bool BusinessRuleCheck { get; set; }
}

/// <summary>
/// 验证错误
/// </summary>
public class ValidationError
{
    /// <summary>
    /// 错误代码
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 相关商品ID
    /// </summary>
    public string? ProductId { get; set; }

    /// <summary>
    /// 严重程度
    /// </summary>
    public ValidationSeverity Severity { get; set; } = ValidationSeverity.Error;
}

/// <summary>
/// 验证警告
/// </summary>
public class ValidationWarning
{
    /// <summary>
    /// 警告代码
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// 警告消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 相关商品ID
    /// </summary>
    public string? ProductId { get; set; }

    /// <summary>
    /// 建议操作
    /// </summary>
    public string Recommendation { get; set; } = string.Empty;
}

/// <summary>
/// 验证严重程度
/// </summary>
public enum ValidationSeverity
{
    Info,
    Warning,
    Error,
    Critical
}

/// <summary>
/// 舍入结果
/// </summary>
public class RoundingResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// 调整后的分摊结果
    /// </summary>
    public AllocationResult AdjustedResult { get; set; } = new();

    /// <summary>
    /// 舍入操作详情
    /// </summary>
    public List<RoundingOperation> RoundingOperations { get; set; } = new();

    /// <summary>
    /// 总舍入调整金额
    /// </summary>
    public decimal TotalRoundingAdjustment { get; set; }
}

/// <summary>
/// 舍入操作
/// </summary>
public class RoundingOperation
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 舍入前金额
    /// </summary>
    public decimal BeforeRounding { get; set; }

    /// <summary>
    /// 舍入后金额
    /// </summary>
    public decimal AfterRounding { get; set; }

    /// <summary>
    /// 舍入调整
    /// </summary>
    public decimal RoundingAdjustment => AfterRounding - BeforeRounding;

    /// <summary>
    /// 舍入方向
    /// </summary>
    public RoundingDirection Direction { get; set; }
}

/// <summary>
/// 舍入方向
/// </summary>
public enum RoundingDirection
{
    Up,
    Down,
    None
}

/// <summary>
/// 分摊预览
/// </summary>
public class AllocationPreview
{
    /// <summary>
    /// 预览分摊详情
    /// </summary>
    public List<AllocationDetail> PreviewDetails { get; set; } = new();

    /// <summary>
    /// 预估总分摊金额
    /// </summary>
    public decimal EstimatedTotalAmount { get; set; }

    /// <summary>
    /// 预估精度损失
    /// </summary>
    public decimal EstimatedPrecisionLoss { get; set; }

    /// <summary>
    /// 潜在问题
    /// </summary>
    public List<string> PotentialIssues { get; set; } = new();

    /// <summary>
    /// 建议策略
    /// </summary>
    public AllocationStrategy RecommendedStrategy { get; set; }
}

/// <summary>
/// 分摊复杂度分析
/// </summary>
public class AllocationComplexityAnalysis
{
    /// <summary>
    /// 复杂度等级
    /// </summary>
    public AllocationComplexity ComplexityLevel { get; set; }

    /// <summary>
    /// 预估处理时间（毫秒）
    /// </summary>
    public long EstimatedProcessingTimeMs { get; set; }

    /// <summary>
    /// 复杂度因子
    /// </summary>
    public Dictionary<string, int> ComplexityFactors { get; set; } = new();

    /// <summary>
    /// 优化建议
    /// </summary>
    public List<string> OptimizationSuggestions { get; set; } = new();
}

/// <summary>
/// 分摊复杂度
/// </summary>
public enum AllocationComplexity
{
    Simple,
    Moderate,
    Complex,
    VeryComplex
}
