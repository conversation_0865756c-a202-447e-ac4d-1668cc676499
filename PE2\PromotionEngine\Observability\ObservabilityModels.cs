global using System.Collections.Concurrent;
global using System.Diagnostics;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Observability;

/// <summary>
/// 计算追踪数据
/// </summary>
public sealed class CalculationTrace
{
    public required string TraceId { get; init; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public long TotalDurationMs { get; set; }
    public required string AlgorithmType { get; init; }
    public required CartSnapshot CartSnapshot { get; init; }
    public PromotionResult? Result { get; set; }
    public Activity? Activity { get; set; }
    public required ConcurrentQueue<CalculationStep> Steps { get; init; }
    public required ConcurrentDictionary<string, object> Metrics { get; init; }
    public bool IsCompleted { get; set; }
}

/// <summary>
/// 计算步骤
/// </summary>
public sealed class CalculationStep
{
    public required string Id { get; init; }
    public required string TraceId { get; init; }
    public required StepType StepType { get; init; }
    public DateTime Timestamp { get; set; }
    public required string Description { get; init; }
    public string? RuleId { get; set; }
    public required Dictionary<string, object> Data { get; init; }
    public long DurationMs { get; set; }
    public bool IsSuccessful { get; set; } = true;
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 步骤类型
/// </summary>
public enum StepType
{
    RuleFiltering,
    ConditionValidation,
    PromotionApplication,
    OptimizationSearch,
    AllocationCalculation,
    ResultValidation,
    InventoryReservation,
    InventoryRelease,
    CacheOperation,
    ErrorHandling
}

/// <summary>
/// 购物车快照
/// </summary>
public sealed class CartSnapshot
{
    public decimal TotalAmount { get; init; }
    public int TotalQuantity { get; init; }
    public int ItemCount { get; init; }
    public required List<CartItemSnapshot> Items { get; init; }
}

/// <summary>
/// 购物车商品快照
/// </summary>
public sealed class CartItemSnapshot
{
    public required string ProductId { get; init; }
    public required string ProductName { get; init; }
    public int Quantity { get; init; }
    public decimal UnitPrice { get; init; }
    public decimal TotalPrice { get; init; }
}

/// <summary>
/// 性能指标
/// </summary>
public sealed class PerformanceMetric
{
    public required string Name { get; init; }
    public double Value { get; set; }
    public DateTime Timestamp { get; set; }
    public required Dictionary<string, string> Tags { get; init; }
}

/// <summary>
/// 促销应用记录
/// </summary>
public sealed class PromotionApplicationRecord
{
    public required string RuleId { get; init; }
    public required string RuleName { get; init; }
    public int ApplicationCount { get; set; }
    public decimal DiscountAmount { get; set; }
    public DateTime AppliedAt { get; set; }
    public required List<string> ConsumedProductIds { get; init; }
    public required List<string> GiftProductIds { get; init; }
}
