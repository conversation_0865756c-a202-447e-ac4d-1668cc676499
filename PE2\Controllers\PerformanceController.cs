using Microsoft.AspNetCore.Mvc;
using PE2.Models;
using PE2.Services;
using System.Diagnostics;

namespace PE2.Controllers;

/// <summary>
/// 性能测试控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class PerformanceController : ControllerBase
{
    private readonly PromotionEngineService _promotionEngineService;
    private readonly ILogger<PerformanceController> _logger;

    public PerformanceController(PromotionEngineService promotionEngineService, ILogger<PerformanceController> logger)
    {
        _promotionEngineService = promotionEngineService;
        _logger = logger;
    }

    /// <summary>
    /// 性能基准测试
    /// </summary>
    [HttpPost("benchmark")]
    public async Task<IActionResult> RunBenchmark([FromQuery] int iterations = 100)
    {
        try
        {
            var results = new List<BenchmarkResult>();
            var scenarios = CreateBenchmarkScenarios();

            foreach (var scenario in scenarios)
            {
                var benchmarkResult = await RunScenarioBenchmark(scenario, iterations);
                results.Add(benchmarkResult);
            }

            var summary = new
            {
                TotalScenarios = results.Count,
                TotalIterations = iterations * results.Count,
                Results = results,
                OverallStatistics = new
                {
                    AverageTime = results.Average(r => r.AverageTimeMs),
                    MinTime = results.Min(r => r.MinTimeMs),
                    MaxTime = results.Max(r => r.MaxTimeMs),
                    TotalTime = results.Sum(r => r.TotalTimeMs)
                }
            };

            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "性能基准测试时发生错误");
            return StatusCode(500, $"性能测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 压力测试
    /// </summary>
    [HttpPost("stress-test")]
    public async Task<IActionResult> RunStressTest([FromQuery] int concurrentRequests = 50, [FromQuery] int duration = 30)
    {
        try
        {
            var cart = CreateComplexCart();
            var tasks = new List<Task<long>>();
            var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(duration));
            var completedRequests = 0;
            var totalTime = 0L;
            var errors = 0;

            // 启动并发请求
            for (int i = 0; i < concurrentRequests; i++)
            {
                tasks.Add(Task.Run(async () =>
                {
                    var requestTime = 0L;
                    while (!cancellationTokenSource.Token.IsCancellationRequested)
                    {
                        try
                        {
                            var stopwatch = Stopwatch.StartNew();
                            await _promotionEngineService.CalculateOptimalPromotionsAsync(cart.Clone());
                            stopwatch.Stop();
                            
                            Interlocked.Add(ref totalTime, stopwatch.ElapsedMilliseconds);
                            Interlocked.Increment(ref completedRequests);
                            requestTime += stopwatch.ElapsedMilliseconds;
                        }
                        catch
                        {
                            Interlocked.Increment(ref errors);
                        }
                    }
                    return requestTime;
                }, cancellationTokenSource.Token));
            }

            await Task.WhenAll(tasks);

            var result = new
            {
                Duration = duration,
                ConcurrentRequests = concurrentRequests,
                CompletedRequests = completedRequests,
                Errors = errors,
                RequestsPerSecond = completedRequests / (double)duration,
                AverageResponseTime = completedRequests > 0 ? totalTime / (double)completedRequests : 0,
                ThroughputMbps = (completedRequests * 1024) / (duration * 1024.0), // 简化计算
                SuccessRate = completedRequests > 0 ? (completedRequests - errors) / (double)completedRequests : 0
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "压力测试时发生错误");
            return StatusCode(500, $"压力测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 算法复杂度分析
    /// </summary>
    [HttpPost("complexity-analysis")]
    public async Task<IActionResult> AnalyzeComplexity()
    {
        try
        {
            var results = new List<ComplexityResult>();
            var itemCounts = new[] { 1, 2, 3, 5, 8, 10, 15, 20 };

            foreach (var itemCount in itemCounts)
            {
                var cart = CreateCartWithItems(itemCount);
                var stopwatch = Stopwatch.StartNew();
                
                var result = await _promotionEngineService.CalculateOptimalPromotionsAsync(cart);
                
                stopwatch.Stop();

                results.Add(new ComplexityResult
                {
                    ItemCount = itemCount,
                    CalculationTimeMs = stopwatch.ElapsedMilliseconds,
                    StepsCount = result.CalculationSteps.Count,
                    AppliedPromotions = result.AppliedPromotions.Count,
                    IgnoredPromotions = result.IgnoredPromotions.Count,
                    IsOptimal = result.IsOptimal
                });
            }

            var analysis = new
            {
                Results = results,
                Trends = new
                {
                    TimeComplexity = AnalyzeTimeComplexity(results),
                    SpaceComplexity = AnalyzeSpaceComplexity(results),
                    ScalabilityFactor = CalculateScalabilityFactor(results)
                }
            };

            return Ok(analysis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "复杂度分析时发生错误");
            return StatusCode(500, $"复杂度分析失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 内存使用分析
    /// </summary>
    [HttpGet("memory-analysis")]
    public async Task<IActionResult> AnalyzeMemoryUsage()
    {
        try
        {
            var beforeGC = GC.GetTotalMemory(false);
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            var afterGC = GC.GetTotalMemory(false);

            var cart = CreateComplexCart();
            var beforeCalculation = GC.GetTotalMemory(false);
            
            var result = await _promotionEngineService.CalculateOptimalPromotionsAsync(cart);
            
            var afterCalculation = GC.GetTotalMemory(false);

            var analysis = new
            {
                MemoryUsage = new
                {
                    BeforeGC = beforeGC,
                    AfterGC = afterGC,
                    BeforeCalculation = beforeCalculation,
                    AfterCalculation = afterCalculation,
                    CalculationMemoryUsed = afterCalculation - beforeCalculation,
                    GCCollections = new
                    {
                        Gen0 = GC.CollectionCount(0),
                        Gen1 = GC.CollectionCount(1),
                        Gen2 = GC.CollectionCount(2)
                    }
                },
                CalculationResult = new
                {
                    CalculationTimeMs = result.CalculationTimeMs,
                    StepsCount = result.CalculationSteps.Count,
                    AppliedPromotions = result.AppliedPromotions.Count
                }
            };

            return Ok(analysis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "内存分析时发生错误");
            return StatusCode(500, $"内存分析失败: {ex.Message}");
        }
    }

    private List<BenchmarkScenario> CreateBenchmarkScenarios()
    {
        return new List<BenchmarkScenario>
        {
            new() { Name = "简单场景", Cart = CreateSimpleCart() },
            new() { Name = "中等复杂度", Cart = CreateMediumCart() },
            new() { Name = "复杂场景", Cart = CreateComplexCart() },
            new() { Name = "高价值订单", Cart = CreateHighValueCart() }
        };
    }

    private async Task<BenchmarkResult> RunScenarioBenchmark(BenchmarkScenario scenario, int iterations)
    {
        var times = new List<long>();
        
        for (int i = 0; i < iterations; i++)
        {
            var stopwatch = Stopwatch.StartNew();
            await _promotionEngineService.CalculateOptimalPromotionsAsync(scenario.Cart.Clone());
            stopwatch.Stop();
            times.Add(stopwatch.ElapsedMilliseconds);
        }

        return new BenchmarkResult
        {
            ScenarioName = scenario.Name,
            Iterations = iterations,
            MinTimeMs = times.Min(),
            MaxTimeMs = times.Max(),
            AverageTimeMs = times.Average(),
            MedianTimeMs = times.OrderBy(t => t).Skip(times.Count / 2).First(),
            TotalTimeMs = times.Sum(),
            StandardDeviation = CalculateStandardDeviation(times)
        };
    }

    private ShoppingCart CreateSimpleCart()
    {
        var cart = new ShoppingCart { Id = "SIMPLE", CustomerId = "TEST" };
        cart.AddItem(new Product { Id = "A", Name = "商品A", Price = 50, Category = "电子产品" }, 1);
        cart.AddItem(new Product { Id = "B", Name = "商品B", Price = 30, Category = "服装" }, 2);
        return cart;
    }

    private ShoppingCart CreateMediumCart()
    {
        var cart = new ShoppingCart { Id = "MEDIUM", CustomerId = "TEST" };
        cart.AddItem(new Product { Id = "A", Name = "商品A", Price = 50, Category = "电子产品" }, 2);
        cart.AddItem(new Product { Id = "B", Name = "商品B", Price = 30, Category = "服装" }, 3);
        cart.AddItem(new Product { Id = "C", Name = "商品C", Price = 20, Category = "家居" }, 4);
        return cart;
    }

    private ShoppingCart CreateComplexCart()
    {
        var cart = new ShoppingCart { Id = "COMPLEX", CustomerId = "TEST" };
        cart.AddItem(new Product { Id = "A", Name = "商品A", Price = 100, Category = "电子产品" }, 3);
        cart.AddItem(new Product { Id = "B", Name = "商品B", Price = 50, Category = "服装" }, 5);
        cart.AddItem(new Product { Id = "C", Name = "商品C", Price = 30, Category = "家居" }, 7);
        cart.AddItem(new Product { Id = "D", Name = "商品D", Price = 80, Category = "服装" }, 2);
        return cart;
    }

    private ShoppingCart CreateHighValueCart()
    {
        var cart = new ShoppingCart { Id = "HIGH_VALUE", CustomerId = "VIP" };
        cart.AddItem(new Product { Id = "A", Name = "高端商品A", Price = 1000, Category = "电子产品" }, 2);
        cart.AddItem(new Product { Id = "B", Name = "高端商品B", Price = 500, Category = "服装" }, 4);
        cart.AddItem(new Product { Id = "C", Name = "高端商品C", Price = 300, Category = "家居" }, 6);
        return cart;
    }

    private ShoppingCart CreateCartWithItems(int itemCount)
    {
        var cart = new ShoppingCart { Id = $"TEST_{itemCount}", CustomerId = "TEST" };
        
        for (int i = 0; i < itemCount; i++)
        {
            cart.AddItem(new Product 
            { 
                Id = $"ITEM_{i}", 
                Name = $"商品{i}", 
                Price = 50 + (i * 10), 
                Category = i % 2 == 0 ? "电子产品" : "服装" 
            }, 1 + (i % 3));
        }
        
        return cart;
    }

    private double CalculateStandardDeviation(List<long> values)
    {
        var average = values.Average();
        var sumOfSquares = values.Sum(v => Math.Pow(v - average, 2));
        return Math.Sqrt(sumOfSquares / values.Count);
    }

    private string AnalyzeTimeComplexity(List<ComplexityResult> results)
    {
        // 简化的复杂度分析
        if (results.Count < 2) return "数据不足";
        
        var ratios = new List<double>();
        for (int i = 1; i < results.Count; i++)
        {
            var timeRatio = (double)results[i].CalculationTimeMs / results[i-1].CalculationTimeMs;
            var sizeRatio = (double)results[i].ItemCount / results[i-1].ItemCount;
            ratios.Add(timeRatio / sizeRatio);
        }
        
        var avgRatio = ratios.Average();
        return avgRatio switch
        {
            < 1.5 => "接近线性 O(n)",
            < 2.5 => "接近对数线性 O(n log n)",
            < 4.0 => "接近平方 O(n²)",
            _ => "可能指数级 O(2^n)"
        };
    }

    private string AnalyzeSpaceComplexity(List<ComplexityResult> results)
    {
        return "线性 O(n) - 基于递归栈深度";
    }

    private double CalculateScalabilityFactor(List<ComplexityResult> results)
    {
        if (results.Count < 2) return 1.0;
        
        var lastResult = results.Last();
        var firstResult = results.First();
        
        return (double)lastResult.CalculationTimeMs / firstResult.CalculationTimeMs;
    }
}

public class BenchmarkScenario
{
    public string Name { get; set; } = string.Empty;
    public ShoppingCart Cart { get; set; } = new();
}

public class BenchmarkResult
{
    public string ScenarioName { get; set; } = string.Empty;
    public int Iterations { get; set; }
    public long MinTimeMs { get; set; }
    public long MaxTimeMs { get; set; }
    public double AverageTimeMs { get; set; }
    public long MedianTimeMs { get; set; }
    public long TotalTimeMs { get; set; }
    public double StandardDeviation { get; set; }
}

public class ComplexityResult
{
    public int ItemCount { get; set; }
    public long CalculationTimeMs { get; set; }
    public int StepsCount { get; set; }
    public int AppliedPromotions { get; set; }
    public int IgnoredPromotions { get; set; }
    public bool IsOptimal { get; set; }
}
