using PE2.Models;

namespace PE2.PromotionEngine.Models;

/// <summary>
/// 促销计算结果
/// </summary>
public class PromotionResult
{
    /// <summary>
    /// 原始购物车
    /// </summary>
    public ShoppingCart OriginalCart { get; set; } = new();

    /// <summary>
    /// 应用促销后的购物车状态
    /// </summary>
    public ShoppingCart ProcessedCart { get; set; } = new();

    /// <summary>
    /// 应用的促销详情列表
    /// </summary>
    public List<AppliedPromotion> AppliedPromotions { get; set; } = new();

    /// <summary>
    /// 被忽略的促销列表（包含忽略原因）
    /// </summary>
    public List<IgnoredPromotion> IgnoredPromotions { get; set; } = new();

    /// <summary>
    /// 计算过程追踪
    /// </summary>
    public List<CalculationStep> CalculationSteps { get; set; } = new();

    /// <summary>
    /// 原始总金额
    /// </summary>
    public decimal OriginalAmount => OriginalCart.TotalAmount;

    /// <summary>
    /// 总优惠金额
    /// </summary>
    public decimal TotalDiscount => AppliedPromotions.Sum(x => x.DiscountAmount);

    /// <summary>
    /// 最终支付金额
    /// </summary>
    public decimal FinalAmount => ProcessedCart?.ActualTotalAmount ?? (OriginalAmount - TotalDiscount);

    /// <summary>
    /// 优惠率
    /// </summary>
    public decimal DiscountRate => OriginalAmount > 0 ? TotalDiscount / OriginalAmount : 0;

    /// <summary>
    /// 计算耗时（毫秒）
    /// </summary>
    public long CalculationTimeMs { get; set; }

    /// <summary>
    /// 是否找到最优解
    /// </summary>
    public bool IsOptimal { get; set; } = true;

    /// <summary>
    /// 算法信息
    /// </summary>
    public string AlgorithmInfo { get; set; } = string.Empty;
}

/// <summary>
/// 应用的促销详情
/// </summary>
public class AppliedPromotion
{
    /// <summary>
    /// 促销规则ID
    /// </summary>
    public string RuleId { get; set; } = string.Empty;

    /// <summary>
    /// 促销规则名称
    /// </summary>
    public string RuleName { get; set; } = string.Empty;

    /// <summary>
    /// 促销类型
    /// </summary>
    public string PromotionType { get; set; } = string.Empty;

    /// <summary>
    /// 优惠金额
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// 消耗的商品列表
    /// </summary>
    public List<ConsumedItem> ConsumedItems { get; set; } = new();

    /// <summary>
    /// 赠送的商品列表
    /// </summary>
    public List<GiftItem> GiftItems { get; set; } = new();

    /// <summary>
    /// 应用次数
    /// </summary>
    public int ApplicationCount { get; set; } = 1;

    /// <summary>
    /// 详细描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 被忽略的促销
/// </summary>
public class IgnoredPromotion
{
    /// <summary>
    /// 促销规则ID
    /// </summary>
    public string RuleId { get; set; } = string.Empty;

    /// <summary>
    /// 促销规则名称
    /// </summary>
    public string RuleName { get; set; } = string.Empty;

    /// <summary>
    /// 忽略原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 忽略类型
    /// </summary>
    public IgnoreReason ReasonType { get; set; }
}

/// <summary>
/// 忽略原因类型
/// </summary>
public enum IgnoreReason
{
    /// <summary>
    /// 条件不满足
    /// </summary>
    ConditionNotMet,

    /// <summary>
    /// 商品数量不足
    /// </summary>
    InsufficientQuantity,

    /// <summary>
    /// 已过期
    /// </summary>
    Expired,

    /// <summary>
    /// 未生效
    /// </summary>
    NotStarted,

    /// <summary>
    /// 与其他促销冲突
    /// </summary>
    Conflict,

    /// <summary>
    /// 非最优选择
    /// </summary>
    NotOptimal,

    /// <summary>
    /// 其他原因
    /// </summary>
    Other
}

/// <summary>
/// 消耗的商品项
/// </summary>
public class ConsumedItem
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 商品名称
    /// </summary>
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// 消耗数量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// 总价
    /// </summary>
    public decimal TotalPrice => Quantity * UnitPrice;
}

/// <summary>
/// 赠品项
/// </summary>
public class GiftItem
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 商品名称
    /// </summary>
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// 赠送数量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 商品价值
    /// </summary>
    public decimal Value { get; set; }

    /// <summary>
    /// 赠品描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 计算步骤
/// </summary>
public class CalculationStep
{
    /// <summary>
    /// 步骤序号
    /// </summary>
    public int StepNumber { get; set; }

    /// <summary>
    /// 步骤描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 步骤类型
    /// </summary>
    public StepType Type { get; set; }

    /// <summary>
    /// 相关数据
    /// </summary>
    public object? Data { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

/// <summary>
/// 计算步骤类型
/// </summary>
public enum StepType
{
    /// <summary>
    /// 开始计算
    /// </summary>
    Start,

    /// <summary>
    /// 规则筛选
    /// </summary>
    RuleFiltering,

    /// <summary>
    /// 条件检查
    /// </summary>
    ConditionCheck,

    /// <summary>
    /// 促销应用
    /// </summary>
    PromotionApplication,

    /// <summary>
    /// 优化搜索
    /// </summary>
    OptimizationSearch,

    /// <summary>
    /// 结果比较
    /// </summary>
    ResultComparison,

    /// <summary>
    /// 互斥性检查
    /// </summary>
    ExclusivityCheck,

    /// <summary>
    /// 互斥冲突
    /// </summary>
    ExclusivityConflict,

    /// <summary>
    /// 促销被排除
    /// </summary>
    PromotionExcluded,

    /// <summary>
    /// 完成计算
    /// </summary>
    Complete
}
