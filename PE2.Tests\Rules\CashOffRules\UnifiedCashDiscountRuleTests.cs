using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.CashOffRules;

/// <summary>
/// 统一减现规则单元测试
/// 测试统一减现规则在各种场景下的正确性
/// </summary>
public class UnifiedCashDiscountRuleTests : TestBase
{
    public UnifiedCashDiscountRuleTests(ITestOutputHelper output) : base(output)
    {
    }

    #region 基础功能测试

    /// <summary>
    /// 测试基于数量的减现规则 - 满足条件
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Basic")]
    public async Task Apply_QuantityBasedRule_ValidCondition_ShouldApplyCorrectly()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateUnifiedCashDiscountRule_QuantityBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_001", "CUSTOMER_001",
            (TestDataGenerator.CreateProductA(), 2) // 2件A商品，满足满1件减10元条件
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "基于数量的减现规则");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(rule.Id, result.AppliedPromotions[0].RuleId);
        Assert.Equal(10.00m, result.TotalDiscount); // 2件商品，每件减10元，共减20元
        Assert.Equal(90.00m, result.FinalAmount); // 原价100元，减20元
    }

    /// <summary>
    /// 测试基于金额的减现规则 - 满足条件
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Basic")]
    public async Task Apply_AmountBasedRule_ValidCondition_ShouldApplyCorrectly()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateUnifiedCashDiscountRule_AmountBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_002", "CUSTOMER_002",
            (TestDataGenerator.CreateProductA(), 3) // 3件A商品，总价150元，满足满100元减20元条件
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "基于金额的减现规则");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(rule.Id, result.AppliedPromotions[0].RuleId);
        Assert.Equal(20.00m, result.TotalDiscount);
        Assert.Equal(130.00m, result.FinalAmount); // 原价150元，减20元
    }

    #endregion

    #region 条件不满足测试

    /// <summary>
    /// 测试数量不足 - 不应用规则
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Insufficient")]
    public async Task Apply_InsufficientQuantity_ShouldNotApply()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateUnifiedCashDiscountRule_QuantityBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_003", "CUSTOMER_003",
            (TestDataGenerator.CreateProductB(), 1) // B商品不在适用范围内
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "数量不足场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0m, result.TotalDiscount);
        Assert.Equal(30.00m, result.FinalAmount); // 原价30元，无折扣
    }

    /// <summary>
    /// 测试金额不足 - 不应用规则
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Insufficient")]
    public async Task Apply_InsufficientAmount_ShouldNotApply()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateUnifiedCashDiscountRule_AmountBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_004", "CUSTOMER_004",
            (TestDataGenerator.CreateProductA(), 1) // 1件A商品，总价50元，不满足满100元条件
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "金额不足场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0m, result.TotalDiscount);
        Assert.Equal(50.00m, result.FinalAmount); // 原价50元，无折扣
    }

    /// <summary>
    /// 测试无适用商品 - 不应用规则
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Insufficient")]
    public async Task Apply_NoApplicableProducts_ShouldNotApply()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateUnifiedCashDiscountRule_QuantityBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_005", "CUSTOMER_005",
            (TestDataGenerator.CreateProductC(), 5) // C商品不在适用范围内
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "无适用商品场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0m, result.TotalDiscount);
        Assert.Equal(100.00m, result.FinalAmount); // 原价100元，无折扣
    }

    #endregion

    #region 重复应用测试

    /// <summary>
    /// 测试可重复应用的规则 - 多次应用
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Multiple")]
    public async Task Apply_RepeatableRule_MultipleApplications_ShouldApplyCorrectly()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateUnifiedCashDiscountRule_Repeatable();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006", "CUSTOMER_006",
            (TestDataGenerator.CreateProductA(), 5) // 5件A商品，可应用5次减现
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "可重复应用规则");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(rule.Id, result.AppliedPromotions[0].RuleId);
        Assert.Equal(50.00m, result.TotalDiscount); // 5件商品，每件减10元，共减50元
        Assert.Equal(200.00m, result.FinalAmount); // 原价250元，减50元
    }

    /// <summary>
    /// 测试不可重复应用的规则 - 只应用一次
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Multiple")]
    public async Task Apply_NonRepeatableRule_SingleApplication_ShouldApplyOnce()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateUnifiedCashDiscountRule_NonRepeatable();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_007", "CUSTOMER_007",
            (TestDataGenerator.CreateProductA(), 5) // 5件A商品，但只能应用一次减现
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "不可重复应用规则");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(rule.Id, result.AppliedPromotions[0].RuleId);
        Assert.Equal(10.00m, result.TotalDiscount); // 只减一次10元
        Assert.Equal(240.00m, result.FinalAmount); // 原价250元，减10元
    }

    #endregion

    #region 边界条件测试

    /// <summary>
    /// 测试空购物车 - 不应用规则
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Boundary")]
    public async Task Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateUnifiedCashDiscountRule_QuantityBased();
        var cart = TestDataGenerator.CreateEmptyCart();

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "空购物车场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0m, result.TotalDiscount);
        Assert.Equal(0m, result.FinalAmount);
    }

    /// <summary>
    /// 测试禁用规则 - 不应用规则
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Boundary")]
    public async Task Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateUnifiedCashDiscountRule_Disabled();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_008", "CUSTOMER_008",
            (TestDataGenerator.CreateProductA(), 2)
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "禁用规则场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0m, result.TotalDiscount);
        Assert.Equal(100.00m, result.FinalAmount);
    }

    #endregion

    #region 复杂场景测试

    /// <summary>
    /// 测试多商品混合场景
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Complex")]
    public async Task Apply_MultipleProducts_ShouldApplyCorrectly()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateUnifiedCashDiscountRule_MultipleProducts();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_009", "CUSTOMER_009",
            (TestDataGenerator.CreateProductA(), 2), // A商品在适用范围内
            (TestDataGenerator.CreateProductB(), 1), // B商品在适用范围内
            (TestDataGenerator.CreateProductC(), 3)  // C商品不在适用范围内
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "多商品混合场景");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(rule.Id, result.AppliedPromotions[0].RuleId);
        Assert.Equal(15.00m, result.TotalDiscount); // A+B商品满3件减15元
        Assert.Equal(175.00m, result.FinalAmount); // 原价190元，减15元
    }

    #endregion

    #region 性能测试

    /// <summary>
    /// 测试大购物车性能
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Performance")]
    public async Task Apply_LargeCart_ShouldPerformWell()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateUnifiedCashDiscountRule_QuantityBased();
        var cart = TestDataGenerator.CreateLargeTestCart(100); // 大量商品的购物车

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        stopwatch.Stop();

        // Assert - 验证结果
        AssertPromotionResult(result, "大购物车性能测试");
        Assert.True(stopwatch.ElapsedMilliseconds < 1000, "处理时间应少于1秒");
        Output.WriteLine($"处理时间: {stopwatch.ElapsedMilliseconds}ms");
    }

    #endregion

    #region 配置验证测试

    /// <summary>
    /// 测试无效配置 - 优雅处理
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Configuration")]
    public async Task Apply_InvalidConfiguration_ShouldHandleGracefully()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateUnifiedCashDiscountRule_InvalidConfig();
        var cart = TestDataGenerator.CreateSimpleTestCart();

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "无效配置场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0m, result.TotalDiscount);
    }

    #endregion
}