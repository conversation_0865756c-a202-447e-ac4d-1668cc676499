global using Microsoft.Extensions.Logging;
global using Microsoft.Extensions.Caching.Memory;
global using Microsoft.Extensions.ObjectPool;
global using System.Collections.Concurrent;
global using System.Diagnostics;
global using System.Runtime;
using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Observability;
using PE2.PromotionEngine.Conditions;
using BenchmarkDotNet.Attributes;
using BenchmarkDotNet.Running;

namespace PE2.PromotionEngine.Performance;

/// <summary>
/// 性能优化器实现 - 智能性能优化引擎
/// 提供促销规则性能分析、优化建议生成、执行顺序优化等功能
/// </summary>
/// <remarks>
/// 建议单元测试：
/// 1. TestPrefilterRulesAsync - 测试规则预筛选功能
/// 2. TestOptimizePromotionCombinationAsync - 测试促销组合优化
/// 3. TestCacheOperations - 测试缓存操作
/// 4. TestComplexityAnalysis - 测试复杂度分析
/// 5. TestPerformanceBenchmark - 测试性能基准测试
/// 6. TestConcurrentOperations - 测试并发操作安全性
/// </remarks>
public sealed class PerformanceOptimizer : IPerformanceOptimizer, IDisposable
{
    private readonly ILogger<PerformanceOptimizer> _logger;
    private readonly IObservabilityEngine _observability;
    private readonly IMemoryCache _memoryCache;
    private readonly IConditionEngine _conditionEngine;
    private readonly ObjectPool<StringBuilder> _stringBuilderPool;
    
    // 性能缓存
    private readonly ConcurrentDictionary<string, OptimizationResult> _optimizationCache;
    private readonly ConcurrentDictionary<string, ComplexityAnalysis> _complexityCache;
    private readonly ConcurrentDictionary<string, PerformanceBenchmark> _benchmarkCache;
    
    // 性能统计
    private readonly ConcurrentDictionary<string, PerformanceMetrics> _performanceMetrics;
    private readonly Timer _cacheCleanupTimer;
    private readonly SemaphoreSlim _optimizationSemaphore;
    
    // 配置参数
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(30);
    private readonly int _maxConcurrentOptimizations = Environment.ProcessorCount * 2;
    private readonly int _complexityThreshold = 1000;
    
    private bool _disposed;

    /// <summary>
    /// 初始化性能优化器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="observability">可观测性引擎</param>
    /// <param name="memoryCache">内存缓存</param>
    /// <param name="conditionEngine">条件引擎</param>
    /// <param name="stringBuilderPool">字符串构建器对象池</param>
    public PerformanceOptimizer(
        ILogger<PerformanceOptimizer> logger,
        IObservabilityEngine observability,
        IMemoryCache memoryCache,
        IConditionEngine conditionEngine,
        ObjectPool<StringBuilder> stringBuilderPool)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _observability = observability ?? throw new ArgumentNullException(nameof(observability));
        _memoryCache = memoryCache ?? throw new ArgumentNullException(nameof(memoryCache));
        _conditionEngine = conditionEngine ?? throw new ArgumentNullException(nameof(conditionEngine));
        _stringBuilderPool = stringBuilderPool ?? throw new ArgumentNullException(nameof(stringBuilderPool));
        
        _optimizationCache = new ConcurrentDictionary<string, OptimizationResult>();
        _complexityCache = new ConcurrentDictionary<string, ComplexityAnalysis>();
        _benchmarkCache = new ConcurrentDictionary<string, PerformanceBenchmark>();
        _performanceMetrics = new ConcurrentDictionary<string, PerformanceMetrics>();
        
        _optimizationSemaphore = new SemaphoreSlim(_maxConcurrentOptimizations, _maxConcurrentOptimizations);
        
        // 启动定时清理任务
        _cacheCleanupTimer = new Timer(CleanupExpiredCache, null, TimeSpan.FromMinutes(10), TimeSpan.FromMinutes(10));
        
        _logger.LogInformation("性能优化器已初始化，最大并发优化数: {MaxConcurrent}", _maxConcurrentOptimizations);
    }

    /// <summary>
    /// 预筛选促销规则
    /// 基于购物车状态和规则条件进行快速筛选，减少后续计算量
    /// </summary>
    /// <param name="allRules">所有促销规则</param>
    /// <param name="cart">购物车</param>
    /// <returns>筛选后的规则列表</returns>
    public async Task<List<PromotionRuleBase>> PrefilterRulesAsync(List<PromotionRuleBase> allRules, ShoppingCart cart)
    {
        if (allRules?.Any() != true)
        {
            _logger.LogWarning("规则列表为空，无需预筛选");
            return new List<PromotionRuleBase>();
        }

        var traceId = _observability.StartCalculationTrace($"PrefilterRules_{cart.Id}");
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            await _optimizationSemaphore.WaitAsync().ConfigureAwait(false);
            
            _observability.TrackRuleFiltering(traceId, "开始规则预筛选", new { 
                TotalRules = allRules.Count, 
                CartItems = cart.Items.Count 
            });

            var filteredRules = new List<PromotionRuleBase>();
            var skippedRules = new List<(string RuleId, string Reason)>();
            
            // 并行预筛选规则
            var filterTasks = allRules.Select(async rule =>
            {
                try
                {
                    // 检查规则是否启用
                    if (!rule.IsEnabled)
                    {
                        return (Rule: rule, Include: false, Reason: "规则未启用");
                    }

                    // 检查时间有效性
                    if (!IsRuleTimeValid(rule))
                    {
                        return (Rule: rule, Include: false, Reason: "规则时间无效");
                    }

                    // 快速条件检查
                    var quickCheck = await QuickConditionCheckAsync(rule, cart).ConfigureAwait(false);
                    if (!quickCheck.CanApply)
                    {
                        return (Rule: rule, Include: false, Reason: quickCheck.Reason);
                    }

                    // 检查库存可用性
                    if (!await CheckInventoryAvailabilityAsync(rule, cart).ConfigureAwait(false))
                    {
                        return (Rule: rule, Include: false, Reason: "库存不足");
                    }

                    return (Rule: rule, Include: true, Reason: "通过预筛选");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "规则预筛选失败: {RuleId}", rule.Id);
                    return (Rule: rule, Include: false, Reason: $"筛选异常: {ex.Message}");
                }
            });

            var filterResults = await Task.WhenAll(filterTasks).ConfigureAwait(false);
            
            foreach (var result in filterResults)
            {
                if (result.Include)
                {
                    filteredRules.Add(result.Rule);
                }
                else
                {
                    skippedRules.Add((result.Rule.Id, result.Reason));
                }
            }

            // 按优先级排序
            filteredRules = filteredRules
                .OrderByDescending(r => r.Priority)
                .ThenBy(r => r.ExecutionOrder)
                .ToList();

            stopwatch.Stop();
            
            _observability.TrackRuleFiltering(traceId, "规则预筛选完成", new {
                FilteredCount = filteredRules.Count,
                SkippedCount = skippedRules.Count,
                ElapsedMs = stopwatch.ElapsedMilliseconds
            });

            // 记录性能指标
            RecordPerformanceMetric("PrefilterRules", stopwatch.ElapsedMilliseconds, allRules.Count);

            _logger.LogInformation("规则预筛选完成: 原始{Original}个，筛选后{Filtered}个，耗时{ElapsedMs}ms", 
                allRules.Count, filteredRules.Count, stopwatch.ElapsedMilliseconds);

            return filteredRules;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "规则预筛选过程发生异常");
            throw;
        }
        finally
        {
            _optimizationSemaphore.Release();
            _observability.EndCalculationTrace(traceId);
        }
    }

    /// <summary>
    /// 优化促销组合算法
    /// 使用智能算法找到最优的促销规则组合
    /// </summary>
    /// <param name="rules">候选规则列表</param>
    /// <param name="cart">购物车</param>
    /// <returns>优化结果</returns>
    public async Task<OptimizationResult> OptimizePromotionCombinationAsync(List<PromotionRuleBase> rules, ShoppingCart cart)
    {
        if (rules?.Any() != true)
        {
            return new OptimizationResult
            {
                OptimizedRules = new List<PromotionRuleBase>(),
                EstimatedCalculationTimeMs = 0,
                OptimizationStrategies = new List<string> { "无规则需要优化" },
                PerformanceImprovement = 0
            };
        }

        var cacheKey = GenerateCacheKey(cart, rules.Select(r => r.Id).ToList());
        
        // 尝试从缓存获取
        if (_optimizationCache.TryGetValue(cacheKey, out var cachedResult))
        {
            _logger.LogDebug("从缓存获取优化结果: {CacheKey}", cacheKey);
            return cachedResult;
        }

        var traceId = _observability.StartCalculationTrace($"OptimizePromotionCombination_{cart.Id}");
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            await _optimizationSemaphore.WaitAsync().ConfigureAwait(false);
            
            _observability.TrackCalculationStep(traceId, "开始促销组合优化", new {
                RuleCount = rules.Count,
                CartValue = cart.TotalAmount
            });

            // 分析规则复杂度
            var complexityAnalysis = AnalyzeComplexity(rules, cart);
            
            // 选择优化策略
            var optimizationStrategy = SelectOptimizationStrategy(complexityAnalysis, rules.Count);
            
            // 执行优化算法
            var optimizedRules = await ExecuteOptimizationAlgorithmAsync(rules, cart, optimizationStrategy).ConfigureAwait(false);
            
            // 计算性能提升
            var performanceImprovement = CalculatePerformanceImprovement(rules, optimizedRules, complexityAnalysis);
            
            var result = new OptimizationResult
            {
                OptimizedRules = optimizedRules,
                EstimatedCalculationTimeMs = complexityAnalysis.EstimatedTimeMs,
                OptimizationStrategies = new List<string> { optimizationStrategy.ToString() },
                PerformanceImprovement = performanceImprovement
            };

            stopwatch.Stop();
            
            // 缓存结果
            _optimizationCache.TryAdd(cacheKey, result);
            
            // 记录性能指标
            RecordPerformanceMetric("OptimizePromotionCombination", stopwatch.ElapsedMilliseconds, rules.Count);
            
            _observability.TrackCalculationStep(traceId, "促销组合优化完成", new {
                OptimizedRuleCount = optimizedRules.Count,
                PerformanceImprovement = performanceImprovement,
                ElapsedMs = stopwatch.ElapsedMilliseconds
            });

            _logger.LogInformation("促销组合优化完成: 优化{Optimized}个规则，性能提升{Improvement:F2}%，耗时{ElapsedMs}ms",
                optimizedRules.Count, performanceImprovement, stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "促销组合优化过程发生异常");
            throw;
        }
        finally
        {
            _optimizationSemaphore.Release();
            _observability.EndCalculationTrace(traceId);
        }
    }

    /// <summary>
    /// 缓存促销计算结果
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <param name="result">计算结果</param>
    /// <param name="expiry">过期时间</param>
    /// <returns>是否缓存成功</returns>
    public async Task<bool> CacheCalculationResultAsync(string cacheKey, PromotionResult result, TimeSpan expiry)
    {
        if (string.IsNullOrEmpty(cacheKey) || result == null)
        {
            return false;
        }

        try
        {
            await Task.Run(() =>
            {
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expiry,
                    Priority = CacheItemPriority.Normal,
                    Size = EstimateCacheSize(result)
                };

                _memoryCache.Set(cacheKey, result, cacheOptions);
            }).ConfigureAwait(false);

            _logger.LogDebug("缓存计算结果成功: {CacheKey}, 过期时间: {Expiry}", cacheKey, expiry);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "缓存计算结果失败: {CacheKey}", cacheKey);
            return false;
        }
    }

    /// <summary>
    /// 获取缓存的计算结果
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <returns>缓存的结果，如果不存在则返回null</returns>
    public async Task<PromotionResult?> GetCachedResultAsync(string cacheKey)
    {
        if (string.IsNullOrEmpty(cacheKey))
        {
            return null;
        }

        try
        {
            return await Task.Run(() =>
            {
                if (_memoryCache.TryGetValue(cacheKey, out var cachedResult) && cachedResult is PromotionResult result)
                {
                    _logger.LogDebug("缓存命中: {CacheKey}", cacheKey);
                    return result;
                }

                _logger.LogDebug("缓存未命中: {CacheKey}", cacheKey);
                return null;
            }).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取缓存结果失败: {CacheKey}", cacheKey);
            return null;
        }
    }

    /// <summary>
    /// 生成缓存键
    /// </summary>
    /// <param name="cart">购物车</param>
    /// <param name="ruleIds">规则ID列表</param>
    /// <returns>缓存键</returns>
    public string GenerateCacheKey(ShoppingCart cart, List<string> ruleIds)
    {
        var sb = _stringBuilderPool.Get();
        try
        {
            sb.Clear();
            sb.Append("PromotionCache_");
            sb.Append(cart.Id);
            sb.Append("_");
            sb.Append(cart.GetHashCode());
            sb.Append("_");
            
            if (ruleIds?.Any() == true)
            {
                sb.Append(string.Join(",", ruleIds.OrderBy(x => x)));
            }
            
            return sb.ToString();
        }
        finally
        {
            _stringBuilderPool.Return(sb);
        }
    }

    /// <summary>
    /// 分析计算复杂度
    /// </summary>
    /// <param name="rules">规则列表</param>
    /// <param name="cart">购物车</param>
    /// <returns>复杂度分析结果</returns>
    public ComplexityAnalysis AnalyzeComplexity(List<PromotionRuleBase> rules, ShoppingCart cart)
    {
        var cacheKey = $"Complexity_{string.Join(",", rules.Select(r => r.Id).OrderBy(x => x))}_{cart.GetHashCode()}";

        if (_complexityCache.TryGetValue(cacheKey, out var cachedAnalysis))
        {
            return cachedAnalysis;
        }

        var analysis = new ComplexityAnalysis();
        var complexityFactors = new Dictionary<string, int>();

        // 分析时间复杂度
        var timeComplexity = AnalyzeTimeComplexity(rules, cart, complexityFactors);
        analysis.TimeComplexity = timeComplexity;

        // 分析空间复杂度
        var spaceComplexity = AnalyzeSpaceComplexity(rules, cart, complexityFactors);
        analysis.SpaceComplexity = spaceComplexity;

        // 计算预估时间和内存
        analysis.EstimatedTimeMs = EstimateCalculationTime(rules, cart, timeComplexity);
        analysis.EstimatedMemoryMB = EstimateMemoryUsage(rules, cart, spaceComplexity);

        analysis.ComplexityFactors = complexityFactors;
        analysis.OptimizationRecommendations = GenerateComplexityOptimizationRecommendations(analysis);

        // 缓存分析结果
        _complexityCache.TryAdd(cacheKey, analysis);

        return analysis;
    }

    /// <summary>
    /// 获取性能基准
    /// </summary>
    /// <returns>性能基准数据</returns>
    public PerformanceBenchmark GetPerformanceBenchmark()
    {
        var cacheKey = "PerformanceBenchmark_Current";

        if (_benchmarkCache.TryGetValue(cacheKey, out var cachedBenchmark))
        {
            return cachedBenchmark;
        }

        var benchmark = new PerformanceBenchmark
        {
            BenchmarkTime = DateTime.Now,
            SystemInfo = GetSystemInfo(),
            Scenarios = GenerateBenchmarkScenarios()
        };

        _benchmarkCache.TryAdd(cacheKey, benchmark);

        return benchmark;
    }

    /// <summary>
    /// 执行性能测试
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <returns>测试结果</returns>
    public async Task<PerformanceTestResult> RunPerformanceTestAsync(PerformanceTestConfig config)
    {
        var result = new PerformanceTestResult
        {
            StartTime = DateTime.Now
        };

        var traceId = _observability.StartCalculationTrace("PerformanceTest");

        try
        {
            _observability.TrackCalculationStep(traceId, "开始性能测试", new {
                ScenarioCount = config.Scenarios.Count,
                TestIterations = config.TestIterations
            });

            var scenarioResults = new Dictionary<string, ScenarioTestResult>();

            foreach (var scenario in config.Scenarios)
            {
                var scenarioResult = await RunScenarioTestAsync(scenario, config).ConfigureAwait(false);
                scenarioResults[scenario.Name] = scenarioResult;
            }

            result.ScenarioResults = scenarioResults;
            result.EndTime = DateTime.Now;
            result.TotalTestTime = result.EndTime - result.StartTime;
            result.OverallStats = CalculateOverallStatistics(scenarioResults);

            _observability.TrackCalculationStep(traceId, "性能测试完成", new {
                TotalTime = result.TotalTestTime.TotalMilliseconds,
                OverallSuccessRate = result.OverallStats.OverallSuccessRate
            });

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "性能测试执行失败");
            throw;
        }
        finally
        {
            _observability.EndCalculationTrace(traceId);
        }
    }

    #region 私有辅助方法

    /// <summary>
    /// 检查规则时间有效性
    /// </summary>
    private static bool IsRuleTimeValid(PromotionRuleBase rule)
    {
        var now = DateTime.Now;
        return now >= rule.StartTime && now <= rule.EndTime;
    }

    /// <summary>
    /// 快速条件检查
    /// </summary>
    private async Task<(bool CanApply, string Reason)> QuickConditionCheckAsync(PromotionRuleBase rule, ShoppingCart cart)
    {
        try
        {
            // 检查基本条件
            if (rule.MinOrderAmount > 0 && cart.TotalAmount < rule.MinOrderAmount)
            {
                return (false, $"订单金额不足，需要{rule.MinOrderAmount:C}");
            }

            if (rule.MinQuantity > 0 && cart.TotalQuantity < rule.MinQuantity)
            {
                return (false, $"商品数量不足，需要{rule.MinQuantity}件");
            }

            // 检查会员等级
            if (!string.IsNullOrEmpty(rule.RequiredMemberLevel) &&
                !string.Equals(cart.MemberLevel, rule.RequiredMemberLevel, StringComparison.OrdinalIgnoreCase))
            {
                return (false, $"会员等级不符，需要{rule.RequiredMemberLevel}");
            }

            return (true, "通过快速检查");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "快速条件检查失败: {RuleId}", rule.Id);
            return (false, $"检查异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 检查库存可用性
    /// </summary>
    private async Task<bool> CheckInventoryAvailabilityAsync(PromotionRuleBase rule, ShoppingCart cart)
    {
        try
        {
            // 这里应该调用库存管理器检查库存
            // 简化实现，假设库存充足
            await Task.Delay(1).ConfigureAwait(false);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "库存检查失败: {RuleId}", rule.Id);
            return false;
        }
    }

    /// <summary>
    /// 选择优化策略
    /// </summary>
    private OptimizationStrategy SelectOptimizationStrategy(ComplexityAnalysis analysis, int ruleCount)
    {
        if (analysis.TimeComplexity >= ComplexityLevel.Quadratic || ruleCount > 50)
        {
            return OptimizationStrategy.Aggressive;
        }

        if (analysis.TimeComplexity >= ComplexityLevel.LinearLogarithmic || ruleCount > 20)
        {
            return OptimizationStrategy.Moderate;
        }

        return OptimizationStrategy.Conservative;
    }

    /// <summary>
    /// 执行优化算法
    /// </summary>
    private async Task<List<PromotionRuleBase>> ExecuteOptimizationAlgorithmAsync(
        List<PromotionRuleBase> rules,
        ShoppingCart cart,
        OptimizationStrategy strategy)
    {
        switch (strategy)
        {
            case OptimizationStrategy.Aggressive:
                return await ExecuteAggressiveOptimizationAsync(rules, cart).ConfigureAwait(false);

            case OptimizationStrategy.Moderate:
                return await ExecuteModerateOptimizationAsync(rules, cart).ConfigureAwait(false);

            case OptimizationStrategy.Conservative:
            default:
                return await ExecuteConservativeOptimizationAsync(rules, cart).ConfigureAwait(false);
        }
    }

    /// <summary>
    /// 激进优化算法
    /// </summary>
    private async Task<List<PromotionRuleBase>> ExecuteAggressiveOptimizationAsync(List<PromotionRuleBase> rules, ShoppingCart cart)
    {
        // 使用贪心算法 + 动态规划
        var optimizedRules = new List<PromotionRuleBase>();
        var remainingRules = rules.ToList();

        while (remainingRules.Any())
        {
            var bestRule = await FindBestRuleAsync(remainingRules, cart).ConfigureAwait(false);
            if (bestRule == null) break;

            optimizedRules.Add(bestRule);
            remainingRules.Remove(bestRule);

            // 移除冲突规则
            remainingRules.RemoveAll(r => HasConflict(bestRule, r));
        }

        return optimizedRules;
    }

    /// <summary>
    /// 温和优化算法
    /// </summary>
    private async Task<List<PromotionRuleBase>> ExecuteModerateOptimizationAsync(List<PromotionRuleBase> rules, ShoppingCart cart)
    {
        // 使用优先级排序 + 简单筛选
        await Task.Delay(1).ConfigureAwait(false);

        return rules
            .Where(r => IsRuleApplicable(r, cart))
            .OrderByDescending(r => CalculateRuleValue(r, cart))
            .ThenByDescending(r => r.Priority)
            .Take(Math.Min(rules.Count, 10)) // 限制规则数量
            .ToList();
    }

    /// <summary>
    /// 保守优化算法
    /// </summary>
    private async Task<List<PromotionRuleBase>> ExecuteConservativeOptimizationAsync(List<PromotionRuleBase> rules, ShoppingCart cart)
    {
        // 简单的优先级排序
        await Task.Delay(1).ConfigureAwait(false);

        return rules
            .Where(r => IsRuleApplicable(r, cart))
            .OrderByDescending(r => r.Priority)
            .ThenBy(r => r.ExecutionOrder)
            .ToList();
    }

    /// <summary>
    /// 分析时间复杂度
    /// </summary>
    private ComplexityLevel AnalyzeTimeComplexity(List<PromotionRuleBase> rules, ShoppingCart cart, Dictionary<string, int> factors)
    {
        var ruleCount = rules.Count;
        var itemCount = cart.Items.Count;

        factors["RuleCount"] = ruleCount;
        factors["ItemCount"] = itemCount;
        factors["TotalOperations"] = ruleCount * itemCount;

        // 基于规则数量和商品数量判断复杂度
        if (ruleCount * itemCount > 10000)
            return ComplexityLevel.Quadratic;

        if (ruleCount * Math.Log(itemCount) > 1000)
            return ComplexityLevel.LinearLogarithmic;

        if (ruleCount + itemCount > 100)
            return ComplexityLevel.Linear;

        return ComplexityLevel.Constant;
    }

    /// <summary>
    /// 分析空间复杂度
    /// </summary>
    private ComplexityLevel AnalyzeSpaceComplexity(List<PromotionRuleBase> rules, ShoppingCart cart, Dictionary<string, int> factors)
    {
        var ruleCount = rules.Count;
        var itemCount = cart.Items.Count;

        factors["MemoryRules"] = ruleCount;
        factors["MemoryItems"] = itemCount;

        // 基于内存使用估算空间复杂度
        if (ruleCount * itemCount > 5000)
            return ComplexityLevel.Quadratic;

        if (ruleCount + itemCount > 500)
            return ComplexityLevel.Linear;

        return ComplexityLevel.Constant;
    }

    /// <summary>
    /// 估算计算时间
    /// </summary>
    private long EstimateCalculationTime(List<PromotionRuleBase> rules, ShoppingCart cart, ComplexityLevel complexity)
    {
        var baseTime = 1; // 基础时间(毫秒)
        var ruleCount = rules.Count;
        var itemCount = cart.Items.Count;

        return complexity switch
        {
            ComplexityLevel.Constant => baseTime,
            ComplexityLevel.Logarithmic => (long)(baseTime * Math.Log(ruleCount + itemCount)),
            ComplexityLevel.Linear => baseTime * (ruleCount + itemCount),
            ComplexityLevel.LinearLogarithmic => (long)(baseTime * (ruleCount + itemCount) * Math.Log(ruleCount + itemCount)),
            ComplexityLevel.Quadratic => baseTime * ruleCount * itemCount,
            ComplexityLevel.Cubic => baseTime * ruleCount * itemCount * itemCount,
            ComplexityLevel.Exponential => (long)(baseTime * Math.Pow(2, Math.Min(ruleCount, 20))),
            _ => baseTime
        };
    }

    /// <summary>
    /// 估算内存使用
    /// </summary>
    private double EstimateMemoryUsage(List<PromotionRuleBase> rules, ShoppingCart cart, ComplexityLevel complexity)
    {
        var baseMemory = 0.1; // 基础内存(MB)
        var ruleCount = rules.Count;
        var itemCount = cart.Items.Count;

        return complexity switch
        {
            ComplexityLevel.Constant => baseMemory,
            ComplexityLevel.Linear => baseMemory * (ruleCount + itemCount) / 100.0,
            ComplexityLevel.Quadratic => baseMemory * ruleCount * itemCount / 10000.0,
            _ => baseMemory * (ruleCount + itemCount) / 100.0
        };
    }

    /// <summary>
    /// 生成复杂度优化建议
    /// </summary>
    private List<string> GenerateComplexityOptimizationRecommendations(ComplexityAnalysis analysis)
    {
        var recommendations = new List<string>();

        if (analysis.TimeComplexity >= ComplexityLevel.Quadratic)
        {
            recommendations.Add("考虑使用规则预筛选减少计算量");
            recommendations.Add("实施并行计算提升性能");
            recommendations.Add("优化算法复杂度，避免嵌套循环");
        }

        if (analysis.SpaceComplexity >= ComplexityLevel.Linear)
        {
            recommendations.Add("使用对象池减少内存分配");
            recommendations.Add("实施缓存策略复用计算结果");
        }

        if (analysis.EstimatedTimeMs > 100)
        {
            recommendations.Add("考虑异步处理长时间计算");
            recommendations.Add("实施超时机制防止阻塞");
        }

        return recommendations;
    }

    /// <summary>
    /// 计算性能提升
    /// </summary>
    private double CalculatePerformanceImprovement(List<PromotionRuleBase> originalRules, List<PromotionRuleBase> optimizedRules, ComplexityAnalysis analysis)
    {
        if (originalRules.Count == 0) return 0;

        var originalComplexity = originalRules.Count * originalRules.Count; // 简化的复杂度计算
        var optimizedComplexity = optimizedRules.Count * optimizedRules.Count;

        return Math.Max(0, (originalComplexity - optimizedComplexity) * 100.0 / originalComplexity);
    }

    /// <summary>
    /// 查找最佳规则
    /// </summary>
    private async Task<PromotionRuleBase?> FindBestRuleAsync(List<PromotionRuleBase> rules, ShoppingCart cart)
    {
        await Task.Delay(1).ConfigureAwait(false);

        return rules
            .Where(r => IsRuleApplicable(r, cart))
            .OrderByDescending(r => CalculateRuleValue(r, cart))
            .FirstOrDefault();
    }

    /// <summary>
    /// 检查规则冲突
    /// </summary>
    private static bool HasConflict(PromotionRuleBase rule1, PromotionRuleBase rule2)
    {
        // 简化的冲突检查逻辑
        return rule1.IsExclusive && rule2.IsExclusive;
    }

    /// <summary>
    /// 检查规则是否适用
    /// </summary>
    private static bool IsRuleApplicable(PromotionRuleBase rule, ShoppingCart cart)
    {
        return rule.IsEnabled &&
               cart.TotalAmount >= rule.MinOrderAmount &&
               cart.TotalQuantity >= rule.MinQuantity;
    }

    /// <summary>
    /// 计算规则价值
    /// </summary>
    private static double CalculateRuleValue(PromotionRuleBase rule, ShoppingCart cart)
    {
        // 简化的价值计算，实际应该基于折扣金额
        return rule.Priority * 100 + (double)rule.DiscountAmount;
    }

    /// <summary>
    /// 估算缓存大小
    /// </summary>
    private static long EstimateCacheSize(PromotionResult result)
    {
        // 简化的大小估算
        return 1024; // 1KB
    }

    /// <summary>
    /// 记录性能指标
    /// </summary>
    private void RecordPerformanceMetric(string operation, long elapsedMs, int itemCount)
    {
        var metric = _performanceMetrics.GetOrAdd(operation, _ => new PerformanceMetrics());

        metric.TotalExecutions++;
        metric.TotalTimeMs += elapsedMs;
        metric.AverageTimeMs = metric.TotalTimeMs / (double)metric.TotalExecutions;
        metric.MaxTimeMs = Math.Max(metric.MaxTimeMs, elapsedMs);
        metric.MinTimeMs = metric.MinTimeMs == 0 ? elapsedMs : Math.Min(metric.MinTimeMs, elapsedMs);
        metric.LastExecutionTime = DateTime.Now;
        metric.ItemCount = itemCount;
    }

    /// <summary>
    /// 获取系统信息
    /// </summary>
    private static SystemInfo GetSystemInfo()
    {
        return new SystemInfo
        {
            CpuInfo = Environment.ProcessorCount + " cores",
            MemoryGB = GC.GetTotalMemory(false) / (1024.0 * 1024.0 * 1024.0),
            DotNetVersion = Environment.Version.ToString(),
            OperatingSystem = Environment.OSVersion.ToString()
        };
    }

    /// <summary>
    /// 生成基准测试场景
    /// </summary>
    private Dictionary<string, BenchmarkData> GenerateBenchmarkScenarios()
    {
        return new Dictionary<string, BenchmarkData>
        {
            ["Small"] = new BenchmarkData
            {
                ScenarioName = "小规模测试",
                ProductCount = 5,
                RuleCount = 3,
                AverageTimeMs = 10,
                MaxTimeMs = 20,
                MinTimeMs = 5,
                StandardDeviation = 3,
                MemoryUsageMB = 1,
                SuccessRate = 0.99
            },
            ["Medium"] = new BenchmarkData
            {
                ScenarioName = "中等规模测试",
                ProductCount = 20,
                RuleCount = 10,
                AverageTimeMs = 50,
                MaxTimeMs = 100,
                MinTimeMs = 30,
                StandardDeviation = 15,
                MemoryUsageMB = 5,
                SuccessRate = 0.95
            },
            ["Large"] = new BenchmarkData
            {
                ScenarioName = "大规模测试",
                ProductCount = 100,
                RuleCount = 50,
                AverageTimeMs = 200,
                MaxTimeMs = 500,
                MinTimeMs = 150,
                StandardDeviation = 50,
                MemoryUsageMB = 20,
                SuccessRate = 0.90
            }
        };
    }

    /// <summary>
    /// 运行场景测试
    /// </summary>
    private async Task<ScenarioTestResult> RunScenarioTestAsync(TestScenario scenario, PerformanceTestConfig config)
    {
        var result = new ScenarioTestResult
        {
            ScenarioName = scenario.Name
        };

        var times = new List<long>();
        var memoryUsages = new List<double>();

        // 预热
        for (int i = 0; i < config.WarmupIterations; i++)
        {
            await SimulateScenarioExecutionAsync(scenario).ConfigureAwait(false);
        }

        // 正式测试
        for (int i = 0; i < config.TestIterations; i++)
        {
            var stopwatch = Stopwatch.StartNew();
            var memoryBefore = GC.GetTotalMemory(false);

            try
            {
                await SimulateScenarioExecutionAsync(scenario).ConfigureAwait(false);
                result.SuccessCount++;
            }
            catch
            {
                result.FailureCount++;
            }

            stopwatch.Stop();
            var memoryAfter = GC.GetTotalMemory(false);

            times.Add(stopwatch.ElapsedMilliseconds);
            memoryUsages.Add((memoryAfter - memoryBefore) / (1024.0 * 1024.0));
        }

        // 计算统计数据
        if (times.Any())
        {
            times.Sort();
            result.AverageTimeMs = times.Average();
            result.MedianTimeMs = times[times.Count / 2];
            result.P95TimeMs = times[(int)(times.Count * 0.95)];
            result.P99TimeMs = times[(int)(times.Count * 0.99)];
            result.MaxTimeMs = times.Max();
            result.MinTimeMs = times.Min();
            result.StandardDeviation = CalculateStandardDeviation(times);
        }

        if (memoryUsages.Any())
        {
            result.AverageMemoryMB = memoryUsages.Average();
        }

        return result;
    }

    /// <summary>
    /// 模拟场景执行
    /// </summary>
    private async Task SimulateScenarioExecutionAsync(TestScenario scenario)
    {
        // 模拟促销计算过程
        await Task.Delay(Random.Shared.Next(1, 10)).ConfigureAwait(false);

        // 模拟一些计算工作
        var sum = 0;
        for (int i = 0; i < scenario.RuleConfig.RuleCount * 100; i++)
        {
            sum += i;
        }
    }

    /// <summary>
    /// 计算标准差
    /// </summary>
    private static double CalculateStandardDeviation(List<long> values)
    {
        if (values.Count < 2) return 0;

        var average = values.Average();
        var sumOfSquares = values.Sum(x => Math.Pow(x - average, 2));
        return Math.Sqrt(sumOfSquares / (values.Count - 1));
    }

    /// <summary>
    /// 计算总体统计
    /// </summary>
    private static OverallStatistics CalculateOverallStatistics(Dictionary<string, ScenarioTestResult> scenarioResults)
    {
        var stats = new OverallStatistics();

        if (scenarioResults.Any())
        {
            stats.TotalTests = scenarioResults.Values.Sum(r => r.SuccessCount + r.FailureCount);
            stats.TotalSuccesses = scenarioResults.Values.Sum(r => r.SuccessCount);
            stats.TotalFailures = scenarioResults.Values.Sum(r => r.FailureCount);
            stats.OverallSuccessRate = stats.TotalTests > 0 ? (double)stats.TotalSuccesses / stats.TotalTests : 0;
            stats.OverallAverageTimeMs = scenarioResults.Values.Average(r => r.AverageTimeMs);

            var bestScenario = scenarioResults.OrderBy(kvp => kvp.Value.AverageTimeMs).FirstOrDefault();
            var worstScenario = scenarioResults.OrderByDescending(kvp => kvp.Value.AverageTimeMs).FirstOrDefault();

            stats.BestScenario = bestScenario.Key ?? "";
            stats.WorstScenario = worstScenario.Key ?? "";
        }

        return stats;
    }

    /// <summary>
    /// 清理过期缓存
    /// </summary>
    private void CleanupExpiredCache(object? state)
    {
        try
        {
            var cutoffTime = DateTime.Now.Subtract(_cacheExpiry);

            // 清理优化缓存
            var expiredOptimizations = _optimizationCache
                .Where(kvp => kvp.Value.EstimatedCalculationTimeMs < cutoffTime.Ticks)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in expiredOptimizations)
            {
                _optimizationCache.TryRemove(key, out _);
            }

            // 清理复杂度缓存
            var expiredComplexity = _complexityCache
                .Where(kvp => kvp.Value.EstimatedTimeMs < cutoffTime.Ticks)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in expiredComplexity)
            {
                _complexityCache.TryRemove(key, out _);
            }

            // 清理基准测试缓存
            var expiredBenchmarks = _benchmarkCache
                .Where(kvp => kvp.Value.BenchmarkTime < cutoffTime)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in expiredBenchmarks)
            {
                _benchmarkCache.TryRemove(key, out _);
            }

            if (expiredOptimizations.Count > 0 || expiredComplexity.Count > 0 || expiredBenchmarks.Count > 0)
            {
                _logger.LogDebug("清理过期缓存: 优化{Opt}个, 复杂度{Comp}个, 基准{Bench}个",
                    expiredOptimizations.Count, expiredComplexity.Count, expiredBenchmarks.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期缓存时发生异常");
        }
    }

    #endregion

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed) return;

        try
        {
            _cacheCleanupTimer?.Dispose();
            _optimizationSemaphore?.Dispose();

            _optimizationCache.Clear();
            _complexityCache.Clear();
            _benchmarkCache.Clear();
            _performanceMetrics.Clear();

            _logger.LogInformation("性能优化器已释放资源");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "释放性能优化器资源时发生异常");
        }
        finally
        {
            _disposed = true;
        }
    }
}

/// <summary>
/// 优化策略枚举
/// </summary>
internal enum OptimizationStrategy
{
    /// <summary>保守策略</summary>
    Conservative,
    /// <summary>温和策略</summary>
    Moderate,
    /// <summary>激进策略</summary>
    Aggressive
}

/// <summary>
/// 性能指标类
/// </summary>
internal sealed class PerformanceMetrics
{
    /// <summary>总执行次数</summary>
    public long TotalExecutions { get; set; }

    /// <summary>总耗时(毫秒)</summary>
    public long TotalTimeMs { get; set; }

    /// <summary>平均耗时(毫秒)</summary>
    public double AverageTimeMs { get; set; }

    /// <summary>最大耗时(毫秒)</summary>
    public long MaxTimeMs { get; set; }

    /// <summary>最小耗时(毫秒)</summary>
    public long MinTimeMs { get; set; }

    /// <summary>最后执行时间</summary>
    public DateTime LastExecutionTime { get; set; }

    /// <summary>处理项目数量</summary>
    public int ItemCount { get; set; }
}
