<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>条件配置组件测试</title>
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3.3.8/dist/vue.global.js"></script>
    
    <!-- Element Plus UI Framework -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    
    <!-- Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.iife.min.js"></script>
    
    <style>
        :root {
            --ant-primary-color: #1890ff;
            --ant-border-color-base: #d9d9d9;
            --ant-border-radius-base: 6px;
            --ant-text-color: rgba(0, 0, 0, 0.85);
            --ant-text-color-secondary: rgba(0, 0, 0, 0.65);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei';
            background-color: #f0f2f5;
            padding: 20px;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .test-section {
            margin-bottom: 24px;
            padding: 16px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
        }

        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #1890ff;
        }

        .condition-demo {
            border: 1px solid #e8e8e8;
            padding: 16px;
            margin-top: 16px;
            background-color: #fafafa;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>条件配置渲染器测试页面</h1>
            
            <div class="test-section">
                <div class="test-title">组合条件测试 (CombinationConditions)</div>
                <condition-config-renderer
                    field-name="combinationConditions"
                    :field-config="{ name: 'combinationConditions', label: '组合条件', type: 'object' }"
                    :model-value="combinationConditionData"
                    rule-type="CombinationGiftRule"
                    @update:model-value="updateCombinationConditions"
                />
                <div class="condition-demo">
                    <h4>当前数据：</h4>
                    <pre>{{ JSON.stringify(combinationConditionData, null, 2) }}</pre>
                </div>
            </div>

            <div class="test-section">
                <div class="test-title">兑换条件测试 (ExchangeConditions)</div>
                <condition-config-renderer
                    field-name="exchangeConditions"
                    :field-config="{ name: 'exchangeConditions', label: '兑换条件', type: 'object' }"
                    :model-value="exchangeConditionData"
                    rule-type="UnifiedDiscountExchangeRule"
                    @update:model-value="updateExchangeConditions"
                />
                <div class="condition-demo">
                    <h4>当前数据：</h4>
                    <pre>{{ JSON.stringify(exchangeConditionData, null, 2) }}</pre>
                </div>
            </div>

            <div class="test-section">
                <div class="test-title">购买条件测试 (BuyConditions)</div>
                <condition-config-renderer
                    field-name="buyConditions"
                    :field-config="{ name: 'buyConditions', label: '购买条件', type: 'object' }"
                    :model-value="buyConditionData"
                    rule-type="UnifiedDiscountExchangeRule"
                    @update:model-value="updateBuyConditions"
                />
                <div class="condition-demo">
                    <h4>当前数据：</h4>
                    <pre>{{ JSON.stringify(buyConditionData, null, 2) }}</pre>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载条件配置渲染器组件 -->
    <script src="./js/components/ConditionConfigRenderer.js"></script>

    <script>
        const { createApp, ref } = Vue;
        
        const TestApp = {
            components: {
                'condition-config-renderer': ConditionConfigRenderer
            },
            setup() {
                const combinationConditionData = ref({
                    requiredProducts: [
                        { productId: 'P001', quantity: 2 }
                    ],
                    totalMinQuantity: 3,
                    totalMinAmount: 100,
                    mustBuyAll: false
                });

                const exchangeConditionData = ref({
                    exchangeItems: [
                        { productId: 'E001', quantity: 1 }
                    ],
                    exchangeRate: 0.8,
                    minExchangeAmount: 50
                });

                const buyConditionData = ref({
                    targetProducts: [
                        { productId: 'B001', quantity: 1 }
                    ],
                    minQuantity: 2,
                    minAmount: 80,
                    calculateBy: 'quantity'
                });

                const updateCombinationConditions = (value) => {
                    combinationConditionData.value = value;
                    console.log('组合条件更新:', value);
                };

                const updateExchangeConditions = (value) => {
                    exchangeConditionData.value = value;
                    console.log('兑换条件更新:', value);
                };

                const updateBuyConditions = (value) => {
                    buyConditionData.value = value;
                    console.log('购买条件更新:', value);
                };

                return {
                    combinationConditionData,
                    exchangeConditionData,
                    buyConditionData,
                    updateCombinationConditions,
                    updateExchangeConditions,
                    updateBuyConditions
                };
            }
        };
        
        // 创建Vue应用
        const app = createApp(TestApp);
        
        // 注册Element Plus
        app.use(ElementPlus);
        
        // 注册Element Plus图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
        
        // 挂载应用
        app.mount('#app');
    </script>
</body>
</html>
