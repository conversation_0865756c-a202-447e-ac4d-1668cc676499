using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.CashDiscountRules;

/// <summary>
/// 减现促销规则基类
/// </summary>
public abstract class BaseCashDiscountRule : PromotionRuleBase
{
    /// <summary>
    /// 验证减现商品是否在购物车中
    /// </summary>
    protected bool ValidateCashDiscountProductsInCart(ShoppingCart cart, List<string> productIds)
  => cart.Items.Any(x => x.Quantity > 0 && productIds.Contains(x.Product.Id));

    /// <summary>
    /// 计算商品的总数量
    /// </summary>
    protected int CalculateTotalQuantity(ShoppingCart cart, List<string> productIds)
    {
        return productIds.Sum(id => cart.GetAvailableProductQuantity(id));
    }

    /// <summary>
    /// 计算商品的总金额
    /// </summary>
    protected decimal CalculateTotalAmount(ShoppingCart cart, List<string> productIds)
    {
        return productIds.Sum(id =>
            cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));
    }

    /// <summary>
    /// 应用减现到购物车
    /// 减现通过按比例分摊到各个商品项来实现
    /// </summary>
    protected void ApplyCashDiscountToCart(ShoppingCart cart, decimal discountAmount, AppliedPromotion promotion, List<string> applicableProductIds)
    {
        // 计算适用商品的总金额
        var applicableItems = cart.Items.Where(x => applicableProductIds.Contains(x.Product.Id)).ToList();
        var totalApplicableAmount = applicableItems.Sum(x => x.SubTotal);

        if (totalApplicableAmount <= 0) return;

        // 按比例分摊减现金额到各个商品项
        var remainingDiscount = discountAmount;

        for (int i = 0; i < applicableItems.Count; i++)
        {
            var item = applicableItems[i];
            decimal itemDiscount;

            if (i == applicableItems.Count - 1)
            {
                // 最后一个商品承担剩余的减现金额（避免精度问题）
                itemDiscount = remainingDiscount;
            }
            else
            {
                // 按比例计算该商品应承担的减现金额
                itemDiscount = Math.Round(discountAmount * item.SubTotal / totalApplicableAmount, 2);
            }

            // 应用减现到商品项
            var discountPerUnit = itemDiscount / item.Quantity;
            item.ActualUnitPrice = Math.Max(0, item.UnitPrice - discountPerUnit);

            // 记录促销详情
            var promotionDetail = new ItemPromotionDetail
            {
                RuleId = promotion.RuleId,
                RuleName = promotion.RuleName,
                PromotionType = promotion.PromotionType,
                DiscountAmount = itemDiscount,
                Description = $"减现促销：{promotion.RuleName}，立减{itemDiscount:C}",
                IsGiftRelated = false
            };

            item.AddPromotionDetail(promotionDetail);
            remainingDiscount -= itemDiscount;
        }
    }

    /// <summary>
    /// 消耗购买条件商品（记录消耗但不修改购物车）
    /// </summary>
    protected List<ConsumedItem> ConsumeConditionProducts(ShoppingCart cart, List<string> productIds, int requiredQuantity)
    {
        var consumedItems = new List<ConsumedItem>();
        var remainingQuantity = requiredQuantity;

        foreach (var productId in productIds)
        {
            if (remainingQuantity <= 0) break;

            var cartItems = cart.Items
                .Where(x => x.Product.Id == productId && x.Quantity > 0)
                .ToList();

            foreach (var cartItem in cartItems)
            {
                if (remainingQuantity <= 0) break;

                var consumeQuantity = Math.Min(cartItem.Quantity, remainingQuantity);

                var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == productId);
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += consumeQuantity;
                }
                else
                {
                    consumedItems.Add(new ConsumedItem
                    {
                        ProductId = productId,
                        ProductName = cartItem.Product.Name,
                        Quantity = consumeQuantity,
                        UnitPrice = cartItem.UnitPrice
                    });
                }

                remainingQuantity -= consumeQuantity;
            }
        }

        return consumedItems;
    }

    /// <summary>
    /// 创建减现记录（作为GiftItem记录，但实际是减现）
    /// </summary>
    protected GiftItem CreateCashDiscountRecord(decimal discountAmount, string description)
    {
        return new GiftItem
        {
            ProductId = "CASH_DISCOUNT",
            ProductName = "立减优惠",
            Quantity = 1,
            Value = discountAmount,
            Description = description
        };
    }
}