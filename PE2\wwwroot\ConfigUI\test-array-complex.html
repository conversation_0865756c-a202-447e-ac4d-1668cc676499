<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Array Complex Field Test</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Element Plus JS -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .field-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .field-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .test-data {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>Array Complex Field Test</h1>
            
            <div class="field-section">
                <div class="field-title">组合条件 (combinationConditions) - Array Complex</div>
                
                <complex-array-renderer
                    field-name="combinationConditions"
                    :field-config="combinationConditionsConfig"
                    :model-value="testData.combinationConditions"
                    @update:model-value="value => updateField('combinationConditions', value)"
                />
                
                <div class="test-data">
                    当前数据: {{ JSON.stringify(testData.combinationConditions, null, 2) }}
                </div>
            </div>
            
            <div class="field-section">
                <div class="field-title">商品选择器测试 - Product Selector</div>
                
                <el-form-item label="单选商品:">
                    <product-selector
                        :model-value="testData.singleProduct"
                        @update:model-value="value => updateField('singleProduct', value)"
                        placeholder="请选择单个商品"
                    />
                </el-form-item>
                
                <el-form-item label="多选商品:">
                    <product-selector
                        :model-value="testData.multipleProducts"
                        @update:model-value="value => updateField('multipleProducts', value)"
                        placeholder="请选择多个商品"
                        :multiple="true"
                    />
                </el-form-item>
                
                <div class="test-data">
                    单选: {{ JSON.stringify(testData.singleProduct, null, 2) }}
                    多选: {{ JSON.stringify(testData.multipleProducts, null, 2) }}
                </div>
            </div>
            
            <div class="field-section">
                <div class="field-title">动态表单渲染器测试</div>
                
                <dynamic-form-renderer
                    :model-value="testData.dynamicForm"
                    :rule-type="'CombinationSpecialPrice'"
                    @update:model-value="value => updateField('dynamicForm', value)"
                />
                
                <div class="test-data">
                    表单数据: {{ JSON.stringify(testData.dynamicForm, null, 2) }}
                </div>
            </div>
        </div>
    </div>

    <!-- Load Components -->
    <script src="./js/components/ProductSelector.js"></script>
    <script src="./js/components/ComplexArrayRenderer.js"></script>
    <script src="./js/components/ConditionConfigRenderer.js"></script>
    <script src="./js/components/EnhancedConditionRenderer.js"></script>
    <script src="./js/components/DynamicFormRenderer_template.js"></script>

    <script>
        const { createApp, ref, reactive } = Vue;
        
        const app = createApp({
            setup() {
                const testData = ref({
                    combinationConditions: [],
                    singleProduct: null,
                    multipleProducts: [],
                    dynamicForm: {
                        ruleId: 'test_combination_rule',
                        ruleName: '测试组合规则',
                        ruleType: 'CombinationSpecialPrice',
                        combinationConditions: []
                    }
                });
                
                const combinationConditionsConfig = {
                    name: 'combinationConditions',
                    type: 'array-complex',
                    displayName: '组合条件',
                    description: '定义商品组合的条件',
                    itemSchema: {
                        type: 'object',
                        properties: {
                            productIds: {
                                name: 'productIds',
                                type: 'product-selector-multiple',
                                displayName: '商品ID列表',
                                description: '参与组合的商品ID'
                            },
                            minQuantity: {
                                name: 'minQuantity',
                                type: 'number',
                                displayName: '最小数量',
                                description: '该组合的最小购买数量',
                                min: 1,
                                defaultValue: 1
                            },
                            maxQuantity: {
                                name: 'maxQuantity',
                                type: 'number',
                                displayName: '最大数量',
                                description: '该组合的最大购买数量（可选）',
                                min: 1
                            }
                        }
                    }
                };
                
                const updateField = (fieldName, value) => {
                    testData.value[fieldName] = value;
                };
                
                return {
                    testData,
                    combinationConditionsConfig,
                    updateField
                };
            }
        });
        
        // Register Element Plus
        app.use(ElementPlus);
        
        // Register Element Plus Icons
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
        
        // Register custom components
        if (typeof ProductSelector !== 'undefined') {
            app.component('ProductSelector', ProductSelector);
            app.component('product-selector', ProductSelector);
        }
        
        if (typeof ComplexArrayRenderer !== 'undefined') {
            app.component('ComplexArrayRenderer', ComplexArrayRenderer);
            app.component('complex-array-renderer', ComplexArrayRenderer);
        }
        
        if (typeof DynamicFormRenderer !== 'undefined') {
            app.component('DynamicFormRenderer', DynamicFormRenderer);
            app.component('dynamic-form-renderer', DynamicFormRenderer);
        }
        
        if (typeof ConditionConfigRenderer !== 'undefined') {
            app.component('ConditionConfigRenderer', ConditionConfigRenderer);
            app.component('condition-config-renderer', ConditionConfigRenderer);
        }
        
        if (typeof EnhancedConditionRenderer !== 'undefined') {
            app.component('EnhancedConditionRenderer', EnhancedConditionRenderer);
            app.component('enhanced-condition-renderer', EnhancedConditionRenderer);
        }
        
        app.mount('#app');
    </script>
</body>
</html>
