using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.SpecialPriceRules;

/// <summary>
/// 梯度特价规则 [OK]
/// 针对某一类商品，一件特价60元，两件特价110元，三件特价150元
/// 场景案例：当购买商品数量大于等于1件时，特价100元；
///           当商品数量大于等于2件时，特价50元；
///           当商品数量大于等于3件时，特价10元。
///促销设置为不翻倍：A商品吊牌价、零售价为1000元；购买A商品4件时，应收金额为10+10/3=13.33元
///促销设置为不翻倍：A商品吊牌价、零售价为1000元；购买A商品5件时，应收金额为10+10/3×2=16.66元
///促销设置为翻倍：A商品吊牌价、零售价为1000元；购买A商品4件时，应收金额为10+10/3=13.33元
///促销设置为翻倍：A商品吊牌价、零售价为1000元；购买A商品5件时，应收金额为10+10/3×2=16.66元
///促销设置为翻倍：A商品吊牌价、零售价为1000元；购买A商品6件时，应收金额为10*2=20元
///备注：对于梯度特价规则，只有规则要求是等于，而不是大于等于，才执行翻倍规则。特价 如果是大于等于 或者大于 都不考虑翻倍，只有等于时才考虑翻倍
///
/// </summary>
public class TieredSpecialPriceRule : BaseSpecialPriceRule
{
    public override string RuleType => "TieredSpecialPrice";

    /// <summary>
    /// 适用的商品ID列表
    /// </summary>
    public List<string> ApplicableProductIds { get; set; } = [];

    /// <summary>
    /// 特价梯度列表（按数量从低到高排序）
    /// </summary>
    public List<SpecialPriceTier> SpecialPriceTiers { get; set; } = new();

    /// <summary>
    /// 是否按金额计算（如果为true，则使用金额条件；否则使用数量条件）
    /// </summary>
    public bool IsByAmount { get; set; } = false;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!ApplicableProductIds.Any() || !SpecialPriceTiers.Any())
            return false;

        // 验证商品是否在购物车中
        if (!ValidateSpecialPriceProductsInCart(cart, ApplicableProductIds))
            return false;

        // 检查是否满足最低梯度条件
        return GetApplicableTier(cart) != null;
    }

    /// <summary>
    /// 获取适用的特价梯度
    /// </summary>
    private SpecialPriceTier? GetApplicableTier(ShoppingCart cart)
    {
        if (IsByAmount)
        {
            var totalAmount = CalculateTotalAmount(cart, ApplicableProductIds);
            return SpecialPriceTiers
                .Where(t => totalAmount >= t.MinAmount)
                .OrderByDescending(t => t.MinAmount)
                .FirstOrDefault();
        }
        else
        {
            var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);
            return SpecialPriceTiers
                .Where(t => totalQuantity >= t.MinQuantity)
                .OrderByDescending(t => t.MinQuantity)
                .FirstOrDefault();
        }
    }

    //public override int CalculateMaxApplications(ShoppingCart cart)
    //{
    //    if (!CheckConditions(cart))
    //        return 0;

    //    var applicableTier = GetApplicableTier(cart);
    //    if (applicableTier == null)
    //        return 0;

    //    var maxApplications = 0;

    //    if (IsByAmount)
    //    {
    //        var totalAmount = CalculateTotalAmount(cart, ApplicableProductIds);

    //        maxApplications = IsRepeatable
    //            ? (int)(totalAmount / applicableTier.MinAmount)
    //            : (totalAmount >= applicableTier.MinAmount ? 1 : 0);
    //    }
    //    else
    //    {
    //        var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);

    //        maxApplications = IsRepeatable
    //            ? totalQuantity / applicableTier.MinQuantity
    //            : (totalQuantity >= applicableTier.MinQuantity ? 1 : 0);
    //    }

    //    if (!IsRepeatable)
    //        maxApplications = Math.Min(maxApplications, 1);

    //    if (MaxApplications > 0)
    //        maxApplications = Math.Min(maxApplications, MaxApplications);

    //    return maxApplications;
    //}

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyTieredSpecialPrice(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用梯度特价促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用梯度特价促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyTieredSpecialPrice(
        ShoppingCart cart,
        int applicationCount
    )
    {
        var applicableTier = GetApplicableTier(cart);
        if (applicableTier == null)
            return (0m, new List<ConsumedItem>(), new List<GiftItem>());

        var applicableItems = cart
            .Items.Where(x => ApplicableProductIds.Contains(x.Product.Id) && x.Quantity > 0)
            .ToList();

        if (!applicableItems.Any())
            return (0m, new List<ConsumedItem>(), new List<GiftItem>());

        var totalQuantity = applicableItems.Sum(x => x.Quantity);
        var tierQuantity = applicableTier.MinQuantity;
        var specialPrice = applicableTier.SpecialPrice;
        var tierUnitPrice = specialPrice / tierQuantity;

        // 计算处理方案
        var (fullTiers, remainingItems) = IsRepeatable
            ? (
                Math.Min(totalQuantity / tierQuantity, applicationCount),
                totalQuantity % tierQuantity
            )
            : (totalQuantity >= tierQuantity ? 1 : 0, Math.Max(0, totalQuantity - tierQuantity));

        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var specialPriceRecords = new List<GiftItem>();

        var promotion = new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType
        };
        var sortedItems = SortItemsByStrategy(applicableItems);
        var expandedItems = ExpandItems(sortedItems);

        var processedCount = 0;

        // 处理完整梯度
        for (int tier = 0; tier < fullTiers; tier++)
        {
            var result = ApplyPricingToItems(
                expandedItems.Skip(processedCount).Take(tierQuantity),
                specialPrice,
                $"梯度特价：{applicableTier.Description}（第{tier + 1}次）",
                promotion
            );

            MergeResults(ref totalDiscountAmount, consumedItems, specialPriceRecords, result);
            processedCount += tierQuantity;
        }

        // 处理剩余商品
        if (remainingItems > 0)
        {
            var remainingTotalPrice = tierUnitPrice * remainingItems;
            var result = ApplyPricingToItems(
                expandedItems.Skip(processedCount).Take(remainingItems),
                remainingTotalPrice,
                $"梯度余量：{remainingItems}件按单价{tierUnitPrice:C}",
                promotion
            );

            MergeResults(ref totalDiscountAmount, consumedItems, specialPriceRecords, result);
        }

        return (totalDiscountAmount, consumedItems, specialPriceRecords);
    }

    /// <summary>
    /// 展开商品为单项列表
    /// </summary>
    private List<(CartItem Item, decimal UnitPrice)> ExpandItems(List<CartItem> items)
    {
        return items
            .SelectMany(item => Enumerable.Repeat((item, item.UnitPrice), item.Quantity))
            .ToList();
    }

    /// <summary>
    /// 对指定商品应用定价
    /// </summary>
    private (
        decimal DiscountAmount,
        List<ConsumedItem> ConsumedItems,
        List<GiftItem> Records
    ) ApplyPricingToItems(
        IEnumerable<(CartItem Item, decimal UnitPrice)> items,
        decimal totalTargetPrice,
        string description,
        AppliedPromotion promotion
    )
    {
        var itemList = items.ToList();
        if (!itemList.Any())
            return (0m, new List<ConsumedItem>(), new List<GiftItem>());

        var originalTotal = itemList.Sum(x => x.UnitPrice);
        var discountAmount = Math.Max(0, originalTotal - totalTargetPrice);

        var consumedItems = new List<ConsumedItem>();
        var records = new List<GiftItem>();

        // 按商品分组处理
        var groups = itemList.GroupBy(x => x.Item.Product.Id);
        foreach (var group in groups)
        {
            var item = group.First().Item;
            var quantity = group.Count();
            var originalAmount = group.Sum(x => x.UnitPrice);
            var targetAmount = Math.Round(totalTargetPrice * originalAmount / originalTotal, 2);
            var newUnitPrice = targetAmount / quantity;
            var itemDiscount = originalAmount - targetAmount;

            if (itemDiscount > 0)
            {
                // 更新商品定价
                UpdateCartItemPricing(item, quantity, newUnitPrice, promotion, description);

                // 记录消耗和折扣
                AddOrUpdateConsumedItem(consumedItems, item, quantity);
                records.Add(
                    CreateSpecialPriceRecord(
                        item.Product.Id,
                        item.Product.Name,
                        quantity,
                        itemDiscount,
                        $"{description}，{quantity}件优惠{itemDiscount:C}"
                    )
                );
            }
        }

        return (discountAmount, consumedItems, records);
    }

    /// <summary>
    /// 更新购物车商品定价
    /// </summary>
    private void UpdateCartItemPricing(
        CartItem cartItem,
        int affectedQuantity,
        decimal newUnitPrice,
        AppliedPromotion promotion,
        string description
    )
    {
        var totalQuantity = cartItem.Quantity;
        var unaffectedQuantity = totalQuantity - affectedQuantity;

        // 计算加权平均价格并更新
        cartItem.ActualUnitPrice =
            (affectedQuantity * newUnitPrice + unaffectedQuantity * cartItem.UnitPrice)
            / totalQuantity;

        // 添加促销详情
        var discountAmount = (cartItem.UnitPrice - newUnitPrice) * affectedQuantity;
        cartItem.AddPromotionDetail(
            new ItemPromotionDetail
            {
                RuleId = promotion.RuleId,
                RuleName = promotion.RuleName,
                PromotionType = promotion.PromotionType,
                DiscountAmount = discountAmount,
                Description = $"{description}（{affectedQuantity}件特价，{unaffectedQuantity}件原价）",
                IsGiftRelated = false
            }
        );
    }

    /// <summary>
    /// 合并结果
    /// </summary>
    private void MergeResults(
        ref decimal totalDiscount,
        List<ConsumedItem> consumedItems,
        List<GiftItem> records,
        (decimal DiscountAmount, List<ConsumedItem> ConsumedItems, List<GiftItem> Records) result
    )
    {
        totalDiscount += result.DiscountAmount;

        foreach (var item in result.ConsumedItems)
            AddOrUpdateConsumedItem(
                consumedItems,
                item.ProductId,
                item.ProductName,
                item.Quantity,
                item.UnitPrice
            );

        records.AddRange(result.Records);
    }

    /// <summary>
    /// 添加或更新消耗商品
    /// </summary>
    private void AddOrUpdateConsumedItem(List<ConsumedItem> list, CartItem cartItem, int quantity)
    {
        AddOrUpdateConsumedItem(
            list,
            cartItem.Product.Id,
            cartItem.Product.Name,
            quantity,
            cartItem.UnitPrice
        );
    }

    private void AddOrUpdateConsumedItem(
        List<ConsumedItem> list,
        string productId,
        string productName,
        int quantity,
        decimal unitPrice
    )
    {
        var existing = list.FirstOrDefault(x => x.ProductId == productId);
        if (existing != null)
            existing.Quantity += quantity;
        else
            list.Add(
                new ConsumedItem
                {
                    ProductId = productId,
                    ProductName = productName,
                    Quantity = quantity,
                    UnitPrice = unitPrice
                }
            );
    }

    /// <summary>
    /// 简化最大应用次数计算
    /// </summary>
    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var applicableTier = GetApplicableTier(cart);
        if (applicableTier == null)
            return 0;

        var total = IsByAmount
            ? CalculateTotalAmount(cart, ApplicableProductIds)
            : CalculateTotalQuantity(cart, ApplicableProductIds);

        var threshold = IsByAmount ? applicableTier.MinAmount : applicableTier.MinQuantity;

        var maxApps = IsRepeatable ? (int)(total / threshold) : (total >= threshold ? 1 : 0);

        return MaxApplications > 0 ? Math.Min(maxApps, MaxApplications) : maxApps;
    }
}

/// <summary>
/// 特价梯度
/// </summary>
public class SpecialPriceTier
{
    /// <summary>
    /// 最小数量要求
    /// </summary>
    public int MinQuantity { get; set; }

    /// <summary>
    /// 最小金额要求
    /// </summary>
    public decimal MinAmount { get; set; }

    /// <summary>
    /// 特价金额
    /// </summary>
    public decimal SpecialPrice { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}
