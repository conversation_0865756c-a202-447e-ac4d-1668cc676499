using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Observability;

/// <summary>
/// 可观测性引擎接口 - 促销计算过程追踪和分析
/// </summary>
public interface IObservabilityEngine
{
    /// <summary>
    /// 开始促销计算追踪
    /// </summary>
    string StartCalculationTrace(ShoppingCart cart, string algorithmType);

    /// <summary>
    /// 结束促销计算追踪
    /// </summary>
    void EndCalculationTrace(string traceId, PromotionResult result);

    /// <summary>
    /// 追踪规则筛选过程
    /// </summary>
    void TrackRuleFiltering(string traceId, List<PromotionRuleBase> allRules, List<PromotionRuleBase> filteredRules, string filterCriteria);

    /// <summary>
    /// 追踪条件验证过程
    /// </summary>
    void TrackConditionValidation(string traceId, string ruleId, string description, object? data = null);

    /// <summary>
    /// 追踪促销应用过程
    /// </summary>
    void TrackPromotionApplication(string traceId, string ruleId, PromotionApplication application);

    /// <summary>
    /// 追踪优化搜索过程
    /// </summary>
    void TrackOptimizationSearch(string traceId, string searchStep, object? searchData = null);

    /// <summary>
    /// 追踪分摊计算过程
    /// </summary>
    void TrackAllocationCalculation(string traceId, string allocationStep, object? allocationData = null);

    /// <summary>
    /// 记录性能指标
    /// </summary>
    void RecordPerformanceMetric(string metricName, double value, Dictionary<string, string>? tags = null);

    /// <summary>
    /// 构建决策树
    /// </summary>
    DecisionTree BuildDecisionTree(string traceId);

    /// <summary>
    /// 生成计算报告
    /// </summary>
    CalculationReport GenerateCalculationReport(string traceId);

    /// <summary>
    /// 获取性能分析
    /// </summary>
    PerformanceAnalysis GetPerformanceAnalysis(TimeSpan timeRange);

    /// <summary>
    /// 导出追踪数据
    /// </summary>
    Task<string> ExportTraceDataAsync(string traceId, ExportFormat format);
}

/// <summary>
/// 决策树节点
/// </summary>
public class DecisionTreeNode
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 节点类型
    /// </summary>
    public DecisionNodeType Type { get; set; }

    /// <summary>
    /// 节点描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 决策结果
    /// </summary>
    public string Result { get; set; } = string.Empty;

    /// <summary>
    /// 耗时（毫秒）
    /// </summary>
    public long DurationMs { get; set; }

    /// <summary>
    /// 子节点
    /// </summary>
    public List<DecisionTreeNode> Children { get; set; } = new();

    /// <summary>
    /// 节点数据
    /// </summary>
    public Dictionary<string, object> Data { get; set; } = new();

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccessful { get; set; } = true;
}

/// <summary>
/// 决策节点类型
/// </summary>
public enum DecisionNodeType
{
    /// <summary>规则筛选</summary>
    RuleFiltering,
    /// <summary>条件验证</summary>
    ConditionValidation,
    /// <summary>促销应用</summary>
    PromotionApplication,
    /// <summary>优化搜索</summary>
    OptimizationSearch,
    /// <summary>结果比较</summary>
    ResultComparison,
    /// <summary>分摊计算</summary>
    AllocationCalculation,
    /// <summary>最终决策</summary>
    FinalDecision
}

/// <summary>
/// 决策树
/// </summary>
public class DecisionTree
{
    /// <summary>
    /// 追踪ID
    /// </summary>
    public string TraceId { get; set; } = string.Empty;

    /// <summary>
    /// 根节点
    /// </summary>
    public DecisionTreeNode Root { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 总耗时
    /// </summary>
    public long TotalDurationMs { get; set; }

    /// <summary>
    /// 节点总数
    /// </summary>
    public int TotalNodes => CountNodes(Root);

    /// <summary>
    /// 最大深度
    /// </summary>
    public int MaxDepth => CalculateMaxDepth(Root);

    private int CountNodes(DecisionTreeNode node)
    {
        return 1 + node.Children.Sum(child => CountNodes(child));
    }

    private int CalculateMaxDepth(DecisionTreeNode node)
    {
        if (!node.Children.Any()) return 1;
        return 1 + node.Children.Max(child => CalculateMaxDepth(child));
    }
}

/// <summary>
/// 计算报告
/// </summary>
public class CalculationReport
{
    /// <summary>
    /// 追踪ID
    /// </summary>
    public string TraceId { get; set; } = string.Empty;

    /// <summary>
    /// 计算开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 计算结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 总耗时
    /// </summary>
    public long TotalDurationMs { get; set; }

    /// <summary>
    /// 处理的规则数量
    /// </summary>
    public int ProcessedRulesCount { get; set; }

    /// <summary>
    /// 应用的促销数量
    /// </summary>
    public int AppliedPromotionsCount { get; set; }

    /// <summary>
    /// 优化搜索深度
    /// </summary>
    public int OptimizationDepth { get; set; }

    /// <summary>
    /// 性能指标
    /// </summary>
    public Dictionary<string, double> PerformanceMetrics { get; set; } = new();

    /// <summary>
    /// 阶段耗时分析
    /// </summary>
    public Dictionary<string, long> PhaseTimings { get; set; } = new();

    /// <summary>
    /// 错误和警告
    /// </summary>
    public List<string> ErrorsAndWarnings { get; set; } = new();

    /// <summary>
    /// 优化建议
    /// </summary>
    public List<string> OptimizationSuggestions { get; set; } = new();
}

/// <summary>
/// 性能分析
/// </summary>
public class PerformanceAnalysis
{
    /// <summary>
    /// 分析时间范围
    /// </summary>
    public TimeSpan TimeRange { get; set; }

    /// <summary>
    /// 总计算次数
    /// </summary>
    public int TotalCalculations { get; set; }

    /// <summary>
    /// 平均计算时间
    /// </summary>
    public double AverageCalculationTimeMs { get; set; }

    /// <summary>
    /// 最大计算时间
    /// </summary>
    public long MaxCalculationTimeMs { get; set; }

    /// <summary>
    /// 最小计算时间
    /// </summary>
    public long MinCalculationTimeMs { get; set; }

    /// <summary>
    /// 95分位数计算时间
    /// </summary>
    public long P95CalculationTimeMs { get; set; }

    /// <summary>
    /// 99分位数计算时间
    /// </summary>
    public long P99CalculationTimeMs { get; set; }

    /// <summary>
    /// 错误率
    /// </summary>
    public double ErrorRate { get; set; }

    /// <summary>
    /// 最常用的促销规则
    /// </summary>
    public Dictionary<string, int> MostUsedRules { get; set; } = new();

    /// <summary>
    /// 性能瓶颈分析
    /// </summary>
    public List<PerformanceBottleneck> Bottlenecks { get; set; } = new();
}

/// <summary>
/// 性能瓶颈
/// </summary>
public class PerformanceBottleneck
{
    /// <summary>
    /// 瓶颈类型
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 影响程度（1-10）
    /// </summary>
    public int Impact { get; set; }

    /// <summary>
    /// 建议解决方案
    /// </summary>
    public string Recommendation { get; set; } = string.Empty;
}

/// <summary>
/// 导出格式
/// </summary>
public enum ExportFormat
{
    Json,
    Xml,
    Csv,
    Excel
}
