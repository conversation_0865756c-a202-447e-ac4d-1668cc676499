using PE2.Models;
using PE2.PromotionEngine.Models;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Rules.CashDiscountRules;

/// <summary>
/// 梯度减现规则
/// 针对某一类商品，满100减10元，满200减30元等
/// 场景：买A类商品大于等于1件时，应收金额立减10元；大于等于2件时，应收金额立减30元；大于等于3件时，应收金额立减70元
/// </summary>
public class GradientCashDiscountRule : BaseCashDiscountRule
{
    public override string RuleType => "GradientCashDiscount";

    /// <summary>
    /// 适用的商品ID列表
    /// </summary>
    public List<string> ApplicableProductIds { get; set; } = new();

    /// <summary>
    /// 梯度减现条件列表（按条件从低到高排序）
    /// </summary>
    public List<GradientDiscountTier> DiscountTiers { get; set; } = new();

    /// <summary>
    /// 是否按金额计算（如果为true，则使用金额条件；否则使用数量条件）
    /// </summary>
    public bool IsByAmount { get; set; } = false;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!ApplicableProductIds.Any() || !DiscountTiers.Any())
            return false;

        // 验证商品是否在购物车中
        if (!ValidateCashDiscountProductsInCart(cart, ApplicableProductIds))
            return false;

        // 检查是否满足最低梯度条件
        return GetApplicableTier(cart) != null;
    }

    /// <summary>
    /// 获取适用的梯度条件
    /// </summary>
    private GradientDiscountTier? GetApplicableTier(ShoppingCart cart)
    {
        if (IsByAmount)
        {
            var totalAmount = CalculateTotalAmount(cart, ApplicableProductIds);
            return DiscountTiers
                .Where(t => totalAmount >= t.MinAmount)
                .OrderByDescending(t => t.MinAmount)
                .FirstOrDefault();
        }
        else
        {
            var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);
            return DiscountTiers
                .Where(t => totalQuantity >= t.MinQuantity)
                .OrderByDescending(t => t.MinQuantity)
                .FirstOrDefault();
        }
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var applicableTier = GetApplicableTier(cart);
        if (applicableTier == null)
            return 0;

        var maxApplications = 0;

        if (IsByAmount)
        {
            var totalAmount = CalculateTotalAmount(cart, ApplicableProductIds);
            
            maxApplications = IsRepeatable 
                ? (int)(totalAmount / applicableTier.MinAmount)
                : (totalAmount >= applicableTier.MinAmount ? 1 : 0);
        }
        else
        {
            var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);
            
            maxApplications = IsRepeatable 
                ? totalQuantity / applicableTier.MinQuantity
                : (totalQuantity >= applicableTier.MinQuantity ? 1 : 0);
        }

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyGradientCashDiscount(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用梯度减现促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用梯度减现促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyGradientCashDiscount(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var discountRecords = new List<GiftItem>(); // 减现记录

        var applicableTier = GetApplicableTier(cart);
        if (applicableTier == null)
            return (0m, consumedItems, discountRecords);

        for (int app = 0; app < applicationCount; app++)
        {
            // 计算本次应用的减现金额
            var currentDiscountAmount = applicableTier.DiscountAmount;
            totalDiscountAmount += currentDiscountAmount;

            // 消耗购买条件商品（记录消耗但不修改购物车）
            var requiredQuantity = IsByAmount ? 0 : applicableTier.MinQuantity;
            if (requiredQuantity > 0)
            {
                var consumed = ConsumeConditionProducts(cart, ApplicableProductIds, requiredQuantity);
                foreach (var item in consumed)
                {
                    var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == item.ProductId);
                    if (existingConsumed != null)
                    {
                        existingConsumed.Quantity += item.Quantity;
                    }
                    else
                    {
                        consumedItems.Add(item);
                    }
                }
            }

            // 创建减现记录
            var description = IsByAmount 
                ? $"梯度减现：满{applicableTier.MinAmount:C}立减{currentDiscountAmount:C}（{applicableTier.Description}，第{app + 1}次应用）"
                : $"梯度减现：满{applicableTier.MinQuantity}件立减{currentDiscountAmount:C}（{applicableTier.Description}，第{app + 1}次应用）";

            discountRecords.Add(CreateCashDiscountRecord(currentDiscountAmount, description));
        }

        // 应用减现到购物车
        var promotion = new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType
        };
        ApplyCashDiscountToCart(cart, totalDiscountAmount, promotion, ApplicableProductIds);

        return (totalDiscountAmount, consumedItems, discountRecords);
    }
}

/// <summary>
/// 梯度减现条件
/// </summary>
public class GradientDiscountTier
{
    /// <summary>
    /// 最小数量要求
    /// </summary>
    public int MinQuantity { get; set; }

    /// <summary>
    /// 最小金额要求
    /// </summary>
    public decimal MinAmount { get; set; }

    /// <summary>
    /// 减现金额
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}
