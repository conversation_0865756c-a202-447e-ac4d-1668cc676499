/**
 * 智能条件配置渲染器
 * 根据促销类型和字段名称动态生成适合的条件配置表单
 */

const ConditionConfigRenderer = {
    name: 'ConditionConfigRenderer',
    props: {
        fieldName: {
            type: String,
            required: true
        },
        fieldConfig: {
            type: Object,
            required: true
        },
        modelValue: {
            type: [Object, String, Array],
            default: () => ({})
        },
        ruleType: {
            type: String,
            required: true
        },
        disabled: {
            type: Boolean,
            default: false
        }
    },
    emits: ['update:modelValue'],
    setup(props, { emit }) {
        const { ref, computed, watch } = Vue;
        const { ElMessage } = ElementPlus;

        // 条件数据        const conditionData = ref({});
        const showAdvanced = ref(false);
        const newProduct = ref({ id: '', quantity: 1 });

        // 根据字段名称和规则类型确定条件配置结构
        const conditionConfig = computed(() => {
            const fieldName = props.fieldName.toLowerCase();
            const ruleType = props.ruleType.toLowerCase();

            if (fieldName.includes('combination')) {
                return getCombinationConditionConfig(ruleType);
            } else if (fieldName.includes('exchange')) {
                return getExchangeConditionConfig(ruleType);
            } else if (fieldName.includes('buy')) {
                return getBuyConditionConfig(ruleType);
            } else if (fieldName.includes('discount')) {
                return getDiscountConditionConfig(ruleType);
            } else {
                return getDefaultConditionConfig();
            }
        });

        // 组合条件配置
        const getCombinationConditionConfig = (ruleType) => {
            return {
                type: 'combination',
                title: '组合购买条件',
                description: '设置商品组合的购买条件',
                fields: [
                    {
                        name: 'requiredProducts',
                        label: '必需商品',
                        type: 'product-list',
                        required: true,
                        description: '客户必须购买的商品列表'
                    },
                    {
                        name: 'totalMinQuantity',
                        label: '总最小数量',
                        type: 'number',
                        min: 1,
                        description: '所有商品的总最小购买数量'
                    },
                    {
                        name: 'totalMinAmount',
                        label: '总最小金额',
                        type: 'number',
                        min: 0,
                        step: 0.01,
                        description: '所有商品的总最小购买金额'
                    },
                    {
                        name: 'mustBuyAll',
                        label: '必须购买所有商品',
                        type: 'switch',
                        default: false,
                        description: '是否必须购买列表中的所有商品'
                    }
                ]
            };
        };

        // 兑换条件配置
        const getExchangeConditionConfig = (ruleType) => {
            return {
                type: 'exchange',
                title: '兑换条件',
                description: '设置兑换优惠的条件',
                fields: [
                    {
                        name: 'exchangeItems',
                        label: '兑换商品',
                        type: 'product-list',
                        required: true,
                        description: '可以兑换的商品列表'
                    },
                    {
                        name: 'exchangeRate',
                        label: '兑换比例',
                        type: 'number',
                        min: 0,
                        max: 1,
                        step: 0.01,
                        description: '兑换折扣比例 (0-1)'
                    },
                    {
                        name: 'minExchangeAmount',
                        label: '最小兑换金额',
                        type: 'number',
                        min: 0,
                        step: 0.01,
                        description: '启动兑换的最小金额'
                    }
                ]
            };
        };

        // 购买条件配置
        const getBuyConditionConfig = (ruleType) => {
            return {
                type: 'buy',
                title: '购买条件',
                description: '设置触发优惠的购买条件',
                fields: [
                    {
                        name: 'targetProducts',
                        label: '目标商品',
                        type: 'product-list',
                        required: true,
                        description: '参与优惠的商品列表'
                    },
                    {
                        name: 'minQuantity',
                        label: '最小购买数量',
                        type: 'number',
                        min: 1,
                        description: '每个商品的最小购买数量'
                    },
                    {
                        name: 'minAmount',
                        label: '最小购买金额',
                        type: 'number',
                        min: 0,
                        step: 0.01,
                        description: '触发优惠的最小购买金额'
                    },
                    {
                        name: 'calculateBy',
                        label: '计算方式',
                        type: 'select',
                        options: [
                            { value: 'quantity', label: '按数量计算' },
                            { value: 'amount', label: '按金额计算' },
                            { value: 'both', label: '数量和金额都满足' }
                        ],
                        default: 'quantity'
                    }
                ]
            };
        };

        // 折扣条件配置
        const getDiscountConditionConfig = (ruleType) => {
            return {
                type: 'discount',
                title: '折扣条件',
                description: '设置折扣优惠的适用条件',
                fields: [
                    {
                        name: 'applicableProducts',
                        label: '适用商品',
                        type: 'product-list',
                        required: true,
                        description: '享受折扣的商品列表'
                    },
                    {
                        name: 'discountType',
                        label: '折扣类型',
                        type: 'select',
                        options: [
                            { value: 'percentage', label: '百分比折扣' },
                            { value: 'fixed', label: '固定金额折扣' },
                            { value: 'special_price', label: '特价' }
                        ],
                        default: 'percentage'
                    },
                    {
                        name: 'discountValue',
                        label: '折扣值',
                        type: 'number',
                        min: 0,
                        step: 0.01,
                        description: '折扣的具体数值'
                    },
                    {
                        name: 'maxDiscountAmount',
                        label: '最大折扣金额',
                        type: 'number',
                        min: 0,
                        step: 0.01,
                        description: '限制最大折扣金额'
                    }
                ]
            };
        };

        // 默认条件配置
        const getDefaultConditionConfig = () => {
            return {
                type: 'general',
                title: '通用条件',
                description: '设置通用的促销条件',
                fields: [
                    {
                        name: 'conditions',
                        label: '条件配置',
                        type: 'json',
                        description: '请输入JSON格式的条件配置'
                    }
                ]
            };
        };

        // 初始化数据
        const initConditionData = () => {
            if (typeof props.modelValue === 'string') {
                try {
                    conditionData.value = JSON.parse(props.modelValue) || {};
                } catch {
                    conditionData.value = {};
                }
            } else if (typeof props.modelValue === 'object') {
                conditionData.value = { ...props.modelValue } || {};
            } else {
                conditionData.value = {};
            }

            // 设置默认值
            conditionConfig.value.fields.forEach(field => {
                if (conditionData.value[field.name] === undefined && field.default !== undefined) {
                    conditionData.value[field.name] = field.default;
                }
            });
        };

        // 更新值
        const updateValue = (fieldName, value) => {
            conditionData.value[fieldName] = value;
            emit('update:modelValue', conditionData.value);
        };        // 添加商品到列表
        const addProductToList = (fieldName, productInfo) => {
            if (!conditionData.value[fieldName]) {
                conditionData.value[fieldName] = [];
            }
            conditionData.value[fieldName].push(productInfo);
            emit('update:modelValue', conditionData.value);
        };

        // 添加商品
        const addProduct = (fieldName) => {
            if (newProduct.value.id) {
                addProductToList(fieldName, {
                    productId: newProduct.value.id,
                    quantity: newProduct.value.quantity
                });
                newProduct.value = { id: '', quantity: 1 };
            }
        };

        // 从列表中移除商品
        const removeProductFromList = (fieldName, index) => {
            if (conditionData.value[fieldName]) {
                conditionData.value[fieldName].splice(index, 1);
                emit('update:modelValue', conditionData.value);
            }
        };

        // 验证配置
        const validateConfig = () => {
            const errors = [];
            conditionConfig.value.fields.forEach(field => {
                if (field.required && !conditionData.value[field.name]) {
                    errors.push(`${field.label}为必填项`);
                }
            });
            return errors;
        };

        // 监听属性变化
        watch(() => props.modelValue, () => {
            initConditionData();
        }, { immediate: true });        return {
            conditionData,
            conditionConfig,
            showAdvanced,
            newProduct,
            updateValue,
            addProduct,
            addProductToList,
            removeProductFromList,
            validateConfig
        };
    },
    template: `
        <div class="condition-config-renderer">
            <div class="condition-header">
                <div class="condition-title">
                    <el-icon><Setting /></el-icon>
                    {{ conditionConfig.title }}
                </div>
                <div class="condition-description">
                    {{ conditionConfig.description }}
                </div>
            </div>

            <div class="condition-fields">
                <el-row :gutter="16">
                    <template v-for="field in conditionConfig.fields" :key="field.name">
                        <el-col :span="field.span || 24">
                            <el-form-item 
                                :label="field.label"
                                :required="field.required"
                            >
                                <template #label>
                                    <span>{{ field.label }}</span>
                                    <el-tooltip
                                        v-if="field.description"
                                        :content="field.description"
                                        placement="top"
                                    >
                                        <el-icon style="margin-left: 4px; color: #999;">
                                            <QuestionFilled />
                                        </el-icon>
                                    </el-tooltip>
                                </template>

                                <!-- 商品列表配置 -->
                                <div v-if="field.type === 'product-list'" class="product-list-config">
                                    <div v-if="(conditionData[field.name] || []).length > 0" class="product-list">
                                        <div 
                                            v-for="(product, index) in conditionData[field.name] || []"
                                            :key="index"
                                            class="product-item"
                                        >
                                            <div class="product-info">
                                                <span class="product-id">{{ product.productId || product }}</span>
                                                <span v-if="product.quantity" class="product-quantity">
                                                    数量: {{ product.quantity }}
                                                </span>
                                            </div>
                                            <el-button 
                                                size="small" 
                                                type="danger" 
                                                circle
                                                @click="removeProductFromList(field.name, index)"
                                                :disabled="disabled"
                                            >
                                                <el-icon><Delete /></el-icon>
                                            </el-button>
                                        </div>
                                    </div>
                                    
                                    <div class="add-product">
                                        <el-input
                                            v-model="newProduct.id"
                                            placeholder="商品ID"
                                            style="width: 120px; margin-right: 8px;"
                                        />
                                        <el-input-number
                                            v-model="newProduct.quantity"
                                            placeholder="数量"
                                            :min="1"
                                            style="width: 100px; margin-right: 8px;"
                                        />
                                        <el-button 
                                            type="primary"
                                            @click="addProduct(field.name)"
                                            :disabled="!newProduct.id || disabled"
                                        >
                                            添加商品
                                        </el-button>
                                    </div>
                                </div>

                                <!-- 数字输入 -->
                                <el-input-number 
                                    v-else-if="field.type === 'number'"
                                    :model-value="conditionData[field.name]"
                                    @update:model-value="value => updateValue(field.name, value)"
                                    :min="field.min"
                                    :max="field.max"
                                    :step="field.step || 1"
                                    style="width: 100%"
                                    :disabled="disabled"
                                />

                                <!-- 选择器 -->
                                <el-select 
                                    v-else-if="field.type === 'select'"
                                    :model-value="conditionData[field.name]"
                                    @update:model-value="value => updateValue(field.name, value)"
                                    style="width: 100%"
                                    :disabled="disabled"
                                >
                                    <el-option 
                                        v-for="option in field.options"
                                        :key="option.value"
                                        :label="option.label"
                                        :value="option.value"
                                    />
                                </el-select>

                                <!-- 开关 -->
                                <el-switch 
                                    v-else-if="field.type === 'switch'"
                                    :model-value="conditionData[field.name]"
                                    @update:model-value="value => updateValue(field.name, value)"
                                    :disabled="disabled"
                                />

                                <!-- JSON编辑器 -->
                                <el-input 
                                    v-else-if="field.type === 'json'"
                                    :model-value="JSON.stringify(conditionData, null, 2)"
                                    @update:model-value="value => { try { updateValue('conditions', JSON.parse(value)); } catch {} }"
                                    type="textarea"
                                    :rows="6"
                                    style="font-family: monospace;"
                                    :disabled="disabled"
                                />

                                <!-- 默认文本输入 -->
                                <el-input 
                                    v-else
                                    :model-value="conditionData[field.name]"
                                    @update:model-value="value => updateValue(field.name, value)"
                                    :disabled="disabled"
                                />
                            </el-form-item>
                        </el-col>
                    </template>
                </el-row>
            </div>

            <div class="condition-preview">
                <el-divider>配置预览</el-divider>
                <el-input 
                    type="textarea"
                    :model-value="JSON.stringify(conditionData, null, 2)"
                    readonly
                    :rows="4"
                    style="font-family: monospace; font-size: 12px;"
                />            </div>
        </div>
    `
};

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ConditionConfigRenderer;
} else if (typeof window !== 'undefined') {
    window.ConditionConfigRenderer = ConditionConfigRenderer;
}
