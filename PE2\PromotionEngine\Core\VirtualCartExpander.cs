using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Rules;

namespace PE2.PromotionEngine.Core;


public enum ReservationType
{
    /// <summary>
    /// 条件占用（如买2送1中的"买2"）
    /// </summary>
    Condition,

    /// <summary>
    /// 执行占用（如买2送1中的"送1"）
    /// </summary>
    Execution
}

/// <summary>
/// 虚拟购物车展开器 - 在促销计算时按需展开为单实例，计算完成后重新聚合
/// </summary>
public class VirtualCartExpander
{
    /// <summary>
    /// 虚拟商品实例
    /// </summary>
    public class VirtualCartInstance
    {
        public string InstanceId { get; set; } = Guid.NewGuid().ToString();
        public string ProductId { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public decimal UnitPrice { get; set; }
        public Product Product { get; set; } = new();

        // 促销相关状态
        public PromotionState State { get; set; } = PromotionState.Available;
        public string? ReservedByRuleId { get; set; }
        public ReservationType? ReservationType { get; set; }
        public decimal ActualUnitPrice { get; set; }

        // 原始来源信息
        public CartItem SourceCartItem { get; set; } = new();
        public int SourceIndex { get; set; } // 在原CartItem中的索引位置
    }

    /// <summary>
    /// 商品实例状态
    /// </summary>
    public enum PromotionState
    {
        Available, // 可用
        ConditionUsed, // 被用作条件
        ExecutionUsed, // 被用作执行
        Excluded // 被排除
    }

    /// <summary>
    /// 将购物车展开为虚拟实例列表
    /// </summary>
    public List<VirtualCartInstance> ExpandCart(ShoppingCart cart)
    {
        var instances = new List<VirtualCartInstance>();

        foreach (var cartItem in cart.Items)
        {
            for (int i = 0; i < cartItem.Quantity; i++)
            {
                instances.Add(
                    new VirtualCartInstance
                    {
                        ProductId = cartItem.Product.Id,
                        ProductName = cartItem.Product.Name,
                        UnitPrice = cartItem.UnitPrice,
                        ActualUnitPrice = cartItem.UnitPrice,
                        Product = cartItem.Product,
                        SourceCartItem = cartItem,
                        SourceIndex = i,
                        State = PromotionState.Available
                    }
                );
            }
        }

        return instances;
    }

    /// <summary>
    /// 将虚拟实例重新聚合回购物车
    /// </summary>
    public ShoppingCart CollapseToCart(
        List<VirtualCartInstance> instances,
        ShoppingCart originalCart
    )
    {
        var newCart = new ShoppingCart
        {
            Id = originalCart.Id,
            CustomerId = originalCart.CustomerId,
            MemberId = originalCart.MemberId,
            CreatedAt = originalCart.CreatedAt,
            UpdatedAt = DateTime.Now
        };

        // 按商品ID、单价、实际单价、是否赠品等维度分组
        var groupedInstances = instances
            .GroupBy(x => new
            {
                x.ProductId,
                x.UnitPrice,
                x.ActualUnitPrice,
                IsGift = x.State == PromotionState.ExecutionUsed && x.ActualUnitPrice == 0
            })
            .ToList();

        foreach (var group in groupedInstances)
        {
            var firstInstance = group.First();
            var quantity = group.Count();

            var cartItem = new CartItem
            {
                Product = firstInstance.Product,
                Quantity = quantity,
                UnitPrice = firstInstance.UnitPrice,
                ActualUnitPrice = firstInstance.ActualUnitPrice,
                IsGift = group.Key.IsGift
            };

            // 聚合促销详情
            AggregatePromotionDetails(cartItem, group.ToList());

            newCart.Items.Add(cartItem);
        }

        return newCart;
    }

    /// <summary>
    /// 为指定商品实例预留促销使用
    /// </summary>
    public bool TryReserveInstances(
        List<VirtualCartInstance> instances,
        string ruleId,
        List<string> productIds,
        int requiredQuantity,
        ReservationType reservationType,
        BenefitSelectionStrategy strategy = BenefitSelectionStrategy.CustomerBenefit
    )
    {
        // 筛选可用的实例
        var availableInstances = instances
            .Where(x => productIds.Contains(x.ProductId) && x.State == PromotionState.Available)
            .ToList();

        if (availableInstances.Sum(x => 1) < requiredQuantity)
            return false;

        // 根据策略排序
        var sortedInstances =
            strategy == BenefitSelectionStrategy.CustomerBenefit
                ? availableInstances.OrderByDescending(x => x.UnitPrice).ToList()
                : availableInstances.OrderBy(x => x.UnitPrice).ToList();

        // 预留实例
        var reserved = 0;
        foreach (var instance in sortedInstances)
        {
            if (reserved >= requiredQuantity)
                break;

            instance.State =
                reservationType == ReservationType.Condition
                    ? PromotionState.ConditionUsed
                    : PromotionState.ExecutionUsed;
            instance.ReservedByRuleId = ruleId;
            instance.ReservationType = reservationType;

            reserved++;
        }

        return reserved == requiredQuantity;
    }

    /// <summary>
    /// 释放指定规则的所有预留
    /// </summary>
    public void ReleaseReservations(List<VirtualCartInstance> instances, string ruleId)
    {
        foreach (var instance in instances.Where(x => x.ReservedByRuleId == ruleId))
        {
            instance.State = PromotionState.Available;
            instance.ReservedByRuleId = null;
            instance.ReservationType = null;
            instance.ActualUnitPrice = instance.UnitPrice; // 恢复原价
        }
    }

    /// <summary>
    /// 应用促销效果到实例
    /// </summary>
    public void ApplyPromotionEffect(
        List<VirtualCartInstance> instances,
        string ruleId,
        decimal newPrice,
        List<VirtualCartInstance> targetInstances
    )
    {
        foreach (var instance in targetInstances)
        {
            instance.ActualUnitPrice = newPrice;
        }
    }

    /// <summary>
    /// 获取被指定规则预留的实例
    /// </summary>
    public List<VirtualCartInstance> GetReservedInstances(
        List<VirtualCartInstance> instances,
        string ruleId,
        ReservationType? reservationType = null
    )
    {
        return instances
            .Where(x =>
                x.ReservedByRuleId == ruleId
                && (reservationType == null || x.ReservationType == reservationType)
            )
            .ToList();
    }

    /// <summary>
    /// 获取可用实例数量
    /// </summary>
    public int GetAvailableCount(List<VirtualCartInstance> instances, string productId)
    {
        return instances.Count(x =>
            x.ProductId == productId && x.State == PromotionState.Available
        );
    }

    /// <summary>
    /// 聚合促销详情
    /// </summary>
    private void AggregatePromotionDetails(CartItem cartItem, List<VirtualCartInstance> instances)
    {
        // 从实例中提取促销信息并聚合到CartItem
        var ruleIds = instances
            .Where(x => !string.IsNullOrEmpty(x.ReservedByRuleId))
            .Select(x => x.ReservedByRuleId!)
            .Distinct()
            .ToList();

        cartItem.AppliedPromotionRuleIds.AddRange(ruleIds);

        // 这里可以根据需要添加更详细的促销详情聚合逻辑
    }
}

/// <summary>
/// 增强的促销规则基类 - 使用虚拟展开模式
/// </summary>
public abstract class VirtualPromotionRuleBase : PromotionRuleBase
{
    protected readonly VirtualCartExpander CartExpander = new();

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            // 1. 展开购物车为虚拟实例
            var instances = CartExpander.ExpandCart(cart);

            // 2. 执行虚拟促销计算
            var result = ApplyVirtualPromotion(instances, applicationCount);

            // 3. 将结果聚合回原购物车
            if (result.Success)
            {
                var newCart = CartExpander.CollapseToCart(instances, cart);

                // 更新原购物车
                cart.Items.Clear();
                cart.Items.AddRange(newCart.Items);
                cart.UpdatedAt = DateTime.Now;

                application.DiscountAmount = result.DiscountAmount;
                application.ConsumedItems = result.ConsumedItems;
                application.GiftItems = result.GiftItems;
                application.IsSuccessful = true;
            }
            else
            {
                application.IsSuccessful = false;
                application.ErrorMessage = result.ErrorMessage;
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = $"促销应用失败: {ex.Message}";
        }

        return application;
    }

    /// <summary>
    /// 虚拟促销计算（由子类实现）
    /// </summary>
    protected abstract VirtualPromotionResult ApplyVirtualPromotion(
        List<VirtualCartExpander.VirtualCartInstance> instances,
        int applicationCount
    );
}

/// <summary>
/// 虚拟促销结果
/// </summary>
public class VirtualPromotionResult
{
    public bool Success { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
    public decimal DiscountAmount { get; set; }
    public List<ConsumedItem> ConsumedItems { get; set; } = new();
    public List<GiftItem> GiftItems { get; set; } = new();
}
