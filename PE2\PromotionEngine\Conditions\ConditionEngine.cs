global using Microsoft.Extensions.Logging;
global using Microsoft.Extensions.Caching.Memory;
using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Observability;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Linq.Expressions;

namespace PE2.PromotionEngine.Conditions;

/// <summary>
/// 条件引擎实现 - 统一化条件验证框架
/// </summary>
public sealed class ConditionEngine : IConditionEngine, IDisposable
{
    private readonly ILogger<ConditionEngine> _logger;
    private readonly IObservabilityEngine _observability;
    private readonly IMemoryCache _conditionCache;
    private readonly ConcurrentDictionary<string, CompiledCondition> _compiledConditions;
    private readonly Dictionary<ConditionType, IConditionValidator> _validators;
    private readonly SemaphoreSlim _compilationSemaphore;
    private readonly Timer _cacheCleanupTimer;
    private bool _disposed;

    public ConditionEngine(
        ILogger<ConditionEngine> logger,
        IObservabilityEngine observability,
        IMemoryCache memoryCache)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _observability = observability ?? throw new ArgumentNullException(nameof(observability));
        _conditionCache = memoryCache ?? throw new ArgumentNullException(nameof(memoryCache));
        _compiledConditions = new ConcurrentDictionary<string, CompiledCondition>();
        _validators = InitializeValidators();
        _compilationSemaphore = new SemaphoreSlim(1, 1);

        // 每10分钟清理一次缓存
        _cacheCleanupTimer = new Timer(CleanupCache, null, TimeSpan.FromMinutes(10), TimeSpan.FromMinutes(10));

        _logger.LogInformation("条件引擎已初始化");
    }

    public async Task<ConditionResult> ValidateAsync(ConditionExpression expression, ShoppingCart cart)
    {
        var stopwatch = Stopwatch.StartNew();
        var traceId = Guid.NewGuid().ToString();

        try
        {
            _observability.TrackConditionValidation(traceId, expression.Id, "开始验证条件");

            // 检查缓存
            var cacheKey = GenerateCacheKey(expression, cart);
            if (expression.IsCacheable && _conditionCache.TryGetValue(cacheKey, out ConditionResult? cachedResult))
            {
                _observability.TrackConditionValidation(traceId, expression.Id, "使用缓存结果");
                return cachedResult;
            }

            // 使用编译后的条件（如果存在）
            if (_compiledConditions.TryGetValue(expression.Id, out var compiled) && compiled.IsValid)
            {
                var result = compiled.ValidationFunction(cart);
                result.ValidationTimeMs = stopwatch.ElapsedMilliseconds;
                
                if (expression.IsCacheable)
                {
                    _conditionCache.Set(cacheKey, result, TimeSpan.FromMinutes(5));
                }

                _observability.TrackConditionValidation(traceId, expression.Id, "使用编译条件验证完成", result);
                return result;
            }

            // 动态验证
            var validationResult = await ValidateDynamicAsync(expression, cart, traceId).ConfigureAwait(false);
            validationResult.ValidationTimeMs = stopwatch.ElapsedMilliseconds;

            if (expression.IsCacheable)
            {
                _conditionCache.Set(cacheKey, validationResult, TimeSpan.FromMinutes(5));
            }

            _observability.TrackConditionValidation(traceId, expression.Id, "动态验证完成", validationResult);
            return validationResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "条件验证失败 ConditionId: {ConditionId}", expression.Id);
            _observability.TrackConditionValidation(traceId, expression.Id, $"验证失败: {ex.Message}", ex);
            return new ConditionResult
            {
                IsSatisfied = false,
                Gap = $"验证异常: {ex.Message}",
                ValidationTimeMs = stopwatch.ElapsedMilliseconds
            };
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public async Task<Dictionary<string, ConditionResult>> ValidateBatchAsync(
        Dictionary<string, ConditionExpression> expressions, 
        ShoppingCart cart)
    {
        var tasks = expressions.Select(async kvp => new
        {
            Key = kvp.Key,
            Result = await ValidateAsync(kvp.Value, cart)
        });

        var results = await Task.WhenAll(tasks);
        return results.ToDictionary(r => r.Key, r => r.Result);
    }

    public async Task<ConditionAnalysis> AnalyzeAsync(ConditionExpression expression, ShoppingCart cart)
    {
        var analysis = new ConditionAnalysis();

        // 计算复杂度
        analysis.ComplexityScore = CalculateComplexity(expression);

        // 预估验证时间
        analysis.EstimatedValidationTimeMs = EstimateValidationTime(expression);

        // 生成优化建议
        analysis.OptimizationSuggestions = GenerateOptimizationSuggestions(expression);

        // 分析依赖字段
        analysis.RequiredDataFields = ExtractRequiredFields(expression);

        return analysis;
    }

    public CompiledCondition CompileCondition(ConditionExpression expression)
    {
        try
        {
            var compiled = new CompiledCondition
            {
                Id = expression.Id,
                CompiledAt = DateTime.Now,
                ValidationFunction = BuildValidationFunction(expression),
                IsValid = true
            };

            _compiledConditions.AddOrUpdate(expression.Id, compiled, (key, old) => compiled);
            return compiled;
        }
        catch (Exception ex)
        {
            _observability.TrackConditionValidation(Guid.NewGuid().ToString(), expression.Id, 
                $"编译失败: {ex.Message}", ex);
            
            return new CompiledCondition
            {
                Id = expression.Id,
                IsValid = false
            };
        }
    }

    private async Task<ConditionResult> ValidateDynamicAsync(ConditionExpression expression, ShoppingCart cart, string traceId)
    {
        // 处理组合条件
        if (expression.SubConditions.Any())
        {
            return await ValidateCompositeCondition(expression, cart, traceId);
        }

        // 处理单一条件
        if (_validators.TryGetValue(expression.Type, out var validator))
        {
            return await validator.ValidateAsync(expression, cart);
        }

        return new ConditionResult
        {
            IsSatisfied = false,
            Gap = $"不支持的条件类型: {expression.Type}"
        };
    }

    private async Task<ConditionResult> ValidateCompositeCondition(ConditionExpression expression, ShoppingCart cart, string traceId)
    {
        var subResults = new Dictionary<string, ConditionResult>();
        var allSatisfied = true;
        var anySatisfied = false;

        foreach (var subCondition in expression.SubConditions)
        {
            var subResult = await ValidateAsync(subCondition, cart);
            subResults[subCondition.Id] = subResult;

            if (subResult.IsSatisfied)
            {
                anySatisfied = true;
            }
            else
            {
                allSatisfied = false;
            }
        }

        var result = new ConditionResult
        {
            SubResults = subResults
        };

        switch (expression.Operator)
        {
            case LogicalOperator.And:
                result.IsSatisfied = allSatisfied;
                result.SatisfactionPercentage = subResults.Values.Average(r => r.SatisfactionPercentage);
                break;
            case LogicalOperator.Or:
                result.IsSatisfied = anySatisfied;
                result.SatisfactionPercentage = subResults.Values.Max(r => r.SatisfactionPercentage);
                break;
            case LogicalOperator.Not:
                var firstResult = subResults.Values.FirstOrDefault();
                result.IsSatisfied = firstResult != null && !firstResult.IsSatisfied;
                result.SatisfactionPercentage = firstResult != null ? 100 - firstResult.SatisfactionPercentage : 0;
                break;
        }

        return result;
    }

    private Dictionary<ConditionType, IConditionValidator> InitializeValidators()
    {
        return new Dictionary<ConditionType, IConditionValidator>
        {
            { ConditionType.ProductQuantity, new ProductQuantityValidator() },
            { ConditionType.ProductAmount, new ProductAmountValidator() },
            { ConditionType.CategoryQuantity, new CategoryQuantityValidator() },
            { ConditionType.CategoryAmount, new CategoryAmountValidator() },
            { ConditionType.TotalQuantity, new TotalQuantityValidator() },
            { ConditionType.TotalAmount, new TotalAmountValidator() },
            { ConditionType.MemberLevel, new MemberLevelValidator() },
            { ConditionType.TimeRange, new TimeRangeValidator() }
        };
    }

    private Func<ShoppingCart, ConditionResult> BuildValidationFunction(ConditionExpression expression)
    {
        // 这里可以使用表达式树或动态编译来构建高性能的验证函数
        // 为简化示例，这里返回一个基本的验证函数
        return cart => ValidateDynamicAsync(expression, cart, Guid.NewGuid().ToString()).Result;
    }

    private string GenerateCacheKey(ConditionExpression expression, ShoppingCart cart)
    {
        // 生成基于条件和购物车状态的缓存键
        var cartHash = $"{cart.TotalQuantity}_{cart.TotalAmount}_{string.Join(",", cart.Items.Select(i => $"{i.Product.Id}:{i.Quantity}"))}";
        return $"{expression.Id}_{cartHash.GetHashCode()}";
    }

    private int CalculateComplexity(ConditionExpression expression)
    {
        var complexity = 1;
        complexity += expression.SubConditions.Sum(sub => CalculateComplexity(sub));
        complexity += expression.Parameters.Count;
        return complexity;
    }

    private long EstimateValidationTime(ConditionExpression expression)
    {
        // 基于条件复杂度估算验证时间
        var baseTime = 1L; // 1ms基础时间
        var complexity = CalculateComplexity(expression);
        return baseTime * complexity;
    }

    private List<string> GenerateOptimizationSuggestions(ConditionExpression expression)
    {
        var suggestions = new List<string>();

        if (expression.SubConditions.Count > 5)
        {
            suggestions.Add("考虑简化条件组合，减少子条件数量");
        }

        if (!expression.IsCacheable)
        {
            suggestions.Add("考虑启用条件缓存以提高性能");
        }

        return suggestions;
    }

    private List<string> ExtractRequiredFields(ConditionExpression expression)
    {
        var fields = new List<string>();

        switch (expression.Type)
        {
            case ConditionType.ProductQuantity:
            case ConditionType.ProductAmount:
                fields.Add("Items");
                break;
            case ConditionType.TotalQuantity:
                fields.Add("TotalQuantity");
                break;
            case ConditionType.TotalAmount:
                fields.Add("TotalAmount");
                break;
            case ConditionType.MemberLevel:
                fields.Add("MemberId");
                break;
        }

        foreach (var subCondition in expression.SubConditions)
        {
            fields.AddRange(ExtractRequiredFields(subCondition));
        }

        return fields.Distinct().ToList();
    }

    private void CleanupCache(object? state)
    {
        try
        {
            // IMemoryCache 会自动处理过期项，这里可以添加自定义清理逻辑
            _logger.LogDebug("执行条件缓存清理");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理条件缓存时发生错误");
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _cacheCleanupTimer?.Dispose();
        _compilationSemaphore?.Dispose();
        _disposed = true;

        _logger.LogInformation("条件引擎已释放");
    }
}
