// 动态表单组件
const DynamicForm = {
  props: {
    fields: Array,
    modelValue: Object,
    ruleType: String
  },
  emits: ['update:modelValue', 'form-changed'],
  template: `
    <div class="dynamic-form" v-if="fields && fields.length">
      <div class="form-section">
        <div class="section-title">基础信息</div>
        <div class="form-row" v-for="field in basicFields" :key="field.name">
          <div class="form-col">
            <form-field 
              :field="field"
              :value="formData[field.name]"
              @update:value="updateField(field.name, $event)"
            />
          </div>
        </div>
      </div>

      <div v-if="conditionFields.length" class="form-section">
        <div class="section-title">条件配置</div>
        <div v-for="field in conditionFields" :key="field.name">
          <array-field 
            :field="field"
            :value="formData[field.name] || []"
            @update:value="updateField(field.name, $event)"
          />
        </div>
      </div>

      <div class="form-section">
        <div class="section-title">高级设置</div>
        <div class="form-row">
          <div class="form-col">
            <form-field 
              :field="{ name: 'applicableCustomerTypes', label: '适用客户类型', type: 'tags' }"
              :value="formData.applicableCustomerTypes || []"
              @update:value="updateField('applicableCustomerTypes', $event)"
            />
          </div>
          <div class="form-col">
            <form-field 
              :field="{ name: 'exclusiveRuleIds', label: '互斥规则ID', type: 'tags' }"
              :value="formData.exclusiveRuleIds || []"
              @update:value="updateField('exclusiveRuleIds', $event)"
            />
          </div>
        </div>
        <div class="form-row">
          <div class="form-col">
            <form-field 
              :field="{ name: 'canStackWithOthers', label: '可与其他规则叠加', type: 'switch', default: false }"
              :value="formData.canStackWithOthers"
              @update:value="updateField('canStackWithOthers', $event)"
            />
          </div>
          <div class="form-col">
            <form-field 
              :field="{ name: 'productExclusivity', label: '商品独占性', type: 'select', options: [
                { label: '独占', value: 'Exclusive' },
                { label: '共享', value: 'Shared' },
                { label: '部分独占', value: 'Partial' }
              ] }"
              :value="formData.productExclusivity || 'Exclusive'"
              @update:value="updateField('productExclusivity', $event)"
            />
          </div>
        </div>
      </div>
    </div>
  `,
  computed: {
    basicFields() {
      return this.fields.filter(field => 
        ['id', 'name', 'description', 'priority', 'isEnabled', 'startTime', 'endTime', 'isRepeatable', 'maxApplications'].includes(field.name)
      );
    },
    conditionFields() {
      return this.fields.filter(field => field.type === 'array');
    },
    formData() {
      return this.modelValue || {};
    }
  },
  watch: {
    ruleType: {
      immediate: true,
      handler(newType) {
        if (newType && (!this.formData.$type || this.formData.$type !== newType)) {
          this.updateField('$type', newType);
        }
      }
    }
  },
  methods: {
    updateField(name, value) {
      const newData = { ...this.formData, [name]: value };
      this.$emit('update:modelValue', newData);
      this.$emit('form-changed', newData);
    }
  }
};

// 单个表单字段组件
const FormField = {
  props: {
    field: Object,
    value: [String, Number, Boolean, Array]
  },
  emits: ['update:value'],
  template: `
    <div class="form-item">
      <label>
        {{ field.label }}
        <span v-if="field.required" style="color: red;">*</span>
      </label>
      
      <!-- 输入框 -->
      <input 
        v-if="field.type === 'input'"
        type="text"
        :value="value || ''"
        :placeholder="field.placeholder"
        @input="$emit('update:value', $event.target.value)"
      />
      
      <!-- 数字输入框 -->
      <input 
        v-else-if="field.type === 'number'"
        type="number"
        :value="value || field.default || 0"
        :step="field.step || 1"
        @input="$emit('update:value', Number($event.target.value))"
      />
      
      <!-- 文本域 -->
      <textarea 
        v-else-if="field.type === 'textarea'"
        :value="value || ''"
        :placeholder="field.placeholder"
        rows="3"
        @input="$emit('update:value', $event.target.value)"
      ></textarea>
      
      <!-- 开关 -->
      <label v-else-if="field.type === 'switch'" class="switch-label">
        <input 
          type="checkbox"
          :checked="value !== undefined ? value : field.default"
          @change="$emit('update:value', $event.target.checked)"
        />
        <span class="switch-text">{{ value ? '是' : '否' }}</span>
      </label>
      
      <!-- 日期时间 -->
      <input 
        v-else-if="field.type === 'datetime'"
        type="datetime-local"
        :value="formatDateTime(value)"
        @input="$emit('update:value', $event.target.value)"
      />
      
      <!-- 选择框 -->
      <select 
        v-else-if="field.type === 'select'"
        :value="value"
        @change="$emit('update:value', $event.target.value)"
      >
        <option value="">请选择...</option>
        <option 
          v-for="option in field.options" 
          :key="option.value"
          :value="option.value"
        >
          {{ option.label }}
        </option>
      </select>
        <!-- 标签输入 -->
      <tags-input 
        v-else-if="field.type === 'tags'"
        :value="value || []"
        :placeholder="field.placeholder || '请输入并按回车添加'"
        @update:value="$emit('update:value', $event)"
      />
      
      <!-- 商品选择器 -->
      <product-selector 
        v-else-if="field.type === 'product-selector'"
        :value="value || []"
        :multiple="true"
        :placeholder="field.placeholder || '请选择商品'"
        @update:value="$emit('update:value', $event)"
      />
    </div>
  `,
  methods: {
    formatDateTime(value) {
      if (!value) return '';
      const date = new Date(value);
      return date.toISOString().slice(0, 16);
    }
  }
};

// 数组字段组件
const ArrayField = {
  props: {
    field: Object,
    value: Array
  },
  emits: ['update:value'],
  template: `
    <div class="array-field">
      <div class="array-field-header">
        <div class="array-field-title">{{ field.label }}</div>
        <button type="button" class="add-btn" @click="addItem">添加{{ field.label }}</button>
      </div>
      
      <div v-if="!items.length" class="empty-state">
        <p>暂无{{ field.label }}，点击上方按钮添加</p>
      </div>
      
      <div v-for="(item, index) in items" :key="index" class="array-item">
        <div class="array-item-header">
          <div class="array-item-title">{{ field.label }} #{{ index + 1 }}</div>
          <button type="button" class="remove-btn" @click="removeItem(index)">删除</button>
        </div>
        
        <div class="form-row" v-for="subField in field.fields" :key="subField.name">
          <div class="form-col">
            <form-field 
              :field="subField"
              :value="item[subField.name]"
              @update:value="updateItem(index, subField.name, $event)"
            />
          </div>
        </div>
      </div>
    </div>
  `,
  computed: {
    items() {
      return this.value || [];
    }
  },
  methods: {    addItem() {
      const newItem = {};
      this.field.fields.forEach(subField => {
        if (subField.type === 'product-selector') {
          newItem[subField.name] = [];
        } else if (subField.type === 'tags') {
          newItem[subField.name] = [];
        } else if (subField.type === 'number') {
          newItem[subField.name] = subField.default || 0;
        } else if (subField.type === 'switch') {
          newItem[subField.name] = subField.default !== undefined ? subField.default : false;
        } else {
          newItem[subField.name] = subField.default || '';
        }
      });
      
      const newItems = [...this.items, newItem];
      this.$emit('update:value', newItems);
    },
    
    removeItem(index) {
      const newItems = this.items.filter((_, i) => i !== index);
      this.$emit('update:value', newItems);
    },
    
    updateItem(index, fieldName, value) {
      const newItems = [...this.items];
      newItems[index] = { ...newItems[index], [fieldName]: value };
      this.$emit('update:value', newItems);
    }
  }
};

// 标签输入组件
const TagsInput = {
  props: {
    value: Array,
    placeholder: String
  },
  emits: ['update:value'],
  template: `
    <div class="tags-input">
      <div class="tags-container">
        <span 
          v-for="(tag, index) in tags" 
          :key="index"
          class="tag"
        >
          {{ tag }}
          <button type="button" class="tag-remove" @click="removeTag(index)">×</button>
        </span>
        <input 
          type="text"
          v-model="inputValue"
          :placeholder="placeholder"
          @keydown.enter.prevent="addTag"
          @keydown.backspace="handleBackspace"
          class="tag-input"
        />
      </div>
    </div>
  `,
  data() {
    return {
      inputValue: ''
    };
  },
  computed: {
    tags() {
      return this.value || [];
    }
  },
  methods: {
    addTag() {
      const value = this.inputValue.trim();
      if (value && !this.tags.includes(value)) {
        const newTags = [...this.tags, value];
        this.$emit('update:value', newTags);
        this.inputValue = '';
      }
    },
    
    removeTag(index) {
      const newTags = this.tags.filter((_, i) => i !== index);
      this.$emit('update:value', newTags);
    },
    
    handleBackspace() {
      if (this.inputValue === '' && this.tags.length > 0) {
        this.removeTag(this.tags.length - 1);
      }
    }
  }
};
