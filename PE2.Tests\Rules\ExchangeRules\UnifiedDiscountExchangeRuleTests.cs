using PE2.Models;
using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.ExchangeRules;

/// <summary>
/// 统一打折换购规则测试
/// 测试场景：购买A商品大于等于1件时，可以0.9折换购B商品
/// </summary>
public class UnifiedDiscountExchangeRuleTests(ITestOutputHelper output) : TestBase(output)
{
    #region 基本功能测试

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "BasicFunctionality")]
    public async Task Apply_BasicDiscountExchange_ShouldApplyCorrectly()
    {
        // Arrange - 创建基本打折换购规则：买A享B商品9折
        var rule = TestDataGenerator.CreateUnifiedDiscountExchangeRule_BuyA_90Percent_ExchangeB();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            new[]
            {
                (TestDataGenerator.CreateProductA(), 1), // A商品50元
                (TestDataGenerator.CreateProductB(), 1) // B商品30元
            }
        );

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.True(result.TotalDiscount > 0);
        Assert.Single(result.AppliedPromotions);

        var appliedPromotion = result.AppliedPromotions.First();
        Assert.Equal("EXCHANGE_DISCOUNT_001", appliedPromotion.RuleId);
        Assert.Equal(3.00m, appliedPromotion.DiscountAmount); // B商品30元*0.1=3元优惠

        // 验证B商品实际支付价格为27元（30*0.9）
        var productBItem = cart.Items.First(x => x.Product.Id == "B");
        Assert.Equal(27.00m, productBItem.ActualUnitPrice);

        Output.WriteLine($"基本打折换购测试 - 优惠金额: {appliedPromotion.DiscountAmount}元");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "QuantityBased")]
    public async Task Apply_QuantityBasedCondition_ShouldApplyCorrectly()
    {
        // Arrange - 创建基于数量的打折换购规则
        var rule = TestDataGenerator.CreateUnifiedDiscountExchangeRule_QuantityBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            new[]
            {
                (TestDataGenerator.CreateProductA(), 2), // 满足数量条件
                (TestDataGenerator.CreateProductB(), 1)
            }
        );
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.True(result.TotalDiscount > 0);
        Assert.Single(result.AppliedPromotions);

        var appliedPromotion = result.AppliedPromotions.First();
        Assert.True(appliedPromotion.DiscountAmount > 0);

        Output.WriteLine($"基于数量的打折换购测试 - 优惠金额: {appliedPromotion.DiscountAmount}元");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "AmountBased")]
    public async Task Apply_AmountBasedCondition_ShouldApplyCorrectly()
    {
        // Arrange - 创建基于金额的打折换购规则
        var rule = TestDataGenerator.CreateUnifiedDiscountExchangeRule_AmountBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            new[]
            {
                (TestDataGenerator.CreateProductA(), 3), // 150元，满足金额条件
                (TestDataGenerator.CreateProductC(), 1)
            }
        );
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.True(result.TotalDiscount > 0);
        Assert.Single(result.AppliedPromotions);

        var appliedPromotion = result.AppliedPromotions.First();
        Assert.True(appliedPromotion.DiscountAmount > 0);

        Output.WriteLine($"基于金额的打折换购测试 - 优惠金额: {appliedPromotion.DiscountAmount}元");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "DifferentDiscountRates")]
    public async Task Apply_DifferentDiscountRates_ShouldApplyCorrectly()
    {
        // Arrange - 测试不同折扣率
        var rule = TestDataGenerator.CreateUnifiedDiscountExchangeRule_50Percent();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            new[]
            {
                (TestDataGenerator.CreateProductA(), 1),
                (TestDataGenerator.CreateProductB(), 1) // 30元商品
            }
        );
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.True(result.TotalDiscount > 0);
        Assert.Single(result.AppliedPromotions);

        var appliedPromotion = result.AppliedPromotions.First();
        Assert.Equal(15.00m, appliedPromotion.DiscountAmount); // 30元*0.5=15元优惠

        var productBItem = cart.Items.First(x => x.Product.Id == "B");
        Assert.Equal(15.00m, productBItem.ActualUnitPrice); // 30*0.5=15元

        Output.WriteLine($"不同折扣率测试 - 优惠金额: {appliedPromotion.DiscountAmount}元");
    }

    #endregion

    #region 条件不满足测试

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "ConditionNotMet")]
    public async Task Apply_InsufficientBuyQuantity_ShouldNotApply()
    {
        // Arrange - 购买数量不足
        var rule = TestDataGenerator.CreateUnifiedDiscountExchangeRule_BuyA_90Percent_ExchangeB();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            new[]
            {
                (TestDataGenerator.CreateProductB(), 1) // 只有B商品，没有A商品
            }
        );
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.Equal(0, result.TotalDiscount);
        Assert.Empty(result.AppliedPromotions);

        Output.WriteLine("购买数量不足测试 - 无优惠应用");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "ConditionNotMet")]
    public async Task Apply_NoExchangeProductInCart_ShouldNotApply()
    {
        // Arrange - 购物车中没有换购商品
        var rule = TestDataGenerator.CreateUnifiedDiscountExchangeRule_BuyA_90Percent_ExchangeB();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            new[]
            {
                (TestDataGenerator.CreateProductA(), 1) // 只有A商品，没有B商品
            }
        );
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.Equal(0, result.TotalDiscount);
        Assert.Empty(result.AppliedPromotions);

        Output.WriteLine("无换购商品测试 - 无优惠应用");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "ConditionNotMet")]
    public async Task Apply_InsufficientBuyAmount_ShouldNotApply()
    {
        // Arrange - 购买金额不足
        var rule = TestDataGenerator.CreateUnifiedDiscountExchangeRule_AmountBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            new[]
            {
                (TestDataGenerator.CreateProductA(), 1), // 50元，不满足100元条件
                (TestDataGenerator.CreateProductC(), 1)
            }
        );
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.Equal(0, result.TotalDiscount);
        Assert.Empty(result.AppliedPromotions);

        Output.WriteLine("购买金额不足测试 - 无优惠应用");
    }

    #endregion

    #region 可重复性测试

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "Repeatability")]
    public async Task Apply_RepeatableRule_ShouldApplyMultipleTimes()
    {
        // Arrange - 可重复的打折换购规则
        var rule = TestDataGenerator.CreateUnifiedDiscountExchangeRule_Repeatable();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            new[]
            {
                (TestDataGenerator.CreateProductA(), 3), // 3件A商品
                (TestDataGenerator.CreateProductB(), 3) // 3件B商品可换购
            }
        );
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.True(result.TotalDiscount > 0);
        Assert.NotEmpty(result.AppliedPromotions);

        // 验证多次应用
        var maxApplications = rule.CalculateMaxApplications(cart);
        Assert.True(maxApplications > 1, "可重复规则应该能应用多次");

        Output.WriteLine($"可重复规则测试 - 最大应用次数: {maxApplications}");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "Repeatability")]
    public async Task Apply_NonRepeatableRule_ShouldApplyOnlyOnce()
    {
        // Arrange - 不可重复的打折换购规则
        var rule = TestDataGenerator.CreateUnifiedDiscountExchangeRule_NonRepeatable();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            new[]
            {
                (TestDataGenerator.CreateProductA(), 5), // 足够多的A商品
                (TestDataGenerator.CreateProductB(), 5) // 足够多的B商品
            }
        );
        TestPromotionRuleService.Rules = [rule];
        // Act
        var maxApplications = rule.CalculateMaxApplications(cart);

        // Assert
        Assert.Equal(1, maxApplications);

        Output.WriteLine($"不可重复规则测试 - 最大应用次数: {maxApplications}");
    }

    #endregion

    #region 利益最大化测试

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "CustomerBenefit")]
    public async Task Apply_CustomerBenefitMaximization_ShouldSelectHighValueProducts()
    {
        // Arrange - 客户利益最大化：应选择高价商品进行换购
        var rule = TestDataGenerator.CreateUnifiedDiscountExchangeRule_CustomerBenefit();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            new[]
            {
                (TestDataGenerator.CreateProductA(), 1),
                (TestDataGenerator.CreateProductB(), 1), // 30元
                (TestDataGenerator.CreateProductD(), 1) // 100元
            }
        );
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.True(result.TotalDiscount > 0);
        Assert.Single(result.AppliedPromotions);

        // 验证选择了高价商品D进行换购
        var productDItem = cart.Items.First(x => x.Product.Id == "D");
        Assert.True(productDItem.ActualUnitPrice < productDItem.UnitPrice, "高价商品应被选为换购商品");

        var productBItem = cart.Items.First(x => x.Product.Id == "B");
        AssertAmountEqual(productBItem.UnitPrice, productBItem.ActualUnitPrice, "低价商品不应被选为换购商品");

        Output.WriteLine($"客户利益最大化测试 - 选择了价值{productDItem.UnitPrice}元的商品进行换购");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "MerchantBenefit")]
    public async Task Apply_MerchantBenefitMaximization_ShouldSelectLowValueProducts()
    {
        // Arrange - 商户利益最大化：应选择低价商品进行换购
        var rule = TestDataGenerator.CreateUnifiedDiscountExchangeRule_MerchantBenefit();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            new[]
            {
                (TestDataGenerator.CreateProductA(), 1),
                (TestDataGenerator.CreateProductB(), 1), // 30元
                (TestDataGenerator.CreateProductD(), 1) // 100元
            }
        );
        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.True(result.TotalDiscount > 0);
        Assert.Single(result.AppliedPromotions);

        // 验证选择了低价商品B进行换购
        var productBItem = cart.Items.First(x => x.Product.Id == "B");
        Assert.True(productBItem.ActualUnitPrice < productBItem.UnitPrice, "低价商品应被选为换购商品");

        var productDItem = cart.Items.First(x => x.Product.Id == "D");
        AssertAmountEqual(productDItem.UnitPrice, productDItem.ActualUnitPrice, "高价商品不应被选为换购商品");

        Output.WriteLine($"商户利益最大化测试 - 选择了价值{productBItem.UnitPrice}元的商品进行换购");
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "BoundaryCondition")]
    public async Task Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateUnifiedDiscountExchangeRule_BuyA_90Percent_ExchangeB();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            new (Product, int)[0]
        );
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.Equal(0, result.TotalDiscount);
        Assert.Empty(result.AppliedPromotions);

        Output.WriteLine("空购物车测试 - 无优惠应用");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "BoundaryCondition")]
    public async Task Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateUnifiedDiscountExchangeRule_Disabled();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            new[]
            {
                (TestDataGenerator.CreateProductA(), 1),
                (TestDataGenerator.CreateProductB(), 1)
            }
        );
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.Equal(0, result.TotalDiscount);
        Assert.Empty(result.AppliedPromotions);

        Output.WriteLine("禁用规则测试 - 无优惠应用");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "BoundaryCondition")]
    public async Task Apply_ZeroDiscountRate_ShouldNotApply()
    {
        // Arrange - 0折扣率（无效配置）
        var rule = TestDataGenerator.CreateUnifiedDiscountExchangeRule_ZeroDiscount();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            new[]
            {
                (TestDataGenerator.CreateProductA(), 1),
                (TestDataGenerator.CreateProductB(), 1)
            }
        );
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.Equal(0, result.TotalDiscount);
        Assert.Empty(result.AppliedPromotions);

        Output.WriteLine("零折扣率测试 - 无优惠应用");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "BoundaryCondition")]
    public async Task Apply_FullDiscountRate_ShouldApplyAsFreeExchange()
    {
        // Arrange - 100%折扣（免费换购）
        var rule = TestDataGenerator.CreateUnifiedDiscountExchangeRule_FreeExchange();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            new[]
            {
                (TestDataGenerator.CreateProductA(), 1),
                (TestDataGenerator.CreateProductB(), 1)
            }
        );

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.True(result.TotalDiscount > 0);
        Assert.Single(result.AppliedPromotions);

        var appliedPromotion = result.AppliedPromotions.First();
        Assert.Equal(30.00m, appliedPromotion.DiscountAmount); // B商品30元全免

        // 验证B商品实际支付价格为0元
        var productBItem = cart.Items.First(x => x.Product.Id == "B");
        Assert.Equal(0m, productBItem.ActualUnitPrice);

        Output.WriteLine($"全折扣率测试 - 优惠金额: {appliedPromotion.DiscountAmount}元");
    }

    #endregion
}
