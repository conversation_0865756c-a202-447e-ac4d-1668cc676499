/**
 * 促销规则管理器组件 - Ant Design 风格
 * 提供规则列表查看、编辑、删除、批量操作等功能
 * 集成API服务实现前后端数据同步
 */

const RuleManagerComponent = {
    name: 'RuleManager',
    template: `
        <div class="rule-manager ant-design">
            <!-- Ant Design 风格头部 -->
            <div class="rule-manager-header">
                <div class="header-content">
                    <div class="header-left">
                        <div class="page-title">
                            <el-icon class="title-icon"><Setting /></el-icon>
                            <div>
                                <h1>促销规则管理</h1>
                                <div class="page-subtitle">{{ rulesStatus }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="header-actions">
                        <el-button @click="$emit('create-rule')" type="primary">
                            <el-icon><Plus /></el-icon>
                            创建规则
                        </el-button>
                        <el-button @click="refreshRules" :loading="loading.refresh">
                            <el-icon><Refresh /></el-icon>
                            刷新
                        </el-button>
                        <el-dropdown @command="handleHeaderAction">
                            <el-button>
                                更多操作
                                <el-icon><ArrowDown /></el-icon>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item command="reload" :disabled="loading.reload">
                                        <el-icon><Upload /></el-icon>
                                        重新加载
                                    </el-dropdown-item>
                                    <el-dropdown-item command="validate" :disabled="loading.validate">
                                        <el-icon><CheckCircle /></el-icon>
                                        验证配置
                                    </el-dropdown-item>
                                    <el-dropdown-item command="export">
                                        <el-icon><Download /></el-icon>
                                        导出规则
                                    </el-dropdown-item>
                                    <el-dropdown-item command="import">
                                        <el-icon><Upload /></el-icon>
                                        导入规则
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </div>
            </div>

            <!-- Ant Design 风格筛选区域 -->
            <div class="rule-manager-filters">
                <div class="filter-section">
                    <div class="filter-row">
                        <div class="filter-item">
                            <el-input
                                v-model="filters.search"
                                placeholder="搜索规则名称或ID"
                                clearable
                                @input="handleFilterChange"
                                class="search-input">
                                <template #prefix>
                                    <el-icon><Search /></el-icon>
                                </template>
                            </el-input>
                        </div>
                        <div class="filter-item">
                            <el-select
                                v-model="filters.type"
                                placeholder="规则类型"
                                clearable
                                @change="handleFilterChange"
                                style="width: 160px;">
                                <el-option
                                    v-for="type in ruleTypes"
                                    :key="type.value"
                                    :label="type.label"
                                    :value="type.value">
                                </el-option>
                            </el-select>
                        </div>
                        <div class="filter-item">
                            <el-select
                                v-model="filters.status"
                                placeholder="状态"
                                clearable
                                @change="handleFilterChange"
                                style="width: 120px;">
                                <el-option label="启用" value="enabled">
                                    <div class="status-option">
                                        <div class="status-dot enabled"></div>
                                        启用
                                    </div>
                                </el-option>
                                <el-option label="禁用" value="disabled">
                                    <div class="status-option">
                                        <div class="status-dot disabled"></div>
                                        禁用
                                    </div>
                                </el-option>
                                <el-option label="已过期" value="expired">
                                    <div class="status-option">
                                        <div class="status-dot expired"></div>
                                        已过期
                                    </div>
                                </el-option>
                                <el-option label="未生效" value="future">
                                    <div class="status-option">
                                        <div class="status-dot future"></div>
                                        未生效
                                    </div>
                                </el-option>
                            </el-select>
                        </div>
                        <div class="filter-item">
                            <el-date-picker
                                v-model="filters.dateRange"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                                @change="handleFilterChange"
                                style="width: 260px;">
                            </el-date-picker>
                        </div>                        <div class="filter-item">
                            <el-radio-group 
                                v-model="viewMode" 
                                @change="handleViewModeChange"
                                size="small">
                                <el-radio-button label="table">
                                    <el-icon><Grid /></el-icon>
                                    表格
                                </el-radio-button>
                                <el-radio-button label="card">
                                    <el-icon><Menu /></el-icon>
                                    卡片
                                </el-radio-button>
                            </el-radio-group>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 批量操作工具栏 -->
            <div class="batch-operations" v-if="selectedRules.length > 0">
                <div class="batch-info">
                    <el-icon><InfoFilled /></el-icon>
                    已选择 <strong>{{ selectedRules.length }}</strong> 条规则
                </div>
                <div class="batch-actions">
                    <el-button @click="batchEnable" size="small">
                        <el-icon><Check /></el-icon>
                        批量启用
                    </el-button>
                    <el-button @click="batchDisable" size="small">
                        <el-icon><Close /></el-icon>
                        批量禁用
                    </el-button>
                    <el-button @click="batchDelete" type="danger" size="small">
                        <el-icon><Delete /></el-icon>
                        批量删除
                    </el-button>
                    <el-button @click="clearSelection" size="small">取消选择</el-button>
                </div>
            </div>            <!-- 主内容区域 -->
            <div class="rule-manager-content">
                <!-- 表格视图 -->
                <div v-if="viewMode === 'table'" class="table-view">
                    <el-table
                        :data="paginatedRules"
                        v-loading="loading.list"
                        @selection-change="handleSelectionChange"
                        :row-class-name="getRowClassName"
                        class="ant-table">
                        <el-table-column type="selection" width="50" align="center"></el-table-column>
                        <el-table-column prop="id" label="规则ID" width="180" show-overflow-tooltip>
                            <template #default="{ row }">
                                <div class="rule-id">
                                    <el-icon class="id-icon"><Key /></el-icon>
                                    {{ row.id }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="name" label="规则名称" min-width="220" show-overflow-tooltip>
                            <template #default="{ row }">
                                <div class="rule-name-cell">
                                    <div class="rule-title">{{ row.name }}</div>
                                    <div class="rule-desc" v-if="row.description">{{ row.description }}</div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="promotionType" label="类型" width="160">
                            <template #default="{ row }">
                                <el-tag :color="getRuleTypeColor(row.promotionType)" class="type-tag">
                                    {{ getRuleTypeLabel(row.promotionType) }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="priority" label="优先级" width="90" sortable align="center">
                            <template #default="{ row }">
                                <el-tag :type="getPriorityType(row.priority)" class="priority-tag">
                                    {{ row.priority }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="状态" width="100" align="center">
                            <template #default="{ row }">
                                <div class="status-cell">
                                    <el-switch
                                        v-model="row.isEnabled"
                                        @change="toggleRuleStatus(row)"
                                        :loading="row._updating"
                                        :active-color="'var(--ant-primary-color)'"
                                        size="small">
                                    </el-switch>
                                    <div class="status-indicator" :class="getStatusClass(row)"></div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="有效期" width="200">
                            <template #default="{ row }">
                                <div class="validity-cell">
                                    <div class="date-range" v-if="row.startTime || row.endTime">
                                        <div class="date-item" v-if="row.startTime">
                                            <el-icon><Calendar /></el-icon>
                                            {{ formatDate(row.startTime) }}
                                        </div>
                                        <div class="date-item" v-if="row.endTime">
                                            <el-icon><Calendar /></el-icon>
                                            {{ formatDate(row.endTime) }}
                                        </div>
                                    </div>
                                    <el-tag 
                                        v-if="getRuleValidity(row) !== 'valid'" 
                                        :type="getValidityTagType(row)" 
                                        size="small"
                                        class="validity-tag">
                                        {{ getValidityLabel(row) }}
                                    </el-tag>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="220" fixed="right" align="center">
                            <template #default="{ row }">
                                <div class="action-buttons">
                                    <el-button 
                                        @click="editRule(row)" 
                                        type="primary" 
                                        link 
                                        size="small">
                                        <el-icon><Edit /></el-icon>
                                        编辑
                                    </el-button>
                                    <el-button 
                                        @click="viewRule(row)" 
                                        link 
                                        size="small">
                                        <el-icon><View /></el-icon>
                                        查看
                                    </el-button>
                                    <el-dropdown @command="(cmd) => handleRowAction(cmd, row)" trigger="click">
                                        <el-button link size="small">
                                            <el-icon><MoreFilled /></el-icon>
                                        </el-button>
                                        <template #dropdown>
                                            <el-dropdown-menu>
                                                <el-dropdown-item command="duplicate">
                                                    <el-icon><DocumentCopy /></el-icon>
                                                    复制规则
                                                </el-dropdown-item>
                                                <el-dropdown-item command="export">
                                                    <el-icon><Download /></el-icon>
                                                    导出规则
                                                </el-dropdown-item>
                                                <el-dropdown-item divided command="delete" class="danger-item">
                                                    <el-icon><Delete /></el-icon>
                                                    删除规则
                                                </el-dropdown-item>
                                            </el-dropdown-menu>
                                        </template>
                                    </el-dropdown>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>

                <!-- 卡片视图 -->
                <div v-else class="card-view">
                    <div class="card-grid" v-loading="loading.list">
                        <div 
                            v-for="rule in paginatedRules" 
                            :key="rule.id" 
                            class="rule-card"
                            :class="{ 'selected': selectedRules.includes(rule) }">
                            <div class="card-header">
                                <div class="card-left">
                                    <el-checkbox
                                        :model-value="selectedRules.includes(rule)"
                                        @change="toggleRuleSelection(rule)">
                                    </el-checkbox>
                                    <div class="rule-info">
                                        <div class="rule-name">{{ rule.name }}</div>
                                        <div class="rule-id">{{ rule.id }}</div>
                                    </div>
                                </div>
                                <div class="card-right">
                                    <el-switch
                                        v-model="rule.isEnabled"
                                        @change="toggleRuleStatus(rule)"
                                        :loading="rule._updating"
                                        size="small">
                                    </el-switch>
                                </div>
                            </div>
                            
                            <div class="card-content">
                                <div class="rule-meta">
                                    <div class="meta-item">
                                        <span class="meta-label">类型:</span>
                                        <el-tag size="small" :color="getRuleTypeColor(rule.promotionType)">
                                            {{ getRuleTypeLabel(rule.promotionType) }}
                                        </el-tag>
                                    </div>
                                    <div class="meta-item">
                                        <span class="meta-label">优先级:</span>
                                        <el-tag :type="getPriorityType(rule.priority)" size="small">
                                            {{ rule.priority }}
                                        </el-tag>
                                    </div>
                                    <div class="meta-item" v-if="getRuleValidity(rule) !== 'valid'">
                                        <span class="meta-label">状态:</span>
                                        <el-tag :type="getValidityTagType(rule)" size="small">
                                            {{ getValidityLabel(rule) }}
                                        </el-tag>
                                    </div>
                                </div>
                                
                                <div class="rule-description" v-if="rule.description">
                                    {{ rule.description }}
                                </div>
                                
                                <div class="rule-dates" v-if="rule.startTime || rule.endTime">
                                    <div class="date-item" v-if="rule.startTime">
                                        <el-icon><Calendar /></el-icon>
                                        开始: {{ formatDate(rule.startTime) }}
                                    </div>
                                    <div class="date-item" v-if="rule.endTime">
                                        <el-icon><Calendar /></el-icon>
                                        结束: {{ formatDate(rule.endTime) }}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card-actions">
                                <el-button @click="editRule(rule)" type="primary" size="small">
                                    <el-icon><Edit /></el-icon>
                                    编辑
                                </el-button>
                                <el-button @click="viewRule(rule)" size="small">
                                    <el-icon><View /></el-icon>
                                    查看
                                </el-button>
                                <el-button @click="duplicateRule(rule)" size="small">
                                    <el-icon><DocumentCopy /></el-icon>
                                    复制
                                </el-button>
                                <el-popconfirm 
                                    title="确定删除这条规则吗？" 
                                    @confirm="deleteRule(rule)"
                                    confirm-button-text="确定"
                                    cancel-button-text="取消">
                                    <template #reference>
                                        <el-button 
                                            type="danger" 
                                            size="small" 
                                            :loading="rule._deleting">
                                            <el-icon><Delete /></el-icon>
                                        </el-button>
                                    </template>
                                </el-popconfirm>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Ant Design 风格分页器 -->
            <div class="rule-manager-pagination" v-if="filteredRules.length > 0">
                <div class="pagination-info">
                    显示第 {{ (pagination.current - 1) * pagination.size + 1 }} - 
                    {{ Math.min(pagination.current * pagination.size, filteredRules.length) }} 条，
                    共 {{ filteredRules.length }} 条
                </div>
                <el-pagination
                    v-model:current-page="pagination.current"
                    v-model:page-size="pagination.size"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="filteredRules.length"
                    layout="sizes, prev, pager, next, jumper"
                    class="ant-pagination">
                </el-pagination>
            </div>

            <!-- 空状态 -->
            <div v-if="!loading.list && filteredRules.length === 0" class="empty-state">
                <div class="empty-content">
                    <el-icon class="empty-icon"><DocumentRemove /></el-icon>
                    <h3>暂无促销规则</h3>
                    <p>{{ filters.search || filters.type || filters.status ? '没有找到符合条件的规则' : '还没有创建任何促销规则' }}</p>
                    <el-button type="primary" @click="$emit('create-rule')">
                        <el-icon><Plus /></el-icon>
                        创建第一个规则
                    </el-button>
                </div>
            </div>

            <!-- 隐藏的文件输入用于导入 -->
            <input 
                ref="fileInput" 
                type="file" 
                accept=".json,.csv" 
                @change="handleImportFile" 
                style="display: none;">
        </div>
    `,    props: {
        visible: {
            type: Boolean,
            default: false
        }
    },
    emits: ['edit-rule', 'view-rule', 'create-rule', 'rules-updated'],
    data() {
        return {
            rules: [],
            filteredRules: [],
            selectedRules: [],
            viewMode: 'table', // table | card
            loading: {
                list: false,
                refresh: false,
                reload: false,
                validate: false
            },
            filters: {
                search: '',
                type: '',
                status: '',
                dateRange: []
            },            pagination: {
                current: 1,
                size: 20
            },
            ruleTypes: [
                { value: 'UnifiedGift', label: '统一买赠', color: '#52c41a' },
                { value: 'TieredGift', label: '阶梯买赠', color: '#52c41a' },
                { value: 'CombinationGift', label: '组合买赠', color: '#52c41a' },
                { value: 'UnifiedSpecialPriceExchange', label: '统一特价换购', color: '#1890ff' },
                { value: 'UnifiedDiscountExchange', label: '统一折扣换购', color: '#1890ff' },
                { value: 'ProductBuyFree', label: '商品买免', color: '#722ed1' },
                { value: 'CombinationBuyFree', label: '组合买免', color: '#722ed1' },
                { value: 'UnifiedCashDiscount', label: '统一减现', color: '#fa8c16' },
                { value: 'GradientCashDiscount', label: '阶梯减现', color: '#fa8c16' },
                { value: 'UnifiedDiscount', label: '统一打折', color: '#eb2f96' },
                { value: 'TieredDiscount', label: '阶梯打折', color: '#eb2f96' },
                { value: 'UnifiedSpecialPrice', label: '统一特价', color: '#13c2c2' }
            ]
        };
    },
    computed: {
        rulesStatus() {
            const total = this.rules.length;
            const enabled = this.rules.filter(r => r.isEnabled).length;
            const valid = this.rules.filter(r => this.getRuleValidity(r) === 'valid').length;
            return `共 ${total} 条规则，${enabled} 条启用，${valid} 条有效`;
        },
        paginatedRules() {
            const start = (this.pagination.current - 1) * this.pagination.size;
            const end = start + this.pagination.size;
            return this.filteredRules.slice(start, end);
        }
    },
    watch: {
        visible(newVal) {
            if (newVal) {
                this.loadRules();
            }
        },
        filters: {
            handler() {
                this.applyFilters();
            },
            deep: true
        }    },
    mounted() {
        this.loadRules();
    },
    methods: {        // API 相关方法
        async loadRules() {
            this.loading.list = true;
            try {
                if (window.apiService && window.apiService.getAllPromotionRules) {
                    const response = await window.apiService.getAllPromotionRules();
                    this.rules = Array.isArray(response) ? response : [];
                } else {
                    // 模拟数据用于演示
                    console.warn('API服务不可用，使用模拟数据');
                    this.rules = this.generateMockRules();
                }
                this.applyFilters();
                this.$emit('rules-updated', this.rules);
            } catch (error) {
                console.error('加载规则失败:', error);
                
                // 如果是网络错误，使用模拟数据并提示用户
                if (error.message.includes('无法连接到后端服务')) {
                    this.$message.warning('后端服务连接失败，正在使用模拟数据演示功能');
                    this.rules = this.generateMockRules();
                    this.applyFilters();
                } else {
                    this.$message.error('加载规则失败: ' + error.message);
                    this.rules = [];
                    this.filteredRules = [];
                }
            } finally {
                this.loading.list = false;
            }
        },

        generateMockRules() {
            return [
                {
                    id: 'RULE_001',
                    name: '满500减50促销',
                    description: '购买满500元立减50元',
                    promotionType: 'UnifiedCashDiscount',
                    priority: 100,
                    isEnabled: true,
                    startTime: '2024-01-01',
                    endTime: '2024-12-31',
                    createdAt: '2024-01-01T00:00:00Z'
                },
                {
                    id: 'RULE_002',
                    name: '买二送一特惠',
                    description: '购买2件指定商品送1件',
                    promotionType: 'UnifiedGift',
                    priority: 80,
                    isEnabled: false,
                    startTime: '2024-03-01',
                    endTime: '2024-03-31',
                    createdAt: '2024-02-15T00:00:00Z'
                },
                {
                    id: 'RULE_003',
                    name: '8折优惠活动',
                    description: '指定商品8折优惠',
                    promotionType: 'UnifiedDiscount',
                    priority: 60,
                    isEnabled: true,
                    startTime: '2024-06-01',
                    endTime: '2024-06-30',
                    createdAt: '2024-05-20T00:00:00Z'
                }
            ];
        },

        // 头部操作
        handleHeaderAction(command) {
            switch (command) {
                case 'reload':
                    this.reloadBackendRules();
                    break;
                case 'validate':
                    this.validateRules();
                    break;
                case 'export':
                    this.exportRules();
                    break;
                case 'import':
                    this.importRules();
                    break;
            }
        },

        async refreshRules() {
            this.loading.refresh = true;
            try {
                await this.loadRules();
                this.$message.success('规则列表已刷新');
            } finally {
                this.loading.refresh = false;
            }
        },

        async reloadBackendRules() {
            this.loading.reload = true;
            try {
                await this.loadRules();
                this.$message.success('已重新加载后端规则');
            } catch (error) {
                this.$message.error('重新加载失败: ' + error.message);
            } finally {
                this.loading.reload = false;
            }
        },

        async validateRules() {
            this.loading.validate = true;
            try {
                // 模拟验证逻辑
                await new Promise(resolve => setTimeout(resolve, 1500));
                const invalidRules = this.rules.filter(rule => !rule.id || !rule.name);
                if (invalidRules.length === 0) {
                    this.$message.success(`所有 ${this.rules.length} 条规则验证通过`);
                } else {
                    this.$message.warning(`发现 ${invalidRules.length} 条无效规则`);
                }
            } finally {
                this.loading.validate = false;
            }
        },

        // 筛选相关
        handleFilterChange() {
            this.pagination.current = 1;
            this.applyFilters();
        },

        applyFilters() {
            let filtered = [...this.rules];

            // 搜索过滤
            if (this.filters.search) {
                const search = this.filters.search.toLowerCase();
                filtered = filtered.filter(rule => 
                    rule.id.toLowerCase().includes(search) ||
                    rule.name.toLowerCase().includes(search) ||
                    (rule.description && rule.description.toLowerCase().includes(search))
                );
            }

            // 类型过滤
            if (this.filters.type) {
                filtered = filtered.filter(rule => rule.promotionType === this.filters.type);
            }

            // 状态过滤
            if (this.filters.status) {
                filtered = filtered.filter(rule => {
                    const validity = this.getRuleValidity(rule);
                    switch (this.filters.status) {
                        case 'enabled':
                            return rule.isEnabled && validity === 'valid';
                        case 'disabled':
                            return !rule.isEnabled;
                        case 'expired':
                            return validity === 'expired';
                        case 'future':
                            return validity === 'future';
                        default:
                            return true;
                    }
                });
            }

            // 日期范围过滤
            if (this.filters.dateRange && this.filters.dateRange.length === 2) {
                const [startDate, endDate] = this.filters.dateRange;
                filtered = filtered.filter(rule => {
                    const ruleStart = rule.startTime ? new Date(rule.startTime) : null;
                    const ruleEnd = rule.endTime ? new Date(rule.endTime) : null;
                    const filterStart = new Date(startDate);
                    const filterEnd = new Date(endDate);
                    
                    return (ruleStart && ruleStart >= filterStart) || 
                           (ruleEnd && ruleEnd <= filterEnd);
                });
            }

            this.filteredRules = filtered;
        },

        handleViewModeChange(mode) {
            this.viewMode = mode;
        },

        // 选择相关
        handleSelectionChange(selection) {
            this.selectedRules = selection;
        },

        toggleRuleSelection(rule) {
            const index = this.selectedRules.findIndex(r => r.id === rule.id);
            if (index > -1) {
                this.selectedRules.splice(index, 1);
            } else {
                this.selectedRules.push(rule);
            }
        },

        clearSelection() {
            this.selectedRules = [];
        },

        // 规则操作
        editRule(rule) {
            this.$emit('edit-rule', rule);
        },

        viewRule(rule) {
            this.$emit('view-rule', rule);
        },

        async duplicateRule(rule) {
            try {
                const newRule = {
                    ...rule,
                    id: `${rule.id}_COPY_${Date.now()}`,
                    name: `${rule.name}（副本）`
                };
                
                if (window.apiService && window.apiService.addPromotionRule) {
                    await window.apiService.addPromotionRule(newRule);
                }
                
                this.$message.success('规则复制成功');
                this.loadRules();
            } catch (error) {
                this.$message.error('复制失败: ' + error.message);
            }
        },

        async deleteRule(rule) {
            rule._deleting = true;
            try {
                if (window.apiService && window.apiService.deletePromotionRule) {
                    await window.apiService.deletePromotionRule(rule.id);
                }
                
                this.$message.success('规则删除成功');
                this.loadRules();
            } catch (error) {
                this.$message.error('删除失败: ' + error.message);
            } finally {
                rule._deleting = false;
            }
        },

        async toggleRuleStatus(rule) {
            rule._updating = true;
            try {
                if (window.apiService && window.apiService.updatePromotionRule) {
                    await window.apiService.updatePromotionRule(rule.id, { isEnabled: rule.isEnabled });
                }
                
                this.$message.success(`规则已${rule.isEnabled ? '启用' : '禁用'}`);
            } catch (error) {
                rule.isEnabled = !rule.isEnabled; // 回滚状态
                this.$message.error('状态更新失败: ' + error.message);
            } finally {
                rule._updating = false;
            }
        },

        handleRowAction(command, row) {
            switch (command) {
                case 'duplicate':
                    this.duplicateRule(row);
                    break;
                case 'export':
                    this.exportSingleRule(row);
                    break;
                case 'delete':
                    this.$confirm('确定删除这条规则吗？', '确认删除', {
                        type: 'warning'
                    }).then(() => {
                        this.deleteRule(row);
                    });
                    break;
            }
        },

        // 批量操作
        async batchEnable() {
            for (const rule of this.selectedRules) {
                rule.isEnabled = true;
                await this.toggleRuleStatus(rule);
            }
            this.$message.success(`已启用 ${this.selectedRules.length} 条规则`);
        },

        async batchDisable() {
            for (const rule of this.selectedRules) {
                rule.isEnabled = false;
                await this.toggleRuleStatus(rule);
            }
            this.$message.success(`已禁用 ${this.selectedRules.length} 条规则`);
        },

        async batchDelete() {
            this.$confirm(`确定删除选中的 ${this.selectedRules.length} 条规则吗？`, '批量删除', {
                type: 'warning'
            }).then(async () => {
                for (const rule of this.selectedRules) {
                    await this.deleteRule(rule);
                }
                this.selectedRules = [];
                this.$message.success('批量删除成功');
            });
        },

        // 导入导出
        exportRules() {
            const data = JSON.stringify(this.rules, null, 2);
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `promotion-rules-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            this.$message.success('规则已导出');
        },

        exportSingleRule(rule) {
            const data = JSON.stringify(rule, null, 2);
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `rule-${rule.id}.json`;
            a.click();
            URL.revokeObjectURL(url);
            this.$message.success('规则已导出');
        },

        importRules() {
            this.$refs.fileInput.click();
        },

        handleImportFile(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    const rules = Array.isArray(data) ? data : [data];
                    
                    // 验证规则格式
                    const validRules = rules.filter(rule => rule.id && rule.name);
                    
                    if (validRules.length > 0) {
                        this.rules = [...this.rules, ...validRules];
                        this.applyFilters();
                        this.$message.success(`成功导入 ${validRules.length} 条规则`);
                    } else {
                        this.$message.error('文件中没有有效的规则数据');
                    }
                } catch (error) {
                    this.$message.error('文件格式错误，请检查JSON格式');
                }
            };
            reader.readAsText(file);
            
            // 清空文件输入
            event.target.value = '';
        },

        // 工具方法
        getRuleTypeLabel(type) {
            const ruleType = this.ruleTypes.find(t => t.value === type);
            return ruleType ? ruleType.label : type;
        },

        getRuleTypeColor(type) {
            const ruleType = this.ruleTypes.find(t => t.value === type);
            return ruleType ? ruleType.color : '#1890ff';
        },

        getPriorityType(priority) {
            if (priority >= 80) return 'danger';
            if (priority >= 50) return 'warning';
            return 'info';
        },

        getStatusClass(rule) {
            const validity = this.getRuleValidity(rule);
            return {
                'status-valid': validity === 'valid' && rule.isEnabled,
                'status-disabled': !rule.isEnabled,
                'status-expired': validity === 'expired',
                'status-future': validity === 'future'
            };
        },

        getRuleValidity(rule) {
            const now = new Date();
            const startTime = rule.startTime ? new Date(rule.startTime) : null;
            const endTime = rule.endTime ? new Date(rule.endTime) : null;

            if (endTime && endTime < now) {
                return 'expired';
            }
            if (startTime && startTime > now) {
                return 'future';
            }
            return 'valid';
        },

        getValidityLabel(rule) {
            const validity = this.getRuleValidity(rule);
            switch (validity) {
                case 'expired':
                    return '已过期';
                case 'future':
                    return '未生效';
                default:
                    return '有效';
            }
        },

        getValidityTagType(rule) {
            const validity = this.getRuleValidity(rule);
            switch (validity) {
                case 'expired':
                    return 'danger';
                case 'future':
                    return 'warning';
                default:
                    return 'success';
            }
        },

        getRowClassName({ row }) {
            const validity = this.getRuleValidity(row);
            if (!row.isEnabled) return 'row-disabled';
            if (validity === 'expired') return 'row-expired';
            if (validity === 'future') return 'row-future';
            return '';
        },

        formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);// 导出组件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RuleManagerComponent;
} else if (typeof window !== 'undefined') {
    window.RuleManagerComponent = RuleManagerComponent;
}

// Ant Design 风格样式
const ruleManagerStyles = `
<style>
/* 规则管理器 - Ant Design 风格 */
.rule-manager.ant-design {
    background: #f5f5f5;
    min-height: calc(100vh - 100px);
    padding: 0;
}

/* 头部区域 */
.rule-manager-header {
    background: white;
    border-bottom: 1px solid #f0f0f0;
    padding: 0;
}

.header-content {
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.title-icon {
    width: 32px;
    height: 32px;
    background: var(--ant-primary-color);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.page-title h1 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #262626;
    line-height: 1.2;
}

.page-subtitle {
    font-size: 14px;
    color: #8c8c8c;
    margin-top: 2px;
}

.header-actions {
    display: flex;
    gap: 8px;
}

/* 筛选区域 */
.rule-manager-filters {
    background: white;
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;
}

.filter-section {
    width: 100%;
}

.filter-row {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-item {
    flex-shrink: 0;
}

.search-input {
    width: 280px;
}

.status-option {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
}

.status-dot.enabled { background: #52c41a; }
.status-dot.disabled { background: #d9d9d9; }
.status-dot.expired { background: #ff4d4f; }
.status-dot.future { background: #faad14; }

/* 批量操作工具栏 */
.batch-operations {
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 6px;
    padding: 12px 16px;
    margin: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.batch-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #1890ff;
    font-size: 14px;
}

.batch-actions {
    display: flex;
    gap: 8px;
}

/* 主内容区域 */
.rule-manager-content {
    padding: 24px;
    background: white;
    margin: 0 24px 24px;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

/* 表格样式 */
.ant-table {
    border-radius: 8px;
    overflow: hidden;
}

.rule-id {
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 13px;
}

.id-icon {
    color: #8c8c8c;
    font-size: 12px;
}

.rule-name-cell {
    padding: 4px 0;
}

.rule-title {
    font-weight: 600;
    color: #262626;
    margin-bottom: 4px;
}

.rule-desc {
    font-size: 12px;
    color: #8c8c8c;
    line-height: 1.4;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.type-tag {
    border-radius: 4px;
    font-weight: 500;
}

.priority-tag {
    font-weight: 600;
    min-width: 32px;
    text-align: center;
}

.status-cell {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.status-indicator {
    width: 6px;
    height: 6px;
    border-radius: 50%;
}

.status-indicator.status-valid { background: #52c41a; }
.status-indicator.status-disabled { background: #d9d9d9; }
.status-indicator.status-expired { background: #ff4d4f; }
.status-indicator.status-future { background: #faad14; }

.validity-cell {
    font-size: 12px;
}

.date-range {
    margin-bottom: 4px;
}

.date-item {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #8c8c8c;
    margin-bottom: 2px;
}

.validity-tag {
    margin-top: 4px;
}

.action-buttons {
    display: flex;
    gap: 4px;
    justify-content: center;
}

/* 表格行状态 */
.el-table .row-disabled {
    background-color: #fafafa;
    color: #bfbfbf;
}

.el-table .row-expired {
    background-color: #fff2f0;
}

.el-table .row-future {
    background-color: #fffbe6;
}

/* 卡片视图 */
.card-view {
    padding: 0;
}

.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
    gap: 16px;
    padding: 24px;
}

.rule-card {
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.rule-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.rule-card.selected {
    border-color: var(--ant-primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.card-left {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    flex: 1;
}

.rule-info .rule-name {
    font-weight: 600;
    color: #262626;
    font-size: 16px;
    margin-bottom: 4px;
    line-height: 1.4;
}

.rule-info .rule-id {
    font-size: 12px;
    color: #8c8c8c;
    font-family: 'Monaco', 'Menlo', monospace;
}

.card-content {
    margin-bottom: 16px;
}

.rule-meta {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 12px;
}

.meta-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.meta-label {
    font-size: 13px;
    color: #8c8c8c;
    min-width: 60px;
}

.rule-description {
    font-size: 13px;
    color: #595959;
    line-height: 1.5;
    margin-bottom: 12px;
    padding: 8px;
    background: #fafafa;
    border-radius: 4px;
}

.rule-dates {
    font-size: 12px;
    color: #8c8c8c;
}

.rule-dates .date-item {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 2px;
}

.card-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    flex-wrap: wrap;
}

/* 分页区域 */
.rule-manager-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: white;
    border-top: 1px solid #f0f0f0;
    margin: 0 24px;
    border-radius: 0 0 8px 8px;
}

.pagination-info {
    font-size: 14px;
    color: #8c8c8c;
}

.ant-pagination {
    margin: 0;
}

/* 空状态 */
.empty-state {
    padding: 80px 24px;
    text-align: center;
    background: white;
    margin: 0 24px 24px;
    border-radius: 8px;
}

.empty-content {
    max-width: 400px;
    margin: 0 auto;
}

.empty-icon {
    font-size: 64px;
    color: #d9d9d9;
    margin-bottom: 16px;
}

.empty-content h3 {
    margin: 0 0 8px 0;
    color: #262626;
    font-size: 16px;
}

.empty-content p {
    margin: 0 0 24px 0;
    color: #8c8c8c;
    font-size: 14px;
}

/* 响应式适配 */
@media (max-width: 1200px) {
    .card-grid {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    }
    
    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-item {
        width: 100%;
    }
    
    .search-input {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .batch-operations {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .batch-actions {
        justify-content: center;
    }
    
    .card-grid {
        grid-template-columns: 1fr;
    }
    
    .rule-manager-content {
        margin: 0 16px 16px;
        padding: 16px;
    }
    
    .rule-manager-filters {
        padding: 16px;
    }
    
    .rule-manager-pagination {
        flex-direction: column;
        gap: 16px;
        padding: 16px;
        margin: 0 16px;
    }
}

/* Element Plus 组件定制 */
.rule-manager .el-button + .el-button {
    margin-left: 0;
}

.rule-manager .el-dropdown-menu__item.danger-item {
    color: #ff4d4f;
}

.rule-manager .el-dropdown-menu__item.danger-item:hover {
    background-color: #fff2f0;
    color: #ff4d4f;
}

.rule-manager .el-segmented {
    background: #f5f5f5;
}

.rule-manager .el-tag {
    border: none;
    font-weight: 500;
}

/* 加载状态 */
.rule-manager .el-loading-mask {
    border-radius: 8px;
}
</style>
`;

// 动态添加样式
if (typeof document !== 'undefined') {
    const styleElement = document.createElement('style');
    styleElement.innerHTML = ruleManagerStyles.replace(/<\/?style>/g, '');
    document.head.appendChild(styleElement);
}
            return `总计: ${total}, 启用: ${enabled}, 有效: ${valid}`;
        },
        statusTagType() {
            const valid = this.rules.filter(r => this.getRuleValidity(r) === 'valid').length;
            if (valid === 0) return 'danger';
            if (valid < this.rules.length / 2) return 'warning';
            return 'success';
        }
    },
    watch: {
        visible(newVal) {
            if (newVal) {
                this.loadRules();
            }
        }
    },
    mounted() {
        if (this.visible) {
            this.loadRules();
        }
    },
    methods: {
        // 数据加载
        async loadRules() {
            this.loading.list = true;
            try {
                this.rules = await window.apiService.getAllPromotionRules();
                this.applyFilters();
                this.$emit('rules-updated', this.rules);
            } catch (error) {
                this.$message.error('加载促销规则失败: ' + error.message);
            } finally {
                this.loading.list = false;
            }
        },

        async refreshRules() {
            this.loading.refresh = true;
            try {
                await this.loadRules();
                this.$message.success('规则列表已刷新');
            } finally {
                this.loading.refresh = false;
            }
        },

        async reloadBackendRules() {
            this.loading.reload = true;
            try {
                await window.apiService.reloadPromotionRules();
                await this.loadRules();
                this.$message.success('后端规则已重新加载');
            } catch (error) {
                this.$message.error('重新加载失败: ' + error.message);
            } finally {
                this.loading.reload = false;
            }
        },

        async validateRules() {
            this.loading.validate = true;
            try {
                const result = await window.apiService.validateRulesFile();
                if (result.isValid) {
                    this.$message.success('规则配置验证通过');
                } else {
                    this.$message.warning('规则配置验证失败: ' + result.errorMessage);
                }
            } catch (error) {
                this.$message.error('验证失败: ' + error.message);
            } finally {
                this.loading.validate = false;
            }
        },

        // 过滤和搜索
        applyFilters() {
            let filtered = [...this.rules];

            // 搜索过滤
            if (this.filters.search) {
                const search = this.filters.search.toLowerCase();
                filtered = filtered.filter(rule =>
                    rule.name.toLowerCase().includes(search) ||
                    rule.id.toLowerCase().includes(search) ||
                    (rule.description && rule.description.toLowerCase().includes(search))
                );
            }

            // 类型过滤
            if (this.filters.type) {
                filtered = filtered.filter(rule => rule.promotionType === this.filters.type);
            }

            // 状态过滤
            if (this.filters.status) {
                filtered = filtered.filter(rule => {
                    const validity = this.getRuleValidity(rule);
                    switch (this.filters.status) {
                        case 'enabled':
                            return rule.isEnabled;
                        case 'disabled':
                            return !rule.isEnabled;
                        case 'expired':
                            return validity === 'expired';
                        case 'future':
                            return validity === 'future';
                        default:
                            return true;
                    }
                });
            }

            // 日期范围过滤
            if (this.filters.dateRange && this.filters.dateRange.length === 2) {
                const [startDate, endDate] = this.filters.dateRange;
                filtered = filtered.filter(rule => {
                    const ruleStart = rule.startTime ? new Date(rule.startTime) : null;
                    const ruleEnd = rule.endTime ? new Date(rule.endTime) : null;
                    const filterStart = new Date(startDate);
                    const filterEnd = new Date(endDate);

                    // 规则的有效期与过滤日期范围有交集
                    if (ruleStart && ruleEnd) {
                        return ruleStart <= filterEnd && ruleEnd >= filterStart;
                    } else if (ruleStart) {
                        return ruleStart <= filterEnd;
                    } else if (ruleEnd) {
                        return ruleEnd >= filterStart;
                    }
                    return true;
                });
            }

            this.filteredRules = filtered;
        },

        handleFilterChange() {
            this.applyFilters();
            this.pagination.current = 1;
        },

        // 规则操作
        async toggleRuleStatus(rule) {
            rule._updating = true;
            try {
                await window.apiService.setPromotionRuleEnabled(rule.id, rule.isEnabled);
                this.$message.success(`规则已${rule.isEnabled ? '启用' : '禁用'}`);
            } catch (error) {
                // 回滚状态
                rule.isEnabled = !rule.isEnabled;
                this.$message.error('状态更新失败: ' + error.message);
            } finally {
                rule._updating = false;
            }
        },

        async deleteRule(rule) {
            rule._deleting = true;
            try {
                await window.apiService.deletePromotionRule(rule.id);
                const index = this.rules.findIndex(r => r.id === rule.id);
                if (index > -1) {
                    this.rules.splice(index, 1);
                    this.applyFilters();
                }
                this.$message.success('规则已删除');
                this.$emit('rules-updated', this.rules);
            } catch (error) {
                this.$message.error('删除失败: ' + error.message);
            } finally {
                rule._deleting = false;
            }
        },

        editRule(rule) {
            this.$emit('edit-rule', rule);
        },

        viewRule(rule) {
            this.$emit('view-rule', rule);
        },

        duplicateRule(rule) {
            const duplicated = { ...rule };
            duplicated.id = window.apiService.generateRuleId(rule.promotionType);
            duplicated.name = rule.name + ' (副本)';
            this.$emit('edit-rule', duplicated);
        },

        // 选择操作
        handleSelectionChange(selection) {
            this.selectedRules = selection;
        },

        toggleRuleSelection(rule) {
            const index = this.selectedRules.findIndex(r => r.id === rule.id);
            if (index > -1) {
                this.selectedRules.splice(index, 1);
            } else {
                this.selectedRules.push(rule);
            }
        },

        clearSelection() {
            this.selectedRules = [];
        },

        // 批量操作
        async batchEnable() {
            await this.batchOperation('enable');
        },

        async batchDisable() {
            await this.batchOperation('disable');
        },

        async batchDelete() {
            await this.batchOperation('delete');
        },

        async batchOperation(type) {
            const operations = this.selectedRules.map(rule => ({
                type,
                id: rule.id,
                config: rule
            }));

            try {
                const results = await window.apiService.batchOperatePromotionRules(operations);
                const success = results.filter(r => r.success).length;
                const failed = results.filter(r => !r.success).length;                if (failed === 0) {
                    this.$message.success(`批量操作成功，处理了 ${success} 条规则`);
                } else {
                    this.$message.warning(`批量操作完成，成功 ${success} 条，失败 ${failed} 条`);
                }

                await this.loadRules();
                this.clearSelection();
            } catch (error) {
                this.$message.error('批量操作失败: ' + error.message);
            }
        },

        // 导入导出
        async exportRules() {
            try {
                const data = await window.apiService.exportPromotionRules('json');
                const blob = new Blob([data], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `promotion-rules-${new Date().toISOString().slice(0, 10)}.json`;
                a.click();
                URL.revokeObjectURL(url);
                this.$message.success('规则已导出');
            } catch (error) {
                this.$message.error('导出失败: ' + error.message);
            }
        },

        async importRules(file) {
            try {
                const text = await this.readFileAsText(file);
                const results = await window.apiService.importPromotionRules(text, 'json');
                const success = results.filter(r => r.success).length;
                const failed = results.filter(r => !r.success).length;                if (failed === 0) {
                    this.$message.success(`导入成功，添加了 ${success} 条规则`);
                } else {
                    this.$message.warning(`导入完成，成功 ${success} 条，失败 ${failed} 条`);
                }

                await this.loadRules();
            } catch (error) {
                this.$message.error('导入失败: ' + error.message);
            }
            return false; // 阻止默认上传
        },

        readFileAsText(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = e => resolve(e.target.result);
                reader.onerror = reject;
                reader.readAsText(file);
            });
        },

        // 辅助方法
        getRuleTypeLabel(type) {
            const found = this.ruleTypes.find(t => t.value === type);
            return found ? found.label : type;
        },

        getRuleValidity(rule) {
            const now = new Date();
            const start = rule.startTime ? new Date(rule.startTime) : null;
            const end = rule.endTime ? new Date(rule.endTime) : null;

            if (start && start > now) return 'future';
            if (end && end < now) return 'expired';
            return 'valid';
        },

        getValidityLabel(rule) {
            const validity = this.getRuleValidity(rule);
            switch (validity) {
                case 'future': return '未生效';
                case 'expired': return '已过期';
                case 'valid': return '有效';
                default: return '未知';
            }
        },

        getValidityTagType(rule) {
            const validity = this.getRuleValidity(rule);
            switch (validity) {
                case 'future': return 'info';
                case 'expired': return 'danger';
                case 'valid': return 'success';
                default: return 'warning';
            }
        },

        formatDate(dateString) {
            if (!dateString) return '';
            return new Date(dateString).toLocaleDateString('zh-CN');
        }
    }
};

// 注册组件
if (typeof window !== 'undefined' && window.Vue) {
    window.RuleManagerComponent = RuleManagerComponent;
    window.RuleManager = RuleManagerComponent; // 也导出为 RuleManager
}
