<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试页面</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    
    <style>
        .debug-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .api-response {
            background: #f5f5f5;
            padding: 16px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="debug-container">
            <h1>API调试页面</h1>
            
            <div style="margin: 20px 0;">
                <el-button @click="testMetadataAPI" type="primary">
                    测试元数据API
                </el-button>
                <el-button @click="testTypesAPI" type="success">
                    测试类型API
                </el-button>
            </div>
            
            <div v-if="loading" class="loading">
                加载中...
            </div>
            
            <div v-if="apiResponse" class="api-response">
                <h3>API响应:</h3>
                {{ JSON.stringify(apiResponse, null, 2) }}
            </div>
            
            <div v-if="buyConditionsField" style="margin-top: 20px;">
                <h3>buyConditions字段解析:</h3>
                <div class="api-response">
                    {{ JSON.stringify(buyConditionsField, null, 2) }}
                </div>
            </div>
        </div>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>

    <script>
        const { createApp, ref } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                const loading = ref(false);
                const apiResponse = ref(null);
                const buyConditionsField = ref(null);

                const testMetadataAPI = async () => {
                    loading.value = true;
                    try {
                        const response = await fetch('/api/promotion/metadata/types/UnifiedSpecialPriceExchangeRule');
                        const data = await response.json();
                        
                        console.log('API响应:', data);
                        apiResponse.value = data;
                        
                        // 查找buyConditions字段
                        if (data && data.fields) {
                            const buyConditionsFieldData = data.fields.find(f => f.name === 'buyConditions');
                            if (buyConditionsFieldData) {
                                buyConditionsField.value = buyConditionsFieldData;
                                console.log('找到buyConditions字段:', buyConditionsFieldData);
                            } else {
                                console.log('未找到buyConditions字段');
                                console.log('可用字段:', data.fields.map(f => f.name));
                            }
                        }
                        
                        ElMessage.success('API调用成功');
                    } catch (error) {
                        console.error('API调用失败:', error);
                        ElMessage.error('API调用失败: ' + error.message);
                    } finally {
                        loading.value = false;
                    }
                };

                const testTypesAPI = async () => {
                    loading.value = true;
                    try {
                        const response = await fetch('/api/promotion/metadata/types');
                        const data = await response.json();
                        
                        console.log('Types API响应:', data);
                        apiResponse.value = data;
                        
                        ElMessage.success('Types API调用成功');
                    } catch (error) {
                        console.error('Types API调用失败:', error);
                        ElMessage.error('Types API调用失败: ' + error.message);
                    } finally {
                        loading.value = false;
                    }
                };

                return {
                    loading,
                    apiResponse,
                    buyConditionsField,
                    testMetadataAPI,
                    testTypesAPI
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
