global using Microsoft.Extensions.Logging;
using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Calculators;

namespace PE2.PromotionEngine.Core;

/// <summary>
/// 促销编排器辅助方法部分
/// </summary>
public sealed partial class PromotionOrchestrator
{
    #region 私有辅助方法

    /// <summary>
    /// 为计算预留库存
    /// </summary>
    private async Task<InventorySnapshot> ReserveInventoryForCalculationAsync(ShoppingCart cart, List<PromotionRuleBase> rules, string calculationId)
    {
        var reservationRequests = new List<InventoryReservationRequest>();
        
        // 为购物车商品预留库存
        foreach (var item in cart.Items)
        {
            reservationRequests.Add(new InventoryReservationRequest
            {
                ProductId = item.Product.Id,
                Quantity = item.Quantity,
                ReservationId = $"{calculationId}_{item.Product.Id}",
                Priority = ReservationPriority.Normal,
                ExpirationTime = DateTime.Now.AddMinutes(30)
            });
        }

        // 为可能的赠品预留库存
        foreach (var rule in rules.Where(r => r.RuleType.Contains("Gift")))
        {
            // 这里需要根据具体的赠品规则来预留库存
            // 简化实现，实际需要分析规则的赠品配置
        }

        var snapshot = await _inventoryManager.CreateSnapshotAsync(calculationId).ConfigureAwait(false);
        
        foreach (var request in reservationRequests)
        {
            await _inventoryManager.ReserveQuantityAsync(request).ConfigureAwait(false);
        }

        return snapshot;
    }

    /// <summary>
    /// 验证规则条件
    /// </summary>
    private async Task<List<PromotionRuleBase>> ValidateRuleConditionsAsync(List<PromotionRuleBase> rules, ShoppingCart cart, string traceId)
    {
        var validatedRules = new List<PromotionRuleBase>();
        
        foreach (var rule in rules)
        {
            try
            {
                // 使用条件引擎验证规则条件
                var isApplicable = rule.IsApplicable(cart, DateTime.Now);
                
                if (isApplicable)
                {
                    validatedRules.Add(rule);
                    _observability.TrackConditionValidation(traceId, rule.Id, "规则条件验证通过", rule);
                }
                else
                {
                    _observability.TrackConditionValidation(traceId, rule.Id, "规则条件验证失败", rule);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "验证规则条件时发生异常: {RuleId}", rule.Id);
                _observability.TrackConditionValidation(traceId, rule.Id, $"规则条件验证异常: {ex.Message}", rule);
            }
        }

        return validatedRules;
    }

    /// <summary>
    /// 执行促销计算
    /// </summary>
    private async Task<PromotionCalculationResult> ExecutePromotionCalculationAsync(PromotionCalculationRequest request, List<PromotionRuleBase> validatedRules, string traceId)
    {
        // 使用现有的PromotionCalculator进行计算
        var calculator = _serviceProvider.GetRequiredService<PromotionCalculator>();
        
        // 根据计算模式执行不同的计算逻辑
        PromotionResult result;
        
        if (request.Mode == CalculationMode.Manual && request.ManuallySelectedPromotions.Any())
        {
            // 手动模式：只计算指定的促销
            var selectedRules = validatedRules.Where(r => request.ManuallySelectedPromotions.Contains(r.Id)).ToList();
            result = await CalculateManualPromotionsAsync(request.Cart, selectedRules, traceId).ConfigureAwait(false);
        }
        else
        {
            // 自动模式：使用优化算法计算最优组合
            result = await CalculateOptimalPromotionsAsync(request.Cart, validatedRules, request.Target, traceId).ConfigureAwait(false);
        }

        return new PromotionCalculationResult
        {
            CalculationId = request.Id,
            IsSuccessful = true,
            OptimalResult = result,
            ProcessedCart = result.ProcessedCart,
            Statistics = new CalculationStatistics
            {
                RulesEvaluated = validatedRules.Count,
                PromotionsApplied = result.AppliedPromotions.Count,
                TotalDiscount = result.TotalDiscount
            }
        };
    }

    /// <summary>
    /// 处理折扣分配
    /// </summary>
    private async Task ProcessDiscountAllocationAsync(PromotionCalculationResult calculationResult, string traceId)
    {
        if (calculationResult.OptimalResult?.AppliedPromotions.Any() != true)
        {
            return;
        }

        foreach (var appliedPromotion in calculationResult.OptimalResult.AppliedPromotions)
        {
            if (appliedPromotion.DiscountAmount > 0)
            {
                var allocationRequest = new AllocationRequest
                {
                    Id = Guid.NewGuid().ToString(),
                    Items = CreateAllocationItems(calculationResult.ProcessedCart, appliedPromotion),
                    Promotion = appliedPromotion,
                    Strategy = AllocationStrategy.ProportionalByAmount,
                    RoundingStrategy = RoundingStrategy.RoundToNearestCent,
                    TailStrategy = TailDifferenceStrategy.AllocateToLargestItem
                };

                var allocationResult = await _allocationEngine.AllocateDiscountAsync(allocationRequest).ConfigureAwait(false);
                
                if (allocationResult.IsSuccessful)
                {
                    _observability.TrackAllocationCalculation(traceId, "折扣分配成功", allocationResult);
                }
                else
                {
                    _observability.TrackAllocationCalculation(traceId, "折扣分配失败", allocationResult);
                    _logger.LogWarning("折扣分配失败: {PromotionId}, {Error}", appliedPromotion.PromotionId, allocationResult.ErrorMessage);
                }
            }
        }
    }

    /// <summary>
    /// 验证和优化结果
    /// </summary>
    private async Task ValidateAndOptimizeResultAsync(PromotionCalculationResult calculationResult, string traceId)
    {
        await Task.Delay(1).ConfigureAwait(false);
        
        if (calculationResult.OptimalResult == null)
        {
            return;
        }

        // 验证计算结果的一致性
        var originalTotal = calculationResult.OptimalResult.OriginalAmount;
        var finalTotal = calculationResult.OptimalResult.FinalAmount;
        var discountTotal = calculationResult.OptimalResult.TotalDiscount;

        if (Math.Abs(originalTotal - finalTotal - discountTotal) > 0.01m)
        {
            _logger.LogWarning("计算结果不一致: 原价{Original:C}, 最终价{Final:C}, 优惠{Discount:C}",
                originalTotal, finalTotal, discountTotal);
            
            _observability.TrackCalculationStep(traceId, "计算结果验证失败", new {
                OriginalTotal = originalTotal,
                FinalTotal = finalTotal,
                DiscountTotal = discountTotal,
                Difference = originalTotal - finalTotal - discountTotal
            });
        }

        // 记录统计信息
        calculationResult.Statistics.ValidationTimeMs = 1;
        calculationResult.Statistics.IsValidationSuccessful = true;
    }

    /// <summary>
    /// 计算手动选择的促销
    /// </summary>
    private async Task<PromotionResult> CalculateManualPromotionsAsync(ShoppingCart cart, List<PromotionRuleBase> selectedRules, string traceId)
    {
        await Task.Delay(1).ConfigureAwait(false);
        
        var result = new PromotionResult
        {
            OriginalCart = cart.Clone(),
            ProcessedCart = cart.Clone(),
            AppliedPromotions = new List<AppliedPromotion>(),
            IsOptimal = false, // 手动模式不保证最优
            AlgorithmInfo = "手动选择模式"
        };

        var workingCart = cart.Clone();
        
        foreach (var rule in selectedRules.OrderByDescending(r => r.Priority))
        {
            try
            {
                if (rule.IsApplicable(workingCart, DateTime.Now))
                {
                    var application = rule.ApplyPromotion(workingCart);
                    if (application.IsSuccessful)
                    {
                        var appliedPromotion = new AppliedPromotion
                        {
                            RuleId = rule.Id,
                            RuleName = rule.Name,
                            PromotionType = rule.RuleType,
                            DiscountAmount = application.DiscountAmount,
                            ApplicationCount = 1
                        };
                        result.AppliedPromotions.Add(appliedPromotion);
                        _observability.TrackPromotionApplication(traceId, rule.Id, application);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "应用手动选择的促销规则时发生异常: {RuleId}", rule.Id);
            }
        }

        result.ProcessedCart = workingCart;
        return result;
    }

    /// <summary>
    /// 计算最优促销组合
    /// </summary>
    private async Task<PromotionResult> CalculateOptimalPromotionsAsync(ShoppingCart cart, List<PromotionRuleBase> rules, OptimizationTarget target, string traceId)
    {
        // 使用性能优化器获取最优组合
        var optimizationResult = await _performanceOptimizer.OptimizePromotionCombinationAsync(rules, cart).ConfigureAwait(false);
        
        if (optimizationResult.IsSuccessful && optimizationResult.OptimalCombination.Any())
        {
            var result = new PromotionResult
            {
                OriginalCart = cart.Clone(),
                ProcessedCart = cart.Clone(),
                AppliedPromotions = new List<AppliedPromotion>(),
                IsOptimal = true,
                AlgorithmInfo = $"优化算法: {optimizationResult.AlgorithmUsed}, 搜索时间: {optimizationResult.SearchTimeMs}ms"
            };

            var workingCart = cart.Clone();
            
            foreach (var ruleId in optimizationResult.OptimalCombination)
            {
                var rule = rules.FirstOrDefault(r => r.Id == ruleId);
                if (rule != null && rule.IsApplicable(workingCart, DateTime.Now))
                {
                    var application = rule.ApplyPromotion(workingCart);
                    if (application.IsSuccessful)
                    {
                        var appliedPromotion = new AppliedPromotion
                        {
                            RuleId = rule.Id,
                            RuleName = rule.Name,
                            PromotionType = rule.RuleType,
                            DiscountAmount = application.DiscountAmount,
                            ApplicationCount = 1
                        };
                        result.AppliedPromotions.Add(appliedPromotion);
                        _observability.TrackPromotionApplication(traceId, rule.Id, application);
                    }
                }
            }

            result.ProcessedCart = workingCart;
            return result;
        }

        // 如果优化失败，使用简单的贪心算法
        return await CalculateGreedyPromotionsAsync(cart, rules, traceId).ConfigureAwait(false);
    }

    /// <summary>
    /// 使用贪心算法计算促销
    /// </summary>
    private async Task<PromotionResult> CalculateGreedyPromotionsAsync(ShoppingCart cart, List<PromotionRuleBase> rules, string traceId)
    {
        await Task.Delay(1).ConfigureAwait(false);
        
        var result = new PromotionResult
        {
            OriginalCart = cart.Clone(),
            ProcessedCart = cart.Clone(),
            AppliedPromotions = new List<AppliedPromotion>(),
            IsOptimal = false,
            AlgorithmInfo = "贪心算法（备用方案）"
        };

        var workingCart = cart.Clone();
        var sortedRules = rules.OrderByDescending(r => r.Priority).ToList();
        
        foreach (var rule in sortedRules)
        {
            try
            {
                if (rule.IsApplicable(workingCart, DateTime.Now))
                {
                    var application = rule.ApplyPromotion(workingCart);
                    if (application.IsSuccessful)
                    {
                        var appliedPromotion = new AppliedPromotion
                        {
                            RuleId = rule.Id,
                            RuleName = rule.Name,
                            PromotionType = rule.RuleType,
                            DiscountAmount = application.DiscountAmount,
                            ApplicationCount = 1
                        };
                        result.AppliedPromotions.Add(appliedPromotion);
                        _observability.TrackPromotionApplication(traceId, rule.Id, application);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "应用贪心算法促销规则时发生异常: {RuleId}", rule.Id);
            }
        }

        result.ProcessedCart = workingCart;
        return result;
    }

    /// <summary>
    /// 创建分配项目
    /// </summary>
    private List<AllocationItem> CreateAllocationItems(ProcessedCart cart, AppliedPromotion promotion)
    {
        var allocationItems = new List<AllocationItem>();
        
        foreach (var item in cart.Items.Where(i => i.IsEligibleForPromotion))
        {
            allocationItems.Add(new AllocationItem
            {
                ProductId = item.Product.Id,
                ProductName = item.Product.Name,
                Quantity = item.Quantity,
                UnitPrice = item.UnitPrice,
                TotalPrice = item.TotalPrice,
                IsEligibleForAllocation = true,
                Weight = 1.0m,
                MaxAllowedDiscount = item.TotalPrice,
                MinAllowedDiscount = 0
            });
        }

        return allocationItems;
    }

    #endregion

    #region 分析方法

    /// <summary>
    /// 分析购物车特征
    /// </summary>
    private CartCharacteristics AnalyzeCartCharacteristics(ShoppingCart cart)
    {
        return new CartCharacteristics
        {
            TotalItems = cart.Items.Sum(i => i.Quantity),
            TotalAmount = cart.TotalAmount,
            UniqueProducts = cart.Items.Count,
            AverageItemPrice = cart.Items.Any() ? cart.Items.Average(i => i.UnitPrice) : 0,
            MaxItemPrice = cart.Items.Any() ? cart.Items.Max(i => i.UnitPrice) : 0,
            MinItemPrice = cart.Items.Any() ? cart.Items.Min(i => i.UnitPrice) : 0,
            Categories = cart.Items.Select(i => i.Product.Category).Distinct().ToList()
        };
    }

    /// <summary>
    /// 分析规则适用性
    /// </summary>
    private async Task<List<RuleApplicabilityInfo>> AnalyzeRuleApplicabilityAsync(ShoppingCart cart, List<PromotionRuleBase> rules)
    {
        await Task.Delay(1).ConfigureAwait(false);
        
        var applicabilityInfo = new List<RuleApplicabilityInfo>();
        
        foreach (var rule in rules)
        {
            var info = new RuleApplicabilityInfo
            {
                RuleId = rule.Id,
                RuleName = rule.Name,
                IsApplicable = rule.IsApplicable(cart, DateTime.Now),
                MaxApplications = rule.CalculateMaxApplications(cart),
                EstimatedDiscount = EstimateRuleDiscount(rule, cart)
            };
            
            applicabilityInfo.Add(info);
        }

        return applicabilityInfo;
    }

    /// <summary>
    /// 估算优化潜力
    /// </summary>
    private async Task<OptimizationPotentialInfo> EstimateOptimizationPotentialAsync(ShoppingCart cart, List<PromotionRuleBase> rules)
    {
        await Task.Delay(1).ConfigureAwait(false);
        
        var applicableRules = rules.Where(r => r.IsApplicable(cart, DateTime.Now)).ToList();
        var totalPotentialDiscount = applicableRules.Sum(r => EstimateRuleDiscount(r, cart));
        
        return new OptimizationPotentialInfo
        {
            ApplicableRulesCount = applicableRules.Count,
            TotalPotentialDiscount = totalPotentialDiscount,
            OptimizationComplexity = DetermineOptimizationComplexity(applicableRules.Count),
            EstimatedCalculationTime = EstimateCalculationTime(applicableRules.Count, cart.Items.Count)
        };
    }

    /// <summary>
    /// 识别潜在冲突
    /// </summary>
    private List<string> IdentifyPotentialConflicts(List<PromotionRuleBase> rules)
    {
        var conflicts = new List<string>();
        
        for (int i = 0; i < rules.Count; i++)
        {
            for (int j = i + 1; j < rules.Count; j++)
            {
                var rule1 = rules[i];
                var rule2 = rules[j];
                
                // 检查互斥规则
                if (rule1.ExclusiveRuleIds.Contains(rule2.Id) || rule2.ExclusiveRuleIds.Contains(rule1.Id))
                {
                    conflicts.Add($"规则 {rule1.Name} 与 {rule2.Name} 互斥");
                }
                
                // 检查商品互斥
                if (!rule1.CanStackWithOthers || !rule2.CanStackWithOthers)
                {
                    conflicts.Add($"规则 {rule1.Name} 与 {rule2.Name} 不能叠加");
                }
            }
        }

        return conflicts;
    }

    /// <summary>
    /// 估算规则折扣
    /// </summary>
    private decimal EstimateRuleDiscount(PromotionRuleBase rule, ShoppingCart cart)
    {
        // 简化实现，实际需要根据规则类型进行详细计算
        try
        {
            var maxApplications = rule.CalculateMaxApplications(cart);
            // 这里需要根据具体的规则类型来估算折扣金额
            // 暂时返回一个简单的估算值
            return maxApplications * 10; // 假设每次应用平均优惠10元
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// 确定优化复杂度
    /// </summary>
    private string DetermineOptimizationComplexity(int ruleCount)
    {
        return ruleCount switch
        {
            <= 5 => "简单",
            <= 15 => "中等",
            <= 30 => "复杂",
            _ => "极复杂"
        };
    }

    /// <summary>
    /// 估算计算时间
    /// </summary>
    private long EstimateCalculationTime(int ruleCount, int itemCount)
    {
        // 基于规则数量和商品数量的简单估算
        var baseTime = ruleCount * itemCount * 0.1;
        return (long)Math.Max(baseTime, 10); // 最少10ms
    }

    #endregion
}
