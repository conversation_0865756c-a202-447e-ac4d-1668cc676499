# 新架构促销引擎 (New Architecture Promotion Engine)

## 📁 目录结构

```
PE2/PromotionEngine/new/
├── Core/                           # 核心基础设施
│   ├── Interfaces/                 # 接口定义
│   │   ├── IPromotionRule.cs      # 促销规则接口
│   │   ├── IConditionValidator.cs  # 条件验证接口
│   │   ├── IPromotionExecutor.cs  # 促销执行接口
│   │   └── IProductOccupation.cs  # 商品占用接口
│   ├── Base/                      # 基类定义
│   │   ├── PromotionRuleBase.cs   # 促销规则基类
│   │   ├── BaseConditionValidator.cs # 条件验证基类
│   │   └── BasePromotionExecutor.cs  # 促销执行基类
│   ├── Models/                    # 核心模型
│   │   ├── PromotionContext.cs    # 促销上下文
│   │   ├── OccupationRequest.cs   # 占用请求
│   │   ├── ValidationResult.cs    # 验证结果
│   │   └── ExecutionResult.cs     # 执行结果
│   └── Enums/                     # 枚举定义
│       ├── PromotionType.cs       # 促销类型
│       ├── OccupationLevel.cs     # 占用级别
│       └── ExecutionStrategy.cs   # 执行策略
├── Rules/                         # 促销规则实现
│   ├── Exchange/                  # 换购规则 (6个)
│   │   ├── Base/
│   │   │   └── BaseExchangeRule.cs
│   │   ├── UnifiedSpecialPriceExchangeRule.cs
│   │   ├── UnifiedDiscountExchangeRule.cs
│   │   ├── UnifiedDiscountAmountExchangeRule.cs
│   │   ├── CombinationSpecialPriceExchangeRule.cs
│   │   ├── CombinationDiscountExchangeRule.cs
│   │   └── CombinationDiscountAmountExchangeRule.cs
│   ├── BuyFree/                   # 买免规则 (2个)
│   │   ├── Base/
│   │   │   └── BaseBuyFreeRule.cs
│   │   ├── ProductBuyFreeRule.cs
│   │   └── CombinationBuyFreeRule.cs
│   ├── CashDiscount/              # 减现规则 (3个)
│   │   ├── Base/
│   │   │   └── BaseCashDiscountRule.cs
│   │   ├── UnifiedCashDiscountRule.cs
│   │   ├── GradientCashDiscountRule.cs
│   │   └── CombinationCashDiscountRule.cs
│   ├── Discount/                  # 打折规则 (6个)
│   │   ├── Base/
│   │   │   └── BaseDiscountRule.cs
│   │   ├── UnifiedDiscountRule.cs
│   │   ├── TieredDiscountRule.cs
│   │   ├── ProgressiveDiscountRule.cs
│   │   ├── CombinationDiscountRule.cs
│   │   ├── FreeFormDiscountRule.cs
│   │   └── CyclicDiscountRule.cs
│   ├── SpecialPrice/              # 特价规则 (4个)
│   │   ├── Base/
│   │   │   └── BaseSpecialPriceRule.cs
│   │   ├── UnifiedSpecialPriceRule.cs
│   │   ├── TieredSpecialPriceRule.cs
│   │   ├── CombinationSpecialPriceRule.cs
│   │   └── IndividualSpecialPriceRule.cs
│   └── BuyGift/                   # 买赠规则 (3个)
│       ├── Base/
│       │   └── BaseBuyGiftRule.cs
│       ├── UnifiedGiftRule.cs
│       ├── TieredGiftRule.cs
│       └── CombinationGiftRule.cs
├── Services/                      # 服务层
│   ├── PromotionOrchestrator.cs   # 促销编排器
│   ├── RuleFactory.cs             # 规则工厂
│   ├── ValidationService.cs       # 验证服务
│   └── ExecutionService.cs        # 执行服务
├── Extensions/                    # 扩展方法
│   ├── ServiceCollectionExtensions.cs # DI扩展
│   ├── CartExtensions.cs          # 购物车扩展
│   └── RuleExtensions.cs          # 规则扩展
└── Configuration/                 # 配置
    ├── PromotionEngineOptions.cs  # 引擎配置
    └── RuleRegistration.cs        # 规则注册
```

## 🏗️ 架构设计原则

### 1. 商品占用管理集成
- 所有规则都集成 `IOccupationSession` 接口
- 支持条件验证和执行分离的三阶段处理
- 自动处理商品占用冲突和排他性管理

### 2. 统一接口设计
- `IPromotionRule`: 统一的促销规则接口
- `IConditionValidator`: 条件验证接口
- `IPromotionExecutor`: 促销执行接口
- `IProductOccupation`: 商品占用接口

### 3. 分层架构
- **Core层**: 基础设施和接口定义
- **Rules层**: 具体规则实现
- **Services层**: 业务服务
- **Extensions层**: 扩展功能

### 4. 设计模式应用
- **工厂模式**: RuleFactory 创建规则实例
- **策略模式**: 不同的执行策略
- **模板方法模式**: 基类定义通用流程
- **依赖注入**: 松耦合设计

## 🔄 迁移策略

### 阶段1: 基础设施搭建
1. 创建核心接口和基类
2. 集成商品占用管理功能
3. 建立服务层架构

### 阶段2: 规则迁移
1. 按优先级迁移规则类型
2. 保留所有原有业务逻辑和注释
3. 集成新架构功能

### 阶段3: 测试验证
1. 创建完整测试用例
2. 验证功能一致性
3. 性能基准测试

## 📋 规则迁移清单

- [ ] Exchange Rules (6个) - 换购规则
- [ ] BuyFree Rules (2个) - 买免规则  
- [ ] CashDiscount Rules (3个) - 减现规则
- [ ] Discount Rules (6个) - 打折规则
- [ ] SpecialPrice Rules (4个) - 特价规则
- [ ] BuyGift Rules (3个) - 买赠规则

## 🎯 新架构优势

1. **统一的商品占用管理**: 解决商品冲突问题
2. **条件执行分离**: 确保促销逻辑正确性
3. **排他性智能管理**: 自动处理规则冲突
4. **更好的可扩展性**: 易于添加新规则类型
5. **完整的可观测性**: 详细的执行追踪
6. **高性能优化**: 内存池和缓存机制
