using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules;

/// <summary>
/// 梯度送赠品促销规则
/// 针对某一类商品，满第一梯度送A商品，满第二梯度送B商品
/// </summary>
public class GradientGiftRule : PromotionRuleBase
{
    public override string RuleType => "GradientGift";

    /// <summary>
    /// 购买条件列表
    /// </summary>
    public List<BuyCondition> BuyConditions { get; set; } = new();

    /// <summary>
    /// 梯度赠品条件列表
    /// </summary>
    public List<GradientGiftCondition> GradientGiftConditions { get; set; } = new();

    /// <summary>
    /// 梯度赠送策略
    /// </summary>
    public GradientGiftStrategy GradientStrategy { get; set; } = GradientGiftStrategy.ByGradient;

    /// <summary>
    /// 赠品选择策略
    /// </summary>
    public GiftSelectionStrategy GiftSelectionStrategy { get; set; } = GiftSelectionStrategy.CustomerBenefit;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!BuyConditions.Any() || !GradientGiftConditions.Any())
            return false;

        // 检查是否至少满足一个完整的梯度条件（包括赠品库存）
        foreach (var buyCondition in BuyConditions)
        {
            var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
            var totalAmount = buyCondition.ProductIds.Sum(id =>
                cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));

            // 找到所有满足购买条件的梯度
            var applicableGradients = GradientGiftConditions
                .Where(g => availableQuantity >= g.RequiredQuantity &&
                           (g.RequiredAmount <= 0 || totalAmount >= g.RequiredAmount))
                .OrderByDescending(g => g.GradientLevel)
                .ToList();

            if (!applicableGradients.Any()) continue;

            // 根据策略确定要应用的梯度
            var gradientsToCheck = GradientStrategy == GradientGiftStrategy.ByGradient
                ? new List<GradientGiftCondition> { applicableGradients.First() }
                : applicableGradients;

            // 检查所有要应用的梯度是否都有足够的赠品库存
            if (gradientsToCheck.All(gradient => CheckGradientGiftStock(cart, gradient)))
                return true;
        }

        return false;
    }

    /// <summary>
    /// 检查特定梯度的赠品库存是否充足
    /// </summary>
    private bool CheckGradientGiftStock(ShoppingCart cart, GradientGiftCondition gradient)
    {
        if (gradient.GiftProductIds.Count > 1)
        {
            // 多选一：只要有一个赠品有库存就可以
            return gradient.GiftProductIds.Any(id =>
                cart.Items.Where(x => x.Product.Id == id).Sum(x => x.Quantity) > 0);
        }
        else
        {
            // 单一赠品：必须有足够库存
            var giftProductId = gradient.GiftProductIds.FirstOrDefault();
            if (string.IsNullOrEmpty(giftProductId)) return false;

            var availableGiftQuantity = cart.Items
                .Where(x => x.Product.Id == giftProductId)
                .Sum(x => x.Quantity);

            return availableGiftQuantity >= gradient.GiftQuantity;
        }
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = 0;

        // 根据购买条件计算可能的应用次数
        foreach (var buyCondition in BuyConditions)
        {
            var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
            var totalAmount = buyCondition.ProductIds.Sum(id =>
                cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));

            // 找到所有满足购买条件的梯度
            var applicableGradients = GradientGiftConditions
                .Where(g => availableQuantity >= g.RequiredQuantity &&
                           (g.RequiredAmount <= 0 || totalAmount >= g.RequiredAmount))
                .OrderByDescending(g => g.GradientLevel)
                .ToList();

            if (!applicableGradients.Any()) continue;

            // 根据策略确定要应用的梯度
            var gradientsToApply = GradientStrategy == GradientGiftStrategy.ByGradient
                ? new List<GradientGiftCondition> { applicableGradients.First() }
                : applicableGradients;

            // 计算基于购买条件的最大应用次数
            var maxByPurchase = IsRepeatable && applicableGradients.Any()
                ? availableQuantity / applicableGradients.First().RequiredQuantity
                : 1;

            // 计算基于赠品库存的最大应用次数
            var maxByGiftStock = CalculateMaxApplicationsByGiftStock(cart, gradientsToApply);

            // 取两者的最小值
            var currentMaxApplications = Math.Min(maxByPurchase, maxByGiftStock);
            maxApplications = Math.Max(maxApplications, currentMaxApplications);
        }

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    /// <summary>
    /// 根据赠品库存计算最大应用次数
    /// </summary>
    private int CalculateMaxApplicationsByGiftStock(ShoppingCart cart, List<GradientGiftCondition> gradients)
    {
        var minApplications = int.MaxValue;

        foreach (var gradient in gradients)
        {
            var maxForThisGradient = CalculateMaxApplicationsForSingleGradient(cart, gradient);
            minApplications = Math.Min(minApplications, maxForThisGradient);
        }

        return minApplications == int.MaxValue ? int.MaxValue : minApplications;
    }

    /// <summary>
    /// 计算单个梯度的最大应用次数（基于赠品库存）
    /// </summary>
    private int CalculateMaxApplicationsForSingleGradient(ShoppingCart cart, GradientGiftCondition gradient)
    {
        if (gradient.GiftProductIds.Count > 1)
        {
            // 多选一的情况：只要有一个赠品有库存就可以应用
            var hasAnyStock = gradient.GiftProductIds.Any(id =>
                cart.Items.Where(x => x.Product.Id == id).Sum(x => x.Quantity) > 0);
            return hasAnyStock ? int.MaxValue : 0;
        }
        else
        {
            // 单一赠品：根据库存数量计算
            var giftProductId = gradient.GiftProductIds.FirstOrDefault();
            if (string.IsNullOrEmpty(giftProductId)) return 0;

            var availableStock = cart.Items
                .Where(x => x.Product.Id == giftProductId)
                .Sum(x => x.Quantity);

            return gradient.GiftQuantity > 0 ? availableStock / gradient.GiftQuantity : 0;
        }
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyGradientGiftPromotion(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用梯度送赠品促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = $"应用梯度送赠品促销时发生错误: {ex.Message}";
        }

        return application;
    }

    /// <summary>
    /// 应用梯度送赠品促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyGradientGiftPromotion(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var giftItems = new List<GiftItem>();

        for (int app = 0; app < applicationCount; app++)
        {
            foreach (var buyCondition in BuyConditions)
            {
                var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                var totalAmount = buyCondition.ProductIds.Sum(id =>
                    cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));

                // 找到所有满足购买条件的梯度
                var applicableGradients = GradientGiftConditions
                    .Where(g => availableQuantity >= g.RequiredQuantity &&
                               (g.RequiredAmount <= 0 || totalAmount >= g.RequiredAmount))
                    .OrderByDescending(g => g.GradientLevel)
                    .ToList();

                if (!applicableGradients.Any()) continue;

                // 根据策略决定赠送哪些梯度的赠品
                var gradientsToApply = GradientStrategy == GradientGiftStrategy.ByGradient
                    ? new List<GradientGiftCondition> { applicableGradients.First() }
                    : applicableGradients;

                // 验证所有要应用的梯度是否都有足够的赠品库存
                if (!gradientsToApply.All(gradient => CheckGradientGiftStock(cart, gradient)))
                    continue;

                // 消耗购买商品
                var totalRequiredQuantity = gradientsToApply.Max(g => g.RequiredQuantity);
                var remainingQuantity = totalRequiredQuantity;

                foreach (var productId in buyCondition.ProductIds)
                {
                    if (remainingQuantity <= 0) break;

                    var availableItems = cart.Items
                        .Where(x => x.Product.Id == productId && x.AvailableQuantity > 0)
                        .ToList();

                    foreach (var item in availableItems)
                    {
                        if (remainingQuantity <= 0) break;

                        var consumeQuantity = Math.Min(item.AvailableQuantity, remainingQuantity);

                        var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == productId);
                        if (existingConsumed != null)
                        {
                            existingConsumed.Quantity += consumeQuantity;
                        }
                        else
                        {
                            consumedItems.Add(new ConsumedItem
                            {
                                ProductId = productId,
                                ProductName = item.Product.Name,
                                Quantity = consumeQuantity,
                                UnitPrice = item.UnitPrice
                            });
                        }

                        item.Quantity -= consumeQuantity;
                        remainingQuantity -= consumeQuantity;
                    }
                }

                // 处理梯度赠品
                foreach (var gradient in gradientsToApply)
                {
                    var selectedProductIds = gradient.GiftProductIds.Count > 1
                        ? SelectOptimalGiftProducts(gradient.GiftProductIds, cart, gradient.GiftQuantity)
                        : gradient.GiftProductIds;

                    var distributedQuantity = 0;
                    foreach (var giftProductId in selectedProductIds)
                    {
                        if (distributedQuantity >= gradient.GiftQuantity) break;

                        var productItem = cart.Items.FirstOrDefault(x => x.Product.Id == giftProductId);
                        if (productItem != null)
                        {
                            var giftQuantity = Math.Min(gradient.GiftQuantity - distributedQuantity, 1);
                            var giftValue = giftQuantity * productItem.UnitPrice;

                            totalDiscountAmount += giftValue;

                            var existingGift = giftItems.FirstOrDefault(x => x.ProductId == giftProductId);
                            if (existingGift != null)
                            {
                                existingGift.Quantity += giftQuantity;
                                existingGift.Value += giftValue;
                            }
                            else
                            {
                                var strategyDescription = GiftSelectionStrategy == GiftSelectionStrategy.CustomerBenefit
                                    ? "客户利益最大化选择"
                                    : "商家利益最大化选择";

                                giftItems.Add(new GiftItem
                                {
                                    ProductId = giftProductId,
                                    ProductName = productItem.Product.Name,
                                    Quantity = giftQuantity,
                                    Value = giftValue,
                                    Description = $"梯度{gradient.GradientLevel}: {gradient.Description} - {strategyDescription}"
                                });
                            }

                            distributedQuantity += giftQuantity;
                        }
                    }
                }
            }
        }

        cart.Items.RemoveAll(x => x.Quantity <= 0);
        return (totalDiscountAmount, consumedItems, giftItems);
    }

    /// <summary>
    /// 根据策略选择最优的赠品商品
    /// </summary>
    private List<string> SelectOptimalGiftProducts(List<string> candidateProductIds, ShoppingCart cart, int requiredQuantity)
    {
        if (!candidateProductIds.Any())
            return new List<string>();

        var productPrices = new List<(string ProductId, decimal Price)>();

        foreach (var productId in candidateProductIds)
        {
            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
            if (cartItem != null)
            {
                productPrices.Add((productId, cartItem.Product.Price));
            }
        }

        if (!productPrices.Any())
            return new List<string>();

        var sortedProducts = GiftSelectionStrategy switch
        {
            GiftSelectionStrategy.CustomerBenefit => productPrices.OrderByDescending(p => p.Price),
            GiftSelectionStrategy.MerchantBenefit => productPrices.OrderBy(p => p.Price),
            _ => productPrices.OrderByDescending(p => p.Price)
        };

        var selectedProducts = new List<string>();
        var remainingQuantity = requiredQuantity;

        foreach (var (productId, _) in sortedProducts)
        {
            if (remainingQuantity <= 0) break;

            var availableQuantity = cart.Items
                .Where(x => x.Product.Id == productId)
                .Sum(x => x.Quantity);

            if (availableQuantity > 0)
            {
                selectedProducts.Add(productId);
                remainingQuantity--;
            }
        }

        return selectedProducts;
    }
}
