using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Conditions;
using PE2.PromotionEngine.Inventory;
using PE2.PromotionEngine.Performance;
using PE2.PromotionEngine.Allocation;
using PE2.PromotionEngine.Observability;

namespace PE2.PromotionEngine.Core;

/// <summary>
/// 促销编排器接口 - 统一协调各个引擎组件
/// </summary>
public interface IPromotionOrchestrator
{
    /// <summary>
    /// 执行完整的促销计算流程
    /// </summary>
    Task<PromotionCalculationResult> CalculatePromotionsAsync(PromotionCalculationRequest request);

    /// <summary>
    /// 执行促销预分析
    /// </summary>
    Task<PromotionPreAnalysis> PreAnalyzePromotionsAsync(ShoppingCart cart, List<PromotionRuleBase> rules);

    /// <summary>
    /// 验证促销兼容性
    /// </summary>
    Task<CompatibilityResult> ValidatePromotionCompatibilityAsync(List<string> promotionIds, ShoppingCart cart);

    /// <summary>
    /// 执行促销回滚
    /// </summary>
    Task<RollbackResult> RollbackPromotionAsync(string calculationId);

    /// <summary>
    /// 获取促销计算状态
    /// </summary>
    Task<CalculationStatus> GetCalculationStatusAsync(string calculationId);

    /// <summary>
    /// 取消正在进行的促销计算
    /// </summary>
    Task<bool> CancelCalculationAsync(string calculationId);
}

/// <summary>
/// 促销计算请求
/// </summary>
public class PromotionCalculationRequest
{
    /// <summary>
    /// 请求ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 购物车
    /// </summary>
    public ShoppingCart Cart { get; set; } = new();

    /// <summary>
    /// 可用促销规则
    /// </summary>
    public List<PromotionRuleBase> AvailableRules { get; set; } = new();

    /// <summary>
    /// 计算模式
    /// </summary>
    public CalculationMode Mode { get; set; } = CalculationMode.Automatic;

    /// <summary>
    /// 手动选择的促销ID列表（当Mode为Manual时使用）
    /// </summary>
    public List<string> ManuallySelectedPromotions { get; set; } = new();

    /// <summary>
    /// 优化目标
    /// </summary>
    public OptimizationTarget Target { get; set; } = OptimizationTarget.MaximizeCustomerBenefit;

    /// <summary>
    /// 性能要求
    /// </summary>
    public PerformanceRequirement PerformanceReq { get; set; } = new();

    /// <summary>
    /// 分摊要求
    /// </summary>
    public AllocationRequirement AllocationReq { get; set; } = new();

    /// <summary>
    /// 可观测性要求
    /// </summary>
    public ObservabilityRequirement ObservabilityReq { get; set; } = new();

    /// <summary>
    /// 超时时间（毫秒）
    /// </summary>
    public int TimeoutMs { get; set; } = 30000;

    /// <summary>
    /// 会话ID
    /// </summary>
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// 用户ID
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// 门店ID
    /// </summary>
    public string StoreId { get; set; } = string.Empty;
}

/// <summary>
/// 计算模式
/// </summary>
public enum CalculationMode
{
    /// <summary>自动优化</summary>
    Automatic,
    /// <summary>手动选择</summary>
    Manual,
    /// <summary>混合模式</summary>
    Hybrid,
    /// <summary>预览模式</summary>
    Preview
}

/// <summary>
/// 优化目标
/// </summary>
public enum OptimizationTarget
{
    /// <summary>最大化客户利益</summary>
    MaximizeCustomerBenefit,
    /// <summary>最大化商家利益</summary>
    MaximizeMerchantBenefit,
    /// <summary>平衡双方利益</summary>
    BalancedBenefit,
    /// <summary>最大化促销数量</summary>
    MaximizePromotionCount,
    /// <summary>最小化计算复杂度</summary>
    MinimizeComplexity
}

/// <summary>
/// 性能要求
/// </summary>
public class PerformanceRequirement
{
    /// <summary>
    /// 最大计算时间（毫秒）
    /// </summary>
    public int MaxCalculationTimeMs { get; set; } = 5000;

    /// <summary>
    /// 是否启用缓存
    /// </summary>
    public bool EnableCaching { get; set; } = true;

    /// <summary>
    /// 是否启用预筛选
    /// </summary>
    public bool EnablePrefiltering { get; set; } = true;

    /// <summary>
    /// 最大搜索深度
    /// </summary>
    public int MaxSearchDepth { get; set; } = 10;

    /// <summary>
    /// 并行计算级别
    /// </summary>
    public ParallelismLevel ParallelismLevel { get; set; } = ParallelismLevel.Medium;
}

/// <summary>
/// 并行计算级别
/// </summary>
public enum ParallelismLevel
{
    None,
    Low,
    Medium,
    High,
    Maximum
}

/// <summary>
/// 分摊要求
/// </summary>
public class AllocationRequirement
{
    /// <summary>
    /// 分摊策略
    /// </summary>
    public AllocationStrategy Strategy { get; set; } = AllocationStrategy.ProportionalByAmount;

    /// <summary>
    /// 舍入策略
    /// </summary>
    public RoundingStrategy RoundingStrategy { get; set; } = RoundingStrategy.RoundToNearestCent;

    /// <summary>
    /// 精度要求
    /// </summary>
    public int Precision { get; set; } = 2;

    /// <summary>
    /// 是否验证分摊结果
    /// </summary>
    public bool ValidateAllocation { get; set; } = true;
}

/// <summary>
/// 可观测性要求
/// </summary>
public class ObservabilityRequirement
{
    /// <summary>
    /// 是否启用详细追踪
    /// </summary>
    public bool EnableDetailedTracking { get; set; } = true;

    /// <summary>
    /// 是否构建决策树
    /// </summary>
    public bool BuildDecisionTree { get; set; } = false;

    /// <summary>
    /// 是否收集性能指标
    /// </summary>
    public bool CollectPerformanceMetrics { get; set; } = true;

    /// <summary>
    /// 追踪级别
    /// </summary>
    public TrackingLevel TrackingLevel { get; set; } = TrackingLevel.Standard;
}

/// <summary>
/// 追踪级别
/// </summary>
public enum TrackingLevel
{
    Minimal,
    Standard,
    Detailed,
    Verbose
}

/// <summary>
/// 促销计算结果
/// </summary>
public class PromotionCalculationResult
{
    /// <summary>
    /// 计算ID
    /// </summary>
    public string CalculationId { get; set; } = string.Empty;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// 最优促销结果
    /// </summary>
    public PromotionResult? OptimalResult { get; set; }

    /// <summary>
    /// 备选方案
    /// </summary>
    public List<PromotionResult> AlternativeResults { get; set; } = new();

    /// <summary>
    /// 处理后的购物车
    /// </summary>
    public ProcessedCart ProcessedCart { get; set; } = new();

    /// <summary>
    /// 计算统计
    /// </summary>
    public CalculationStatistics Statistics { get; set; } = new();

    /// <summary>
    /// 决策树（如果启用）
    /// </summary>
    public DecisionTree? DecisionTree { get; set; }

    /// <summary>
    /// 计算报告
    /// </summary>
    public CalculationReport CalculationReport { get; set; } = new();

    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 警告信息
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 库存快照ID
    /// </summary>
    public string? InventorySnapshotId { get; set; }

    /// <summary>
    /// 追踪ID
    /// </summary>
    public string TraceId { get; set; } = string.Empty;
}

/// <summary>
/// 计算统计
/// </summary>
public class CalculationStatistics
{
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 总耗时（毫秒）
    /// </summary>
    public long TotalDurationMs { get; set; }

    /// <summary>
    /// 各阶段耗时
    /// </summary>
    public Dictionary<string, long> PhaseTimings { get; set; } = new();

    /// <summary>
    /// 处理的规则数量
    /// </summary>
    public int ProcessedRulesCount { get; set; }

    /// <summary>
    /// 筛选后的规则数量
    /// </summary>
    public int FilteredRulesCount { get; set; }

    /// <summary>
    /// 应用的促销数量
    /// </summary>
    public int AppliedPromotionsCount { get; set; }

    /// <summary>
    /// 搜索深度
    /// </summary>
    public int SearchDepth { get; set; }

    /// <summary>
    /// 缓存命中次数
    /// </summary>
    public int CacheHits { get; set; }

    /// <summary>
    /// 缓存未命中次数
    /// </summary>
    public int CacheMisses { get; set; }

    /// <summary>
    /// 内存使用峰值（MB）
    /// </summary>
    public double PeakMemoryUsageMB { get; set; }
}

/// <summary>
/// 促销预分析
/// </summary>
public class PromotionPreAnalysis
{
    /// <summary>
    /// 可应用的促销规则
    /// </summary>
    public List<PromotionRuleBase> ApplicableRules { get; set; } = new();

    /// <summary>
    /// 不可应用的促销规则及原因
    /// </summary>
    public Dictionary<PromotionRuleBase, string> InapplicableRules { get; set; } = new();

    /// <summary>
    /// 互斥促销组
    /// </summary>
    public List<List<PromotionRuleBase>> MutuallyExclusiveGroups { get; set; } = new();

    /// <summary>
    /// 预估最优组合
    /// </summary>
    public List<PromotionRuleBase> EstimatedOptimalCombination { get; set; } = new();

    /// <summary>
    /// 预估节省金额
    /// </summary>
    public decimal EstimatedSavings { get; set; }

    /// <summary>
    /// 复杂度分析
    /// </summary>
    public ComplexityAnalysis ComplexityAnalysis { get; set; } = new();

    /// <summary>
    /// 建议
    /// </summary>
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// 兼容性结果
/// </summary>
public class CompatibilityResult
{
    /// <summary>
    /// 是否兼容
    /// </summary>
    public bool IsCompatible { get; set; }

    /// <summary>
    /// 兼容的促销组合
    /// </summary>
    public List<List<string>> CompatibleCombinations { get; set; } = new();

    /// <summary>
    /// 冲突的促销对
    /// </summary>
    public List<(string, string, string)> ConflictingPairs { get; set; } = new();

    /// <summary>
    /// 兼容性分析
    /// </summary>
    public string CompatibilityAnalysis { get; set; } = string.Empty;
}

/// <summary>
/// 计算状态
/// </summary>
public class CalculationStatus
{
    /// <summary>
    /// 计算ID
    /// </summary>
    public string CalculationId { get; set; } = string.Empty;

    /// <summary>
    /// 状态
    /// </summary>
    public CalculationState State { get; set; }

    /// <summary>
    /// 进度百分比
    /// </summary>
    public int ProgressPercentage { get; set; }

    /// <summary>
    /// 当前阶段
    /// </summary>
    public string CurrentPhase { get; set; } = string.Empty;

    /// <summary>
    /// 已耗时（毫秒）
    /// </summary>
    public long ElapsedTimeMs { get; set; }

    /// <summary>
    /// 预估剩余时间（毫秒）
    /// </summary>
    public long EstimatedRemainingTimeMs { get; set; }

    /// <summary>
    /// 中间结果
    /// </summary>
    public object? IntermediateResult { get; set; }
}

/// <summary>
/// 计算状态枚举
/// </summary>
public enum CalculationState
{
    /// <summary>排队中</summary>
    Queued,
    /// <summary>初始化</summary>
    Initializing,
    /// <summary>预分析</summary>
    PreAnalyzing,
    /// <summary>规则筛选</summary>
    FilteringRules,
    /// <summary>条件验证</summary>
    ValidatingConditions,
    /// <summary>库存预占用</summary>
    ReservingInventory,
    /// <summary>优化搜索</summary>
    OptimizationSearch,
    /// <summary>分摊计算</summary>
    CalculatingAllocation,
    /// <summary>结果验证</summary>
    ValidatingResults,
    /// <summary>完成</summary>
    Completed,
    /// <summary>失败</summary>
    Failed,
    /// <summary>已取消</summary>
    Cancelled,
    /// <summary>超时</summary>
    TimedOut
}
