using PE2.PromotionEngine.Observability;
using PE2.PromotionEngine.Conditions;
using PE2.PromotionEngine.Inventory;
using PE2.PromotionEngine.Performance;
using PE2.PromotionEngine.Allocation;
using PE2.PromotionEngine.Rules.ExchangeRules;
using System.Collections.Concurrent;
using System.Text.Json;

namespace PE2.PromotionEngine.Core;

/// <summary>
/// 促销规则工厂实现
/// 负责创建和管理促销规则实例
/// </summary>
public sealed class PromotionRuleFactory : IPromotionRuleFactory, IDisposable
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<PromotionRuleFactory> _logger;
    private readonly ConcurrentDictionary<string, Type> _ruleTypes = new();
    private readonly ConcurrentDictionary<string, RulePerformanceMetrics> _performanceMetrics = new();
    private bool _disposed;

    /// <summary>
    /// 构造函数
    /// </summary>
    public PromotionRuleFactory(
        IServiceProvider serviceProvider,
        ILogger<PromotionRuleFactory> logger)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        RegisterBuiltInRuleTypes();
    }

    /// <summary>
    /// 创建促销规则实例
    /// </summary>
    public INewPromotionRule CreateRule(string ruleType, object configuration)
    {
        ArgumentException.ThrowIfNullOrEmpty(ruleType);
        ArgumentNullException.ThrowIfNull(configuration);

        if (!_ruleTypes.TryGetValue(ruleType, out var type))
        {
            throw new NotSupportedException($"不支持的规则类型: {ruleType}");
        }

        try
        {
            // 验证配置
            var validationResult = ValidateConfiguration(ruleType, configuration);
            if (!validationResult.IsValid)
            {
                throw new ArgumentException($"规则配置无效: {string.Join(", ", validationResult.Errors)}");
            }

            // 获取基础设施组件
            var logger = _serviceProvider.GetRequiredService<ILogger<NewPromotionRuleBase>>();
            var observability = _serviceProvider.GetRequiredService<IObservabilityEngine>();
            var conditionEngine = _serviceProvider.GetRequiredService<IConditionEngine>();
            var inventoryManager = _serviceProvider.GetRequiredService<IInventoryManager>();
            var performanceOptimizer = _serviceProvider.GetRequiredService<IPerformanceOptimizer>();
            var allocationEngine = _serviceProvider.GetRequiredService<IAllocationEngine>();

            // 创建规则实例
            var rule = (INewPromotionRule)Activator.CreateInstance(
                type,
                logger,
                observability,
                conditionEngine,
                inventoryManager,
                performanceOptimizer,
                allocationEngine,
                configuration)!;

            _logger.LogDebug("成功创建促销规则实例: {RuleType}, {RuleId}", ruleType, rule.Id);
            return rule;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建促销规则实例失败: {RuleType}", ruleType);
            throw;
        }
    }

    /// <summary>
    /// 获取支持的规则类型列表
    /// </summary>
    public IEnumerable<string> GetSupportedRuleTypes()
    {
        return _ruleTypes.Keys.ToList();
    }

    /// <summary>
    /// 验证规则配置
    /// </summary>
    public ValidationResult ValidateConfiguration(string ruleType, object configuration)
    {
        ArgumentException.ThrowIfNullOrEmpty(ruleType);
        ArgumentNullException.ThrowIfNull(configuration);

        if (!_ruleTypes.ContainsKey(ruleType))
        {
            return ValidationResult.Failure($"不支持的规则类型: {ruleType}");
        }

        var errors = new List<string>();
        var warnings = new List<string>();

        try
        {
            // 基本配置验证
            if (configuration is JsonElement jsonElement)
            {
                if (!jsonElement.TryGetProperty("id", out var idProperty) || 
                    string.IsNullOrEmpty(idProperty.GetString()))
                {
                    errors.Add("规则ID不能为空");
                }

                if (!jsonElement.TryGetProperty("name", out var nameProperty) || 
                    string.IsNullOrEmpty(nameProperty.GetString()))
                {
                    errors.Add("规则名称不能为空");
                }

                if (jsonElement.TryGetProperty("priority", out var priorityProperty))
                {
                    if (!priorityProperty.TryGetInt32(out var priority) || priority < 0)
                    {
                        warnings.Add("优先级应为非负整数");
                    }
                }

                if (jsonElement.TryGetProperty("maxApplications", out var maxAppProperty))
                {
                    if (!maxAppProperty.TryGetInt32(out var maxApp) || maxApp < 0)
                    {
                        errors.Add("最大应用次数应为非负整数");
                    }
                }
            }

            // 规则类型特定验证
            ValidateRuleSpecificConfiguration(ruleType, configuration, errors, warnings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证规则配置时发生异常: {RuleType}", ruleType);
            errors.Add($"配置验证异常: {ex.Message}");
        }

        if (errors.Count > 0)
        {
            return ValidationResult.Failure(errors.ToArray());
        }

        if (warnings.Count > 0)
        {
            return ValidationResult.SuccessWithWarnings(warnings.ToArray());
        }

        return ValidationResult.Success();
    }

    /// <summary>
    /// 注册规则类型
    /// </summary>
    public void RegisterRuleType<T>(string ruleType) where T : class, INewPromotionRule
    {
        ArgumentException.ThrowIfNullOrEmpty(ruleType);
        
        _ruleTypes.TryAdd(ruleType, typeof(T));
        _logger.LogDebug("注册促销规则类型: {RuleType} -> {Type}", ruleType, typeof(T).Name);
    }

    /// <summary>
    /// 获取规则性能指标
    /// </summary>
    public RulePerformanceMetrics? GetPerformanceMetrics(string ruleType)
    {
        return _performanceMetrics.TryGetValue(ruleType, out var metrics) ? metrics : null;
    }

    /// <summary>
    /// 更新规则性能指标
    /// </summary>
    public void UpdatePerformanceMetrics(string ruleType, string ruleId, long executionTimeMs, bool isSuccess)
    {
        var metrics = _performanceMetrics.GetOrAdd(ruleType, _ => new RulePerformanceMetrics
        {
            RuleId = ruleId,
            RuleType = ruleType,
            MinExecutionTimeMs = long.MaxValue
        });

        lock (metrics)
        {
            metrics.ExecutionCount++;
            if (isSuccess)
                metrics.SuccessCount++;
            else
                metrics.FailureCount++;

            metrics.LastExecutionTime = DateTime.UtcNow;
            metrics.MaxExecutionTimeMs = Math.Max(metrics.MaxExecutionTimeMs, executionTimeMs);
            metrics.MinExecutionTimeMs = Math.Min(metrics.MinExecutionTimeMs, executionTimeMs);
            
            // 计算平均执行时间
            var totalTime = metrics.AverageExecutionTimeMs * (metrics.ExecutionCount - 1) + executionTimeMs;
            metrics.AverageExecutionTimeMs = totalTime / metrics.ExecutionCount;
        }
    }

    /// <summary>
    /// 注册内置规则类型
    /// </summary>
    private void RegisterBuiltInRuleTypes()
    {
        // 这里将在后续迁移过程中添加具体的规则类型注册
        _logger.LogInformation("开始注册内置促销规则类型");
        
        // 交换规则类型 (6个) - 新架构
        RegisterRuleType<NewUnifiedSpecialPriceExchangeRule>("UnifiedSpecialPriceExchange");
        RegisterRuleType<NewUnifiedDiscountExchangeRule>("UnifiedDiscountExchange");
        // RegisterRuleType<NewUnifiedDiscountAmountExchangeRule>("UnifiedDiscountAmountExchange");
        // RegisterRuleType<NewCombinationSpecialPriceExchangeRule>("CombinationSpecialPriceExchange");
        // RegisterRuleType<NewCombinationDiscountExchangeRule>("CombinationDiscountExchange");
        // RegisterRuleType<NewCombinationDiscountAmountExchangeRule>("CombinationDiscountAmountExchange");

        // 买免规则类型 (2个)
        // RegisterRuleType<ProductBuyFreeRule>("ProductBuyFree");
        // RegisterRuleType<CombinationBuyFreeRule>("CombinationBuyFree");

        // 现金折扣规则类型 (3个)
        // RegisterRuleType<UnifiedCashDiscountRule>("UnifiedCashDiscount");
        // RegisterRuleType<GradientCashDiscountRule>("GradientCashDiscount");
        // RegisterRuleType<CombinationCashDiscountRule>("CombinationCashDiscount");

        // 折扣规则类型 (6个)
        // RegisterRuleType<UnifiedDiscountRule>("UnifiedDiscount");
        // RegisterRuleType<TieredDiscountRule>("TieredDiscount");
        // RegisterRuleType<ProgressiveDiscountRule>("ProgressiveDiscount");
        // RegisterRuleType<CombinationDiscountRule>("CombinationDiscount");
        // RegisterRuleType<FreeFormDiscountRule>("FreeFormDiscount");
        // RegisterRuleType<CyclicDiscountRule>("CyclicDiscount");

        // 特价规则类型 (4个)
        // RegisterRuleType<UnifiedSpecialPriceRule>("UnifiedSpecialPrice");
        // RegisterRuleType<TieredSpecialPriceRule>("TieredSpecialPrice");
        // RegisterRuleType<CombinationSpecialPriceRule>("CombinationSpecialPrice");
        // RegisterRuleType<IndividualSpecialPriceRule>("IndividualSpecialPrice");

        // 买赠规则类型 (3个)
        // RegisterRuleType<UnifiedGiftRule>("UnifiedGift");
        // RegisterRuleType<TieredGiftRule>("TieredGift");
        // RegisterRuleType<CombinationGiftRule>("CombinationGift");

        _logger.LogInformation("内置促销规则类型注册完成，共注册 {Count} 种类型", _ruleTypes.Count);
    }

    /// <summary>
    /// 验证规则特定配置
    /// </summary>
    private static void ValidateRuleSpecificConfiguration(string ruleType, object configuration, List<string> errors, List<string> warnings)
    {
        // 根据不同规则类型进行特定验证
        // 这里将在具体规则迁移时实现
        switch (ruleType)
        {
            case "UnifiedSpecialPriceExchange":
            case "UnifiedDiscountExchange":
            case "UnifiedDiscountAmountExchange":
                // 交换规则验证逻辑
                break;
                
            case "ProductBuyFree":
            case "CombinationBuyFree":
                // 买免规则验证逻辑
                break;
                
            // 其他规则类型的验证逻辑...
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _ruleTypes.Clear();
            _performanceMetrics.Clear();
            _disposed = true;
        }
    }
}
