// JSON预览组件
const JsonPreview = {
  props: {
    data: Object,
    title: {
      type: String,
      default: 'JSON预览'
    }
  },
  template: `
    <div class="preview-container">
      <div class="preview-header">
        <div class="preview-title">{{ title }}</div>
        <button class="copy-btn" @click="copyToClipboard" title="复制JSON">复制</button>
      </div>
      
      <div v-if="!data || Object.keys(data).length === 0" class="empty-state">
        <h3>暂无数据</h3>
        <p>请在左侧选择促销类型并填写配置信息</p>
      </div>
      
      <div v-else class="json-preview">{{ formattedJson }}</div>
      
      <div class="preview-actions" style="margin-top: 16px;">
        <button class="add-btn" @click="downloadJson" :disabled="!hasData">下载JSON</button>
        <button class="add-btn" @click="validateJson" :disabled="!hasData" style="margin-left: 8px;">验证配置</button>
      </div>
      
      <div v-if="validationMessage" :class="['validation-message', validationStatus]" style="margin-top: 12px; padding: 8px; border-radius: 4px;">
        {{ validationMessage }}
      </div>
    </div>
  `,
  data() {
    return {
      validationMessage: '',
      validationStatus: ''
    };
  },
  computed: {
    formattedJson() {
      if (!this.data) return '';
      try {
        return JSON.stringify(this.data, null, 2);
      } catch (error) {
        return '无效的JSON数据';
      }
    },
    hasData() {
      return this.data && Object.keys(this.data).length > 0;
    }
  },
  methods: {
    async copyToClipboard() {
      try {
        await navigator.clipboard.writeText(this.formattedJson);
        this.showNotification('JSON已复制到剪贴板', 'success');
      } catch (error) {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = this.formattedJson;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        this.showNotification('JSON已复制到剪贴板', 'success');
      }
    },
    
    downloadJson() {
      if (!this.hasData) return;
      
      const dataStr = this.formattedJson;
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `${this.data.id || 'promotion-rule'}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      this.showNotification('JSON文件下载成功', 'success');
    },
    
    validateJson() {
      if (!this.hasData) return;
      
      const requiredFields = ['$type', 'id', 'name'];
      const missingFields = requiredFields.filter(field => !this.data[field]);
      
      if (missingFields.length > 0) {
        this.validationMessage = `缺少必填字段: ${missingFields.join(', ')}`;
        this.validationStatus = 'error';
        return;
      }
      
      // 检查ID格式
      if (!/^[A-Z_][A-Z0-9_]*$/.test(this.data.id)) {
        this.validationMessage = 'ID格式不正确，应使用大写字母、数字和下划线，且以字母或下划线开头';
        this.validationStatus = 'error';
        return;
      }
      
      // 检查时间范围
      if (this.data.startTime && this.data.endTime) {
        const start = new Date(this.data.startTime);
        const end = new Date(this.data.endTime);
        if (start >= end) {
          this.validationMessage = '结束时间必须晚于开始时间';
          this.validationStatus = 'error';
          return;
        }
      }
      
      this.validationMessage = '配置验证通过！';
      this.validationStatus = 'success';
      
      setTimeout(() => {
        this.validationMessage = '';
        this.validationStatus = '';
      }, 3000);
    },
    
    showNotification(message, type) {
      // 创建通知元素
      const notification = document.createElement('div');
      notification.className = `notification ${type}`;
      notification.textContent = message;
      
      document.body.appendChild(notification);
      
      // 3秒后移除通知
      setTimeout(() => {
        if (document.body.contains(notification)) {
          document.body.removeChild(notification);
        }
      }, 3000);
    }
  }
};
