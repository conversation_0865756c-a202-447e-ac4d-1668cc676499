[{"$type": "BuyXGetY", "id": "GRADIENT_CUSTOMER_BENEFIT_100", "name": "梯度赠品 - 客户利益最大化", "description": "买2送1，买3送2", "priority": 25, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftType": "GradientGift", "gradientStrategy": "ByGradient", "giftSelectionStrategy": "MerchantBenefit", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "buyConditions": [{"productIds": ["A", "B"], "requiredQuantity": 1}], "giftConditions": [], "combinationConditions": [], "gradientGiftConditions": [{"gradientLevel": 1, "requiredQuantity": 2, "requiredAmount": 0, "giftProductIds": ["C"], "giftQuantity": 1, "description": "买2送1"}, {"gradientLevel": 2, "requiredQuantity": 3, "requiredAmount": 0, "giftProductIds": ["C"], "giftQuantity": 2, "description": "买3送2"}]}]