/* 全局样式 */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f5f5f5;
}

.promotion-config-container {
    display: flex;
    height: 100vh;
    background: #f5f5f5;
}

/* 侧边栏样式 */
.sidebar {
    width: 320px;
    background: white;
    border-right: 1px solid #e8e8e8;
    overflow-y: auto;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e8e8e8;
    background: #fafafa;
}

.sidebar-header h2 {
    margin: 0;
    color: #1890ff;
    font-size: 18px;
    font-weight: 600;
}

/* 促销类型选择器 */
.promotion-categories {
    padding: 16px;
}

.category-group {
    margin-bottom: 24px;
}

.category-title {
    font-size: 14px;
    font-weight: 600;
    color: #595959;
    margin-bottom: 12px;
    padding: 0 4px;
}

.promotion-type-card {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 8px;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.promotion-type-card:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.promotion-type-card.selected {
    border-color: #1890ff;
    background: #e6f7ff;
}

.type-icon {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    background: #1890ff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    color: white;
    font-size: 16px;
}

.type-info h4 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 500;
    color: #262626;
}

.type-info p {
    margin: 0;
    font-size: 12px;
    color: #8c8c8c;
    line-height: 1.4;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.content-header {
    padding: 20px 24px;
    background: white;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.content-header h1 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #262626;
}

.header-actions {
    display: flex;
    gap: 12px;
}

.content-body {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.form-container {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    background: white;
    margin: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-container {
    width: 400px;
    padding: 24px;
    overflow-y: auto;
    background: white;
    margin: 16px 16px 16px 0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 动态表单样式 */
.form-section {
    margin-bottom: 32px;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #1890ff;
}

.form-row {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
}

.form-col {
    flex: 1;
}

.form-item {
    margin-bottom: 16px;
}

.form-item label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: #262626;
}

.form-item input,
.form-item select,
.form-item textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-item input:focus,
.form-item select:focus,
.form-item textarea:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 数组字段样式 */
.array-field {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    padding: 16px;
    background: #fafafa;
}

.array-field-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.array-field-title {
    font-weight: 500;
    color: #262626;
}

.array-item {
    background: white;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 16px;
    margin-bottom: 12px;
    position: relative;
}

.array-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.array-item-title {
    font-weight: 500;
    color: #595959;
}

.remove-btn {
    background: #ff4d4f;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 12px;
}

.remove-btn:hover {
    background: #ff7875;
}

.add-btn {
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
}

.add-btn:hover {
    background: #40a9ff;
}

/* JSON预览样式 */
.json-preview {
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    padding: 16px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 500px;
    overflow-y: auto;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.preview-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
}

.copy-btn {
    background: #52c41a;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    font-size: 12px;
}

.copy-btn:hover {
    background: #73d13d;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .preview-container {
        width: 300px;
    }
}

@media (max-width: 992px) {
    .promotion-config-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
        max-height: 300px;
    }
    
    .content-body {
        flex-direction: column;
    }
    
    .preview-container {
        width: 100%;
        margin: 0 16px 16px 16px;
    }
}

/* 加载状态 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #8c8c8c;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #8c8c8c;
}

.empty-state h3 {
    margin: 0 0 8px 0;
    color: #595959;
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
    cursor: help;
}

.tooltip-text {
    visibility: hidden;
    background-color: #333;
    color: white;
    text-align: center;
    border-radius: 4px;
    padding: 8px 12px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -80px;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 16px;
    border-radius: 4px;
    color: white;
    font-size: 14px;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

.notification.success {
    background: #52c41a;
}

.notification.error {
    background: #ff4d4f;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
