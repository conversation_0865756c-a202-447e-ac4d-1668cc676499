[{"$type": "UnifiedGift", "id": "VALUE_PROPORTION_001", "name": "按价值比例分摊测试 - 买2件A送1件B", "description": "测试按价值比例分摊法：A原价100元，B原价50元，顾客支付200元", "priority": 50, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "giftSelectionStrategy": "CustomerBenefit", "buyConditions": [{"productIds": ["A"], "requiredQuantity": 2}], "giftConditions": [{"productIds": ["B"], "giftQuantity": 1}]}, {"$type": "UnifiedGift", "id": "VALUE_PROPORTION_002", "name": "按价值比例分摊测试 - 买2件A送1件A", "description": "测试同商品买赠：A原价100元，顾客支付200元，共3件A", "priority": 50, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "isByAmount": false, "minAmount": 0, "giftSameProduct": true, "giftSelectionStrategy": "CustomerBenefit", "buyConditions": [{"productIds": ["A"], "requiredQuantity": 2}], "giftConditions": [{"productIds": ["A"], "giftQuantity": 1}]}, {"$type": "UnifiedGift", "id": "VALUE_PROPORTION_003", "name": "按价值比例分摊测试 - 复杂场景", "description": "测试复杂场景：买3件A送2件B，A原价120元，B原价60元", "priority": 50, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "giftSelectionStrategy": "CustomerBenefit", "buyConditions": [{"productIds": ["A"], "requiredQuantity": 3}], "giftConditions": [{"productIds": ["B"], "giftQuantity": 2}]}, {"$type": "GradientGift", "id": "VALUE_PROPORTION_004", "name": "梯度赠品按价值比例分摊测试", "description": "测试梯度赠品的按价值比例分摊", "priority": 50, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "gradientStrategy": "ByGradient", "giftSelectionStrategy": "CustomerBenefit", "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1}], "gradientGiftConditions": [{"gradientLevel": 1, "requiredQuantity": 2, "requiredAmount": 0, "giftProductIds": ["B"], "giftQuantity": 1, "description": "购买2件A送1件B"}]}, {"$type": "CombinationGift", "id": "VALUE_PROPORTION_005", "name": "组合赠品按价值比例分摊测试", "description": "测试组合赠品的按价值比例分摊", "priority": 50, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftSelectionStrategy": "CustomerBenefit", "combinationConditions": [{"productId": "A", "requiredQuantity": 1, "minAmount": 0}, {"productId": "B", "requiredQuantity": 1, "minAmount": 0}], "giftConditions": [{"productIds": ["C"], "giftQuantity": 1}]}]