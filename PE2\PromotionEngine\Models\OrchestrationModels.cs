using PE2.Models;

namespace PE2.PromotionEngine.Models;

/// <summary>
/// 促销预分析结果
/// </summary>
public sealed class PromotionPreAnalysis
{
    /// <summary>
    /// 购物车ID
    /// </summary>
    public required string CartId { get; init; }

    /// <summary>
    /// 分析时间
    /// </summary>
    public DateTime AnalysisTime { get; init; }

    /// <summary>
    /// 购物车特征
    /// </summary>
    public CartCharacteristics? CartCharacteristics { get; set; }

    /// <summary>
    /// 规则适用性分析
    /// </summary>
    public List<RuleApplicabilityInfo> RuleApplicability { get; set; } = new();

    /// <summary>
    /// 优化潜力分析
    /// </summary>
    public OptimizationPotentialInfo? OptimizationPotential { get; set; }

    /// <summary>
    /// 潜在冲突列表
    /// </summary>
    public List<string> PotentialConflicts { get; set; } = new();
}

/// <summary>
/// 兼容性验证结果
/// </summary>
public sealed class CompatibilityResult
{
    /// <summary>
    /// 是否兼容
    /// </summary>
    public bool IsCompatible { get; set; }

    /// <summary>
    /// 冲突的促销ID列表
    /// </summary>
    public List<string> ConflictingPromotions { get; set; } = new();

    /// <summary>
    /// 兼容性问题描述
    /// </summary>
    public List<string> CompatibilityIssues { get; set; } = new();

    /// <summary>
    /// 建议的解决方案
    /// </summary>
    public List<string> SuggestedSolutions { get; set; } = new();
}

/// <summary>
/// 回滚结果
/// </summary>
public sealed class RollbackResult
{
    /// <summary>
    /// 计算ID
    /// </summary>
    public required string CalculationId { get; init; }

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// 回滚时间
    /// </summary>
    public DateTime? RollbackTime { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 回滚的操作列表
    /// </summary>
    public List<string> RolledBackOperations { get; set; } = new();
}

/// <summary>
/// 计算统计信息
/// </summary>
public sealed class CalculationStatistics
{
    /// <summary>
    /// 总计算时间（毫秒）
    /// </summary>
    public long TotalCalculationTimeMs { get; set; }

    /// <summary>
    /// 规则评估数量
    /// </summary>
    public int RulesEvaluated { get; set; }

    /// <summary>
    /// 应用的促销数量
    /// </summary>
    public int PromotionsApplied { get; set; }

    /// <summary>
    /// 总折扣金额
    /// </summary>
    public decimal TotalDiscount { get; set; }

    /// <summary>
    /// 缓存命中次数
    /// </summary>
    public int CacheHits { get; set; }

    /// <summary>
    /// 缓存未命中次数
    /// </summary>
    public int CacheMisses { get; set; }

    /// <summary>
    /// 内存使用峰值（字节）
    /// </summary>
    public long PeakMemoryUsageBytes { get; set; }

    /// <summary>
    /// 验证时间（毫秒）
    /// </summary>
    public long ValidationTimeMs { get; set; }

    /// <summary>
    /// 验证是否成功
    /// </summary>
    public bool IsValidationSuccessful { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 性能指标
    /// </summary>
    public Dictionary<string, object> PerformanceMetrics { get; set; } = new();
}

/// <summary>
/// 处理后的购物车
/// </summary>
public sealed class ProcessedCart
{
    /// <summary>
    /// 原始购物车
    /// </summary>
    public ShoppingCart OriginalCart { get; }

    /// <summary>
    /// 购物车项目列表
    /// </summary>
    public List<ProcessedCartItem> Items { get; set; } = new();

    /// <summary>
    /// 总金额
    /// </summary>
    public decimal TotalAmount => Items.Sum(i => i.TotalPrice);

    /// <summary>
    /// 实际总金额（应用促销后）
    /// </summary>
    public decimal ActualTotalAmount => Items.Sum(i => i.ActualSubTotal);

    /// <summary>
    /// 总折扣金额
    /// </summary>
    public decimal TotalDiscountAmount => Items.Sum(i => i.TotalDiscountAmount);

    /// <summary>
    /// 构造函数
    /// </summary>
    public ProcessedCart(ShoppingCart originalCart)
    {
        OriginalCart = originalCart;
        
        // 初始化处理后的购物车项目
        foreach (var item in originalCart.Items)
        {
            Items.Add(new ProcessedCartItem
            {
                Product = item.Product,
                Quantity = item.Quantity,
                UnitPrice = item.UnitPrice,
                TotalPrice = item.SubTotal,
                ActualUnitPrice = item.UnitPrice,
                ActualSubTotal = item.SubTotal,
                IsGift = false,
                IsEligibleForPromotion = true,
                TotalDiscountAmount = 0,
                AppliedPromotionRuleIds = new List<string>(),
                PromotionDetails = new List<PromotionDetail>()
            });
        }
    }

    /// <summary>
    /// 克隆方法
    /// </summary>
    public ProcessedCart Clone()
    {
        var cloned = new ProcessedCart(OriginalCart.Clone());
        cloned.Items = Items.Select(i => i.Clone()).ToList();
        return cloned;
    }
}

/// <summary>
/// 处理后的购物车项目
/// </summary>
public sealed class ProcessedCartItem
{
    /// <summary>
    /// 商品信息
    /// </summary>
    public required Product Product { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 原始单价
    /// </summary>
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// 原始总价
    /// </summary>
    public decimal TotalPrice { get; set; }

    /// <summary>
    /// 实际单价（应用促销后）
    /// </summary>
    public decimal ActualUnitPrice { get; set; }

    /// <summary>
    /// 实际小计（应用促销后）
    /// </summary>
    public decimal ActualSubTotal { get; set; }

    /// <summary>
    /// 是否为赠品
    /// </summary>
    public bool IsGift { get; set; }

    /// <summary>
    /// 是否符合促销条件
    /// </summary>
    public bool IsEligibleForPromotion { get; set; }

    /// <summary>
    /// 总折扣金额
    /// </summary>
    public decimal TotalDiscountAmount { get; set; }

    /// <summary>
    /// 应用的促销规则ID列表
    /// </summary>
    public List<string> AppliedPromotionRuleIds { get; set; } = new();

    /// <summary>
    /// 促销详情列表
    /// </summary>
    public List<PromotionDetail> PromotionDetails { get; set; } = new();

    /// <summary>
    /// 克隆方法
    /// </summary>
    public ProcessedCartItem Clone()
    {
        return new ProcessedCartItem
        {
            Product = Product,
            Quantity = Quantity,
            UnitPrice = UnitPrice,
            TotalPrice = TotalPrice,
            ActualUnitPrice = ActualUnitPrice,
            ActualSubTotal = ActualSubTotal,
            IsGift = IsGift,
            IsEligibleForPromotion = IsEligibleForPromotion,
            TotalDiscountAmount = TotalDiscountAmount,
            AppliedPromotionRuleIds = new List<string>(AppliedPromotionRuleIds),
            PromotionDetails = PromotionDetails.Select(p => p.Clone()).ToList()
        };
    }
}

/// <summary>
/// 促销详情
/// </summary>
public sealed class PromotionDetail
{
    /// <summary>
    /// 规则ID
    /// </summary>
    public required string RuleId { get; set; }

    /// <summary>
    /// 规则名称
    /// </summary>
    public required string RuleName { get; set; }

    /// <summary>
    /// 促销类型
    /// </summary>
    public required string PromotionType { get; set; }

    /// <summary>
    /// 折扣金额
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否为赠品
    /// </summary>
    public bool IsGift { get; set; }

    /// <summary>
    /// 克隆方法
    /// </summary>
    public PromotionDetail Clone()
    {
        return new PromotionDetail
        {
            RuleId = RuleId,
            RuleName = RuleName,
            PromotionType = PromotionType,
            DiscountAmount = DiscountAmount,
            Description = Description,
            IsGift = IsGift
        };
    }
}
