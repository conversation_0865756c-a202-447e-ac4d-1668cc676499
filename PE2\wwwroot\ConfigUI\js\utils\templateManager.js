// 模板管理器 - 提供常用促销模板和自定义模板功能
const TemplateManager = {
  
  // 预定义模板
  predefinedTemplates: {
    buyGift: [
      {
        id: 'buy1get1',
        name: '买一赠一',
        description: '购买指定商品满1件即可获得同款商品1件',
        icon: '🎁',
        template: {
          $type: 'UnifiedGift',
          name: '买一赠一活动',
          description: '购买指定商品满1件即可获得同款商品1件',
          priority: 10,
          isEnabled: true,
          isRepeatable: false,
          maxApplications: 1,
          canStackWithOthers: false,
          productExclusivity: 'SameType',
          buyConditions: [{
            productIds: [],
            requiredQuantity: 1
          }],
          giftConditions: [{
            giftProductIds: [],
            giftQuantity: 1,
            description: '赠送同款商品'
          }]
        }
      },
      {
        id: 'buy2get1',
        name: '买二赠一',
        description: '购买指定商品满2件即可获得同款商品1件',
        icon: '🎉',
        template: {
          $type: 'UnifiedGift',
          name: '买二赠一活动',
          description: '购买指定商品满2件即可获得同款商品1件',
          priority: 10,
          isEnabled: true,
          isRepeatable: true,
          maxApplications: 0,
          canStackWithOthers: false,
          productExclusivity: 'SameType',
          buyConditions: [{
            productIds: [],
            requiredQuantity: 2
          }],
          giftConditions: [{
            giftProductIds: [],
            giftQuantity: 1,
            description: '赠送同款商品'
          }]
        }
      },
      {
        id: 'crossSellGift',
        name: '交叉销售赠品',
        description: '购买A商品满指定金额赠送B商品',
        icon: '🔄',
        template: {
          $type: 'UnifiedGift',
          name: '交叉销售赠品活动',
          description: '购买指定商品满指定金额赠送其他商品',
          priority: 15,
          isEnabled: true,
          isRepeatable: true,
          maxApplications: 0,
          canStackWithOthers: true,
          productExclusivity: 'None',
          buyConditions: [{
            productIds: [],
            requiredAmount: 100
          }],
          giftConditions: [{
            giftProductIds: [],
            giftQuantity: 1,
            description: '满额赠品'
          }]
        }
      }
    ],
    
    discount: [
      {
        id: 'volume10',
        name: '满量10%折扣',
        description: '购买满指定数量享受10%折扣',
        icon: '📊',
        template: {
          $type: 'VolumeDiscount',
          name: '满量折扣活动',
          description: '购买满指定数量享受折扣',
          priority: 5,
          isEnabled: true,
          isRepeatable: true,
          maxApplications: 0,
          canStackWithOthers: true,
          productExclusivity: 'None',
          productIds: [],
          discountTiers: [{
            minQuantity: 3,
            discountRate: 10,
            description: '满3件享受9折'
          }]
        }
      },
      {
        id: 'amount20',
        name: '满额20%折扣',
        description: '购买满指定金额享受20%折扣',
        icon: '💰',
        template: {
          $type: 'AmountDiscount',
          name: '满额折扣活动',
          description: '购买满指定金额享受折扣',
          priority: 5,
          isEnabled: true,
          isRepeatable: false,
          maxApplications: 1,
          canStackWithOthers: true,
          productExclusivity: 'None',
          productIds: [],
          minAmount: 200,
          discountRate: 20
        }
      }
    ],
    
    bundle: [
      {
        id: 'combo3',
        name: '三件套餐',
        description: '指定三款商品组合优惠价',
        icon: '📦',
        template: {
          $type: 'BundleOffer',
          name: '三件套餐优惠',
          description: '指定三款商品组合享受优惠价格',
          priority: 20,
          isEnabled: true,
          isRepeatable: true,
          maxApplications: 0,
          canStackWithOthers: false,
          productExclusivity: 'SameType',
          bundleProducts: [
            { productId: '', quantity: 1 },
            { productId: '', quantity: 1 },
            { productId: '', quantity: 1 }
          ],
          bundlePrice: 0,
          originalPrice: 0
        }
      }
    ]
  },
  
  // 自定义模板存储
  customTemplates: [],
  
  // 获取所有模板
  getAllTemplates() {
    const templates = [];
    
    // 添加预定义模板
    Object.keys(this.predefinedTemplates).forEach(category => {
      const categoryTemplates = this.predefinedTemplates[category].map(template => ({
        ...template,
        category,
        isPredefined: true
      }));
      templates.push(...categoryTemplates);
    });
    
    // 添加自定义模板
    const customTemplates = this.customTemplates.map(template => ({
      ...template,
      isPredefined: false
    }));
    templates.push(...customTemplates);
    
    return templates;
  },
  
  // 按分类获取模板
  getTemplatesByCategory(category) {
    if (category === 'custom') {
      return this.customTemplates;
    }
    return this.predefinedTemplates[category] || [];
  },
  
  // 获取单个模板
  getTemplate(templateId) {
    const allTemplates = this.getAllTemplates();
    return allTemplates.find(template => template.id === templateId);
  },
  
  // 保存自定义模板
  saveCustomTemplate(templateData) {
    const template = {
      id: this.generateTemplateId(),
      name: templateData.name || '自定义模板',
      description: templateData.description || '',
      icon: templateData.icon || '⚙️',
      category: 'custom',
      template: { ...templateData.template },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    this.customTemplates.push(template);
    this.saveToLocalStorage();
    
    return template;
  },
  
  // 更新自定义模板
  updateCustomTemplate(templateId, updates) {
    const index = this.customTemplates.findIndex(t => t.id === templateId);
    if (index !== -1) {
      this.customTemplates[index] = {
        ...this.customTemplates[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      this.saveToLocalStorage();
      return this.customTemplates[index];
    }
    return null;
  },
  
  // 删除自定义模板
  deleteCustomTemplate(templateId) {
    const index = this.customTemplates.findIndex(t => t.id === templateId);
    if (index !== -1) {
      const deleted = this.customTemplates.splice(index, 1)[0];
      this.saveToLocalStorage();
      return deleted;
    }
    return null;
  },
  
  // 生成模板ID
  generateTemplateId() {
    return 'custom_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  },
  
  // 从模板应用数据
  applyTemplate(templateId) {
    const template = this.getTemplate(templateId);
    if (template) {
      // 深拷贝模板数据
      const data = JSON.parse(JSON.stringify(template.template));
      
      // 生成新的规则ID
      data.id = this.generateRuleId();
      
      return data;
    }
    return null;
  },
  
  // 生成规则ID
  generateRuleId() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 6);
    return `rule_${timestamp}_${random}`;
  },
  
  // 保存到本地存储
  saveToLocalStorage() {
    try {
      localStorage.setItem('promotion_custom_templates', JSON.stringify(this.customTemplates));
    } catch (error) {
      console.warn('无法保存模板到本地存储:', error);
    }
  },
  
  // 从本地存储加载
  loadFromLocalStorage() {
    try {
      const stored = localStorage.getItem('promotion_custom_templates');
      if (stored) {
        this.customTemplates = JSON.parse(stored);
      }
    } catch (error) {
      console.warn('无法从本地存储加载模板:', error);
      this.customTemplates = [];
    }
  },
  
  // 导出模板
  exportTemplate(templateId) {
    const template = this.getTemplate(templateId);
    if (template) {
      const exportData = {
        ...template,
        exportedAt: new Date().toISOString(),
        version: '1.0'
      };
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
        type: 'application/json' 
      });
      
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `template_${template.name}_${Date.now()}.json`;
      a.click();
      URL.revokeObjectURL(url);
    }
  },
  
  // 导入模板
  async importTemplate(file) {
    try {
      const text = await file.text();
      const templateData = JSON.parse(text);
      
      // 验证模板格式
      if (!templateData.template || !templateData.name) {
        throw new Error('无效的模板格式');
      }
      
      // 生成新ID避免冲突
      templateData.id = this.generateTemplateId();
      templateData.category = 'custom';
      templateData.createdAt = new Date().toISOString();
      templateData.updatedAt = new Date().toISOString();
      
      this.customTemplates.push(templateData);
      this.saveToLocalStorage();
      
      return templateData;
    } catch (error) {
      throw new Error('导入模板失败: ' + error.message);
    }
  },
  
  // 搜索模板
  searchTemplates(query) {
    const allTemplates = this.getAllTemplates();
    const lowerQuery = query.toLowerCase();
    
    return allTemplates.filter(template => 
      template.name.toLowerCase().includes(lowerQuery) ||
      template.description.toLowerCase().includes(lowerQuery) ||
      template.category.toLowerCase().includes(lowerQuery)
    );
  },
  
  // 获取模板使用统计
  getTemplateStats() {
    const stats = {
      total: this.getAllTemplates().length,
      predefined: Object.values(this.predefinedTemplates).flat().length,
      custom: this.customTemplates.length,
      byCategory: {}
    };
    
    Object.keys(this.predefinedTemplates).forEach(category => {
      stats.byCategory[category] = this.predefinedTemplates[category].length;
    });
    
    if (this.customTemplates.length > 0) {
      stats.byCategory.custom = this.customTemplates.length;
    }
    
    return stats;
  }
};

// 初始化时加载本地存储的模板
TemplateManager.loadFromLocalStorage();

// 导出到全局
window.TemplateManager = TemplateManager;
