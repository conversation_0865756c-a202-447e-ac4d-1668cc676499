using PE2.Models;
using PE2.PromotionEngine.Models;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Rules.ExchangeRules;

/// <summary>
/// 换购促销规则基类
/// 核心原则：换购商品必须已在购物车中（POS扫码原则），不能程序添加新商品
/// </summary>
public abstract class BaseExchangeRule : PromotionRuleBase
{
    /// <summary>
    /// 换购策略：梯度换购 vs 全部换购
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public ExchangeStrategy ExchangeStrategy { get; set; } = ExchangeStrategy.ByGradient;

    /// <summary>
    /// 换购商品选择策略：客户利益最大化 vs 商家利益最大化
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public ExchangeSelectionStrategy ExchangeSelectionStrategy { get; set; } = ExchangeSelectionStrategy.CustomerBenefit;

    /// <summary>
    /// 验证换购商品是否在购物车中
    /// POS系统核心：只能对已扫码的商品进行换购，不能添加新商品
    /// </summary>
    protected bool ValidateExchangeProductsInCart(ShoppingCart cart, List<string> exchangeProductIds)
    {
        foreach (var productId in exchangeProductIds)
        {
            var hasProduct = cart.Items.Any(x => x.Product.Id == productId && x.Quantity > 0);
            if (!hasProduct)
            {
                return false; // 换购商品不在购物车中，条件不成立
            }
        }
        return true;
    }

    /// <summary>
    /// 根据策略选择最优的换购商品
    /// </summary>
    protected List<string> SelectOptimalExchangeProducts(List<string> candidateProductIds, ShoppingCart cart, int requiredQuantity)
    {
        if (!candidateProductIds.Any())
            return new List<string>();

        // 只考虑购物车中实际存在的商品
        var availableProducts = candidateProductIds
            .Where(id => cart.Items.Any(x => x.Product.Id == id && x.Quantity > 0))
            .ToList();

        if (!availableProducts.Any())
            return new List<string>();

        var productPrices = new List<(string ProductId, decimal Price)>();
        foreach (var productId in availableProducts)
        {
            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
            if (cartItem != null)
            {
                productPrices.Add((productId, cartItem.UnitPrice));
            }
        }

        if (!productPrices.Any())
            return new List<string>();

        // 根据策略排序
        var sortedProducts = ExchangeSelectionStrategy switch
        {
            ExchangeSelectionStrategy.CustomerBenefit => productPrices.OrderByDescending(p => p.Price), // 客户利益：换购高价商品
            ExchangeSelectionStrategy.MerchantBenefit => productPrices.OrderBy(p => p.Price), // 商家利益：换购低价商品
            _ => productPrices.OrderByDescending(p => p.Price)
        };

        var selectedProducts = new List<string>();
        var remainingQuantity = requiredQuantity;

        foreach (var (productId, _) in sortedProducts)
        {
            if (remainingQuantity <= 0) break;

            var availableQuantity = cart.Items
                .Where(x => x.Product.Id == productId)
                .Sum(x => x.Quantity);

            if (availableQuantity > 0)
            {
                selectedProducts.Add(productId);
                remainingQuantity--;
            }
        }

        return selectedProducts;
    }

    /// <summary>
    /// 计算换购折扣金额
    /// </summary>
    protected decimal CalculateExchangeDiscount(decimal originalPrice, decimal exchangePrice)
    {
        return Math.Max(0, originalPrice - exchangePrice);
    }

    /// <summary>
    /// 计算特价换购的实际支付金额
    /// </summary>
    protected decimal CalculateSpecialPriceExchange(decimal addAmount)
    {
        return addAmount;
    }

    /// <summary>
    /// 计算打折换购的实际支付金额
    /// </summary>
    protected decimal CalculateDiscountExchange(decimal originalPrice, decimal discountRate)
    {
        return originalPrice * discountRate;
    }

    /// <summary>
    /// 计算优惠换购的实际支付金额
    /// </summary>
    protected decimal CalculateDiscountAmountExchange(decimal originalPrice, decimal discountAmount)
    {
        return Math.Max(0, originalPrice - discountAmount);
    }
}

/// <summary>
/// 换购策略枚举
/// </summary>
public enum ExchangeStrategy
{
    /// <summary>
    /// 梯度换购：按梯度逐级换购
    /// </summary>
    ByGradient = 0,

    /// <summary>
    /// 全部换购：满足条件时换购所有可换购商品
    /// </summary>
    AllExchange = 1
}

/// <summary>
/// 换购商品选择策略枚举
/// </summary>
public enum ExchangeSelectionStrategy
{
    /// <summary>
    /// 客户利益最大化：优先换购高价值商品
    /// </summary>
    CustomerBenefit = 0,

    /// <summary>
    /// 商家利益最大化：优先换购低价值商品
    /// </summary>
    MerchantBenefit = 1
}
