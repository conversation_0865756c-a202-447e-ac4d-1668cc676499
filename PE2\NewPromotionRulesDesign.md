# POSPE2 新促销规则设计方案

## 🎯 设计目标

基于现有的6类促销规则（买赠、打折、特价、换购、买免、减现），设计10种创新的促销规则类型，以满足现代零售业务的多样化需求。

---

## 🚀 十大新促销规则详细设计

### 1. 时段促销规则 (TimePeriodPromotionRule) ⭐⭐⭐⭐⭐

#### 业务场景
- **早鸟价**：上午10点前购买享受特价
- **夜间特惠**：晚上8点后全场8折
- **午餐时段**：11:30-13:30餐饮类商品优惠
- **周末狂欢**：周六日特定商品买一送一

#### 技术实现
```csharp
public class TimePeriodPromotionRule : PromotionRuleBase
{
    public override string RuleType => "TimePeriodPromotion";
    
    /// <summary>
    /// 时段配置列表
    /// </summary>
    public List<TimePeriodConfig> TimePeriods { get; set; } = new();
    
    /// <summary>
    /// 促销类型：折扣、特价、买赠等
    /// </summary>
    public TimePeriodPromotionType PromotionType { get; set; }
    
    /// <summary>
    /// 促销参数
    /// </summary>
    public TimePeriodPromotionParams Parameters { get; set; }
}

public class TimePeriodConfig
{
    public DayOfWeek? DayOfWeek { get; set; }  // null表示每天
    public TimeSpan StartTime { get; set; }
    public TimeSpan EndTime { get; set; }
    public List<string> ApplicableProductIds { get; set; } = new();
    public List<string> ApplicableCategories { get; set; } = new();
}

public enum TimePeriodPromotionType
{
    Discount,      // 折扣
    SpecialPrice,  // 特价
    BuyGift,       // 买赠
    CashDiscount   // 减现
}
```

#### 配置示例
```json
{
  "$type": "TimePeriodPromotion",
  "id": "TIME_EARLY_BIRD_001",
  "name": "早鸟特价 - 上午10点前8折",
  "timePeriods": [
    {
      "startTime": "06:00:00",
      "endTime": "10:00:00",
      "applicableCategories": ["服装", "鞋帽"]
    }
  ],
  "promotionType": "Discount",
  "parameters": {
    "discountRate": 0.8
  }
}
```

### 2. 会员等级促销规则 (MembershipTierPromotionRule) ⭐⭐⭐⭐⭐

#### 业务场景
- **VIP专享**：金卡会员享受额外9折
- **新会员礼遇**：新注册会员首单免运费
- **等级阶梯**：不同等级享受不同折扣力度
- **积分倍增**：钻石会员积分3倍奖励

#### 技术实现
```csharp
public class MembershipTierPromotionRule : PromotionRuleBase
{
    public override string RuleType => "MembershipTierPromotion";
    
    /// <summary>
    /// 会员等级配置
    /// </summary>
    public List<MembershipTierConfig> TierConfigs { get; set; } = new();
    
    /// <summary>
    /// 是否可与其他促销叠加
    /// </summary>
    public bool CanStackWithOthers { get; set; } = true;
}

public class MembershipTierConfig
{
    public string TierName { get; set; }  // Bronze, Silver, Gold, Platinum, Diamond
    public int MinTierLevel { get; set; }  // 最低等级要求
    public decimal DiscountRate { get; set; }  // 折扣率
    public decimal CashDiscountAmount { get; set; }  // 减现金额
    public int PointsMultiplier { get; set; } = 1;  // 积分倍数
    public List<string> ExclusiveProductIds { get; set; } = new();  // 专享商品
}

public class CustomerMembershipInfo
{
    public string CustomerId { get; set; }
    public string TierName { get; set; }
    public int TierLevel { get; set; }
    public DateTime JoinDate { get; set; }
    public decimal TotalSpent { get; set; }
}
```

### 3. 库存清仓规则 (InventoryClearanceRule) ⭐⭐⭐⭐

#### 业务场景
- **库存预警**：库存低于10件时自动5折清仓
- **季末清仓**：过季商品库存清零促销
- **临期商品**：保质期临近商品特价处理
- **滞销处理**：长期滞销商品梯度降价

#### 技术实现
```csharp
public class InventoryClearanceRule : PromotionRuleBase
{
    public override string RuleType => "InventoryClearance";
    
    /// <summary>
    /// 清仓策略配置
    /// </summary>
    public List<ClearanceStrategy> Strategies { get; set; } = new();
    
    /// <summary>
    /// 库存数据源
    /// </summary>
    public string InventoryDataSource { get; set; }
}

public class ClearanceStrategy
{
    public ClearanceTriggerType TriggerType { get; set; }
    public int InventoryThreshold { get; set; }  // 库存阈值
    public int DaysThreshold { get; set; }  // 天数阈值
    public decimal DiscountRate { get; set; }  // 清仓折扣
    public List<string> ApplicableProductIds { get; set; } = new();
    public List<string> ApplicableCategories { get; set; } = new();
}

public enum ClearanceTriggerType
{
    LowInventory,     // 低库存
    SlowMoving,       // 滞销
    Expiring,         // 临期
    Seasonal          // 季节性
}
```

### 4. 节日主题促销规则 (HolidayThemePromotionRule) ⭐⭐⭐⭐

#### 业务场景
- **情人节专题**：情侣套装特价
- **母亲节感恩**：女装全场买二送一
- **双11狂欢**：预售+尾款组合优惠
- **春节特惠**：红色商品额外折扣

#### 技术实现
```csharp
public class HolidayThemePromotionRule : PromotionRuleBase
{
    public override string RuleType => "HolidayThemePromotion";
    
    /// <summary>
    /// 节日配置
    /// </summary>
    public HolidayConfig Holiday { get; set; }
    
    /// <summary>
    /// 主题促销配置
    /// </summary>
    public List<ThemePromotionConfig> ThemeConfigs { get; set; } = new();
}

public class HolidayConfig
{
    public string HolidayName { get; set; }  // 节日名称
    public DateTime StartDate { get; set; }  // 开始日期
    public DateTime EndDate { get; set; }    // 结束日期
    public List<string> ThemeKeywords { get; set; } = new();  // 主题关键词
    public List<string> ThemeColors { get; set; } = new();    // 主题颜色
}

public class ThemePromotionConfig
{
    public string ThemeName { get; set; }
    public List<string> MatchingProductIds { get; set; } = new();
    public List<string> MatchingCategories { get; set; } = new();
    public List<string> MatchingKeywords { get; set; } = new();
    public PromotionAction Action { get; set; }
}
```

### 5. 连带销售规则 (CrossSellPromotionRule) ⭐⭐⭐⭐⭐

#### 业务场景
- **智能推荐**：买衬衫推荐领带，组合优惠
- **套装搭配**：上衣+裤子+鞋子组合价
- **配件优惠**：买手机送手机壳半价
- **品类联动**：买护肤品送化妆品试用装

#### 技术实现
```csharp
public class CrossSellPromotionRule : PromotionRuleBase
{
    public override string RuleType => "CrossSellPromotion";
    
    /// <summary>
    /// 主商品配置
    /// </summary>
    public List<string> PrimaryProductIds { get; set; } = new();
    
    /// <summary>
    /// 推荐商品配置
    /// </summary>
    public List<CrossSellRecommendation> Recommendations { get; set; } = new();
    
    /// <summary>
    /// 推荐算法类型
    /// </summary>
    public RecommendationAlgorithm Algorithm { get; set; }
}

public class CrossSellRecommendation
{
    public List<string> RecommendedProductIds { get; set; } = new();
    public decimal DiscountRate { get; set; }  // 推荐商品折扣
    public decimal BundleDiscountRate { get; set; }  // 组合折扣
    public int MaxRecommendedQuantity { get; set; } = 1;
    public string RecommendationReason { get; set; }  // 推荐理由
}

public enum RecommendationAlgorithm
{
    RuleBased,        // 基于规则
    Collaborative,    // 协同过滤
    ContentBased,     // 基于内容
    Hybrid           // 混合算法
}
```

### 6. 积分兑换规则 (PointsExchangeRule) ⭐⭐⭐⭐

#### 业务场景
- **积分抵现**：1000积分抵10元现金
- **积分换购**：积分+现金购买特定商品
- **积分倍增**：特定商品购买获得双倍积分
- **积分专区**：纯积分兑换专属商品

#### 技术实现
```csharp
public class PointsExchangeRule : PromotionRuleBase
{
    public override string RuleType => "PointsExchange";
    
    /// <summary>
    /// 兑换配置列表
    /// </summary>
    public List<PointsExchangeConfig> ExchangeConfigs { get; set; } = new();
    
    /// <summary>
    /// 积分兑换比例 (积分:现金)
    /// </summary>
    public decimal PointsToCashRatio { get; set; } = 100;  // 100积分=1元
}

public class PointsExchangeConfig
{
    public PointsExchangeType ExchangeType { get; set; }
    public List<string> ApplicableProductIds { get; set; } = new();
    public int RequiredPoints { get; set; }  // 所需积分
    public decimal RequiredCash { get; set; }  // 所需现金
    public decimal DiscountAmount { get; set; }  // 抵扣金额
    public int MaxExchangeQuantity { get; set; } = 1;
}

public enum PointsExchangeType
{
    PointsOnly,       // 纯积分兑换
    PointsPlusCash,   // 积分+现金
    PointsDiscount,   // 积分抵现
    PointsMultiplier  // 积分倍增
}
```

### 7. 新客专享规则 (NewCustomerExclusiveRule) ⭐⭐⭐⭐

#### 业务场景
- **新客礼包**：首次购买享受特殊优惠
- **注册有礼**：新注册用户专享折扣
- **首单免邮**：新客户首单免运费
- **新人专区**：新客户专属商品和价格

#### 技术实现
```csharp
public class NewCustomerExclusiveRule : PromotionRuleBase
{
    public override string RuleType => "NewCustomerExclusive";
    
    /// <summary>
    /// 新客户定义
    /// </summary>
    public NewCustomerDefinition CustomerDefinition { get; set; }
    
    /// <summary>
    /// 专享优惠配置
    /// </summary>
    public List<ExclusiveBenefit> Benefits { get; set; } = new();
}

public class NewCustomerDefinition
{
    public int MaxDaysSinceRegistration { get; set; } = 30;  // 注册后多少天内算新客
    public int MaxPreviousOrders { get; set; } = 0;  // 最大历史订单数
    public decimal MaxPreviousSpent { get; set; } = 0;  // 最大历史消费金额
    public bool RequireFirstOrder { get; set; } = true;  // 是否要求首单
}

public class ExclusiveBenefit
{
    public BenefitType Type { get; set; }
    public decimal Value { get; set; }  // 优惠值
    public List<string> ApplicableProductIds { get; set; } = new();
    public List<string> ApplicableCategories { get; set; } = new();
    public string Description { get; set; }
}

public enum BenefitType
{
    PercentageDiscount,  // 百分比折扣
    FixedDiscount,       // 固定金额折扣
    FreeShipping,        // 免运费
    FreeGift,           // 免费赠品
    SpecialPrice        // 特价
}
```

### 8. 复购奖励规则 (RepeatPurchaseRewardRule) ⭐⭐⭐⭐

#### 业务场景
- **忠诚度奖励**：连续购买获得递增折扣
- **回购优惠**：30天内再次购买享受优惠
- **品牌忠诚**：同品牌商品复购奖励
- **频次奖励**：购买频次越高优惠越大

#### 技术实现
```csharp
public class RepeatPurchaseRewardRule : PromotionRuleBase
{
    public override string RuleType => "RepeatPurchaseReward";
    
    /// <summary>
    /// 复购检测配置
    /// </summary>
    public RepeatPurchaseDetection Detection { get; set; }
    
    /// <summary>
    /// 奖励阶梯配置
    /// </summary>
    public List<RewardTier> RewardTiers { get; set; } = new();
}

public class RepeatPurchaseDetection
{
    public int DetectionPeriodDays { get; set; } = 90;  // 检测周期
    public RepeatPurchaseScope Scope { get; set; }  // 检测范围
    public int MinPurchaseCount { get; set; } = 2;  // 最小购买次数
    public decimal MinPurchaseAmount { get; set; } = 0;  // 最小购买金额
}

public class RewardTier
{
    public int PurchaseCount { get; set; }  // 购买次数
    public decimal DiscountRate { get; set; }  // 折扣率
    public decimal BonusPoints { get; set; }  // 奖励积分
    public List<string> FreeGiftProductIds { get; set; } = new();  // 免费赠品
}

public enum RepeatPurchaseScope
{
    SameProduct,    // 同一商品
    SameCategory,   // 同一品类
    SameBrand,      // 同一品牌
    AnyProduct      // 任意商品
}
```

### 9. 社交分享促销规则 (SocialSharingPromotionRule) ⭐⭐⭐

#### 业务场景
- **分享有礼**：分享商品到社交媒体获得折扣
- **邀请奖励**：邀请好友注册双方都获得优惠
- **晒单返现**：购买后晒单评价获得返现
- **病毒营销**：分享传播达到一定量级触发团购价

#### 技术实现
```csharp
public class SocialSharingPromotionRule : PromotionRuleBase
{
    public override string RuleType => "SocialSharingPromotion";
    
    /// <summary>
    /// 分享平台配置
    /// </summary>
    public List<SharingPlatformConfig> Platforms { get; set; } = new();
    
    /// <summary>
    /// 奖励配置
    /// </summary>
    public List<SharingReward> Rewards { get; set; } = new();
}

public class SharingPlatformConfig
{
    public string PlatformName { get; set; }  // 微信、微博、抖音等
    public string PlatformId { get; set; }
    public bool IsEnabled { get; set; } = true;
    public int RewardPoints { get; set; }  // 分享奖励积分
}

public class SharingReward
{
    public SharingActionType ActionType { get; set; }
    public int RequiredCount { get; set; }  // 所需次数/人数
    public decimal RewardValue { get; set; }  // 奖励值
    public RewardType RewardType { get; set; }
}

public enum SharingActionType
{
    Share,          // 分享
    Invite,         // 邀请
    Review,         // 评价
    Like,           // 点赞
    Comment         // 评论
}
```

### 10. 动态定价规则 (DynamicPricingRule) ⭐⭐⭐⭐⭐

#### 业务场景
- **需求定价**：根据商品热度动态调整价格
- **竞争定价**：基于竞品价格自动调价
- **时段定价**：不同时段不同价格策略
- **个性化定价**：基于用户画像的个性化价格

#### 技术实现
```csharp
public class DynamicPricingRule : PromotionRuleBase
{
    public override string RuleType => "DynamicPricing";
    
    /// <summary>
    /// 定价策略
    /// </summary>
    public DynamicPricingStrategy Strategy { get; set; }
    
    /// <summary>
    /// 价格调整配置
    /// </summary>
    public List<PriceAdjustmentConfig> Adjustments { get; set; } = new();
    
    /// <summary>
    /// 价格边界
    /// </summary>
    public PriceBoundary Boundary { get; set; }
}

public class PriceAdjustmentConfig
{
    public PricingFactor Factor { get; set; }  // 定价因子
    public decimal Weight { get; set; }  // 权重
    public decimal MinAdjustment { get; set; }  // 最小调整幅度
    public decimal MaxAdjustment { get; set; }  // 最大调整幅度
    public string DataSource { get; set; }  // 数据源
}

public class PriceBoundary
{
    public decimal MinDiscountRate { get; set; } = 0.5m;  // 最低5折
    public decimal MaxMarkupRate { get; set; } = 1.2m;    // 最高1.2倍
    public decimal BasePrice { get; set; }  // 基准价格
}

public enum DynamicPricingStrategy
{
    DemandBased,      // 基于需求
    CompetitionBased, // 基于竞争
    TimeBased,        // 基于时间
    PersonalizedBased // 基于个性化
}

public enum PricingFactor
{
    Demand,           // 需求量
    Inventory,        // 库存量
    Competition,      // 竞争价格
    CustomerSegment,  // 客户细分
    TimeOfDay,        // 时段
    Seasonality       // 季节性
}
```

---

## 🏗️ 实现架构设计

### 1. 新规则基类扩展
```csharp
// 扩展现有的PromotionRuleBase
public abstract class AdvancedPromotionRuleBase : PromotionRuleBase
{
    /// <summary>
    /// 外部数据依赖
    /// </summary>
    public List<ExternalDataDependency> DataDependencies { get; set; } = new();
    
    /// <summary>
    /// 实时数据更新间隔（秒）
    /// </summary>
    public int DataUpdateIntervalSeconds { get; set; } = 300;
    
    /// <summary>
    /// 是否需要用户画像数据
    /// </summary>
    public bool RequiresCustomerProfile { get; set; } = false;
    
    /// <summary>
    /// 是否需要实时库存数据
    /// </summary>
    public bool RequiresRealTimeInventory { get; set; } = false;
}

public class ExternalDataDependency
{
    public string DataSourceName { get; set; }
    public string DataSourceType { get; set; }  // API, Database, Cache
    public string ConnectionString { get; set; }
    public int TimeoutSeconds { get; set; } = 30;
}
```

### 2. 数据服务接口
```csharp
public interface ICustomerProfileService
{
    Task<CustomerProfile> GetCustomerProfileAsync(string customerId);
    Task<MembershipInfo> GetMembershipInfoAsync(string customerId);
    Task<PurchaseHistory> GetPurchaseHistoryAsync(string customerId, int days);
}

public interface IInventoryService
{
    Task<int> GetCurrentInventoryAsync(string productId);
    Task<InventoryMovement> GetInventoryMovementAsync(string productId, int days);
}

public interface ISocialSharingService
{
    Task<SharingStats> GetSharingStatsAsync(string customerId, string productId);
    Task<bool> ValidateSharingActionAsync(string customerId, SharingAction action);
}
```

### 3. 配置文件扩展
```json
{
  "advancedPromotionRules": [
    {
      "$type": "TimePeriodPromotion",
      "id": "TIME_001",
      "name": "早鸟特价",
      "dataUpdateIntervalSeconds": 60,
      "timePeriods": [...],
      "promotionType": "Discount",
      "parameters": {...}
    }
  ],
  "externalDataSources": {
    "customerProfile": {
      "type": "API",
      "endpoint": "https://api.customer.com/profile",
      "timeout": 30
    },
    "inventory": {
      "type": "Database",
      "connectionString": "...",
      "timeout": 10
    }
  }
}
```

---

## 📊 实现优先级建议

| 规则类型 | 业务价值 | 技术复杂度 | 实现成本 | 优先级 |
|----------|----------|------------|----------|--------|
| 时段促销规则 | 高 | 低 | 低 | 🔥🔥🔥🔥🔥 |
| 会员等级促销 | 高 | 中 | 中 | 🔥🔥🔥🔥🔥 |
| 连带销售规则 | 高 | 高 | 高 | 🔥🔥🔥🔥🔥 |
| 新客专享规则 | 中 | 低 | 低 | 🔥🔥🔥🔥 |
| 积分兑换规则 | 中 | 中 | 中 | 🔥🔥🔥🔥 |
| 库存清仓规则 | 中 | 中 | 中 | 🔥🔥🔥🔥 |
| 复购奖励规则 | 中 | 中 | 中 | 🔥🔥🔥 |
| 节日主题促销 | 中 | 低 | 低 | 🔥🔥🔥 |
| 社交分享促销 | 低 | 高 | 高 | 🔥🔥 |
| 动态定价规则 | 高 | 高 | 高 | 🔥🔥 |

---

## 🎯 实施建议

### 第一阶段（1-2个月）
1. **时段促销规则** - 最容易实现，业务价值高
2. **新客专享规则** - 技术简单，营销效果好
3. **会员等级促销** - 基础功能，后续扩展基础

### 第二阶段（3-4个月）
1. **积分兑换规则** - 完善会员体系
2. **库存清仓规则** - 提升库存周转
3. **节日主题促销** - 增强营销灵活性

### 第三阶段（5-6个月）
1. **连带销售规则** - 提升客单价
2. **复购奖励规则** - 增强客户粘性

### 第四阶段（长期规划）
1. **社交分享促销** - 病毒式营销
2. **动态定价规则** - 智能定价系统

通过分阶段实施，可以确保系统稳定性的同时，逐步增强促销引擎的功能和竞争力。