[{"$type": "UnifiedSpecialPriceExchange", "id": "EXCHANGE_SPECIAL_001", "name": "统一特价换购测试 - 买A加1元换B", "description": "购买A商品1件时，若再增加1元可换购B商品", "priority": 60, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "exchangeStrategy": "ByGradient", "exchangeSelectionStrategy": "CustomerBenefit", "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1, "requiredAmount": 0}], "exchangeConditions": [{"exchangeProductIds": ["B"], "exchangeQuantity": 1, "addAmount": 1.0, "description": "加1元换购B商品"}]}, {"$type": "UnifiedDiscountExchange", "id": "EXCHANGE_DISCOUNT_001", "name": "统一打折换购测试 - 买A享B商品9折", "description": "购买A商品大于等于1件时，可以0.9折换购B商品", "priority": 55, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "exchangeStrategy": "ByGradient", "exchangeSelectionStrategy": "CustomerBenefit", "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1, "requiredAmount": 0}], "exchangeConditions": [{"exchangeProductIds": ["B"], "exchangeQuantity": 1, "discountRate": 0.9, "description": "9折换购B商品"}]}, {"$type": "UnifiedDiscountAmountExchange", "id": "EXCHANGE_DISCOUNT_AMOUNT_001", "name": "统一优惠换购测试 - 买A享B商品优惠100元", "description": "购买A商品大于等于1件时，可以优惠100元购买B商品", "priority": 50, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "exchangeStrategy": "ByGradient", "exchangeSelectionStrategy": "CustomerBenefit", "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1, "requiredAmount": 0}], "exchangeConditions": [{"exchangeProductIds": ["B"], "exchangeQuantity": 1, "discountAmount": 100.0, "description": "优惠100元换购B商品"}]}, {"$type": "CombinationSpecialPriceExchange", "id": "EXCHANGE_COMBINATION_SPECIAL_001", "name": "组合特价换购测试 - 买A+B加100元换C", "description": "购买A、B商品各大于等于1件时，可以加100元换C商品", "priority": 65, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "exchangeStrategy": "ByGradient", "exchangeSelectionStrategy": "CustomerBenefit", "combinationConditions": [{"productId": "A", "requiredQuantity": 1, "requiredAmount": 0}, {"productId": "B", "requiredQuantity": 1, "requiredAmount": 0}], "exchangeConditions": [{"exchangeProductIds": ["C"], "exchangeQuantity": 1, "addAmount": 100.0, "description": "加100元换购C商品"}]}, {"$type": "CombinationDiscountExchange", "id": "EXCHANGE_COMBINATION_DISCOUNT_001", "name": "组合打折换购测试 - 买A+B享C商品9折", "description": "购买A、B商品各大于等于1件时，可以0.9折购买C商品", "priority": 60, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "exchangeStrategy": "ByGradient", "exchangeSelectionStrategy": "CustomerBenefit", "combinationConditions": [{"productId": "A", "requiredQuantity": 1, "requiredAmount": 0}, {"productId": "B", "requiredQuantity": 1, "requiredAmount": 0}], "exchangeConditions": [{"exchangeProductIds": ["C"], "exchangeQuantity": 1, "discountRate": 0.9, "description": "9折换购C商品"}]}, {"$type": "CombinationDiscountAmountExchange", "id": "EXCHANGE_COMBINATION_DISCOUNT_AMOUNT_001", "name": "组合优惠换购测试 - 买A+B享C商品优惠1元", "description": "购买A、B商品各大于等于1件时，可以优惠1元购买C商品", "priority": 55, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "exchangeStrategy": "ByGradient", "exchangeSelectionStrategy": "CustomerBenefit", "combinationConditions": [{"productId": "A", "requiredQuantity": 1, "requiredAmount": 0}, {"productId": "B", "requiredQuantity": 1, "requiredAmount": 0}], "exchangeConditions": [{"exchangeProductIds": ["C"], "exchangeQuantity": 1, "discountAmount": 1.0, "description": "优惠1元换购C商品"}]}]