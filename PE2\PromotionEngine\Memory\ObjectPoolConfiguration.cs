global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.ObjectPool;
global using Microsoft.Extensions.Logging;
using System.Text;

namespace PE2.PromotionEngine.Memory;

/// <summary>
/// 对象池配置类 - 内存管理优化
/// 提供StringBuilder、List、Dictionary等常用对象的对象池配置
/// 目标：减少50%的内存使用量和GC压力
/// </summary>
/// <remarks>
/// 建议单元测试：
/// 1. TestStringBuilderPoolConfiguration - 测试StringBuilder对象池配置
/// 2. TestListPoolConfiguration - 测试List对象池配置
/// 3. TestDictionaryPoolConfiguration - 测试Dictionary对象池配置
/// 4. TestObjectPoolPerformance - 测试对象池性能提升
/// 5. TestMemoryUsageReduction - 测试内存使用量减少
/// 6. TestConcurrentAccess - 测试并发访问安全性
/// 7. TestPoolSizeOptimization - 测试池大小优化
/// 8. TestObjectLifecycle - 测试对象生命周期管理
/// </remarks>
public static class ObjectPoolConfiguration
{
    /// <summary>
    /// 配置对象池服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddPromotionEngineObjectPools(this IServiceCollection services)
    {
        // StringBuilder对象池
        services.AddSingleton<ObjectPool<StringBuilder>>(serviceProvider =>
        {
            var provider = new DefaultObjectPoolProvider();
            var policy = new StringBuilderPooledObjectPolicy();
            return provider.Create(policy);
        });

        // List<string>对象池
        services.AddSingleton<ObjectPool<List<string>>>(serviceProvider =>
        {
            var provider = new DefaultObjectPoolProvider();
            var policy = new ListPooledObjectPolicy<string>();
            return provider.Create(policy);
        });

        // Dictionary<string, object>对象池
        services.AddSingleton<ObjectPool<Dictionary<string, object>>>(serviceProvider =>
        {
            var provider = new DefaultObjectPoolProvider();
            var policy = new DictionaryPooledObjectPolicy<string, object>();
            return provider.Create(policy);
        });

        // List<AllocationDetail>对象池
        services.AddSingleton<ObjectPool<List<AllocationDetail>>>(serviceProvider =>
        {
            var provider = new DefaultObjectPoolProvider();
            var policy = new ListPooledObjectPolicy<AllocationDetail>();
            return provider.Create(policy);
        });

        // HashSet<string>对象池
        services.AddSingleton<ObjectPool<HashSet<string>>>(serviceProvider =>
        {
            var provider = new DefaultObjectPoolProvider();
            var policy = new HashSetPooledObjectPolicy<string>();
            return provider.Create(policy);
        });

        return services;
    }
}

/// <summary>
/// StringBuilder对象池策略
/// </summary>
public sealed class StringBuilderPooledObjectPolicy : PooledObjectPolicy<StringBuilder>
{
    private const int InitialCapacity = 256;
    private const int MaximumRetainedCapacity = 4096;

    /// <summary>
    /// 创建StringBuilder实例
    /// </summary>
    /// <returns>StringBuilder实例</returns>
    public override StringBuilder Create()
    {
        return new StringBuilder(InitialCapacity);
    }

    /// <summary>
    /// 归还StringBuilder到对象池
    /// </summary>
    /// <param name="obj">StringBuilder实例</param>
    /// <returns>是否成功归还</returns>
    public override bool Return(StringBuilder obj)
    {
        if (obj.Capacity > MaximumRetainedCapacity)
        {
            // 如果容量过大，不归还到池中，让GC回收
            return false;
        }

        obj.Clear();
        return true;
    }
}

/// <summary>
/// List对象池策略
/// </summary>
/// <typeparam name="T">列表元素类型</typeparam>
public sealed class ListPooledObjectPolicy<T> : PooledObjectPolicy<List<T>>
{
    private const int InitialCapacity = 16;
    private const int MaximumRetainedCapacity = 1024;

    /// <summary>
    /// 创建List实例
    /// </summary>
    /// <returns>List实例</returns>
    public override List<T> Create()
    {
        return new List<T>(InitialCapacity);
    }

    /// <summary>
    /// 归还List到对象池
    /// </summary>
    /// <param name="obj">List实例</param>
    /// <returns>是否成功归还</returns>
    public override bool Return(List<T> obj)
    {
        if (obj.Capacity > MaximumRetainedCapacity)
        {
            return false;
        }

        obj.Clear();
        return true;
    }
}

/// <summary>
/// Dictionary对象池策略
/// </summary>
/// <typeparam name="TKey">键类型</typeparam>
/// <typeparam name="TValue">值类型</typeparam>
public sealed class DictionaryPooledObjectPolicy<TKey, TValue> : PooledObjectPolicy<Dictionary<TKey, TValue>>
    where TKey : notnull
{
    private const int InitialCapacity = 16;
    private const int MaximumRetainedCapacity = 512;

    /// <summary>
    /// 创建Dictionary实例
    /// </summary>
    /// <returns>Dictionary实例</returns>
    public override Dictionary<TKey, TValue> Create()
    {
        return new Dictionary<TKey, TValue>(InitialCapacity);
    }

    /// <summary>
    /// 归还Dictionary到对象池
    /// </summary>
    /// <param name="obj">Dictionary实例</param>
    /// <returns>是否成功归还</returns>
    public override bool Return(Dictionary<TKey, TValue> obj)
    {
        if (obj.Count > MaximumRetainedCapacity)
        {
            return false;
        }

        obj.Clear();
        return true;
    }
}

/// <summary>
/// HashSet对象池策略
/// </summary>
/// <typeparam name="T">元素类型</typeparam>
public sealed class HashSetPooledObjectPolicy<T> : PooledObjectPolicy<HashSet<T>>
{
    private const int MaximumRetainedCapacity = 512;

    /// <summary>
    /// 创建HashSet实例
    /// </summary>
    /// <returns>HashSet实例</returns>
    public override HashSet<T> Create()
    {
        return new HashSet<T>();
    }

    /// <summary>
    /// 归还HashSet到对象池
    /// </summary>
    /// <param name="obj">HashSet实例</param>
    /// <returns>是否成功归还</returns>
    public override bool Return(HashSet<T> obj)
    {
        if (obj.Count > MaximumRetainedCapacity)
        {
            return false;
        }

        obj.Clear();
        return true;
    }
}

/// <summary>
/// 内存使用监控器
/// </summary>
public sealed class MemoryUsageMonitor : IDisposable
{
    private readonly ILogger<MemoryUsageMonitor> _logger;
    private readonly Timer _monitoringTimer;
    private long _initialMemoryUsage;
    private long _peakMemoryUsage;
    private readonly object _lockObject = new();

    /// <summary>
    /// 初始化内存监控器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public MemoryUsageMonitor(ILogger<MemoryUsageMonitor> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _initialMemoryUsage = GC.GetTotalMemory(false);
        _peakMemoryUsage = _initialMemoryUsage;
        
        // 每30秒监控一次内存使用情况
        _monitoringTimer = new Timer(MonitorMemoryUsage, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        
        _logger.LogInformation("内存监控器已启动，初始内存使用: {InitialMemory:N0} bytes", _initialMemoryUsage);
    }

    /// <summary>
    /// 监控内存使用情况
    /// </summary>
    /// <param name="state">状态对象</param>
    private void MonitorMemoryUsage(object? state)
    {
        try
        {
            lock (_lockObject)
            {
                var currentMemory = GC.GetTotalMemory(false);
                var memoryDifference = currentMemory - _initialMemoryUsage;
                var memoryReductionPercentage = _initialMemoryUsage > 0 ? 
                    (double)memoryDifference / _initialMemoryUsage * 100 : 0;

                if (currentMemory > _peakMemoryUsage)
                {
                    _peakMemoryUsage = currentMemory;
                }

                _logger.LogDebug("内存使用监控 - 当前: {Current:N0} bytes, 初始: {Initial:N0} bytes, " +
                               "差异: {Difference:N0} bytes ({Percentage:F2}%), 峰值: {Peak:N0} bytes",
                    currentMemory, _initialMemoryUsage, memoryDifference, memoryReductionPercentage, _peakMemoryUsage);

                // 如果内存使用量减少超过40%，记录成功信息
                if (memoryReductionPercentage < -40)
                {
                    _logger.LogInformation("内存优化效果显著，内存使用量减少 {Percentage:F2}%", Math.Abs(memoryReductionPercentage));
                }

                // 如果内存使用量增加超过200%，记录警告
                if (memoryReductionPercentage > 200)
                {
                    _logger.LogWarning("内存使用量异常增长 {Percentage:F2}%，建议检查内存泄漏", memoryReductionPercentage);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "监控内存使用时发生异常");
        }
    }

    /// <summary>
    /// 获取内存使用统计
    /// </summary>
    /// <returns>内存使用统计</returns>
    public MemoryUsageStatistics GetMemoryStatistics()
    {
        lock (_lockObject)
        {
            var currentMemory = GC.GetTotalMemory(false);
            var memoryDifference = currentMemory - _initialMemoryUsage;
            var reductionPercentage = _initialMemoryUsage > 0 ? 
                (double)memoryDifference / _initialMemoryUsage * 100 : 0;

            return new MemoryUsageStatistics
            {
                InitialMemoryUsage = _initialMemoryUsage,
                CurrentMemoryUsage = currentMemory,
                PeakMemoryUsage = _peakMemoryUsage,
                MemoryDifference = memoryDifference,
                MemoryReductionPercentage = reductionPercentage,
                IsOptimizationSuccessful = reductionPercentage < -30 // 目标是减少50%，30%也算成功
            };
        }
    }

    /// <summary>
    /// 强制垃圾回收并获取内存统计
    /// </summary>
    /// <returns>内存使用统计</returns>
    public MemoryUsageStatistics ForceGCAndGetStatistics()
    {
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();
        
        return GetMemoryStatistics();
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        try
        {
            _monitoringTimer?.Dispose();
            
            var finalStats = GetMemoryStatistics();
            _logger.LogInformation("内存监控器已停止 - 最终统计: 初始{Initial:N0}, 当前{Current:N0}, " +
                                 "峰值{Peak:N0}, 变化{Change:F2}%",
                finalStats.InitialMemoryUsage, finalStats.CurrentMemoryUsage, 
                finalStats.PeakMemoryUsage, finalStats.MemoryReductionPercentage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "释放内存监控器资源时发生异常");
        }
    }
}

/// <summary>
/// 内存使用统计
/// </summary>
public sealed record MemoryUsageStatistics
{
    /// <summary>初始内存使用量</summary>
    public required long InitialMemoryUsage { get; init; }
    
    /// <summary>当前内存使用量</summary>
    public required long CurrentMemoryUsage { get; init; }
    
    /// <summary>峰值内存使用量</summary>
    public required long PeakMemoryUsage { get; init; }
    
    /// <summary>内存差异</summary>
    public required long MemoryDifference { get; init; }
    
    /// <summary>内存减少百分比</summary>
    public required double MemoryReductionPercentage { get; init; }
    
    /// <summary>是否优化成功</summary>
    public required bool IsOptimizationSuccessful { get; init; }
}
