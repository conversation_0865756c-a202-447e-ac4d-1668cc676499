using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Processors;

/// <summary>
/// 折扣分摊处理器
/// 使用按价值比例分摊法，将顾客实际支付的总金额按照促销组合中所有商品（包括赠品）的原始单独售价的比例分摊到每个商品上
/// 形成每个商品的"有效售价"或"折后单价"
/// </summary>
public class DiscountAllocationProcessor
{
    /// <summary>
    /// 处理促销结果，进行按价值比例分摊
    /// </summary>
    public void ProcessPromotionResult(PromotionResult result)
    {
        // 初始化处理后的购物车
        result.ProcessedCart = result.OriginalCart.Clone();
        result.ProcessedCart.InitializeActualPrices();

        // 按顺序处理每个应用的促销
        foreach (var appliedPromotion in result.AppliedPromotions)
        {
            ProcessSinglePromotion(result.ProcessedCart, appliedPromotion);
        }

        // 处理四舍五入问题
        HandleRoundingIssues(result.ProcessedCart, result.AppliedPromotions);
    }

    /// <summary>
    /// 处理单个促销的分摊
    /// </summary>
    private void ProcessSinglePromotion(ShoppingCart cart, AppliedPromotion promotion)
    {
        switch (promotion.PromotionType)
        {
            case "PercentageDiscount":
            case "FixedAmountDiscount":
            case "CategoryDiscount":
            case "TotalAmountDiscount":
                ProcessDiscountPromotion(cart, promotion);
                break;
            case "BuyXGetY":
            case "UnifiedGift":
            case "GradientGift":
            case "CombinationGift":
                ProcessBuyGetGiftPromotion(cart, promotion);
                break;
            case "BundleOffer":
                ProcessBundlePromotion(cart, promotion);
                break;
        }
    }

    /// <summary>
    /// 处理折扣类促销
    /// </summary>
    private void ProcessDiscountPromotion(ShoppingCart cart, AppliedPromotion promotion)
    {
        var consumedItems = promotion.ConsumedItems;
        var totalDiscountAmount = promotion.DiscountAmount;

        if (!consumedItems.Any() || totalDiscountAmount <= 0)
            return;

        // 计算参与促销的商品总价值
        var totalOriginalAmount = consumedItems.Sum(c => c.TotalPrice);

        if (totalOriginalAmount <= 0)
            return;

        var remainingDiscount = totalDiscountAmount;

        // 按比例分摊折扣
        for (int i = 0; i < consumedItems.Count; i++)
        {
            var consumedItem = consumedItems[i];
            var cartItems = cart.Items.Where(x =>
                x.Product.Id == consumedItem.ProductId &&
                !x.IsGift &&
                x.UnitPrice == consumedItem.UnitPrice).ToList();

            if (!cartItems.Any()) continue;

            // 计算该商品应分摊的折扣金额
            decimal itemDiscountAmount;
            if (i == consumedItems.Count - 1)
            {
                // 最后一个商品承担剩余的折扣金额（处理四舍五入）
                itemDiscountAmount = remainingDiscount;
            }
            else
            {
                var discountRatio = consumedItem.TotalPrice / totalOriginalAmount;
                itemDiscountAmount = Math.Round(totalDiscountAmount * discountRatio, 2);
                remainingDiscount -= itemDiscountAmount;
            }

            // 分摊到具体的购物车项
            AllocateDiscountToCartItems(cartItems, consumedItem.Quantity, itemDiscountAmount, promotion);
        }
    }

    /// <summary>
    /// 处理买赠促销
    /// 使用按价值比例分摊法：
    /// 1. 计算促销组合中所有商品（包括赠品）的原始总价值
    /// 2. 将顾客实际支付金额按比例分摊到每个商品上
    /// 3. 正确处理同商品买赠，避免重复显示赠品
    /// </summary>
    private void ProcessBuyGetGiftPromotion(ShoppingCart cart, AppliedPromotion promotion)
    {
        if (!promotion.ConsumedItems.Any() && !promotion.GiftItems.Any())
            return;

        // 使用按价值比例分摊法处理买赠促销
        ApplyValueProportionAllocation(cart, promotion);
    }

    /// <summary>
    /// 按价值比例分摊法处理买赠促销
    /// 核心算法：将顾客实际支付的总金额，按照促销组合中所有商品（包括赠品）的原始单独售价的比例，分摊到每个商品上
    /// </summary>
    private void ApplyValueProportionAllocation(ShoppingCart cart, AppliedPromotion promotion)
    {
        // 第一步：收集所有参与促销的商品信息
        var promotionItems = CollectPromotionItems(cart, promotion);
        if (!promotionItems.Any()) return;

        // 第二步：计算总原价和顾客实际支付金额
        var totalOriginalValue = promotionItems.Sum(item => item.OriginalTotalValue);
        var customerPayment = totalOriginalValue - promotion.DiscountAmount;

        // 第三步：按比例分摊，计算每个商品的有效售价
        var allocationRatio = customerPayment / totalOriginalValue;

        // 第四步：应用分摊结果到购物车
        ApplyAllocationToCart(cart, promotionItems, allocationRatio, promotion);
    }

    /// <summary>
    /// 促销商品信息
    /// </summary>
    private class PromotionItemInfo
    {
        public string ProductId { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public decimal OriginalUnitPrice { get; set; }
        public int TotalQuantity { get; set; }
        public decimal OriginalTotalValue => OriginalUnitPrice * TotalQuantity;
        public int ConsumedQuantity { get; set; } // 作为购买条件消耗的数量
        public int GiftQuantity { get; set; } // 作为赠品的数量
        public bool IsSameProductGift => ConsumedQuantity > 0 && GiftQuantity > 0; // 是否为同商品买赠
    }

    /// <summary>
    /// 收集所有参与促销的商品信息
    /// </summary>
    private List<PromotionItemInfo> CollectPromotionItems(ShoppingCart cart, AppliedPromotion promotion)
    {
        var promotionItems = new Dictionary<string, PromotionItemInfo>();

        // 收集消耗的商品（购买条件）
        foreach (var consumedItem in promotion.ConsumedItems)
        {
            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == consumedItem.ProductId);
            if (cartItem == null) continue;

            if (!promotionItems.ContainsKey(consumedItem.ProductId))
            {
                promotionItems[consumedItem.ProductId] = new PromotionItemInfo
                {
                    ProductId = consumedItem.ProductId,
                    ProductName = cartItem.Product.Name,
                    OriginalUnitPrice = cartItem.UnitPrice
                };
            }

            promotionItems[consumedItem.ProductId].ConsumedQuantity += consumedItem.Quantity;
            promotionItems[consumedItem.ProductId].TotalQuantity += consumedItem.Quantity;
        }

        // 收集赠品
        foreach (var giftItem in promotion.GiftItems)
        {
            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == giftItem.ProductId);
            if (cartItem == null) continue;

            if (!promotionItems.ContainsKey(giftItem.ProductId))
            {
                promotionItems[giftItem.ProductId] = new PromotionItemInfo
                {
                    ProductId = giftItem.ProductId,
                    ProductName = cartItem.Product.Name,
                    OriginalUnitPrice = cartItem.UnitPrice
                };
            }

            promotionItems[giftItem.ProductId].GiftQuantity += giftItem.Quantity;
            promotionItems[giftItem.ProductId].TotalQuantity += giftItem.Quantity;
        }

        return promotionItems.Values.ToList();
    }

    /// <summary>
    /// 应用分摊结果到购物车
    /// </summary>
    private void ApplyAllocationToCart(ShoppingCart cart, List<PromotionItemInfo> promotionItems, decimal allocationRatio, AppliedPromotion promotion)
    {
        foreach (var promotionItem in promotionItems)
        {
            // 计算该商品的有效售价
            var effectiveUnitPrice = Math.Round(promotionItem.OriginalUnitPrice * allocationRatio, 2);

            if (promotionItem.IsSameProductGift)
            {
                // 同商品买赠：需要拆分购物车项
                ProcessSameProductGiftAllocation(cart, promotionItem, effectiveUnitPrice, promotion);
            }
            else
            {
                // 不同商品买赠：分别处理购买商品和赠品
                ProcessDifferentProductGiftAllocation(cart, promotionItem, effectiveUnitPrice, promotion);
            }
        }
    }

    /// <summary>
    /// 处理同商品买赠的分摊（如买2件A送1件A）
    /// 正确的做法：将原购物车项拆分，避免重复显示赠品
    /// </summary>
    private void ProcessSameProductGiftAllocation(ShoppingCart cart, PromotionItemInfo promotionItem, decimal effectiveUnitPrice, AppliedPromotion promotion)
    {
        // 查找原购物车项
        var cartItems = cart.Items.Where(x =>
            x.Product.Id == promotionItem.ProductId &&
            !x.IsGift &&
            Math.Abs(x.UnitPrice - promotionItem.OriginalUnitPrice) < 0.01m).ToList();

        var totalProcessedQuantity = 0;
        var targetTotalQuantity = promotionItem.TotalQuantity; // 购买数量 + 赠品数量

        foreach (var cartItem in cartItems)
        {
            var remainingQuantity = targetTotalQuantity - totalProcessedQuantity;
            if (remainingQuantity <= 0) break;

            var processQuantity = Math.Min(cartItem.Quantity, remainingQuantity);

            if (processQuantity == cartItem.Quantity)
            {
                // 整个购物车项都参与促销，直接修改价格
                cartItem.ActualUnitPrice = effectiveUnitPrice;
                AddPromotionDetailToCartItem(cartItem, promotion, effectiveUnitPrice, promotionItem.OriginalUnitPrice);
            }
            else
            {
                // 部分数量参与促销，需要拆分
                // 1. 减少原购物车项的数量
                cartItem.Quantity -= processQuantity;

                // 2. 创建新的促销商品项
                var promotionCartItem = new CartItem
                {
                    Product = cartItem.Product,
                    Quantity = processQuantity,
                    UnitPrice = promotionItem.OriginalUnitPrice,
                    ActualUnitPrice = effectiveUnitPrice,
                    IsGift = false
                };

                AddPromotionDetailToCartItem(promotionCartItem, promotion, effectiveUnitPrice, promotionItem.OriginalUnitPrice);
                cart.Items.Add(promotionCartItem);
            }

            totalProcessedQuantity += processQuantity;
        }
    }

    /// <summary>
    /// 处理不同商品买赠的分摊（如买A送B）
    /// </summary>
    private void ProcessDifferentProductGiftAllocation(ShoppingCart cart, PromotionItemInfo promotionItem, decimal effectiveUnitPrice, AppliedPromotion promotion)
    {
        if (promotionItem.ConsumedQuantity > 0)
        {
            // 处理购买的商品
            ProcessConsumedItemAllocation(cart, promotionItem, effectiveUnitPrice, promotion);
        }

        if (promotionItem.GiftQuantity > 0)
        {
            // 处理赠品：添加新的赠品项，价格为有效售价
            AddGiftItemToCart(cart, promotionItem, effectiveUnitPrice, promotion);
        }
    }

    /// <summary>
    /// 处理消耗商品的分摊
    /// </summary>
    private void ProcessConsumedItemAllocation(ShoppingCart cart, PromotionItemInfo promotionItem, decimal effectiveUnitPrice, AppliedPromotion promotion)
    {
        var cartItems = cart.Items.Where(x =>
            x.Product.Id == promotionItem.ProductId &&
            !x.IsGift &&
            Math.Abs(x.UnitPrice - promotionItem.OriginalUnitPrice) < 0.01m).ToList();

        var remainingQuantity = promotionItem.ConsumedQuantity;

        foreach (var cartItem in cartItems)
        {
            if (remainingQuantity <= 0) break;

            var allocatedQuantity = Math.Min(cartItem.Quantity, remainingQuantity);

            if (allocatedQuantity == cartItem.Quantity)
            {
                // 整个购物车项都参与促销
                cartItem.ActualUnitPrice = effectiveUnitPrice;
                AddPromotionDetailToCartItem(cartItem, promotion, effectiveUnitPrice, promotionItem.OriginalUnitPrice);
            }
            else
            {
                // 部分数量参与促销，需要拆分
                cartItem.Quantity -= allocatedQuantity;

                var promotionCartItem = new CartItem
                {
                    Product = cartItem.Product,
                    Quantity = allocatedQuantity,
                    UnitPrice = promotionItem.OriginalUnitPrice,
                    ActualUnitPrice = effectiveUnitPrice,
                    IsGift = false
                };

                AddPromotionDetailToCartItem(promotionCartItem, promotion, effectiveUnitPrice, promotionItem.OriginalUnitPrice);
                cart.Items.Add(promotionCartItem);
            }

            remainingQuantity -= allocatedQuantity;
        }
    }

    /// <summary>
    /// 添加赠品项到购物车
    /// </summary>
    private void AddGiftItemToCart(ShoppingCart cart, PromotionItemInfo promotionItem, decimal effectiveUnitPrice, AppliedPromotion promotion)
    {
        var product = cart.Items.FirstOrDefault(x => x.Product.Id == promotionItem.ProductId)?.Product;
        if (product == null) return;

        var giftCartItem = new CartItem
        {
            Product = product,
            Quantity = promotionItem.GiftQuantity,
            UnitPrice = promotionItem.OriginalUnitPrice,
            ActualUnitPrice = effectiveUnitPrice, // 赠品也按比例分摊，不是0
            IsGift = true
        };

        AddPromotionDetailToCartItem(giftCartItem, promotion, effectiveUnitPrice, promotionItem.OriginalUnitPrice, true);
        cart.Items.Add(giftCartItem);
    }

    /// <summary>
    /// 为购物车项添加促销详情
    /// </summary>
    private void AddPromotionDetailToCartItem(CartItem cartItem, AppliedPromotion promotion, decimal effectiveUnitPrice, decimal originalUnitPrice, bool isGift = false)
    {
        var discountPerUnit = originalUnitPrice - effectiveUnitPrice;
        var totalDiscount = discountPerUnit * cartItem.Quantity;

        var description = isGift
            ? $"赠品 - {promotion.RuleName}（按价值比例分摊）"
            : $"{promotion.RuleName}（按价值比例分摊）";

        var promotionDetail = new ItemPromotionDetail
        {
            RuleId = promotion.RuleId,
            RuleName = promotion.RuleName,
            PromotionType = promotion.PromotionType,
            DiscountAmount = totalDiscount,
            Description = description,
            IsGiftRelated = isGift
        };

        cartItem.AddPromotionDetail(promotionDetail);
    }

    /// <summary>
    /// 处理组合套餐促销
    /// </summary>
    private void ProcessBundlePromotion(ShoppingCart cart, AppliedPromotion promotion)
    {
        var consumedItems = promotion.ConsumedItems;
        var totalDiscountAmount = promotion.DiscountAmount;

        if (!consumedItems.Any() || totalDiscountAmount <= 0)
            return;

        // 套餐促销按商品价值比例分摊折扣
        var totalOriginalAmount = consumedItems.Sum(c => c.TotalPrice);
        var remainingDiscount = totalDiscountAmount;

        for (int i = 0; i < consumedItems.Count; i++)
        {
            var consumedItem = consumedItems[i];
            var cartItems = cart.Items.Where(x =>
                x.Product.Id == consumedItem.ProductId &&
                !x.IsGift &&
                x.UnitPrice == consumedItem.UnitPrice).ToList();

            if (!cartItems.Any()) continue;

            decimal itemDiscountAmount;
            if (i == consumedItems.Count - 1)
            {
                itemDiscountAmount = remainingDiscount;
            }
            else
            {
                var discountRatio = consumedItem.TotalPrice / totalOriginalAmount;
                itemDiscountAmount = Math.Round(totalDiscountAmount * discountRatio, 2);
                remainingDiscount -= itemDiscountAmount;
            }

            AllocateDiscountToCartItems(cartItems, consumedItem.Quantity, itemDiscountAmount, promotion);
        }
    }

    /// <summary>
    /// 将折扣分摊到具体的购物车项
    /// </summary>
    private void AllocateDiscountToCartItems(List<CartItem> cartItems, int consumedQuantity,
        decimal totalDiscountAmount, AppliedPromotion promotion)
    {
        var remainingQuantity = consumedQuantity;
        var remainingDiscount = totalDiscountAmount;

        foreach (var cartItem in cartItems)
        {
            if (remainingQuantity <= 0) break;

            var allocatedQuantity = Math.Min(cartItem.Quantity, remainingQuantity);
            var itemDiscountAmount = (totalDiscountAmount * allocatedQuantity) / consumedQuantity;

            // 四舍五入到分
            itemDiscountAmount = Math.Round(itemDiscountAmount, 2);

            // 确保不超过商品原价
            var maxDiscount = cartItem.UnitPrice * allocatedQuantity;
            itemDiscountAmount = Math.Min(itemDiscountAmount, maxDiscount);

            // 更新实际支付单价
            var discountPerUnit = itemDiscountAmount / allocatedQuantity;
            cartItem.ActualUnitPrice = Math.Max(0, cartItem.UnitPrice - discountPerUnit);

            // 添加促销详情
            cartItem.AddPromotionDetail(new ItemPromotionDetail
            {
                RuleId = promotion.RuleId,
                RuleName = promotion.RuleName,
                PromotionType = promotion.PromotionType,
                DiscountAmount = itemDiscountAmount,
                Description = $"{promotion.RuleName} - 优惠 {itemDiscountAmount:C}",
                IsGiftRelated = false
            });

            remainingQuantity -= allocatedQuantity;
            remainingDiscount -= itemDiscountAmount;
        }
    }

    /// <summary>
    /// 处理四舍五入导致的金额差异
    /// </summary>
    private void HandleRoundingIssues(ShoppingCart cart, List<AppliedPromotion> appliedPromotions)
    {
        var expectedTotalDiscount = appliedPromotions.Sum(p => p.DiscountAmount);
        var actualTotalDiscount = cart.TotalDiscountAmount;
        var difference = expectedTotalDiscount - actualTotalDiscount;

        if (Math.Abs(difference) < 0.01m) return; // 差异小于1分，忽略

        // 找到最大金额的非赠品商品进行调整
        var adjustableItem = cart.Items
            .Where(x => !x.IsGift && x.ActualUnitPrice > 0)
            .OrderByDescending(x => x.ActualSubTotal)
            .FirstOrDefault();

        if (adjustableItem != null)
        {
            // 调整实际支付单价
            var adjustment = difference / adjustableItem.Quantity;
            adjustableItem.ActualUnitPrice = Math.Max(0, adjustableItem.ActualUnitPrice - adjustment);

            // 更新促销详情
            var roundingDetail = new ItemPromotionDetail
            {
                RuleId = "ROUNDING_ADJUSTMENT",
                RuleName = "四舍五入调整",
                PromotionType = "RoundingAdjustment",
                DiscountAmount = difference,
                Description = $"四舍五入调整: {difference:C}",
                IsGiftRelated = false
            };

            adjustableItem.AddPromotionDetail(roundingDetail);
        }
    }

    /// <summary>
    /// 验证分摊结果的正确性
    /// </summary>
    public ValidationResult ValidateAllocation(PromotionResult result)
    {
        var validationResult = new ValidationResult();

        // 验证总金额是否正确
        var expectedFinalAmount = result.OriginalAmount - result.TotalDiscount;
        var actualFinalAmount = result.ProcessedCart.ActualTotalAmount;
        var amountDifference = Math.Abs(expectedFinalAmount - actualFinalAmount);

        validationResult.IsValid = amountDifference < 0.01m;
        validationResult.AmountDifference = amountDifference;

        if (!validationResult.IsValid)
        {
            validationResult.ErrorMessage = $"金额验证失败：期望 {expectedFinalAmount:C}，实际 {actualFinalAmount:C}，差异 {amountDifference:C}";
        }

        // 验证赠品是否正确标记
        var giftItems = result.ProcessedCart.Items.Where(x => x.IsGift).ToList();
        foreach (var giftItem in giftItems)
        {
            if (giftItem.ActualUnitPrice != 0)
            {
                validationResult.IsValid = false;
                validationResult.ErrorMessage += $" 赠品 {giftItem.Product.Name} 价格应为0，实际为 {giftItem.ActualUnitPrice:C}";
            }
        }

        return validationResult;
    }
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; set; } = true;
    public decimal AmountDifference { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
}
