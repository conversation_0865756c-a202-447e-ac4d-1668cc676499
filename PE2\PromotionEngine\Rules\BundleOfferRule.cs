using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules;

/// <summary>
/// 组合套餐优惠规则（如：A+B+C组合价99元）
/// </summary>
public class BundleOfferRule : PromotionRuleBase
{
    public override string RuleType => "BundleOffer";

    /// <summary>
    /// 套餐商品组合
    /// </summary>
    public List<BundleItem> BundleItems { get; set; } = new();

    /// <summary>
    /// 套餐价格
    /// </summary>
    public decimal BundlePrice { get; set; }

    /// <summary>
    /// 是否允许超量购买（超出套餐数量的商品按原价计算）
    /// </summary>
    public bool AllowExcessQuantity { get; set; } = true;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        foreach (var bundleItem in BundleItems)
        {
            var availableQuantity = cart.GetAvailableProductQuantity(bundleItem.ProductId);
            if (availableQuantity < bundleItem.RequiredQuantity)
                return false;
        }

        return true;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = int.MaxValue;

        foreach (var bundleItem in BundleItems)
        {
            var availableQuantity = cart.GetAvailableProductQuantity(bundleItem.ProductId);
            var maxByItem = availableQuantity / bundleItem.RequiredQuantity;
            maxApplications = Math.Min(maxApplications, maxByItem);
        }

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications == int.MaxValue ? 0 : maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var totalDiscountAmount = 0m;
            var consumedItems = new List<ConsumedItem>();

            for (int app = 0; app < applicationCount; app++)
            {
                var bundleOriginalPrice = 0m;
                var bundleConsumedItems = new List<ConsumedItem>();
                var canApplyBundle = true;

                // 检查并消耗套餐中的每个商品
                foreach (var bundleItem in BundleItems)
                {
                    var remainingQuantity = bundleItem.RequiredQuantity;
                    var itemOriginalPrice = 0m;

                    var availableItems = cart.Items
                        .Where(x => x.Product.Id == bundleItem.ProductId && x.AvailableQuantity > 0)
                        .ToList();

                    foreach (var item in availableItems)
                    {
                        if (remainingQuantity <= 0) break;

                        var consumeQuantity = Math.Min(item.AvailableQuantity, remainingQuantity);
                        
                        if (consumeQuantity > 0)
                        {
                            itemOriginalPrice += consumeQuantity * item.UnitPrice;

                            // 记录消耗的商品
                            var existingConsumed = bundleConsumedItems.FirstOrDefault(x => x.ProductId == bundleItem.ProductId);
                            if (existingConsumed != null)
                            {
                                existingConsumed.Quantity += consumeQuantity;
                            }
                            else
                            {
                                bundleConsumedItems.Add(new ConsumedItem
                                {
                                    ProductId = bundleItem.ProductId,
                                    ProductName = item.Product.Name,
                                    Quantity = consumeQuantity,
                                    UnitPrice = item.UnitPrice
                                });
                            }

                            item.Quantity -= consumeQuantity;
                            remainingQuantity -= consumeQuantity;
                        }
                    }

                    // 如果某个商品数量不足，则无法应用套餐
                    if (remainingQuantity > 0)
                    {
                        canApplyBundle = false;
                        
                        // 回滚已消耗的商品
                        foreach (var consumed in bundleConsumedItems)
                        {
                            var items = cart.Items.Where(x => x.Product.Id == consumed.ProductId).ToList();
                            var restoreQuantity = consumed.Quantity;
                            
                            foreach (var item in items)
                            {
                                if (restoreQuantity <= 0) break;
                                var restore = Math.Min(restoreQuantity, consumed.Quantity);
                                item.Quantity += restore;
                                restoreQuantity -= restore;
                            }
                        }
                        break;
                    }

                    bundleOriginalPrice += itemOriginalPrice;
                }

                if (!canApplyBundle)
                    break;

                // 计算套餐优惠
                var bundleDiscount = bundleOriginalPrice - BundlePrice;
                if (bundleDiscount > 0)
                {
                    totalDiscountAmount += bundleDiscount;
                    consumedItems.AddRange(bundleConsumedItems);
                }
                else
                {
                    // 如果套餐价格不优惠，回滚商品
                    foreach (var consumed in bundleConsumedItems)
                    {
                        var items = cart.Items.Where(x => x.Product.Id == consumed.ProductId).ToList();
                        var restoreQuantity = consumed.Quantity;
                        
                        foreach (var item in items)
                        {
                            if (restoreQuantity <= 0) break;
                            var restore = Math.Min(restoreQuantity, consumed.Quantity);
                            item.Quantity += restore;
                            restoreQuantity -= restore;
                        }
                    }
                    break;
                }
            }

            // 清理数量为0的商品项
            cart.Items.RemoveAll(x => x.Quantity <= 0);

            application.DiscountAmount = totalDiscountAmount;
            application.ConsumedItems = consumedItems;
            application.IsSuccessful = totalDiscountAmount > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用套餐优惠";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = $"应用套餐优惠时发生错误: {ex.Message}";
        }

        return application;
    }
}

/// <summary>
/// 套餐商品项
/// </summary>
public class BundleItem
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 套餐中需要的数量
    /// </summary>
    public int RequiredQuantity { get; set; }
}
