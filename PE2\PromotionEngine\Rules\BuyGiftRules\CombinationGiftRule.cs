using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.BuyGiftRules;

/// <summary>
/// 组合送赠品规则 [OK]
/// 针对某些组合商品，必须购买A+B+C等商品且满足数量或金额条件才能赠送
/// 场景案例：购买A商品和B商品各1件时，送1件C商品。
/// 促销设置为不翻倍：A，B商品吊牌价、零售价为1000元；购买A、B商品各2件时，应收金额为4000元（送1件C商品）
/// 促销设置为翻2倍：A，B商品吊牌价、零售价为1000元，购买A、B商品各2件时，应收金额为4000元（送2件C商品）
/// 备注：1.送券场景；送券场景需要CRM支持送券到会员账户的功能，送券功能需要与仅限会员一起搭配使用。
///       2.赠送商品需要在购物车中存在，且赠送商品的数量或价值需满足条件才触发发促销规则执行
///       3.赠送商品策略：客户利益最大化 vs 商家利益最大化 需要根据配置来决定
/// </summary>
public class CombinationGiftRule : BaseBuyGiftRule
{
    public override string RuleType => "CombinationGift";

    /// <summary>
    /// 组合购买条件列表
    /// </summary>
    public List<CombinationGiftCondition> CombinationConditions { get; set; } = new();

    /// <summary>
    /// 赠品商品ID列表
    /// </summary>
    public List<string> GiftProductIds { get; set; } = new();

    /// <summary>
    /// 赠品数量
    /// </summary>
    public int GiftQuantity { get; set; }

    /// <summary>
    /// 券赠品列表（送券场景）
    /// </summary>
    public List<CouponGift> CouponGifts { get; set; } = new();

    /// <summary>
    /// 是否仅限会员（送券时必须为true）
    /// </summary>
    public bool MemberOnly { get; set; } = false;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!CombinationConditions.Any())
            return false;

        // 验证组合商品是否在购物车中
        var allConditionProductIds = CombinationConditions.Select(c => c.ProductId).ToList();
        if (!ValidateBuyGiftProductsInCart(cart, allConditionProductIds))
            return false;

        // 如果有实物赠品，验证赠品是否在购物车中
        if (GiftProductIds.Any() && !ValidateGiftProductsInCart(cart, GiftProductIds))
            return false;

        // 如果有券赠品且仅限会员，验证会员身份
        if (CouponGifts.Any() && MemberOnly && string.IsNullOrEmpty(cart.MemberId))
            return false;

        // 检查组合购买条件
        return CheckCombinationConditions(cart);
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyCombinationGift(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0 || result.Item3.Any();

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用组合送赠品促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用组合送赠品促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyCombinationGift(
        ShoppingCart cart,
        int applicationCount
    )
    {
        var totalGiftValue = 0m;
        var consumedItems = new List<ConsumedItem>();
        var giftItems = new List<GiftItem>(); // 赠品记录

        for (int app = 0; app < applicationCount; app++)
        {
            // 消耗组合条件商品（记录消耗但不修改购物车）
            var combinationConsumed = ConsumeCombinationProducts(cart);
            if (!combinationConsumed.Any())
                break;

            // 处理实物赠品
            if (GiftProductIds.Any())
            {
                var giftProducts = ProcessCombinationPhysicalGifts(cart, app);
                totalGiftValue += giftProducts.Sum(g => g.Value);
                giftItems.AddRange(giftProducts);
            }

            // 处理券赠品
            if (CouponGifts.Any() && !string.IsNullOrEmpty(cart.MemberId))
            {
                var couponGiftItems = ProcessCombinationCouponGifts(cart, app);
                giftItems.AddRange(couponGiftItems);
            }

            // 合并消耗记录
            foreach (var item in combinationConsumed)
            {
                var existingConsumed = consumedItems.FirstOrDefault(x =>
                    x.ProductId == item.ProductId
                );
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += item.Quantity;
                }
                else
                {
                    consumedItems.Add(item);
                }
            }
        }

        return (totalGiftValue, consumedItems, giftItems);
    }

    // ...existing code...

    /// <summary>
    /// 检查组合购买条件
    /// </summary>
    private bool CheckCombinationConditions(ShoppingCart cart)
    {
        foreach (var condition in CombinationConditions)
        {
            var availableQuantity = cart.GetAvailableProductQuantity(condition.ProductId);
            var totalAmount = cart
                .Items.Where(x => x.Product.Id == condition.ProductId)
                .Sum(x => x.SubTotal);

            // 修复：同时检查数量和金额条件
            if (condition.RequiredQuantity > 0 && availableQuantity < condition.RequiredQuantity)
                return false;

            if (condition.RequiredAmount > 0 && totalAmount < condition.RequiredAmount)
                return false;

            // 修复：如果两个条件都为0，则视为无效条件
            if (condition.RequiredQuantity <= 0 && condition.RequiredAmount <= 0)
                return false;
        }

        return true;
    }

    // ...existing code...

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = int.MaxValue;

        // 修复：正确计算每个组合条件的最大应用次数
        foreach (var condition in CombinationConditions)
        {
            var conditionMaxApplications = 0;

            if (condition.RequiredQuantity > 0)
            {
                var availableQuantity = cart.GetAvailableProductQuantity(condition.ProductId);
                conditionMaxApplications = IsRepeatable
                    ? availableQuantity / condition.RequiredQuantity
                    : (availableQuantity >= condition.RequiredQuantity ? 1 : 0);
            }
            else if (condition.RequiredAmount > 0)
            {
                var totalAmount = cart
                    .Items.Where(x => x.Product.Id == condition.ProductId)
                    .Sum(x => x.SubTotal);
                conditionMaxApplications = IsRepeatable
                    ? (int)(totalAmount / condition.RequiredAmount)
                    : (totalAmount >= condition.RequiredAmount ? 1 : 0);
            }
            else
            {
                // 修复：如果条件无效，应该返回0
                return 0;
            }

            maxApplications = Math.Min(maxApplications, conditionMaxApplications);
        }

        if (maxApplications == int.MaxValue)
            maxApplications = 1;

        // 如果有实物赠品，还需要考虑赠品的可用数量
        if (GiftProductIds.Any())
        {
            var availableGiftQuantity = CalculateTotalQuantity(cart, GiftProductIds);
            var maxByGiftQuantity = availableGiftQuantity / GiftQuantity;
            maxApplications = Math.Min(maxApplications, maxByGiftQuantity);
        }

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    // ...existing code...

    /// <summary>
    /// 消耗组合条件商品（修复：支持按金额消耗）
    /// </summary>
    private List<ConsumedItem> ConsumeCombinationProducts(ShoppingCart cart)
    {
        var consumedItems = new List<ConsumedItem>();

        foreach (var condition in CombinationConditions)
        {
            List<ConsumedItem> conditionConsumed;

            if (condition.RequiredQuantity > 0)
            {
                // 按数量消耗
                conditionConsumed = ConsumeConditionProducts(
                    cart,
                    new List<string> { condition.ProductId },
                    condition.RequiredQuantity
                );
            }
            else if (condition.RequiredAmount > 0)
            {
                // 修复：按金额消耗
                conditionConsumed = ConsumeConditionProductsByAmount(
                    cart,
                    condition.ProductId,
                    condition.RequiredAmount
                );
            }
            else
            {
                // 无效条件，跳过
                continue;
            }

            consumedItems.AddRange(conditionConsumed);
        }

        return consumedItems;
    }

    /// <summary>
    /// 按金额消耗指定商品（新增方法）
    /// </summary>
    private List<ConsumedItem> ConsumeConditionProductsByAmount(
        ShoppingCart cart,
        string productId,
        decimal requiredAmount
    )
    {
        var consumedItems = new List<ConsumedItem>();
        var remainingAmount = requiredAmount;

        // 获取该商品的所有购物车项目
        var cartItems = cart.Items.Where(x => x.Product.Id == productId && x.Quantity > 0).ToList();

        // 根据赠品选择策略排序
        if (GiftSelectionStrategy == BenefitSelectionStrategy.MerchantBenefit)
        {
            // 商家利益最大化：优先消耗高价商品
            cartItems = cartItems.OrderByDescending(x => x.UnitPrice).ToList();
        }
        else
        {
            // 客户利益最大化：优先消耗低价商品
            cartItems = cartItems.OrderBy(x => x.UnitPrice).ToList();
        }

        foreach (var cartItem in cartItems)
        {
            if (remainingAmount <= 0)
                break;

            // 计算需要消耗多少数量才能满足金额要求
            var itemTotalValue = cartItem.Quantity * cartItem.UnitPrice;

            if (itemTotalValue <= remainingAmount)
            {
                // 全部消耗
                var consumeQuantity = cartItem.Quantity;
                var consumeAmount = itemTotalValue;

                consumedItems.Add(
                    new ConsumedItem
                    {
                        ProductId = productId,
                        ProductName = cartItem.Product.Name,
                        Quantity = consumeQuantity,
                        UnitPrice = cartItem.UnitPrice
                    }
                );

                cartItem.Quantity = 0;
                remainingAmount -= consumeAmount;
            }
            else
            {
                // 部分消耗：计算需要消耗的数量
                var consumeQuantity = Math.Ceiling(remainingAmount / cartItem.UnitPrice);
                consumeQuantity = Math.Min(consumeQuantity, cartItem.Quantity);
                var actualConsumeAmount = consumeQuantity * cartItem.UnitPrice;

                consumedItems.Add(
                    new ConsumedItem
                    {
                        ProductId = productId,
                        ProductName = cartItem.Product.Name,
                        Quantity = (int)consumeQuantity,
                        UnitPrice = cartItem.UnitPrice
                    }
                );

                cartItem.Quantity -= (int)consumeQuantity;
                remainingAmount -= actualConsumeAmount;
            }
        }

        return consumedItems;
    }

    // ...existing code...

    /// <summary>
    /// 处理组合实物赠品（修复：改进条件描述）
    /// </summary>
    private List<GiftItem> ProcessCombinationPhysicalGifts(ShoppingCart cart, int applicationIndex)
    {
        var giftItems = new List<GiftItem>();

        // 收集可用的赠品
        var availableGifts = new List<(string ProductId, int Quantity, decimal UnitPrice)>();
        foreach (var giftProductId in GiftProductIds)
        {
            var cartItems = cart
                .Items.Where(x => x.Product.Id == giftProductId && x.Quantity > 0)
                .ToList();
            foreach (var cartItem in cartItems)
            {
                availableGifts.Add((giftProductId, cartItem.Quantity, cartItem.UnitPrice));
            }
        }

        // 选择赠品
        var selectedGifts = SelectGiftProducts(availableGifts, GiftQuantity);
        if (!selectedGifts.Any())
            return giftItems;

        // 应用赠品效果到购物车
        var promotion = new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType
        };
        ApplyGiftEffectToCart(cart, selectedGifts, promotion);

        // 记录赠品
        foreach (var gift in selectedGifts)
        {
            var strategyDescription =
                GiftSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                    ? "客户利益最大化"
                    : "商家利益最大化";

            // 修复：改进条件描述，支持金额条件
            var conditionDescription = string.Join(
                "、",
                CombinationConditions.Select(c =>
                {
                    if (c.RequiredQuantity > 0)
                        return $"{c.ProductId}满{c.RequiredQuantity}件";
                    else if (c.RequiredAmount > 0)
                        return $"{c.ProductId}满{c.RequiredAmount:C}";
                    else
                        return $"{c.ProductId}无效条件";
                })
            );

            var existingGift = giftItems.FirstOrDefault(x => x.ProductId == gift.ProductId);
            if (existingGift != null)
            {
                existingGift.Quantity += gift.Quantity;
                existingGift.Value += gift.Quantity * gift.UnitPrice;
            }
            else
            {
                var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == gift.ProductId);
                giftItems.Add(
                    new GiftItem
                    {
                        ProductId = gift.ProductId,
                        ProductName = cartItem?.Product.Name ?? gift.ProductId,
                        Quantity = gift.Quantity,
                        Value = gift.Quantity * gift.UnitPrice,
                        Description =
                            $"组合送赠品：{conditionDescription}，赠送{gift.Quantity}件，价值{gift.Quantity * gift.UnitPrice:C}（第{applicationIndex + 1}次应用，{strategyDescription}）"
                    }
                );
            }
        }

        return giftItems;
    }

    /// <summary>
    /// 处理组合券赠品（修复：改进条件描述）
    /// </summary>
    private List<GiftItem> ProcessCombinationCouponGifts(ShoppingCart cart, int applicationIndex)
    {
        var giftItems = new List<GiftItem>();

        foreach (var couponGift in CouponGifts)
        {
            // 发送券到会员账户（异步处理）
            _ = Task.Run(async () => await SendCouponToMemberAsync(cart.MemberId, couponGift));

            // 修复：改进条件描述，支持金额条件
            var conditionDescription = string.Join(
                "、",
                CombinationConditions.Select(c =>
                {
                    if (c.RequiredQuantity > 0)
                        return $"{c.ProductId}满{c.RequiredQuantity}件";
                    else if (c.RequiredAmount > 0)
                        return $"{c.ProductId}满{c.RequiredAmount:C}";
                    else
                        return $"{c.ProductId}无效条件";
                })
            );

            giftItems.Add(
                new GiftItem
                {
                    ProductId = couponGift.CouponId,
                    ProductName = couponGift.CouponName,
                    Quantity = couponGift.Quantity,
                    Value = couponGift.CouponValue * couponGift.Quantity,
                    Description =
                        $"组合送券：{conditionDescription}，{couponGift.Description}，价值{couponGift.CouponValue * couponGift.Quantity:C}（第{applicationIndex + 1}次应用）"
                }
            );
        }

        return giftItems;
    }
}

/// <summary>
/// 组合赠品条件
/// </summary>
public class CombinationGiftCondition
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 所需数量
    /// </summary>
    public int RequiredQuantity { get; set; }

    /// <summary>
    /// 所需金额
    /// </summary>
    public decimal RequiredAmount { get; set; }
}
