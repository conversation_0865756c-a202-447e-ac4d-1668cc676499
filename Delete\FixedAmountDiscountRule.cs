using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules;

/// <summary>
/// 固定金额折扣规则（如：满100减20）
/// </summary>
[Obsolete("老版本促销，已通用")]
public class FixedAmountDiscountRule : PromotionRuleBase
{
    public override string RuleType => "FixedAmountDiscount";

    /// <summary>
    /// 适用的商品ID列表（空表示适用于所有商品）
    /// </summary>
    public List<string> ApplicableProductIds { get; set; } = new();

    /// <summary>
    /// 最小购买金额
    /// </summary>
    public decimal MinAmount { get; set; }

    /// <summary>
    /// 折扣金额
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// 最大折扣金额（0表示无限制）
    /// </summary>
    public decimal MaxDiscountAmount { get; set; } = 0;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        var applicableAmount = GetApplicableAmount(cart);
        return applicableAmount >= MinAmount;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        if (!IsRepeatable)
            return 1;

        var applicableAmount = GetApplicableAmount(cart);
        var maxByAmount = (int)(applicableAmount / MinAmount);

        if (MaxApplications > 0)
            maxByAmount = Math.Min(maxByAmount, MaxApplications);

        return maxByAmount;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var totalDiscountAmount = 0m;
            var consumedItems = new List<ConsumedItem>();

            for (int app = 0; app < applicationCount; app++)
            {
                var applicableItems = GetApplicableItems(cart);
                var currentAmount = applicableItems.Sum(x => x.AvailableQuantity * x.UnitPrice);

                if (currentAmount < MinAmount)
                    break;

                var currentDiscount = DiscountAmount;

                // 检查最大折扣限制
                if (MaxDiscountAmount > 0)
                {
                    var remainingMaxDiscount = MaxDiscountAmount - totalDiscountAmount;
                    currentDiscount = Math.Min(currentDiscount, remainingMaxDiscount);
                }

                // 不能超过实际金额
                currentDiscount = Math.Min(currentDiscount, currentAmount);

                if (currentDiscount <= 0)
                    break;

                totalDiscountAmount += currentDiscount;

                // 按比例消耗商品（模拟折扣分摊）
                var remainingDiscount = currentDiscount;
                var totalApplicableAmount = currentAmount;

                foreach (var item in applicableItems.OrderByDescending(x => x.UnitPrice))
                {
                    if (remainingDiscount <= 0) break;

                    var itemTotalValue = item.AvailableQuantity * item.UnitPrice;
                    var itemDiscountRatio = itemTotalValue / totalApplicableAmount;
                    var itemDiscount = Math.Min(remainingDiscount, currentDiscount * itemDiscountRatio);

                    if (itemDiscount > 0)
                    {
                        // 计算需要消耗的数量（按比例）
                        var consumeQuantity = Math.Min(item.AvailableQuantity,
                            (int)Math.Ceiling(itemDiscount / item.UnitPrice));

                        if (consumeQuantity > 0)
                        {
                            // 记录消耗的商品
                            var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == item.Product.Id);
                            if (existingConsumed != null)
                            {
                                existingConsumed.Quantity += consumeQuantity;
                            }
                            else
                            {
                                consumedItems.Add(new ConsumedItem
                                {
                                    ProductId = item.Product.Id,
                                    ProductName = item.Product.Name,
                                    Quantity = consumeQuantity,
                                    UnitPrice = item.UnitPrice
                                });
                            }

                            item.Quantity -= consumeQuantity;
                            remainingDiscount -= itemDiscount;
                        }
                    }
                }
            }

            // 清理数量为0的商品项
            cart.Items.RemoveAll(x => x.Quantity <= 0);

            application.DiscountAmount = totalDiscountAmount;
            application.ConsumedItems = consumedItems;
            application.IsSuccessful = totalDiscountAmount > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用固定金额折扣";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = $"应用固定金额折扣时发生错误: {ex.Message}";
        }

        return application;
    }

    private decimal GetApplicableAmount(ShoppingCart cart)
    {
        var applicableItems = GetApplicableItems(cart);
        return applicableItems.Sum(x => x.AvailableQuantity * x.UnitPrice);
    }

    private List<CartItem> GetApplicableItems(ShoppingCart cart)
    {
        if (ApplicableProductIds.Count == 0)
        {
            // 适用于所有商品
            return cart.Items.Where(x => x.AvailableQuantity > 0).ToList();
        }
        else
        {
            // 适用于指定商品
            return cart.Items
                .Where(x => x.AvailableQuantity > 0 && ApplicableProductIds.Contains(x.Product.Id))
                .ToList();
        }
    }
}
