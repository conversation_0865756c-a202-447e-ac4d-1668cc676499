### 测试组合折扣规则修复 - B商品既是条件又是折扣对象
### 场景：购买A商品50元*1 + B商品30元*2 + C商品20元*1
### 规则：购买A(1件) + B(1件)，B和C商品都可以打9折
### 预期结果：
### - A(1件)和B(1件)作为条件被消耗
### - 剩余的B(1件)和C(1件)享受9折
### - 总价：50 + 30 + (30*0.9) + (20*0.9) = 50 + 30 + 27 + 18 = 125元
### - 折扣金额：(30*0.1) + (20*0.1) = 3 + 2 = 5元

POST {{host}}/api/promotion/test-combination-discount
Content-Type: application/json

{
  "cart": {
    "items": [
      {
        "product": {
          "id": "A",
          "name": "商品A",
          "price": 50.00,
          "category": "电子产品"
        },
        "quantity": 1,
        "unitPrice": 50.00
      },
      {
        "product": {
          "id": "B", 
          "name": "商品B",
          "price": 30.00,
          "category": "服装"
        },
        "quantity": 2,
        "unitPrice": 30.00
      },
      {
        "product": {
          "id": "C",
          "name": "商品C", 
          "price": 20.00,
          "category": "家居"
        },
        "quantity": 1,
        "unitPrice": 20.00
      }
    ]
  },
  "rules": [
    {
      "$type": "CombinationDiscount",
      "id": "COMBINATION_DISCOUNT_B_BOTH",
      "name": "组合折扣测试 - 买A+B享B和C商品9折",
      "description": "购买A商品和B商品各大于等于1件，B和C商品都可以打9折",
      "priority": 85,
      "isEnabled": true,
      "startTime": "2024-01-01T00:00:00",
      "endTime": "2025-12-31T23:59:59",
      "isRepeatable": true,
      "maxApplications": 2,
      "applicableCustomerTypes": [],
      "exclusiveRuleIds": [],
      "canStackWithOthers": true,
      "productExclusivity": "None",
      "discountSelectionStrategy": "CustomerBenefit",
      "discountRate": 0.9,
      "discountProductIds": [ "C", "B" ],
      "combinationConditions": [
        {
          "productIds": [ "A" ],
          "requiredQuantity": 1,
          "requiredAmount": 0
        },
        {
          "productIds": [ "B" ],
          "requiredQuantity": 1,
          "requiredAmount": 0
        }
      ]
    }
  ]
}

###

### 测试多B商品场景 - 买3个B商品
### 场景：购买A商品50元*1 + B商品30元*3
### 规则：购买A(1件) + B(1件)，B商品可以打9折
### 预期结果：
### - A(1件)和B(1件)作为条件被消耗
### - 剩余的B(2件)享受9折
### - 总价：50 + 30 + (30*2*0.9) = 50 + 30 + 54 = 134元
### - 折扣金额：(30*2*0.1) = 6元

POST {{host}}/api/promotion/test-combination-discount
Content-Type: application/json

{
  "cart": {
    "items": [
      {
        "product": {
          "id": "A",
          "name": "商品A",
          "price": 50.00,
          "category": "电子产品"
        },
        "quantity": 1,
        "unitPrice": 50.00
      },
      {
        "product": {
          "id": "B", 
          "name": "商品B",
          "price": 30.00,
          "category": "服装"
        },
        "quantity": 3,
        "unitPrice": 30.00
      }
    ]
  },
  "rules": [
    {
      "$type": "CombinationDiscount",
      "id": "COMBINATION_DISCOUNT_B_ONLY",
      "name": "组合折扣测试 - 买A+B享B商品9折",
      "description": "购买A商品和B商品各大于等于1件，B商品打9折",
      "priority": 85,
      "isEnabled": true,
      "startTime": "2024-01-01T00:00:00",
      "endTime": "2025-12-31T23:59:59",
      "isRepeatable": true,
      "maxApplications": 2,
      "applicableCustomerTypes": [],
      "exclusiveRuleIds": [],
      "canStackWithOthers": true,
      "productExclusivity": "None",
      "discountSelectionStrategy": "CustomerBenefit",
      "discountRate": 0.9,
      "discountProductIds": [ "B" ],
      "combinationConditions": [
        {
          "productIds": [ "A" ],
          "requiredQuantity": 1,
          "requiredAmount": 0
        },
        {
          "productIds": [ "B" ],
          "requiredQuantity": 1,
          "requiredAmount": 0
        }
      ]
    }
  ]
}

###

### 验证边界情况 - B商品数量刚好等于条件要求
### 场景：购买A商品50元*1 + B商品30元*1
### 规则：购买A(1件) + B(1件)，B商品可以打9折
### 预期结果：
### - A(1件)和B(1件)作为条件被消耗
### - 没有剩余的B商品可以享受折扣
### - 总价：50 + 30 = 80元
### - 折扣金额：0元

POST {{host}}/api/promotion/test-combination-discount
Content-Type: application/json

{
  "cart": {
    "items": [
      {
        "product": {
          "id": "A",
          "name": "商品A",
          "price": 50.00,
          "category": "电子产品"
        },
        "quantity": 1,
        "unitPrice": 50.00
      },
      {
        "product": {
          "id": "B", 
          "name": "商品B",
          "price": 30.00,
          "category": "服装"
        },
        "quantity": 1,
        "unitPrice": 30.00
      }
    ]
  },
  "rules": [
    {
      "$type": "CombinationDiscount",
      "id": "COMBINATION_DISCOUNT_BOUNDARY",
      "name": "组合折扣边界测试 - B商品数量刚好",
      "description": "购买A商品和B商品各1件，B商品打9折",
      "priority": 85,
      "isEnabled": true,
      "startTime": "2024-01-01T00:00:00",
      "endTime": "2025-12-31T23:59:59",
      "isRepeatable": true,
      "maxApplications": 2,
      "applicableCustomerTypes": [],
      "exclusiveRuleIds": [],
      "canStackWithOthers": true,
      "productExclusivity": "None",
      "discountSelectionStrategy": "CustomerBenefit",
      "discountRate": 0.9,
      "discountProductIds": [ "B" ],
      "combinationConditions": [
        {
          "productIds": [ "A" ],
          "requiredQuantity": 1,
          "requiredAmount": 0
        },
        {
          "productIds": [ "B" ],
          "requiredQuantity": 1,
          "requiredAmount": 0
        }
      ]
    }
  ]
}
