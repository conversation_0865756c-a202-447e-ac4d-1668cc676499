using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.ExchangeRules;

/// <summary>
/// 组合优惠换购规则 [OK]
/// 某A类商品满X件或X元并且某B类商品满Y件或Y元，优惠Y元，换购一件C类商品
/// 场景案例：购买A、B商品各大于等于1件时，可以优惠1元购买C商品。
///A、B商品吊牌价、零售价都为1000元，C商品吊牌价、零售价为99元；
///购买A、B、C商品各1件时，应收金额为1000+1000+（99-1）= 2098元（C商品优惠1元）
///备注：1.组合特价换购规则允许多次应用，但每次应用必须满足所有组合条件，并且每次应用只能消耗一次组合条件商品。
///      2.如果条件商品和优惠商品相同，需要考虑根据客户利益最大化或商家利益最大化来选择换购商品。
///      3.优惠商品和条件商品可以是同一商品，但不能是同一件商品（即不能同时满足购买和优惠条件）。
/// </summary>
public class CombinationDiscountAmountExchangeRule : BaseExchangeRule
{
    public override string RuleType => "CombinationDiscountAmountExchange";

    public List<CombinationBuyCondition> CombinationConditions { get; set; } = new();
    public List<DiscountAmountExchangeCondition> ExchangeConditions { get; set; } = new();

    protected override bool CheckConditions(ShoppingCart cart)
    {
        return CombinationConditions.Any() && ExchangeConditions.Any() &&
               CheckCombinationConditions(cart) && CheckExchangeProductAvailability(cart);
    }

    /// <summary>
    /// 检查组合购买条件
    /// </summary>
    private bool CheckCombinationConditions(ShoppingCart cart)
    {
        return CombinationConditions.All(condition => IsConditionSatisfied(cart, condition));
    }

    /// <summary>
    /// 检查单个条件是否满足
    /// </summary>
    private bool IsConditionSatisfied(ShoppingCart cart, CombinationBuyCondition condition)
    {
        var (availableQuantity, totalAmount) = GetConditionMetrics(cart, condition.ProductId);

        return (condition.RequiredQuantity <= 0 || availableQuantity >= condition.RequiredQuantity) &&
               (condition.RequiredAmount <= 0 || totalAmount >= condition.RequiredAmount);
    }

    /// <summary>
    /// 获取条件相关的数量和金额指标
    /// </summary>
    private (int quantity, decimal amount) GetConditionMetrics(ShoppingCart cart, List<string> productIds)
    {
        var quantity = productIds.Sum(cart.GetAvailableProductQuantity);
        var amount = cart.Items
            .Where(x => productIds.Contains(x.Product.Id) && x.AvailableQuantity > 0)
            .Sum(x => x.UnitPrice * x.AvailableQuantity);

        return (quantity, amount);
    }

    /// <summary>
    /// 检查换购商品可用性
    /// 确保在消耗组合条件商品后，仍有足够的换购商品可用
    /// </summary>
    private bool CheckExchangeProductAvailability(ShoppingCart cart)
    {
        var simulatedCart = SimulateCartAfterCombinationConsumption(cart);
        var requiredExchangeQuantity = ExchangeConditions.Sum(c => c.ExchangeQuantity);

        var allExchangeProductIds = ExchangeConditions
            .SelectMany(c => c.ExchangeProductIds)
            .Distinct()
            .ToList();

        var availableExchangeQuantity = allExchangeProductIds
            .Sum(id => simulatedCart.GetValueOrDefault(id, 0));

        return availableExchangeQuantity >= requiredExchangeQuantity;
    }

    /// <summary>
    /// 模拟消耗组合条件商品后的购物车状态
    /// </summary>
    private Dictionary<string, int> SimulateCartAfterCombinationConsumption(ShoppingCart cart)
    {
        var simulatedAvailability = cart.Items.ToDictionary(x => x.Product.Id, x => x.AvailableQuantity);

        foreach (var condition in CombinationConditions)
        {
            ConsumeConditionFromSimulation(cart, condition, simulatedAvailability);
        }

        return simulatedAvailability;
    }

    /// <summary>
    /// 在模拟中消耗条件商品
    /// </summary>
    private void ConsumeConditionFromSimulation(ShoppingCart cart, CombinationBuyCondition condition,
        Dictionary<string, int> simulatedAvailability)
    {
        if (condition.RequiredQuantity > 0)
        {
            ConsumeByQuantity(condition.ProductId, condition.RequiredQuantity, simulatedAvailability);
        }
        else if (condition.RequiredAmount > 0)
        {
            ConsumeByAmount(cart, condition.ProductId, condition.RequiredAmount, simulatedAvailability);
        }
    }

    /// <summary>
    /// 按数量消耗商品
    /// </summary>
    private void ConsumeByQuantity(List<string> productIds, int requiredQuantity, Dictionary<string, int> availability)
    {
        var remaining = requiredQuantity;
        foreach (var productId in productIds.Where(availability.ContainsKey))
        {
            if (remaining <= 0) break;
            var consume = Math.Min(availability[productId], remaining);
            availability[productId] -= consume;
            remaining -= consume;
        }
    }

    /// <summary>
    /// 按金额消耗商品
    /// </summary>
    private void ConsumeByAmount(ShoppingCart cart, List<string> productIds, decimal requiredAmount,
        Dictionary<string, int> availability)
    {
        var remaining = requiredAmount;

        var sortedProductIds = productIds
            .Where(availability.ContainsKey)
            .Where(id => availability[id] > 0)
            .Select(id => new
            {
                ProductId = id,
                UnitPrice = cart.Items.FirstOrDefault(x => x.Product.Id == id)?.UnitPrice ?? 0
            })
            .Where(x => x.UnitPrice > 0)
            .OrderByDescending(x => x.UnitPrice)
            .ToList();

        foreach (var item in sortedProductIds)
        {
            if (remaining <= 0) break;

            var availableQuantity = availability[item.ProductId];
            if (availableQuantity <= 0) continue;

            var neededQuantity = (int)Math.Ceiling(remaining / item.UnitPrice);
            var actualConsume = Math.Min(availableQuantity, neededQuantity);

            availability[item.ProductId] -= actualConsume;
            remaining -= actualConsume * item.UnitPrice;
        }
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart)) return 0;

        var maxApplications = int.MaxValue;

        // 计算每个组合条件的最大应用次数
        foreach (var condition in CombinationConditions)
        {
            var conditionMaxApplications = CalculateMaxApplicationsByCondition(cart, condition);
            maxApplications = Math.Min(maxApplications, conditionMaxApplications);
        }

        // 检查换购商品限制
        var simulatedCart = SimulateCartAfterCombinationConsumption(cart);
        var requiredExchangeQuantity = ExchangeConditions.Sum(c => c.ExchangeQuantity);
        if (requiredExchangeQuantity > 0)
        {
            var availableExchangeQuantity = ExchangeConditions
                .SelectMany(c => c.ExchangeProductIds)
                .Distinct()
                .Sum(id => simulatedCart.GetValueOrDefault(id, 0));

            if (availableExchangeQuantity < requiredExchangeQuantity)
            {
                return 0; // 没有足够的换购商品
            }

            maxApplications = Math.Min(maxApplications, availableExchangeQuantity / requiredExchangeQuantity);
        }

        if (maxApplications == int.MaxValue)
            maxApplications = 1;

        return ApplyApplicationLimits(maxApplications);
    }

    /// <summary>
    /// 计算单个条件的最大应用次数
    /// </summary>
    private int CalculateMaxApplicationsByCondition(ShoppingCart cart, CombinationBuyCondition condition)
    {
        var (availableQuantity, totalAmount) = GetConditionMetrics(cart, condition.ProductId);

        // 按数量要求计算
        if (condition.RequiredQuantity > 0)
        {
            return IsRepeatable
                ? availableQuantity / condition.RequiredQuantity
                : (availableQuantity >= condition.RequiredQuantity ? 1 : 0);
        }

        // 按金额要求计算
        if (condition.RequiredAmount > 0)
        {
            return IsRepeatable
                ? (int)(totalAmount / condition.RequiredAmount)
                : (totalAmount >= condition.RequiredAmount ? 1 : 0);
        }

        return IsRepeatable ? int.MaxValue : 1;
    }

    /// <summary>
    /// 应用应用次数限制
    /// </summary>
    private int ApplyApplicationLimits(int maxApplications)
    {
        if (!IsRepeatable) maxApplications = Math.Min(maxApplications, 1);
        if (MaxApplications > 0) maxApplications = Math.Min(maxApplications, MaxApplications);
        return maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var (discountAmount, consumedItems, exchangeItems) = ApplyCombinationDiscountAmountExchange(cart, applicationCount);
            application.DiscountAmount = discountAmount;
            application.ConsumedItems = consumedItems;
            application.GiftItems = exchangeItems;
            application.IsSuccessful = discountAmount > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用组合优惠换购促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用组合优惠换购促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyCombinationDiscountAmountExchange(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var exchangeItems = new List<GiftItem>();

        for (int app = 0; app < applicationCount; app++)
        {
            if (!CheckConditions(cart)) break;

            if (ConsumeCombinationConditionProducts(cart, consumedItems))
            {
                totalDiscountAmount += ProcessExchangeConditions(cart, exchangeItems);
            }
        }

        return (totalDiscountAmount, consumedItems, exchangeItems);
    }

    /// <summary>
    /// 处理换购条件
    /// </summary>
    private decimal ProcessExchangeConditions(ShoppingCart cart, List<GiftItem> exchangeItems)
    {
        var totalDiscount = 0m;
        var applicableExchanges = GetApplicableExchangeConditions();

        foreach (var exchange in applicableExchanges)
        {
            totalDiscount += ProcessExchangeCondition(cart, exchange, exchangeItems);
        }

        return totalDiscount;
    }

    /// <summary>
    /// 处理单个换购条件
    /// </summary>
    private decimal ProcessExchangeCondition(ShoppingCart cart, DiscountAmountExchangeCondition exchange, List<GiftItem> exchangeItems)
    {
        var totalDiscount = 0m;
        var exchangedCount = 0;
        var requiredQuantity = exchange.ExchangeQuantity;

        // 获取所有可用的换购商品
        var availableExchangeItems = cart.Items
            .Where(x => exchange.ExchangeProductIds.Contains(x.Product.Id) && x.AvailableQuantity > 0)
            .ToList();

        // 根据策略排序
        var sortedItems = ExchangeSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
            ? availableExchangeItems.OrderByDescending(x => x.UnitPrice) // 客户利益：优先换购高价商品
            : availableExchangeItems.OrderBy(x => x.UnitPrice); // 商家利益：优先换购低价商品

        foreach (var cartItem in sortedItems)
        {
            if (exchangedCount >= requiredQuantity) break;

            var availableForExchange = cartItem.AvailableQuantity;
            var needToExchange = Math.Min(availableForExchange, requiredQuantity - exchangedCount);

            if (needToExchange > 0)
            {
                var originalPrice = cartItem.UnitPrice;
                var discountedPrice = CalculateDiscountAmountExchange(originalPrice, exchange.DiscountAmount);
                var discountPerItem = originalPrice - discountedPrice;

                if (discountPerItem > 0)
                {
                    var totalDiscountForThisItem = discountPerItem * needToExchange;
                    totalDiscount += totalDiscountForThisItem;

                    // 修改购物车中的实际支付价格（只对换购的数量生效）
                    if (needToExchange == cartItem.Quantity)
                    {
                        // 全部换购
                        cartItem.ActualUnitPrice = discountedPrice;
                    }
                    else
                    {
                        // 部分换购：需要分离商品
                        var remainingQuantity = cartItem.Quantity - needToExchange;
                        cartItem.Quantity = remainingQuantity;

                        // 创建新的换购商品项
                        var exchangeCartItem = new CartItem
                        {
                            Product = cartItem.Product,
                            Quantity = needToExchange,
                            UnitPrice = cartItem.UnitPrice,
                            ActualUnitPrice = discountedPrice,
                            IsGift = false
                        };
                        cart.Items.Add(exchangeCartItem);
                    }

                    var strategyDescription = ExchangeSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                        ? "客户利益最大化" : "商家利益最大化";

                    exchangeItems.Add(new GiftItem
                    {
                        ProductId = cartItem.Product.Id,
                        ProductName = cartItem.Product.Name,
                        Quantity = needToExchange,
                        Value = totalDiscountForThisItem,
                        Description = $"组合优惠换购：优惠{exchange.DiscountAmount:C}换购{needToExchange}件，节省{totalDiscountForThisItem:C}（{strategyDescription}）"
                    });

                    exchangedCount += needToExchange;
                }
            }
        }

        return totalDiscount;
    }

    /// <summary>
    /// 消耗组合条件商品
    /// </summary>
    private bool ConsumeCombinationConditionProducts(ShoppingCart cart, List<ConsumedItem> consumedItems)
    {
        return CombinationConditions.All(condition => ConsumeConditionProducts(cart, condition, consumedItems));
    }

    /// <summary>
    /// 消耗单个条件的商品
    /// </summary>
    private bool ConsumeConditionProducts(ShoppingCart cart, CombinationBuyCondition condition, List<ConsumedItem> consumedItems)
    {
        if (condition.RequiredQuantity > 0)
        {
            return ConsumeProductsByQuantity(cart, condition.ProductId, condition.RequiredQuantity, consumedItems);
        }
        if (condition.RequiredAmount > 0)
        {
            return ConsumeProductsByAmount(cart, condition.ProductId, condition.RequiredAmount, consumedItems);
        }
        return true;
    }

    /// <summary>
    /// 按数量消耗商品
    /// </summary>
    private bool ConsumeProductsByQuantity(ShoppingCart cart, List<string> productIds, int requiredQuantity, List<ConsumedItem> consumedItems)
    {
        var remaining = requiredQuantity;
        var availableItems = cart.Items.Where(x => productIds.Contains(x.Product.Id) && x.AvailableQuantity > 0);

        foreach (var item in availableItems)
        {
            if (remaining <= 0) break;
            var consumeQuantity = Math.Min(item.AvailableQuantity, remaining);
            AddOrUpdateConsumedItem(consumedItems, item, consumeQuantity);
            item.Quantity -= consumeQuantity;
            remaining -= consumeQuantity;
        }

        return remaining <= 0;
    }

    /// <summary>
    /// 按金额消耗商品
    /// </summary>
    private bool ConsumeProductsByAmount(ShoppingCart cart, List<string> productIds, decimal requiredAmount, List<ConsumedItem> consumedItems)
    {
        var remaining = requiredAmount;

        var availableItems = cart.Items
            .Where(x => productIds.Contains(x.Product.Id) && x.AvailableQuantity > 0)
            .OrderByDescending(x => x.UnitPrice)
            .ToList();

        foreach (var item in availableItems)
        {
            if (remaining <= 0) break;

            var neededQuantity = (int)Math.Ceiling(remaining / item.UnitPrice);
            var consumeQuantity = Math.Min(item.AvailableQuantity, neededQuantity);

            AddOrUpdateConsumedItem(consumedItems, item, consumeQuantity);
            item.Quantity -= consumeQuantity;
            remaining -= consumeQuantity * item.UnitPrice;
        }

        return remaining <= 0;
    }

    /// <summary>
    /// 添加或更新消耗商品记录
    /// </summary>
    private void AddOrUpdateConsumedItem(List<ConsumedItem> consumedItems, CartItem item, int consumeQuantity)
    {
        var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == item.Product.Id);
        if (existingConsumed != null)
        {
            existingConsumed.Quantity += consumeQuantity;
        }
        else
        {
            consumedItems.Add(new ConsumedItem
            {
                ProductId = item.Product.Id,
                ProductName = item.Product.Name,
                Quantity = consumeQuantity,
                UnitPrice = item.UnitPrice
            });
        }
    }

    /// <summary>
    /// 获取适用的换购条件
    /// </summary>
    private List<DiscountAmountExchangeCondition> GetApplicableExchangeConditions()
    {
        return ExchangeStrategy switch
        {
            ExchangeStrategy.ByGradient => ExchangeConditions.Take(1).ToList(),
            ExchangeStrategy.AllExchange => ExchangeConditions.ToList(),
            _ => ExchangeConditions.Take(1).ToList()
        };
    }
}