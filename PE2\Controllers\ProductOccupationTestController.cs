using Microsoft.AspNetCore.Mvc;
using PE2.Models;
using PE2.PromotionEngine.Core;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Rules;

namespace PE2.Controllers;

/// <summary>
/// 商品占用和条件筛选测试控制器
/// 演示新架构的完整使用流程
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ProductOccupationTestController : ControllerBase
{
    private readonly ProductOccupationEngine _occupationEngine;
    private readonly ConditionExecutionSeparator _separator;
    private readonly ExclusivityManager _exclusivityManager;
    private readonly ILogger<ProductOccupationTestController> _logger;

    public ProductOccupationTestController(
        ProductOccupationEngine occupationEngine,
        ConditionExecutionSeparator separator,
        ExclusivityManager exclusivityManager,
        ILogger<ProductOccupationTestController> logger)
    {
        _occupationEngine = occupationEngine;
        _separator = separator;
        _exclusivityManager = exclusivityManager;
        _logger = logger;
    }

    /// <summary>
    /// 测试基本的商品占用功能
    /// </summary>
    [HttpPost("test-basic-occupation")]
    public async Task<IActionResult> TestBasicOccupation([FromBody] TestOccupationRequest request)
    {
        try
        {
            var cart = CreateTestCart(request.CartItems);
            
            using var session = await _occupationEngine.CreateSessionAsync(cart);
            
            // 测试条件预留
            var conditionResult = await session.TryReserveForConditionAsync(
                "TEST_RULE_001",
                "测试规则",
                request.ProductIds,
                request.RequiredQuantity);

            if (!conditionResult.IsSuccessful)
            {
                return BadRequest(new
                {
                    Success = false,
                    Message = "条件预留失败",
                    Error = conditionResult.ErrorMessage,
                    Conflicts = conditionResult.Conflicts
                });
            }

            // 测试执行预留
            var executionResult = await session.TryReserveForExecutionAsync(
                "TEST_RULE_001",
                "测试规则",
                request.ProductIds,
                request.ExecutionQuantity);

            if (!executionResult.IsSuccessful)
            {
                await session.ReleaseReservationsAsync("TEST_RULE_001");
                return BadRequest(new
                {
                    Success = false,
                    Message = "执行预留失败",
                    Error = executionResult.ErrorMessage,
                    Conflicts = executionResult.Conflicts
                });
            }

            // 提交会话
            var commitResult = await session.CommitAsync();
            
            var stateReport = session.GetStateReport();

            return Ok(new
            {
                Success = true,
                Message = "商品占用测试成功",
                ConditionResult = conditionResult,
                ExecutionResult = executionResult,
                CommitResult = commitResult,
                StateReport = stateReport
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "测试商品占用时发生异常");
            return StatusCode(500, new { Success = false, Message = ex.Message });
        }
    }

    /// <summary>
    /// 测试条件与执行分离
    /// </summary>
    [HttpPost("test-condition-execution-separation")]
    public async Task<IActionResult> TestConditionExecutionSeparation([FromBody] TestSeparationRequest request)
    {
        try
        {
            var cart = CreateTestCart(request.CartItems);
            var rule = CreateTestRule(request.RuleType, request.RuleConfig);

            var result = await _separator.ApplyPromotionWithSeparationAsync(cart, rule);

            return Ok(new
            {
                Success = result.IsSuccessful,
                Message = result.IsSuccessful ? "条件执行分离测试成功" : "条件执行分离测试失败",
                Result = result,
                FailureStage = result.FailureStage,
                ErrorMessage = result.ErrorMessage,
                ConditionResult = result.ConditionResult,
                ExecutionResult = result.ExecutionResult,
                ApplicationResult = result.ApplicationResult,
                FinalCart = result.FinalCart
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "测试条件执行分离时发生异常");
            return StatusCode(500, new { Success = false, Message = ex.Message });
        }
    }

    /// <summary>
    /// 测试排他性管理
    /// </summary>
    [HttpPost("test-exclusivity-management")]
    public async Task<IActionResult> TestExclusivityManagement([FromBody] TestExclusivityRequest request)
    {
        try
        {
            var cart = CreateTestCart(request.CartItems);
            var rules = request.Rules.Select(r => CreateTestRule(r.RuleType, r.RuleConfig)).ToList();

            // 分析排他性
            var analysisResult = await _exclusivityManager.AnalyzeExclusivityAsync(rules, cart);

            // 选择最优组合
            var selectionResult = await _exclusivityManager.SelectOptimalCombinationAsync(
                rules, 
                cart, 
                request.OptimizationStrategy);

            return Ok(new
            {
                Success = true,
                Message = "排他性管理测试完成",
                AnalysisResult = new
                {
                    TotalConflicts = analysisResult.Conflicts.Count,
                    Conflicts = analysisResult.Conflicts,
                    ExclusivityGroups = analysisResult.ExclusivityGroups,
                    CompatibilityMatrix = analysisResult.CompatibilityMatrix,
                    OptimalCombinations = analysisResult.OptimalCombinations.Count
                },
                SelectionResult = new
                {
                    SelectedRulesCount = selectionResult.SelectedRules.Count,
                    SelectedRules = selectionResult.SelectedRules.Select(r => new { r.Id, r.Name }),
                    Score = selectionResult.Score,
                    Strategy = selectionResult.Strategy,
                    Reason = selectionResult.Reason,
                    AlternativesCount = selectionResult.AlternativeCombinations.Count,
                    Alternatives = selectionResult.AlternativeCombinations.Take(3)
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "测试排他性管理时发生异常");
            return StatusCode(500, new { Success = false, Message = ex.Message });
        }
    }

    /// <summary>
    /// 测试完整的促销应用流程
    /// </summary>
    [HttpPost("test-complete-flow")]
    public async Task<IActionResult> TestCompleteFlow([FromBody] TestCompleteFlowRequest request)
    {
        try
        {
            var cart = CreateTestCart(request.CartItems);
            var rules = request.Rules.Select(r => CreateTestRule(r.RuleType, r.RuleConfig)).ToList();

            var results = new List<object>();

            // 1. 排他性分析
            var exclusivityAnalysis = await _exclusivityManager.AnalyzeExclusivityAsync(rules, cart);
            
            // 2. 选择最优组合
            var optimalSelection = await _exclusivityManager.SelectOptimalCombinationAsync(
                rules, cart, OptimizationStrategy.MaxCustomerBenefit);

            // 3. 逐个应用选中的规则
            foreach (var rule in optimalSelection.SelectedRules)
            {
                var separationResult = await _separator.ApplyPromotionWithSeparationAsync(cart, rule);
                results.Add(new
                {
                    RuleId = rule.Id,
                    RuleName = rule.Name,
                    Success = separationResult.IsSuccessful,
                    FailureStage = separationResult.FailureStage,
                    ErrorMessage = separationResult.ErrorMessage
                });

                if (separationResult.IsSuccessful && separationResult.FinalCart != null)
                {
                    cart = separationResult.FinalCart;
                }
            }

            return Ok(new
            {
                Success = true,
                Message = "完整流程测试完成",
                ExclusivityAnalysis = new
                {
                    ConflictsCount = exclusivityAnalysis.Conflicts.Count,
                    GroupsCount = exclusivityAnalysis.ExclusivityGroups.Count
                },
                OptimalSelection = new
                {
                    SelectedRulesCount = optimalSelection.SelectedRules.Count,
                    Score = optimalSelection.Score
                },
                ApplicationResults = results,
                FinalCart = new
                {
                    TotalItems = cart.Items.Count,
                    TotalAmount = cart.Items.Sum(i => i.FinalUnitPrice * i.Quantity),
                    Items = cart.Items.Select(i => new
                    {
                        i.ProductId,
                        i.ProductName,
                        i.Quantity,
                        OriginalPrice = i.OriginalUnitPrice,
                        FinalPrice = i.FinalUnitPrice,
                        Discount = i.OriginalUnitPrice - i.FinalUnitPrice
                    })
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "测试完整流程时发生异常");
            return StatusCode(500, new { Success = false, Message = ex.Message });
        }
    }

    /// <summary>
    /// 获取架构设计说明
    /// </summary>
    [HttpGet("architecture-overview")]
    public IActionResult GetArchitectureOverview()
    {
        return Ok(new
        {
            Title = "商品占用和条件筛选架构设计",
            CoreComponents = new[]
            {
                new
                {
                    Name = "ProductOccupationEngine",
                    Description = "商品占用引擎，管理商品的预留和占用状态",
                    Features = new[] { "事务性会话", "冲突检测", "状态管理", "自动清理" }
                },
                new
                {
                    Name = "ConditionExecutionSeparator",
                    Description = "条件与执行分离器，实现促销的两阶段处理",
                    Features = new[] { "条件验证", "执行预留", "效果应用", "回滚机制" }
                },
                new
                {
                    Name = "ExclusivityManager",
                    Description = "排他性管理器，处理促销规则间的互斥关系",
                    Features = new[] { "冲突分析", "最优选择", "兼容性矩阵", "智能推荐" }
                }
            },
            DesignPrinciples = new[]
            {
                "混合式商品表示：保持业务语义的同时支持精确占用管理",
                "三阶段处理：条件验证 → 执行预留 → 效果应用",
                "事务性操作：支持提交和回滚，确保数据一致性",
                "层级排他：支持None/Partial/Exclusive三级排他性",
                "智能优化：根据不同策略选择最优促销组合"
            },
            Benefits = new[]
            {
                "解决商品占用冲突问题",
                "实现条件与执行的清晰分离",
                "支持复杂的排他性管理",
                "提供完整的可观测性",
                "保持高性能和可扩展性"
            }
        });
    }

    /// <summary>
    /// 创建测试购物车
    /// </summary>
    private ProcessedCart CreateTestCart(List<TestCartItem> items)
    {
        var cart = new ProcessedCart
        {
            CartId = Guid.NewGuid().ToString("N"),
            CustomerId = "TEST_CUSTOMER",
            Items = items.Select(item => new ProcessedCartItem
            {
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                Quantity = item.Quantity,
                OriginalUnitPrice = item.UnitPrice,
                FinalUnitPrice = item.UnitPrice
            }).ToList()
        };

        return cart;
    }

    /// <summary>
    /// 创建测试规则
    /// </summary>
    private PromotionRuleBase CreateTestRule(string ruleType, Dictionary<string, object> config)
    {
        // 这里应该根据ruleType和config创建具体的规则实例
        // 简化实现，返回一个基础规则
        return new TestPromotionRule
        {
            Id = Guid.NewGuid().ToString("N"),
            Name = $"测试规则_{ruleType}",
            Description = "测试用促销规则",
            RuleType = ruleType,
            IsEnabled = true,
            Priority = 100,
            CanStackWithOthers = config.GetValueOrDefault("CanStack", true).ToString() == "True",
            ProductExclusivity = Enum.Parse<ProductExclusivityLevel>(
                config.GetValueOrDefault("ExclusivityLevel", "None").ToString() ?? "None")
        };
    }
}

/// <summary>
/// 测试用促销规则
/// </summary>
public class TestPromotionRule : PromotionRuleBase
{
    public override string RuleType { get; } = "Test";
}

/// <summary>
/// 测试占用请求
/// </summary>
public class TestOccupationRequest
{
    public List<TestCartItem> CartItems { get; set; } = [];
    public List<string> ProductIds { get; set; } = [];
    public int RequiredQuantity { get; set; }
    public int ExecutionQuantity { get; set; }
}

/// <summary>
/// 测试分离请求
/// </summary>
public class TestSeparationRequest
{
    public List<TestCartItem> CartItems { get; set; } = [];
    public string RuleType { get; set; } = string.Empty;
    public Dictionary<string, object> RuleConfig { get; set; } = new();
}

/// <summary>
/// 测试排他性请求
/// </summary>
public class TestExclusivityRequest
{
    public List<TestCartItem> CartItems { get; set; } = [];
    public List<TestRuleConfig> Rules { get; set; } = [];
    public OptimizationStrategy OptimizationStrategy { get; set; } = OptimizationStrategy.MaxCustomerBenefit;
}

/// <summary>
/// 测试完整流程请求
/// </summary>
public class TestCompleteFlowRequest
{
    public List<TestCartItem> CartItems { get; set; } = [];
    public List<TestRuleConfig> Rules { get; set; } = [];
}

/// <summary>
/// 测试购物车项
/// </summary>
public class TestCartItem
{
    public required string ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
}

/// <summary>
/// 测试规则配置
/// </summary>
public class TestRuleConfig
{
    public string RuleType { get; set; } = string.Empty;
    public Dictionary<string, object> RuleConfig { get; set; } = new();
}
