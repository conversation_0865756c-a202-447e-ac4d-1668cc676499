using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.BuyFreeRules;

/// <summary>
/// 商品买免规则 [OK]
/// 针对某一类商品，满X件免Y件
/// 场景案例：购买A商品3件时，免1件。
/// 促销设置为不翻倍：A商品吊牌价、零售价1000元；购买A商品3件时，应收金额为1000*3 - 1000 = 2000元
/// 促销设置为翻2倍：A商品吊牌价、零售价1000元；购买A商品6件时，应收金额为1000*6 - 1000 - 1000 = 4000元
/// 备注：商品买免需要考虑设置中的 客户利益最大化还是商户利益最大化来决定免费商品的选择策略
/// </summary>
public class ProductBuyFreeRule : BaseBuyFreeRule
{
    public override string RuleType => "ProductBuyFree";

    /// <summary>
    /// 适用的商品ID列表
    /// </summary>
    public List<string> ApplicableProductIds { get; set; } = [];

    /// <summary>
    /// 购买数量要求
    /// </summary>
    public int BuyQuantity { get; set; }

    /// <summary>
    /// 免费数量
    /// </summary>
    public int FreeQuantity { get; set; }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyProductBuyFree(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用买免促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用商品买免促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyProductBuyFree(
        ShoppingCart cart,
        int applicationCount
    )
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var giftItems = new List<GiftItem>();

        var promotion = new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType
        };
        var strategyDesc =
            FreeItemSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                ? "客户利益最大化"
                : "商户利益最大化";
        var allowedApplications =
            MaxApplications > 0 ? Math.Min(applicationCount, MaxApplications) : applicationCount;

        var processedByProduct = new Dictionary<string, int>();

        for (int app = 0; app < allowedApplications; app++)
        {
            var (buyItems, freeItems) = CalculateBuyFreeAllocation(cart, processedByProduct);
            if (!buyItems.Any() || !freeItems.Any())
                break;

            // 处理购买商品
            ProcessItems(buyItems, processedByProduct, consumedItems, null, null, null);

            // 处理免费商品
            var freeDiscount = ProcessFreeItems(
                freeItems,
                processedByProduct,
                consumedItems,
                giftItems,
                promotion,
                app + 1,
                strategyDesc
            );

            totalDiscountAmount += freeDiscount;

            if (!IsRepeatable)
                break;
        }

        return (totalDiscountAmount, consumedItems, giftItems);
    }

    /// <summary>
    /// 计算买免分配方案
    /// </summary>
    private (
        List<(CartItem Item, int Quantity)> BuyItems,
        List<(CartItem Item, int Quantity)> FreeItems
    ) CalculateBuyFreeAllocation(ShoppingCart cart, Dictionary<string, int> processedByProduct)
    {
        var applicableItems = GetAvailableItems(cart, processedByProduct);
        if (!HasSufficientItems(applicableItems))
            return (new List<(CartItem, int)>(), new List<(CartItem, int)>());

        // 按策略排序：客户利益用便宜的买贵的免，商户利益用贵的买便宜的免
        var buyOrderItems =
            FreeItemSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                ? applicableItems.OrderBy(x => x.Item.UnitPrice).ToList()
                : applicableItems.OrderByDescending(x => x.Item.UnitPrice).ToList();

        var freeOrderItems =
            FreeItemSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                ? applicableItems.OrderByDescending(x => x.Item.UnitPrice).ToList()
                : applicableItems.OrderBy(x => x.Item.UnitPrice).ToList();

        var buyItems = AllocateItems(buyOrderItems, BuyQuantity);
        if (buyItems.Sum(x => x.Quantity) < BuyQuantity)
            return (new List<(CartItem, int)>(), new List<(CartItem, int)>());

        // 更新可用数量后分配免费商品
        var updatedAvailable = UpdateAvailableAfterAllocation(applicableItems, buyItems);
        var freeItems = AllocateItems(
            freeOrderItems
                .Select(x => (x.Item, updatedAvailable.GetValueOrDefault(x.Item.Product.Id, 0)))
                .ToList(),
            FreeQuantity
        );

        return freeItems.Sum(x => x.Quantity) >= FreeQuantity
            ? (buyItems, freeItems)
            : (new List<(CartItem, int)>(), new List<(CartItem, int)>());
    }

    /// <summary>
    /// 获取可用商品列表
    /// </summary>
    private List<(CartItem Item, int Available)> GetAvailableItems(
        ShoppingCart cart,
        Dictionary<string, int> processedByProduct
    )
    {
        return cart
            .Items.Where(x => ApplicableProductIds.Contains(x.Product.Id) && x.Quantity > 0)
            .Select(x =>
                (x, Math.Max(0, x.Quantity - processedByProduct.GetValueOrDefault(x.Product.Id, 0)))
            )
            .Where(x => x.Item2 > 0)
            .ToList();
    }

    /// <summary>
    /// 检查是否有足够商品
    /// </summary>
    private bool HasSufficientItems(List<(CartItem Item, int Available)> items)
    {
        return items.Sum(x => x.Available) >= BuyQuantity + FreeQuantity;
    }

    /// <summary>
    /// 分配商品
    /// </summary>
    private List<(CartItem Item, int Quantity)> AllocateItems(
        List<(CartItem Item, int Available)> items,
        int targetQuantity
    )
    {
        var result = new List<(CartItem Item, int Quantity)>();
        var remaining = targetQuantity;

        foreach (var (item, available) in items)
        {
            if (remaining <= 0)
                break;
            var allocate = Math.Min(remaining, available);
            if (allocate > 0)
            {
                result.Add((item, allocate));
                remaining -= allocate;
            }
        }

        return result;
    }

    /// <summary>
    /// 更新分配后的可用数量
    /// </summary>
    private Dictionary<string, int> UpdateAvailableAfterAllocation(
        List<(CartItem Item, int Available)> original,
        List<(CartItem Item, int Quantity)> allocated
    )
    {
        var result = original.ToDictionary(x => x.Item.Product.Id, x => x.Available);
        foreach (var (item, quantity) in allocated)
        {
            result[item.Product.Id] = Math.Max(
                0,
                result.GetValueOrDefault(item.Product.Id, 0) - quantity
            );
        }
        return result;
    }

    /// <summary>
    /// 处理免费商品
    /// </summary>
    private decimal ProcessFreeItems(
        List<(CartItem Item, int Quantity)> freeItems,
        Dictionary<string, int> processedByProduct,
        List<ConsumedItem> consumedItems,
        List<GiftItem> giftItems,
        AppliedPromotion promotion,
        int appIndex,
        string strategyDesc
    )
    {
        var totalDiscount = 0m;

        foreach (var (item, quantity) in freeItems)
        {
            var freeAmount = item.UnitPrice * quantity;
            totalDiscount += freeAmount;

            // 更新商品定价
            var totalQty = item.Quantity;
            var paidQty = totalQty - quantity;
            item.ActualUnitPrice = (paidQty * item.UnitPrice) / totalQty;

            // 添加促销详情
            item.AddPromotionDetail(
                new ItemPromotionDetail
                {
                    RuleId = promotion.RuleId,
                    RuleName = promotion.RuleName,
                    PromotionType = promotion.PromotionType,
                    DiscountAmount = freeAmount,
                    Description =
                        $"买{BuyQuantity}免{FreeQuantity}：{quantity}件免费（第{appIndex}次，{strategyDesc}）",
                    IsGiftRelated = true
                }
            );

            // 记录免费商品和消耗
            giftItems.Add(
                new GiftItem
                {
                    ProductId = item.Product.Id,
                    ProductName = item.Product.Name,
                    Quantity = quantity,

                    Description = $"买{BuyQuantity}免{FreeQuantity}：{quantity}件免费，节省{freeAmount:C}",
                }
            );

            AddOrUpdateConsumed(
                consumedItems,
                item.Product.Id,
                item.Product.Name,
                quantity,
                item.UnitPrice
            );
            processedByProduct[item.Product.Id] =
                processedByProduct.GetValueOrDefault(item.Product.Id, 0) + quantity;
        }

        return totalDiscount;
    }

    /// <summary>
    /// 处理商品（通用方法）
    /// </summary>
    private void ProcessItems(
        List<(CartItem Item, int Quantity)> items,
        Dictionary<string, int> processedByProduct,
        List<ConsumedItem> consumedItems,
        List<GiftItem> giftItems,
        AppliedPromotion promotion,
        string description
    )
    {
        foreach (var (item, quantity) in items)
        {
            AddOrUpdateConsumed(
                consumedItems,
                item.Product.Id,
                item.Product.Name,
                quantity,
                item.UnitPrice
            );
            processedByProduct[item.Product.Id] =
                processedByProduct.GetValueOrDefault(item.Product.Id, 0) + quantity;
        }
    }

    /// <summary>
    /// 添加或更新消耗商品
    /// </summary>
    private void AddOrUpdateConsumed(
        List<ConsumedItem> list,
        string productId,
        string productName,
        int qty,
        decimal unitPrice
    )
    {
        var existing = list.FirstOrDefault(x => x.ProductId == productId);
        if (existing != null)
            existing.Quantity += qty;
        else
            list.Add(
                new ConsumedItem
                {
                    ProductId = productId,
                    ProductName = productName,
                    Quantity = qty,
                    UnitPrice = unitPrice
                }
            );
    }

    /// <summary>
    /// 简化最大应用次数计算
    /// </summary>
    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var applicableItems = cart.Items.Where(x =>
            ApplicableProductIds.Contains(x.Product.Id) && x.Quantity > 0
        );
        if (!applicableItems.Any())
            return 0;

        var maxApps = applicableItems.Sum(x => x.Quantity) / (BuyQuantity + FreeQuantity);

        if (!IsRepeatable)
            maxApps = Math.Min(maxApps, 1);
        return MaxApplications > 0 ? Math.Min(maxApps, MaxApplications) : maxApps;
    }

    /// <summary>
    /// 简化条件检查
    /// </summary>
    protected override bool CheckConditions(ShoppingCart cart)
    {
        var applicableItems = cart.Items.Where(x =>
            ApplicableProductIds.Contains(x.Product.Id) && x.Quantity > 0
        );
        if (!applicableItems.Any())
            return false;

        return applicableItems.Sum(x => x.Quantity) >= BuyQuantity + FreeQuantity;
    }
}
