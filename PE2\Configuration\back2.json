﻿[
  {
    "$type": "UnifiedSpecialPriceExchange",
    "id": "EXCHANGE_SPECIAL_001",
    "name": "统一特价换购测试 - 买A加1元换B",
    "description": "购买A商品1件时，若再增加1元可换购B商品",
    "priority": 60,
    "isEnabled": true,
    "startTime": "2024-01-01T00:00:00",
    "endTime": "2025-12-31T23:59:59",
    "isRepeatable": false,
    "maxApplications": 1,
    "applicableCustomerTypes": [],
    "exclusiveRuleIds": [],
    "canStackWithOthers": false,
    "productExclusivity": "Exclusive",
    "exchangeStrategy": "ByGradient",
    "exchangeSelectionStrategy": "CustomerBenefit",
    "buyConditions": [
      {
        "productIds": [ "A" ],
        "requiredQuantity": 1,
        "requiredAmount": 0
      }
    ],
    "exchangeConditions": [
      {
        "exchangeProductIds": [ "B" ],
        "exchangeQuantity": 1,
        "addAmount": 1.00,
        "description": "加1元换购B商品"
      }
    ]
  },
  {
    "$type": "CombinationBuyFree",
    "id": "COMBINATION_BUY_FREE_001",
    "name": "组合买免测试 - 买A+B免最低价（不翻倍）",
    "description": "购买A商品2件、B商品1件，免费送其中价格最低的1件商品",
    "priority": 80,
    "isEnabled": true,
    "startTime": "2024-01-01T00:00:00",
    "endTime": "2025-12-31T23:59:59",
    "isRepeatable": false,
    "maxApplications": 1,
    "applicableCustomerTypes": [],
    "exclusiveRuleIds": [],
    "canStackWithOthers": false,
    "productExclusivity": "Exclusive",
    "freeItemSelectionStrategy": "CustomerBenefit",
    "freeQuantity": 1,
    "freeFromCombinationOnly": true,
    "combinationConditions": [
      {
        "productId": "A",
        "requiredQuantity": 2,
        "requiredAmount": 0
      },
      {
        "productId": "B",
        "requiredQuantity": 1,
        "requiredAmount": 0
      }
    ]
  }
]