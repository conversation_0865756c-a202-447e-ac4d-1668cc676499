<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>促销规则配置管理系统 - 专业版</title>
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3.3.8/dist/vue.global.js"></script>
    
    <!-- Element Plus UI Framework -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    
    <!-- Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.iife.min.js"></script>
    
    <style>
        /* Ant Design 设计变量 */
        :root {
            --ant-primary-color: #1890ff;
            --ant-primary-color-hover: #40a9ff;
            --ant-primary-color-active: #096dd9;
            --ant-success-color: #52c41a;
            --ant-warning-color: #faad14;
            --ant-error-color: #ff4d4f;
            --ant-text-color: rgba(0, 0, 0, 0.85);
            --ant-text-color-secondary: rgba(0, 0, 0, 0.65);
            --ant-text-color-disabled: rgba(0, 0, 0, 0.25);
            --ant-background-color-base: #f0f2f5;
            --ant-component-background: #ffffff;
            --ant-border-color-base: #d9d9d9;
            --ant-border-color-split: #f0f0f0;
            --ant-border-radius-base: 6px;
            --ant-box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
            --ant-padding-lg: 24px;
            --ant-padding-md: 16px;
            --ant-padding-sm: 12px;
            --ant-padding-xs: 8px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ant-background-color-base);            color: var(--ant-text-color);
            font-size: 14px;
            line-height: 1.5715;
            margin: 0;
            padding: 0;
        }

        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* 头部样式 */
        .header {
            background: var(--ant-component-background);
            border-bottom: 1px solid var(--ant-border-color-split);
            padding: var(--ant-padding-md) var(--ant-padding-lg);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: var(--ant-padding-sm);
        }

        .header-title h1 {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
            color: var(--ant-text-color);
        }

        .header-subtitle {
            font-size: 14px;
            color: var(--ant-text-color-secondary);
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: var(--ant-padding-xs);
        }

        /* 布局样式 */
        .layout {
            display: flex;
            flex: 1;
            min-height: 0;
        }

        .sidebar {
            width: 280px;
            background: var(--ant-component-background);
            border-right: 1px solid var(--ant-border-color-split);
            overflow-y: auto;
        }

        .sidebar-section {
            padding: var(--ant-padding-md);
        }

        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--ant-text-color);
            margin-bottom: var(--ant-padding-sm);
            display: flex;
            align-items: center;
            gap: var(--ant-padding-xs);
        }

        /* 促销类型卡片样式 */
        .promotion-type-grid {
            display: flex;
            flex-direction: column;
            gap: var(--ant-padding-xs);
        }

        .promotion-type-card {
            padding: var(--ant-padding-sm);
            border: 1px solid var(--ant-border-color-base);
            border-radius: var(--ant-border-radius-base);
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            background: var(--ant-component-background);
        }

        .promotion-type-card:hover {
            border-color: var(--ant-primary-color-hover);
        }

        .promotion-type-card.active {
            background: #e6f7ff;
            border-color: var(--ant-primary-color);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: var(--ant-padding-xs);
            margin-bottom: 4px;
        }

        .card-icon {
            font-size: 16px;
            color: var(--ant-primary-color);
        }

        .card-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--ant-text-color);
        }

        .card-description {
            font-size: 12px;
            color: var(--ant-text-color-secondary);
            line-height: 1.4;
        }

        /* 主面板样式 */
        .main-panel {
            flex: 1;
            background: var(--ant-component-background);
            padding: var(--ant-padding-lg);
            overflow-y: auto;
        }

        /* Ant Design 风格步骤指示器 */
        .step-indicator {
            display: flex;
            align-items: center;
            margin-bottom: var(--ant-padding-lg);
            padding: var(--ant-padding-lg) 0;
        }

        .step {
            flex: 1;
            display: flex;
            align-items: center;
            position: relative;
            cursor: pointer;
        }

        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 16px;
            right: -50%;
            width: 100%;
            height: 1px;
            background: var(--ant-border-color-split);
            z-index: 0;
        }

        .step.completed:not(:last-child)::after {
            background: var(--ant-primary-color);
        }

        .step-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 600;
            border: 2px solid var(--ant-border-color-base);
            background: var(--ant-component-background);
            color: var(--ant-text-color-secondary);
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            z-index: 1;
            position: relative;
        }

        .step-content {
            margin-left: var(--ant-padding-sm);
            flex: 1;
        }

        .step-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--ant-text-color-secondary);
            line-height: 1.4;
            transition: color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        }

        .step-description {
            font-size: 12px;
            color: var(--ant-text-color-disabled);
            margin-top: 2px;
            line-height: 1.3;
        }

        /* 激活状态 */
        .step.active .step-icon {
            background: var(--ant-primary-color);
            border-color: var(--ant-primary-color);
            color: white;
        }

        .step.active .step-title {
            color: var(--ant-primary-color);
            font-weight: 600;
        }

        .step.active .step-description {
            color: var(--ant-text-color-secondary);
        }

        /* 完成状态 */
        .step.completed .step-icon {
            background: var(--ant-primary-color);
            border-color: var(--ant-primary-color);
            color: white;
        }

        .step.completed .step-title {
            color: var(--ant-text-color);
        }

        .step.completed .step-description {
            color: var(--ant-text-color-secondary);
        }

        /* 悬浮效果 */
        .step:hover .step-icon {
            border-color: var(--ant-primary-color-hover);
        }

        .step:hover .step-title {
            color: var(--ant-primary-color-hover);
        }

        /* Ant Design 风格表单区域 */
        .form-container {
            max-width: 800px;
        }

        .form-section {
            background: var(--ant-component-background);
            border: 1px solid var(--ant-border-color-split);
            border-radius: var(--ant-border-radius-base);
            padding: var(--ant-padding-lg);
            margin-bottom: var(--ant-padding-lg);
            transition: box-shadow 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        }

        .form-section:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: var(--ant-padding-sm);
            margin-bottom: var(--ant-padding-lg);
            padding-bottom: var(--ant-padding-md);
            border-bottom: 1px solid var(--ant-border-color-split);
        }

        .section-icon {
            width: 32px;
            height: 32px;
            border-radius: var(--ant-border-radius-base);
            background: #f0f9ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--ant-primary-color);
            font-size: 16px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--ant-text-color);
            margin: 0;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--ant-padding-lg);
        }

        /* 右侧预览面板样式 */
        .right-panel {
            width: 400px;
            background: var(--ant-background-color-base);
            border-left: 1px solid var(--ant-border-color-split);
            display: flex;
            flex-direction: column;
        }

        .preview-header {
            padding: var(--ant-padding-lg);
            border-bottom: 1px solid var(--ant-border-color-split);
            background: var(--ant-component-background);
        }

        .preview-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--ant-text-color);
            margin-bottom: var(--ant-padding-xs);
            display: flex;
            align-items: center;
            gap: var(--ant-padding-xs);
        }

        .preview-subtitle {
            font-size: 14px;
            color: var(--ant-text-color-secondary);
        }

        .preview-content {
            flex: 1;
            padding: var(--ant-padding-lg);
            overflow-y: auto;
        }

        .json-editor {
            background: #f6f8fa;
            color: #24292f;
            border-radius: var(--ant-border-radius-base);
            padding: var(--ant-padding-md);
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 12px;
            line-height: 1.45;
            overflow-x: auto;
            border: 1px solid var(--ant-border-color-base);
            min-height: 300px;
            white-space: pre-wrap;
        }

        /* 验证状态样式 */
        .validation-status {
            margin-top: var(--ant-padding-md);
            padding: var(--ant-padding-sm);
            border-radius: var(--ant-border-radius-base);
            font-size: 14px;
        }

        .validation-status.valid {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: var(--ant-success-color);
        }

        .validation-status.invalid {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: var(--ant-error-color);
        }

        .action-buttons {
            padding: var(--ant-padding-lg);
            border-top: 1px solid var(--ant-border-color-split);
            background: var(--ant-component-background);
            display: flex;
            gap: var(--ant-padding-xs);
        }

        /* Element Plus 组件样式覆写 */
        .el-button--primary {
            background-color: var(--ant-primary-color) !important;
            border-color: var(--ant-primary-color) !important;
        }

        .el-button--primary:hover {
            background-color: var(--ant-primary-color-hover) !important;
            border-color: var(--ant-primary-color-hover) !important;
        }

        .el-button--primary:active {
            background-color: var(--ant-primary-color-active) !important;
            border-color: var(--ant-primary-color-active) !important;
        }

        .el-input__wrapper {
            border-radius: var(--ant-border-radius-base) !important;
        }

        .el-select .el-input__wrapper {
            border-radius: var(--ant-border-radius-base) !important;
        }

        /* Element Plus 表单组件优化 */
        .el-form-item {
            margin-bottom: var(--ant-padding-lg) !important;
        }

        .el-form-item__label {
            font-size: 14px !important;
            font-weight: 500 !important;
            color: var(--ant-text-color) !important;
            line-height: 1.5715 !important;
            padding-bottom: var(--ant-padding-xs) !important;
        }

        .el-input__wrapper {
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
        }

        .el-input__wrapper:hover {
            border-color: var(--ant-primary-color-hover) !important;
        }

        .el-input__wrapper.is-focus {
            border-color: var(--ant-primary-color) !important;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
        }

        .el-textarea__inner {
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
        }

        .el-textarea__inner:hover {
            border-color: var(--ant-primary-color-hover) !important;
        }

        .el-textarea__inner:focus {
            border-color: var(--ant-primary-color) !important;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
        }

        .el-input-number {
            width: 100%;
        }

        .el-select .el-input__wrapper:hover {
            border-color: var(--ant-primary-color-hover) !important;
        }

        .el-select .el-input.is-focus .el-input__wrapper {
            border-color: var(--ant-primary-color) !important;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
        }

        /* 步骤导航按钮区域优化 */
        .step-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: var(--ant-padding-lg);
            padding-top: var(--ant-padding-lg);
            border-top: 1px solid var(--ant-border-color-split);
        }

        .step-navigation .el-button {
            min-width: 100px;
        }

        /* 简洁的过渡动画 */
        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.2s ease;
        }

        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }

        /* 商品选择器样式 */
        .product-selector {
            border: 1px dashed var(--ant-border-color-base);
            border-radius: var(--ant-border-radius-base);
            padding: var(--ant-padding-md);
            text-align: center;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            background: var(--ant-component-background);
        }

        .product-selector:hover {
            border-color: var(--ant-primary-color);
            background: #f0f9ff;
        }

        .product-tags {
            display: flex;
            flex-wrap: wrap;
            gap: var(--ant-padding-xs);
            margin-bottom: var(--ant-padding-sm);
        }

        .empty-state {
            text-align: center;
            padding: 80px 20px;
            color: var(--ant-text-color-secondary);
        }

        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 20px;
            color: var(--ant-text-color-disabled);
        }

        /* ComplexArrayRenderer 组件样式 */
        .complex-array-renderer .array-item-card {
            margin-bottom: 12px;
        }
        
        .complex-array-renderer .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .complex-array-renderer .item-title {
            font-weight: 500;
            color: #303133;
        }
        
        .complex-array-renderer .item-actions {
            display: flex;
            gap: 8px;
        }
        
        .complex-array-renderer .item-content {
            margin-top: 8px;
        }
        
        .complex-array-renderer .text-placeholder {
            color: #909399;
            font-style: italic;
        }
        
        .complex-array-renderer .array-simple-input {
            width: 100%;
        }
        
        .complex-array-renderer .array-item-input {
            display: flex;
            gap: 8px;
            margin-bottom: 8px;
            align-items: center;
        }
        
        .complex-array-renderer .array-item-input .el-input {
            flex: 1;
        }

        /* DynamicFormRenderer 组件样式 */
        .dynamic-form-renderer {
            padding: 24px;
            background: var(--ant-component-background);
            border-radius: var(--ant-border-radius-base);
        }

        .dynamic-form-renderer .form-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--ant-border-color-split);
        }

        .dynamic-form-renderer .form-header h3 {
            margin: 0 0 8px 0;
            font-size: 18px;
            font-weight: 600;
            color: var(--ant-text-color);
        }

        .dynamic-form-renderer .form-description {
            margin: 0;
            font-size: 14px;
            color: var(--ant-text-color-secondary);
        }

        .dynamic-form-renderer .group-title {
            font-weight: 600;
            color: var(--ant-text-color);
        }

        .dynamic-form-renderer .array-field {
            width: 100%;
        }

        .dynamic-form-renderer .array-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            padding: 8px;
            border: 1px solid var(--ant-border-color-base);
            border-radius: var(--ant-border-radius-base);
            background: #fafafa;
        }

        .dynamic-form-renderer .array-item-content {
            flex: 1;
            margin-right: 8px;
        }

        /* 商品选择器样式 */
        .dynamic-form-renderer .product-selector-field {
            border: 1px solid var(--ant-border-color-base);
            border-radius: var(--ant-border-radius-base);
            padding: 16px;
            background: #fafafa;
        }

        .dynamic-form-renderer .selected-products {
            margin-bottom: 12px;
        }

        .dynamic-form-renderer .add-product-section {
            display: flex;
            align-items: center;
        }

        /* JSON编辑器样式 */
        .dynamic-form-renderer .json-editor-field {
            width: 100%;
        }

        .dynamic-form-renderer .json-editor-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .dynamic-form-renderer .json-hint {
            margin-top: 4px;
        }

        /* 普通数组字段样式 */
        .dynamic-form-renderer .normal-array-field {
            width: 100%;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .right-panel {
                width: 350px;
            }
        }

        @media (max-width: 768px) {
            .layout {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                order: 2;
                max-height: 300px;
            }
            
            .main-panel {
                padding: var(--ant-padding-md);
                order: 1;
            }
            
            .right-panel {
                width: 100%;
                order: 3;
                max-height: 400px;
            }

            .header {
                padding: var(--ant-padding-sm) var(--ant-padding-md);
            }

            .header-title h1 {
                font-size: 18px;
            }

            .header-actions {
                flex-wrap: wrap;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .step-icon {
                width: 28px;
                height: 28px;
                font-size: 14px;
            }
            
            .step-title {
                font-size: 13px;
            }
            
            .step-description {
                display: none;
            }
        }

            .layout {
                padding: var(--ant-padding-md);
            }

            .category-grid,
            .type-grid {
                grid-template-columns: 1fr;
            }

            .products-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- PromotionConfigSystem组件将在这里渲染 -->
    </div>    <!-- 加载配置和组件 -->
    <script src="./js/config/promotionTypes.js"></script>    <script src="./js/services/apiService.js"></script>
    <script src="./js/services/metadataService.js"></script>
    <script src="./js/components/ProductSelector.js"></script>
    <script src="./js/components/ConditionConfigRenderer.js"></script>
    <script src="./js/components/ComplexArrayRenderer.js"></script>
    <script src="./js/components/EnhancedConditionRenderer.js"></script>
    <script src="./js/components/DynamicFormRenderer_template.js"></script>
    <script src="./js/components/RuleManager.js"></script><script src="./js/components/PromotionConfigMainComponentHybrid.js"></script>    <script>
        // 使用混合版本主组件（三栏布局 + 反射API）
        const { createApp } = Vue;
        
        // 检查组件是否正确加载
        console.log('ProductSelector:', typeof ProductSelector);
        console.log('ComplexArrayRenderer:', typeof ComplexArrayRenderer);
        console.log('DynamicFormRenderer:', typeof DynamicFormRenderer);
        console.log('PromotionConfigMainComponentHybrid:', typeof PromotionConfigMainComponentHybrid);
        
        // 创建Vue应用
        const app = createApp(PromotionConfigMainComponentHybrid);
        
        // 注册Element Plus
        app.use(ElementPlus);
        
        // 注册Element Plus图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
          // 注册自定义组件（只注册已定义的组件）
        if (typeof ProductSelector !== 'undefined') {
            app.component('ProductSelector', ProductSelector);
            app.component('product-selector', ProductSelector);  // 同时注册kebab-case版本
        }
        if (typeof ComplexArrayRenderer !== 'undefined') {
            app.component('ComplexArrayRenderer', ComplexArrayRenderer);
            app.component('complex-array-renderer', ComplexArrayRenderer);  // 同时注册kebab-case版本
        }
        if (typeof DynamicFormRenderer !== 'undefined') {
            app.component('DynamicFormRenderer', DynamicFormRenderer);
            app.component('dynamic-form-renderer', DynamicFormRenderer);  // 同时注册kebab-case版本
        }
        
        // 挂载应用
        app.mount('#app');
    </script>
</body>
</html>
