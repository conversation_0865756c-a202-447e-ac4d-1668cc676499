F:\ac\POSPE2\PE2\bin\Debug\net9.0\appsettings.Development.json
F:\ac\POSPE2\PE2\bin\Debug\net9.0\appsettings.json
F:\ac\POSPE2\PE2\bin\Debug\net9.0\Configuration\promotion-rules.json
F:\ac\POSPE2\PE2\bin\Debug\net9.0\PE2.staticwebassets.endpoints.json
F:\ac\POSPE2\PE2\bin\Debug\net9.0\PE2.exe
F:\ac\POSPE2\PE2\bin\Debug\net9.0\PE2.deps.json
F:\ac\POSPE2\PE2\bin\Debug\net9.0\PE2.runtimeconfig.json
F:\ac\POSPE2\PE2\bin\Debug\net9.0\PE2.dll
F:\ac\POSPE2\PE2\bin\Debug\net9.0\PE2.pdb
F:\ac\POSPE2\PE2\bin\Debug\net9.0\Microsoft.AspNetCore.OpenApi.dll
F:\ac\POSPE2\PE2\bin\Debug\net9.0\Microsoft.OpenApi.dll
F:\ac\POSPE2\PE2\bin\Debug\net9.0\Swashbuckle.AspNetCore.Swagger.dll
F:\ac\POSPE2\PE2\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerGen.dll
F:\ac\POSPE2\PE2\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerUI.dll
F:\ac\POSPE2\PE2\obj\Debug\net9.0\PE2.csproj.AssemblyReference.cache
F:\ac\POSPE2\PE2\obj\Debug\net9.0\PE2.GeneratedMSBuildEditorConfig.editorconfig
F:\ac\POSPE2\PE2\obj\Debug\net9.0\PE2.AssemblyInfoInputs.cache
F:\ac\POSPE2\PE2\obj\Debug\net9.0\PE2.AssemblyInfo.cs
F:\ac\POSPE2\PE2\obj\Debug\net9.0\PE2.csproj.CoreCompileInputs.cache
F:\ac\POSPE2\PE2\obj\Debug\net9.0\PE2.MvcApplicationPartsAssemblyInfo.cs
F:\ac\POSPE2\PE2\obj\Debug\net9.0\PE2.MvcApplicationPartsAssemblyInfo.cache
F:\ac\POSPE2\PE2\obj\Debug\net9.0\scopedcss\bundle\PE2.styles.css
F:\ac\POSPE2\PE2\obj\Debug\net9.0\staticwebassets.build.json
F:\ac\POSPE2\PE2\obj\Debug\net9.0\staticwebassets.development.json
F:\ac\POSPE2\PE2\obj\Debug\net9.0\staticwebassets.build.endpoints.json
F:\ac\POSPE2\PE2\obj\Debug\net9.0\staticwebassets\msbuild.PE2.Microsoft.AspNetCore.StaticWebAssets.props
F:\ac\POSPE2\PE2\obj\Debug\net9.0\staticwebassets\msbuild.PE2.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
F:\ac\POSPE2\PE2\obj\Debug\net9.0\staticwebassets\msbuild.build.PE2.props
F:\ac\POSPE2\PE2\obj\Debug\net9.0\staticwebassets\msbuild.buildMultiTargeting.PE2.props
F:\ac\POSPE2\PE2\obj\Debug\net9.0\staticwebassets\msbuild.buildTransitive.PE2.props
F:\ac\POSPE2\PE2\obj\Debug\net9.0\staticwebassets.pack.json
F:\ac\POSPE2\PE2\obj\Debug\net9.0\PE2.csproj.Up2Date
F:\ac\POSPE2\PE2\obj\Debug\net9.0\PE2.dll
F:\ac\POSPE2\PE2\obj\Debug\net9.0\refint\PE2.dll
F:\ac\POSPE2\PE2\obj\Debug\net9.0\PE2.pdb
F:\ac\POSPE2\PE2\obj\Debug\net9.0\PE2.genruntimeconfig.cache
F:\ac\POSPE2\PE2\obj\Debug\net9.0\ref\PE2.dll
F:\ac\POSPE2\PE2\bin\Debug\net9.0\Configuration\promotion-rules-with-exclusivity.json
F:\ac\POSPE2\PE2\bin\Debug\net9.0\Configuration\buy-get-gift-rules.json
F:\ac\POSPE2\PE2\bin\Debug\net9.0\Configuration\gift-selection-strategy-rules.json
F:\ac\POSPE2\PE2\bin\Debug\net9.0\PE2.staticwebassets.runtime.json
F:\ac\POSPE2\PE2\obj\Debug\net9.0\compressed\6aazo58n67-5ipweew5fc.gz
F:\ac\POSPE2\PE2\obj\Debug\net9.0\compressed\w83e256bdn-a18q5nlcb0.gz
F:\ac\POSPE2\PE2\bin\Debug\net9.0\Configuration\back.json
F:\ac\POSPE2\PE2\obj\Debug\net9.0\staticwebassets.upToDateCheck.txt
