namespace PE2.PromotionEngine.Models;

/// <summary>
/// 买赠类型
/// </summary>
public enum BuyGetGiftType
{
    /// <summary>
    /// 统一送赠品：满X件或X元，赠送某类商品Z件
    /// </summary>
    UnifiedGift = 0,

    /// <summary>
    /// 梯度送赠品：针对某一类商品，满第一梯度送A商品，满第二梯度送B商品
    /// </summary>
    GradientGift = 1,

    /// <summary>
    /// 组合送赠品：针对某些组合商品，必须购买A+B+C等商品且满足数量或金额条件才能赠送
    /// </summary>
    CombinationGift = 2
}

/// <summary>
/// 梯度赠送策略
/// </summary>
public enum GradientGiftStrategy
{
    /// <summary>
    /// 按梯度送：只送达到的最高梯度对应的赠品
    /// </summary>
    ByGradient = 0,

    /// <summary>
    /// 全部送：送所有达到梯度的赠品
    /// </summary>
    SendAll = 1
}

/// <summary>
/// 赠品选择策略
/// </summary>
public enum GiftSelectionStrategy
{
    /// <summary>
    /// 客户利益最大化：选择价值最高的商品作为赠品
    /// </summary>
    CustomerBenefit = 0,

    /// <summary>
    /// 商家利益最大化：选择价值最低的商品作为赠品
    /// </summary>
    MerchantBenefit = 1
}

/// <summary>
/// 购买条件
/// </summary>
public class BuyCondition
{
    /// <summary>
    /// 商品ID列表
    /// </summary>
    public List<string> ProductIds { get; set; } = new();

    /// <summary>
    /// 需要购买的数量
    /// </summary>
    public int RequiredQuantity { get; set; }
}

/// <summary>
/// 赠品条件
/// </summary>
public class GiftCondition
{
    /// <summary>
    /// 赠品商品ID列表
    /// </summary>
    public List<string> ProductIds { get; set; } = new();

    /// <summary>
    /// 赠送数量
    /// </summary>
    public int GiftQuantity { get; set; }
}

/// <summary>
/// 组合购买条件
/// </summary>
public class CombinationCondition
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 需要购买的数量
    /// </summary>
    public int RequiredQuantity { get; set; }

    /// <summary>
    /// 最小金额要求（可选）
    /// </summary>
    public decimal MinAmount { get; set; } = 0;
}

/// <summary>
/// 梯度赠品条件
/// </summary>
public class GradientGiftCondition
{
    /// <summary>
    /// 梯度级别（数字越大级别越高）
    /// </summary>
    public int GradientLevel { get; set; }

    /// <summary>
    /// 触发条件：需要购买的数量
    /// </summary>
    public int RequiredQuantity { get; set; }

    /// <summary>
    /// 触发条件：需要购买的金额（可选）
    /// </summary>
    public decimal RequiredAmount { get; set; } = 0;

    /// <summary>
    /// 赠品商品ID列表
    /// </summary>
    public List<string> GiftProductIds { get; set; } = new();

    /// <summary>
    /// 赠送数量
    /// </summary>
    public int GiftQuantity { get; set; }

    /// <summary>
    /// 梯度描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}
