using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Core;

/// <summary>
/// 新架构促销规则接口
/// 定义促销规则的核心行为契约
/// </summary>
public interface INewPromotionRule : IDisposable
{
    #region 基本属性

    /// <summary>
    /// 规则ID
    /// </summary>
    string Id { get; }

    /// <summary>
    /// 规则名称
    /// </summary>
    string Name { get; }

    /// <summary>
    /// 规则描述
    /// </summary>
    string Description { get; }

    /// <summary>
    /// 规则类型
    /// </summary>
    string RuleType { get; }

    /// <summary>
    /// 优先级
    /// </summary>
    int Priority { get; }

    /// <summary>
    /// 是否启用
    /// </summary>
    bool IsEnabled { get; }

    /// <summary>
    /// 是否可重复应用
    /// </summary>
    bool IsRepeatable { get; }

    /// <summary>
    /// 最大应用次数
    /// </summary>
    int MaxApplications { get; }

    /// <summary>
    /// 是否可与其他促销叠加
    /// </summary>
    bool CanStackWithOthers { get; }

    /// <summary>
    /// 排斥的规则ID列表
    /// </summary>
    List<string> ExclusiveRuleIds { get; }

    #endregion

    #region 核心方法

    /// <summary>
    /// 检查规则是否在有效期内
    /// </summary>
    /// <param name="checkTime">检查时间</param>
    /// <returns>是否在有效期内</returns>
    bool IsInValidPeriod(DateTime checkTime);

    /// <summary>
    /// 异步检查规则是否适用于指定购物车
    /// </summary>
    /// <param name="cart">购物车</param>
    /// <param name="checkTime">检查时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否适用</returns>
    Task<bool> IsApplicableAsync(ShoppingCart cart, DateTime checkTime, CancellationToken cancellationToken = default);

    /// <summary>
    /// 异步计算可应用的最大次数
    /// </summary>
    /// <param name="cart">购物车</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>最大应用次数</returns>
    Task<int> CalculateMaxApplicationsAsync(ShoppingCart cart, CancellationToken cancellationToken = default);

    /// <summary>
    /// 异步应用促销规则
    /// </summary>
    /// <param name="cart">处理后的购物车</param>
    /// <param name="applicationCount">应用次数</param>
    /// <param name="traceId">追踪ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>应用的促销结果</returns>
    Task<AppliedPromotion> ApplyPromotionAsync(
        ProcessedCart cart, 
        int applicationCount = 1, 
        string? traceId = null,
        CancellationToken cancellationToken = default);

    #endregion
}

/// <summary>
/// 促销规则工厂接口
/// 用于创建和管理促销规则实例
/// </summary>
public interface IPromotionRuleFactory
{
    /// <summary>
    /// 创建促销规则实例
    /// </summary>
    /// <param name="ruleType">规则类型</param>
    /// <param name="configuration">规则配置</param>
    /// <returns>促销规则实例</returns>
    INewPromotionRule CreateRule(string ruleType, object configuration);

    /// <summary>
    /// 获取支持的规则类型列表
    /// </summary>
    /// <returns>规则类型列表</returns>
    IEnumerable<string> GetSupportedRuleTypes();

    /// <summary>
    /// 验证规则配置
    /// </summary>
    /// <param name="ruleType">规则类型</param>
    /// <param name="configuration">规则配置</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateConfiguration(string ruleType, object configuration);
}

/// <summary>
/// 配置验证结果
/// </summary>
public sealed class ValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; init; }

    /// <summary>
    /// 错误消息列表
    /// </summary>
    public List<string> Errors { get; init; } = [];

    /// <summary>
    /// 警告消息列表
    /// </summary>
    public List<string> Warnings { get; init; } = [];

    /// <summary>
    /// 创建成功的验证结果
    /// </summary>
    public static ValidationResult Success() => new() { IsValid = true };

    /// <summary>
    /// 创建失败的验证结果
    /// </summary>
    public static ValidationResult Failure(params string[] errors) => new()
    {
        IsValid = false,
        Errors = errors.ToList()
    };

    /// <summary>
    /// 创建带警告的成功验证结果
    /// </summary>
    public static ValidationResult SuccessWithWarnings(params string[] warnings) => new()
    {
        IsValid = true,
        Warnings = warnings.ToList()
    };
}

/// <summary>
/// 促销规则性能指标
/// </summary>
public sealed class RulePerformanceMetrics
{
    /// <summary>
    /// 规则ID
    /// </summary>
    public required string RuleId { get; init; }

    /// <summary>
    /// 规则类型
    /// </summary>
    public required string RuleType { get; init; }

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTimeMs { get; set; }

    /// <summary>
    /// 最大执行时间（毫秒）
    /// </summary>
    public long MaxExecutionTimeMs { get; set; }

    /// <summary>
    /// 最小执行时间（毫秒）
    /// </summary>
    public long MinExecutionTimeMs { get; set; }

    /// <summary>
    /// 执行次数
    /// </summary>
    public long ExecutionCount { get; set; }

    /// <summary>
    /// 成功次数
    /// </summary>
    public long SuccessCount { get; set; }

    /// <summary>
    /// 失败次数
    /// </summary>
    public long FailureCount { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => ExecutionCount > 0 ? (double)SuccessCount / ExecutionCount : 0;

    /// <summary>
    /// 最后执行时间
    /// </summary>
    public DateTime? LastExecutionTime { get; set; }

    /// <summary>
    /// 内存使用峰值（字节）
    /// </summary>
    public long PeakMemoryUsageBytes { get; set; }
}

/// <summary>
/// 促销规则上下文
/// 提供规则执行时的上下文信息
/// </summary>
public sealed class PromotionRuleContext
{
    /// <summary>
    /// 追踪ID
    /// </summary>
    public string? TraceId { get; init; }

    /// <summary>
    /// 客户ID
    /// </summary>
    public string? CustomerId { get; init; }

    /// <summary>
    /// 会话ID
    /// </summary>
    public string? SessionId { get; init; }

    /// <summary>
    /// 渠道信息
    /// </summary>
    public string? Channel { get; init; }

    /// <summary>
    /// 执行时间
    /// </summary>
    public DateTime ExecutionTime { get; init; } = DateTime.UtcNow;

    /// <summary>
    /// 额外属性
    /// </summary>
    public Dictionary<string, object> Properties { get; init; } = new();

    /// <summary>
    /// 是否为测试模式
    /// </summary>
    public bool IsTestMode { get; init; }

    /// <summary>
    /// 性能监控是否启用
    /// </summary>
    public bool EnablePerformanceMonitoring { get; init; } = true;
}
