using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Core;
using PE2.PromotionEngine.Observability;
using PE2.PromotionEngine.Conditions;
using PE2.PromotionEngine.Inventory;
using PE2.PromotionEngine.Performance;
using PE2.PromotionEngine.Allocation;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Rules.ExchangeRules;

/// <summary>
/// 新架构组合折扣换购规则
/// 某A类商品满X件或X元并且某B类商品满Y件或Y元，可Y折换购一件C类商品
/// 集成新架构的所有基础设施组件，支持.NET 9.0语法和异步模式
/// </summary>
public sealed class NewCombinationDiscountExchangeRule : NewExchangeRuleBase, INewPromotionRule
{
    /// <summary>
    /// 构造函数
    /// </summary>
    public NewCombinationDiscountExchangeRule(
        ILogger<NewCombinationDiscountExchangeRule> logger,
        IObservabilityEngine observability,
        IConditionEngine conditionEngine,
        IInventoryManager inventoryManager,
        IPerformanceOptimizer performanceOptimizer,
        IAllocationEngine allocationEngine,
        CombinationDiscountExchangeConfiguration configuration)
        : base(logger, observability, conditionEngine, inventoryManager, performanceOptimizer, allocationEngine)
    {
        ArgumentNullException.ThrowIfNull(configuration);
        
        Id = configuration.Id;
        Name = configuration.Name;
        Description = configuration.Description;
        Priority = configuration.Priority;
        IsEnabled = configuration.IsEnabled;
        StartTime = configuration.StartTime;
        EndTime = configuration.EndTime;
        IsRepeatable = configuration.IsRepeatable;
        MaxApplications = configuration.MaxApplications;
        ApplicableCustomerTypes = configuration.ApplicableCustomerTypes;
        ExclusiveRuleIds = configuration.ExclusiveRuleIds;
        CanStackWithOthers = configuration.CanStackWithOthers;
        ProductExclusivity = configuration.ProductExclusivity;
        Strategy = configuration.Strategy;
        BenefitSelection = configuration.BenefitSelection;
        BuyCondition = configuration.BuyCondition;
        ExchangeCondition = configuration.ExchangeCondition;
        CombinationConditions = configuration.CombinationConditions;
        ExchangeConditions = configuration.ExchangeConditions;
    }

    /// <summary>
    /// 规则类型
    /// </summary>
    public override string RuleType => "CombinationDiscountExchange";

    /// <summary>
    /// 组合购买条件列表
    /// </summary>
    public List<CombinationBuyCondition> CombinationConditions { get; init; } = [];

    /// <summary>
    /// 折扣换购条件列表
    /// </summary>
    public List<DiscountExchangeCondition> ExchangeConditions { get; init; } = [];

    /// <summary>
    /// 异步检查促销条件是否满足
    /// </summary>
    protected override async Task<bool> CheckConditionsAsync(ShoppingCart cart, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查组合购买条件
            if (!await CheckCombinationConditionsAsync(cart, cancellationToken).ConfigureAwait(false))
                return false;

            // 验证换购商品是否在购物车中
            var allExchangeProductIds = ExchangeConditions
                .SelectMany(c => c.ExchangeProductIds)
                .Distinct()
                .ToList();

            return await ValidateExchangeProductsInCartAsync(cart, allExchangeProductIds, cancellationToken).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "检查组合折扣换购条件时发生异常: {RuleId}", Id);
            return false;
        }
    }

    /// <summary>
    /// 异步计算可应用的最大次数
    /// </summary>
    public override async Task<int> CalculateMaxApplicationsAsync(ShoppingCart cart, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!await CheckConditionsAsync(cart, cancellationToken).ConfigureAwait(false))
                return 0;

            // 使用性能优化器计算最大应用次数
            var optimizationContext = new OptimizationContext
            {
                Cart = cart,
                Rules = [this],
                Target = OptimizationTarget.MaximizeApplications
            };

            var result = await PerformanceOptimizer.OptimizeRuleCombinationAsync(optimizationContext, cancellationToken).ConfigureAwait(false);
            
            // 计算基于组合条件的最大应用次数
            var maxByConditions = await CalculateMaxApplicationsByCombinationConditionsAsync(cart, cancellationToken).ConfigureAwait(false);

            // 计算基于换购商品的最大应用次数
            var maxByExchangeProducts = await CalculateMaxApplicationsByExchangeProductsAsync(cart, cancellationToken).ConfigureAwait(false);

            // 取两者最小值，确保换购逻辑正确
            var maxApplications = Math.Min(maxByConditions, maxByExchangeProducts);

            return ApplyApplicationLimits(maxApplications);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "计算组合折扣换购最大应用次数时发生异常: {RuleId}", Id);
            return 0;
        }
    }

    /// <summary>
    /// 异步应用促销规则
    /// </summary>
    public override async Task<AppliedPromotion> ApplyPromotionAsync(
        ProcessedCart cart, 
        int applicationCount = 1, 
        string? traceId = null,
        CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        
        try
        {
            // 记录开始追踪
            if (!string.IsNullOrEmpty(traceId))
            {
                Observability.TrackCalculationStep(traceId, new CalculationStep
                {
                    Id = Guid.NewGuid().ToString("N"),
                    TraceId = traceId,
                    StepType = StepType.PromotionApplication,
                    Timestamp = startTime,
                    Description = $"开始应用组合折扣换购规则: {Name}",
                    RuleId = Id,
                    Data = new Dictionary<string, object>
                    {
                        ["applicationCount"] = applicationCount,
                        ["cartItemCount"] = cart.Items.Count,
                        ["combinationConditionsCount"] = CombinationConditions.Count
                    }
                });
            }

            // 预留库存
            var reservationId = await ReserveInventoryAsync(cart, applicationCount, cancellationToken).ConfigureAwait(false);

            try
            {
                // 应用组合折扣换购逻辑
                var (discountAmount, consumedItems, exchangeItems) = await ApplyCombinationDiscountExchangeAsync(
                    cart, applicationCount, traceId, cancellationToken).ConfigureAwait(false);

                var appliedPromotion = CreateAppliedPromotion(discountAmount, applicationCount, consumedItems, exchangeItems);

                // 记录促销应用追踪
                TrackPromotionApplication(traceId, appliedPromotion);

                // 使用分配引擎进行精确的折扣分配
                if (discountAmount > 0)
                {
                    var allocationRequest = new AllocationRequest
                    {
                        TotalDiscount = discountAmount,
                        Items = cart.Items.Where(item => exchangeItems.Any(ei => ei.ProductId == item.ProductId)).ToList(),
                        Strategy = AllocationStrategy.ProportionalByValue,
                        RoundingStrategy = RoundingStrategy.RoundToNearest
                    };

                    var allocationResult = await AllocationEngine.AllocateDiscountAsync(allocationRequest, cancellationToken).ConfigureAwait(false);
                    
                    // 应用分配结果到购物车
                    ApplyAllocationToCart(cart, allocationResult);
                }

                return appliedPromotion;
            }
            finally
            {
                // 释放库存预留
                if (!string.IsNullOrEmpty(reservationId))
                {
                    await InventoryManager.ReleaseReservationAsync(reservationId, cancellationToken).ConfigureAwait(false);
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "应用组合折扣换购规则时发生异常: {RuleId}", Id);
            
            return CreateAppliedPromotion(0, 0, [], []);
        }
        finally
        {
            // 记录性能指标
            var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
            Logger.LogDebug("组合折扣换购规则执行完成: {RuleId}, 耗时: {ExecutionTime}ms", Id, executionTime);
        }
    }

    /// <summary>
    /// 检查组合购买条件
    /// </summary>
    private async Task<bool> CheckCombinationConditionsAsync(ShoppingCart cart, CancellationToken cancellationToken)
    {
        try
        {
            foreach (var condition in CombinationConditions)
            {
                var conditionResult = await ConditionEngine.ValidateConditionAsync(condition, cart, cancellationToken).ConfigureAwait(false);
                if (!conditionResult.IsValid)
                    return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "检查组合购买条件时发生异常: {RuleId}", Id);
            return false;
        }
    }

    /// <summary>
    /// 根据组合条件计算最大应用次数
    /// </summary>
    private async Task<int> CalculateMaxApplicationsByCombinationConditionsAsync(ShoppingCart cart, CancellationToken cancellationToken)
    {
        try
        {
            var maxApplications = int.MaxValue;

            foreach (var condition in CombinationConditions)
            {
                var conditionMaxApplications = await CalculateMaxApplicationsByConditionAsync(cart, condition, cancellationToken).ConfigureAwait(false);
                maxApplications = Math.Min(maxApplications, conditionMaxApplications);
            }

            return maxApplications == int.MaxValue ? 1 : maxApplications;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "计算组合条件最大应用次数时发生异常: {RuleId}", Id);
            return 0;
        }
    }

    /// <summary>
    /// 计算单个条件的最大应用次数
    /// </summary>
    private async Task<int> CalculateMaxApplicationsByConditionAsync(ShoppingCart cart, CombinationBuyCondition condition, CancellationToken cancellationToken)
    {
        try
        {
            var availableQuantity = condition.ProductId.Sum(id => cart.GetAvailableProductQuantity(id));
            var totalAmount = condition.ProductId.Sum(id =>
                cart.Items.Where(x => x.ProductId == id).Sum(x => x.SubTotal));

            // 按数量要求计算
            if (condition.RequiredQuantity > 0)
            {
                return IsRepeatable
                    ? availableQuantity / condition.RequiredQuantity
                    : (availableQuantity >= condition.RequiredQuantity ? 1 : 0);
            }

            // 按金额要求计算
            if (condition.RequiredAmount > 0)
            {
                return IsRepeatable
                    ? (int)(totalAmount / condition.RequiredAmount)
                    : (totalAmount >= condition.RequiredAmount ? 1 : 0);
            }

            // 如果既没有数量要求也没有金额要求，认为总是满足
            return IsRepeatable ? int.MaxValue : 1;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "计算单个条件最大应用次数时发生异常: {RuleId}", Id);
            return 0;
        }
    }

    /// <summary>
    /// 根据换购商品可用性计算最大应用次数
    /// </summary>
    private async Task<int> CalculateMaxApplicationsByExchangeProductsAsync(ShoppingCart cart, CancellationToken cancellationToken)
    {
        try
        {
            var applicableExchanges = GetApplicableExchangeConditions();
            if (!applicableExchanges.Any())
                return 0;

            var maxApplications = int.MaxValue;

            foreach (var exchange in applicableExchanges)
            {
                var totalAvailableQuantity = 0;

                foreach (var productId in exchange.ExchangeProductIds)
                {
                    var available = await InventoryManager.GetAvailableQuantityAsync(productId, cancellationToken).ConfigureAwait(false);
                    totalAvailableQuantity += available;
                }

                var requiredQuantity = Math.Max(exchange.ExchangeQuantity, 1);
                var maxByThisExchange = totalAvailableQuantity / requiredQuantity;

                maxApplications = Math.Min(maxApplications, maxByThisExchange);
            }

            return maxApplications == int.MaxValue ? 0 : maxApplications;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "计算换购商品最大应用次数时发生异常: {RuleId}", Id);
            return 0;
        }
    }

    /// <summary>
    /// 获取适用的换购条件
    /// </summary>
    private List<DiscountExchangeCondition> GetApplicableExchangeConditions()
    {
        return ExchangeConditions.Where(c => c.IsEnabled).ToList();
    }

    /// <summary>
    /// 应用应用次数限制
    /// </summary>
    private int ApplyApplicationLimits(int maxApplications)
    {
        if (!IsRepeatable)
        {
            maxApplications = Math.Min(maxApplications, 1);
        }
        else if (MaxApplications > 0)
        {
            maxApplications = Math.Min(maxApplications, MaxApplications);
        }

        return maxApplications;
    }

    /// <summary>
    /// 验证换购商品是否在购物车中
    /// </summary>
    private async Task<bool> ValidateExchangeProductsInCartAsync(
        ShoppingCart cart,
        List<string> exchangeProductIds,
        CancellationToken cancellationToken)
    {
        try
        {
            foreach (var productId in exchangeProductIds)
            {
                var hasProduct = cart.Items.Any(item => item.ProductId == productId);
                if (!hasProduct)
                    return false;

                // 验证库存可用性
                var available = await InventoryManager.GetAvailableQuantityAsync(productId, cancellationToken).ConfigureAwait(false);
                if (available <= 0)
                    return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "验证换购商品时发生异常: {RuleId}", Id);
            return false;
        }
    }

    /// <summary>
    /// 预留库存
    /// </summary>
    private async Task<string?> ReserveInventoryAsync(ProcessedCart cart, int applicationCount, CancellationToken cancellationToken)
    {
        try
        {
            var requiredProducts = new List<(string ProductId, int Quantity)>();

            // 计算需要预留的商品数量
            foreach (var exchange in GetApplicableExchangeConditions())
            {
                foreach (var productId in exchange.ExchangeProductIds)
                {
                    var requiredQuantity = exchange.ExchangeQuantity * applicationCount;
                    requiredProducts.Add((productId, requiredQuantity));
                }
            }

            if (!requiredProducts.Any())
                return null;

            var reservationRequest = new ReservationRequest
            {
                ReservationId = Guid.NewGuid().ToString("N"),
                ProductQuantities = requiredProducts.ToDictionary(p => p.ProductId, p => p.Quantity),
                Priority = ReservationPriority.High,
                ExpirationTime = DateTime.UtcNow.AddMinutes(5),
                Source = $"Rule_{Id}"
            };

            var result = await InventoryManager.ReserveProductsAsync(reservationRequest, cancellationToken).ConfigureAwait(false);
            return result.IsSuccessful ? reservationRequest.ReservationId : null;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "预留库存时发生异常: {RuleId}", Id);
            return null;
        }
    }

    /// <summary>
    /// 应用组合折扣换购逻辑
    /// </summary>
    private async Task<(decimal totalDiscount, List<ConsumedItem> consumed, List<GiftItem> exchanged)> ApplyCombinationDiscountExchangeAsync(
        ProcessedCart cart,
        int applicationCount,
        string? traceId,
        CancellationToken cancellationToken)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var exchangeItems = new List<GiftItem>();

        try
        {
            for (int application = 0; application < applicationCount; application++)
            {
                var (success, discount, exchanges) = await TryApplySingleCombinationDiscountExchangeAsync(
                    cart, consumedItems, application + 1, traceId, cancellationToken).ConfigureAwait(false);

                if (!success)
                    break; // 无法继续应用，停止

                totalDiscountAmount += discount;
                exchangeItems.AddRange(exchanges);
            }

            return (totalDiscountAmount, consumedItems, exchangeItems);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "应用组合折扣换购逻辑时发生异常: {RuleId}", Id);
            return (0, [], []);
        }
    }

    /// <summary>
    /// 尝试应用单次组合折扣换购
    /// </summary>
    private async Task<(bool success, decimal discount, List<GiftItem> exchanges)> TryApplySingleCombinationDiscountExchangeAsync(
        ProcessedCart cart,
        List<ConsumedItem> consumedItems,
        int applicationIndex,
        string? traceId,
        CancellationToken cancellationToken)
    {
        try
        {
            // 验证组合购买条件
            if (!await CheckCombinationConditionsAsync(cart, cancellationToken).ConfigureAwait(false))
                return (false, 0, []);

            // 消耗组合条件商品
            var consumedInThisApplication = await ConsumeCombinationConditionProductsAsync(cart, cancellationToken).ConfigureAwait(false);
            consumedItems.AddRange(consumedInThisApplication);

            // 应用折扣换购逻辑
            var (discountAmount, exchangeItems) = await ApplyDiscountExchangeLogicAsync(cart, traceId, cancellationToken).ConfigureAwait(false);

            return (discountAmount > 0, discountAmount, exchangeItems);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "尝试应用单次组合折扣换购时发生异常: {RuleId}, ApplicationIndex: {ApplicationIndex}", Id, applicationIndex);
            return (false, 0, []);
        }
    }

    /// <summary>
    /// 消耗组合条件商品
    /// </summary>
    private async Task<List<ConsumedItem>> ConsumeCombinationConditionProductsAsync(ProcessedCart cart, CancellationToken cancellationToken)
    {
        var consumedItems = new List<ConsumedItem>();

        try
        {
            foreach (var condition in CombinationConditions)
            {
                var consumed = ConsumeSingleConditionProducts(cart, condition);
                consumedItems.AddRange(consumed);
            }

            return consumedItems;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "消耗组合条件商品时发生异常: {RuleId}", Id);
            return [];
        }
    }

    /// <summary>
    /// 消耗单个条件的商品
    /// </summary>
    private List<ConsumedItem> ConsumeSingleConditionProducts(ProcessedCart cart, CombinationBuyCondition condition)
    {
        var consumedItems = new List<ConsumedItem>();

        if (condition.RequiredQuantity > 0)
        {
            // 按数量消耗
            var remainingQuantity = condition.RequiredQuantity;
            foreach (var productId in condition.ProductId)
            {
                if (remainingQuantity <= 0) break;

                var cartItem = cart.Items.FirstOrDefault(item => item.ProductId == productId && item.Quantity > 0);
                if (cartItem != null)
                {
                    var consumeQuantity = Math.Min(cartItem.Quantity, remainingQuantity);

                    consumedItems.Add(new ConsumedItem
                    {
                        ProductId = productId,
                        ProductName = cartItem.ProductName,
                        Quantity = consumeQuantity,
                        UnitPrice = cartItem.OriginalUnitPrice
                    });

                    cartItem.Quantity -= consumeQuantity;
                    remainingQuantity -= consumeQuantity;
                }
            }
        }
        else if (condition.RequiredAmount > 0)
        {
            // 按金额消耗
            var remainingAmount = condition.RequiredAmount;
            foreach (var productId in condition.ProductId)
            {
                if (remainingAmount <= 0) break;

                var cartItem = cart.Items.FirstOrDefault(item => item.ProductId == productId && item.Quantity > 0);
                if (cartItem != null)
                {
                    var maxAffordableQuantity = (int)Math.Floor(remainingAmount / cartItem.OriginalUnitPrice);
                    var consumeQuantity = Math.Min(cartItem.Quantity, maxAffordableQuantity);

                    if (consumeQuantity > 0)
                    {
                        consumedItems.Add(new ConsumedItem
                        {
                            ProductId = productId,
                            ProductName = cartItem.ProductName,
                            Quantity = consumeQuantity,
                            UnitPrice = cartItem.OriginalUnitPrice
                        });

                        cartItem.Quantity -= consumeQuantity;
                        remainingAmount -= consumeQuantity * cartItem.OriginalUnitPrice;
                    }
                }
            }
        }

        return consumedItems;
    }

    /// <summary>
    /// 应用折扣换购逻辑
    /// </summary>
    private async Task<(decimal discountAmount, List<GiftItem> exchangeItems)> ApplyDiscountExchangeLogicAsync(
        ProcessedCart cart,
        string? traceId,
        CancellationToken cancellationToken)
    {
        var totalDiscountAmount = 0m;
        var exchangeItems = new List<GiftItem>();

        try
        {
            var applicableExchanges = GetApplicableExchangeConditions();

            foreach (var exchange in applicableExchanges)
            {
                var (discount, items) = await ProcessSingleDiscountExchangeConditionAsync(cart, exchange, traceId, cancellationToken).ConfigureAwait(false);
                totalDiscountAmount += discount;
                exchangeItems.AddRange(items);
            }

            return (totalDiscountAmount, exchangeItems);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "应用折扣换购逻辑时发生异常: {RuleId}", Id);
            return (0, []);
        }
    }

    /// <summary>
    /// 处理单个折扣换购条件
    /// </summary>
    private async Task<(decimal discountAmount, List<GiftItem> exchangeItems)> ProcessSingleDiscountExchangeConditionAsync(
        ProcessedCart cart,
        DiscountExchangeCondition exchange,
        string? traceId,
        CancellationToken cancellationToken)
    {
        var discountAmount = 0m;
        var exchangeItems = new List<GiftItem>();

        try
        {
            foreach (var productId in exchange.ExchangeProductIds)
            {
                var cartItem = cart.Items.FirstOrDefault(item => item.ProductId == productId);
                if (cartItem == null) continue;

                var exchangeQuantity = Math.Min(cartItem.Quantity, exchange.ExchangeQuantity);
                if (exchangeQuantity <= 0) continue;

                // 计算折扣换购优惠
                var originalPrice = cartItem.OriginalUnitPrice * exchangeQuantity;
                var discountedPrice = originalPrice * (1 - exchange.DiscountRate);
                var itemDiscount = originalPrice - discountedPrice;

                if (itemDiscount > 0)
                {
                    discountAmount += itemDiscount;

                    // 更新商品实际价格
                    cartItem.ActualUnitPrice = cartItem.OriginalUnitPrice * (1 - exchange.DiscountRate);

                    exchangeItems.Add(new GiftItem
                    {
                        ProductId = productId,
                        ProductName = cartItem.ProductName,
                        Quantity = exchangeQuantity,
                        UnitPrice = cartItem.ActualUnitPrice,
                        OriginalUnitPrice = cartItem.OriginalUnitPrice
                    });
                }
            }

            return (discountAmount, exchangeItems);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "处理单个折扣换购条件时发生异常: {RuleId}", Id);
            return (0, []);
        }
    }

    /// <summary>
    /// 应用分配结果到购物车
    /// </summary>
    private static void ApplyAllocationToCart(ProcessedCart cart, AllocationResult allocationResult)
    {
        foreach (var allocation in allocationResult.Allocations)
        {
            var cartItem = cart.Items.FirstOrDefault(item => item.ProductId == allocation.ProductId);
            if (cartItem != null)
            {
                cartItem.ActualUnitPrice = Math.Max(0, cartItem.ActualUnitPrice - allocation.AllocatedDiscount / cartItem.Quantity);
            }
        }
    }
}

/// <summary>
/// 组合折扣换购规则配置
/// </summary>
public sealed class CombinationDiscountExchangeConfiguration
{
    public required string Id { get; init; }
    public required string Name { get; init; }
    public string Description { get; init; } = string.Empty;
    public int Priority { get; init; } = 0;
    public bool IsEnabled { get; init; } = true;
    public DateTime? StartTime { get; init; }
    public DateTime? EndTime { get; init; }
    public bool IsRepeatable { get; init; } = true;
    public int MaxApplications { get; init; } = 1;
    public List<string> ApplicableCustomerTypes { get; init; } = [];
    public List<string> ExclusiveRuleIds { get; init; } = [];
    public bool CanStackWithOthers { get; init; } = true;
    public ProductExclusivityLevel ProductExclusivity { get; init; } = ProductExclusivityLevel.None;
    public ExchangeStrategy Strategy { get; init; } = ExchangeStrategy.Discount;
    public BenefitSelectionStrategy BenefitSelection { get; init; } = BenefitSelectionStrategy.CustomerBenefit;
    public required ExchangeBuyCondition BuyCondition { get; init; }
    public required ExchangeCondition ExchangeCondition { get; init; }
    public List<CombinationBuyCondition> CombinationConditions { get; init; } = [];
    public List<DiscountExchangeCondition> ExchangeConditions { get; init; } = [];
}
