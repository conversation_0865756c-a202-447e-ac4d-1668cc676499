using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.BuyFreeRules;

/// <summary>
/// 买免促销规则基类
/// 核心原则：只处理已扫码进入购物车的商品，通过调整价格实现免费效果
/// </summary>
public abstract class BaseBuyFreeRule : PromotionRuleBase
{
    /// <summary>
    /// 免费商品选择策略：客户利益最大化 vs 商家利益最大化
    /// </summary>
    public FreeItemSelectionStrategy FreeItemSelectionStrategy { get; set; } = FreeItemSelectionStrategy.CustomerBenefit;

    /// <summary>
    /// 规则类型（由子类实现）
    /// </summary>
    public abstract override string RuleType { get; }

    /// <summary>
    /// 检查促销条件是否满足（由子类实现）
    /// </summary>
    protected abstract override bool CheckConditions(ShoppingCart cart);

    /// <summary>
    /// 计算可应用的最大次数（由子类实现）
    /// </summary>
    public abstract override int CalculateMaxApplications(ShoppingCart cart);

    /// <summary>
    /// 应用促销规则（由子类实现）
    /// </summary>
    public abstract override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1);

    /// <summary>
    /// 验证买免商品是否在购物车中
    /// POS系统核心：只能对已扫码的商品进行买免处理
    /// </summary>
    protected bool ValidateBuyFreeProductsInCart(ShoppingCart cart, List<string> productIds)
    {
        foreach (var productId in productIds)
        {
            var hasProduct = cart.Items.Any(x => x.Product.Id == productId && x.Quantity > 0);
            if (!hasProduct)
            {
                return false; // 买免商品不在购物车中，条件不成立
            }
        }
        return true;
    }

    /// <summary>
    /// 根据策略选择要免费的商品
    /// </summary>
    protected List<(string ProductId, int Quantity, decimal UnitPrice)> SelectFreeProducts(
        List<(string ProductId, int Quantity, decimal UnitPrice)> candidateProducts, 
        int freeQuantity)
    {
        if (!candidateProducts.Any() || freeQuantity <= 0)
            return new List<(string, int, decimal)>();

        var freeProducts = new List<(string ProductId, int Quantity, decimal UnitPrice)>();
        var remainingFreeQuantity = freeQuantity;

        // 根据策略排序
        var sortedProducts = FreeItemSelectionStrategy switch
        {
            FreeItemSelectionStrategy.CustomerBenefit => candidateProducts.OrderByDescending(p => p.UnitPrice), // 客户利益：免费高价商品
            FreeItemSelectionStrategy.MerchantBenefit => candidateProducts.OrderBy(p => p.UnitPrice), // 商家利益：免费低价商品
            _ => candidateProducts.OrderByDescending(p => p.UnitPrice)
        };

        foreach (var product in sortedProducts)
        {
            if (remainingFreeQuantity <= 0) break;

            var freeQty = Math.Min(product.Quantity, remainingFreeQuantity);
            if (freeQty > 0)
            {
                freeProducts.Add((product.ProductId, freeQty, product.UnitPrice));
                remainingFreeQuantity -= freeQty;
            }
        }

        return freeProducts;
    }

    /// <summary>
    /// 计算买免折扣金额
    /// </summary>
    protected decimal CalculateFreeDiscount(List<(string ProductId, int Quantity, decimal UnitPrice)> freeProducts)
    {
        return freeProducts.Sum(p => p.Quantity * p.UnitPrice);
    }

    /// <summary>
    /// 应用免费效果到购物车
    /// 通过将商品的ActualUnitPrice设置为0来实现免费
    /// </summary>
    protected void ApplyFreeEffectToCart(ShoppingCart cart, List<(string ProductId, int Quantity, decimal UnitPrice)> freeProducts, AppliedPromotion promotion)
    {
        var newItemsToAdd = new List<CartItem>();

        foreach (var freeProduct in freeProducts)
        {
            var remainingFreeQuantity = freeProduct.Quantity;

            var cartItems = cart.Items.Where(x =>
                x.Product.Id == freeProduct.ProductId &&
                Math.Abs(x.UnitPrice - freeProduct.UnitPrice) < 0.01m &&
                x.Quantity > 0).ToList();

            foreach (var cartItem in cartItems)
            {
                if (remainingFreeQuantity <= 0) break;

                var freeQuantity = Math.Min(cartItem.Quantity, remainingFreeQuantity);

                if (freeQuantity == cartItem.Quantity)
                {
                    // 整个购物车项都免费
                    cartItem.ActualUnitPrice = 0;
                    AddFreePromotionDetail(cartItem, promotion, freeProduct.UnitPrice);
                }
                else
                {
                    // 部分数量免费，需要拆分购物车项
                    var newItems = SplitCartItemForFree(cartItem, freeQuantity, promotion, freeProduct.UnitPrice);
                    newItemsToAdd.AddRange(newItems);
                }

                remainingFreeQuantity -= freeQuantity;
            }
        }

        // 将新的免费商品项添加到购物车
        cart.Items.AddRange(newItemsToAdd);
    }

    /// <summary>
    /// 拆分购物车项以处理部分免费的情况
    /// </summary>
    private List<CartItem> SplitCartItemForFree(CartItem originalItem, int freeQuantity, AppliedPromotion promotion, decimal originalUnitPrice)
    {
        var newItems = new List<CartItem>();

        // 减少原购物车项的数量（保持原价）
        originalItem.Quantity -= freeQuantity;

        // 创建新的免费商品项
        var freeCartItem = new CartItem
        {
            Product = originalItem.Product,
            Quantity = freeQuantity,
            UnitPrice = originalUnitPrice,
            ActualUnitPrice = 0, // 免费
            IsGift = false
        };

        AddFreePromotionDetail(freeCartItem, promotion, originalUnitPrice);
        newItems.Add(freeCartItem);

        return newItems;
    }

    /// <summary>
    /// 为免费商品添加促销详情
    /// </summary>
    private void AddFreePromotionDetail(CartItem cartItem, AppliedPromotion promotion, decimal originalUnitPrice)
    {
        var totalDiscount = originalUnitPrice * cartItem.Quantity;
        var strategyDescription = FreeItemSelectionStrategy == FreeItemSelectionStrategy.CustomerBenefit
            ? "客户利益最大化"
            : "商家利益最大化";

        var promotionDetail = new ItemPromotionDetail
        {
            RuleId = promotion.RuleId,
            RuleName = promotion.RuleName,
            PromotionType = promotion.PromotionType,
            DiscountAmount = totalDiscount,
            Description = $"买免促销：免费{cartItem.Quantity}件，节省{totalDiscount:C}（{strategyDescription}）",
            IsGiftRelated = true // 免费商品视为赠品相关
        };

        cartItem.AddPromotionDetail(promotionDetail);
    }
}

/// <summary>
/// 免费商品选择策略枚举
/// </summary>
public enum FreeItemSelectionStrategy
{
    /// <summary>
    /// 客户利益最大化：优先免费高价值商品
    /// </summary>
    CustomerBenefit = 0,

    /// <summary>
    /// 商家利益最大化：优先免费低价值商品
    /// </summary>
    MerchantBenefit = 1
}
