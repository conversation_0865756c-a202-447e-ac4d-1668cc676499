using PE2.Models;
using PE2.PromotionEngine.Models;
using Serilog;

namespace PE2.PromotionEngine.Rules.CashDiscountRules;

/// <summary>
/// 组合减现规则 [OK]
/// 某A类商品满X件或Y元并且某B类商品满X件或Y元，立减N元
/// 场景案例：A、B商品，零售、应收价为1000元；购买A、B商品，若数量各大于1件时，则可以优惠10元钱。
/// A、B商品零售价、应收价为1000元，C商品零售价、应收价为99元；购买1件A，1件B，1件C时，应收金额为1000+1000+99-10=2089元
/// A、B商品零售价、应收价为1000元，C商品零售价、应收价为99元；购买2件A，1件B，2件C时，应收金额为1000*2+1000+99*2-10=3188元
/// A、B商品零售价、应收价为1000元，C商品零售价、应收价为99元；购买2件A，2件B，1件C，且促销设置为优惠不翻倍时，应收金额为1000*2+1000*2+99-10=4089元
/// A、B商品零售价、应收价为1000元，C商品零售价、应收价为99元；购买2件A，2件B，1件C，且促销设置为优惠翻倍时，应收金额为1000*2+1000*2+99-10*2=4079元（因为A+B的组合刚好是条件1A+1B的2倍）
///备注：翻倍为促销的设置项，是以条件商品的设置为基准，满足其n倍，则可以按照指定设置的翻倍次数，去享受多次优惠的叠加优惠。
/// </summary>
public class CombinationCashDiscountRule : BaseCashDiscountRule
{
    public override string RuleType => "CombinationCashDiscount";

    /// <summary>
    /// 组合购买条件列表
    /// </summary>
    public List<CombinationDiscountCondition> CombinationConditions { get; set; } = new();

    /// <summary>
    /// 减现金额
    /// </summary>
    public decimal DiscountAmount { get; set; }

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!CombinationConditions.Any())
            return false;

        // 验证组合商品是否在购物车中
        var allProductIds = CombinationConditions.Select(c => c.ProductId).ToList();
        if (!ValidateCashDiscountProductsInCart(cart, allProductIds))
            return false;

        // 检查组合购买条件
        return CheckCombinationConditions(cart);
    }

    /// <summary>
    /// 检查组合购买条件
    /// </summary>
    private bool CheckCombinationConditions(ShoppingCart cart)
    {
        foreach (var condition in CombinationConditions)
        {
            var availableQuantity = cart.GetAvailableProductQuantity(condition.ProductId);
            var totalAmount = cart
                .Items.Where(x => x.Product.Id == condition.ProductId)
                .Sum(x => x.SubTotal);

            // 检查数量条件（如果设置了RequiredQuantity）
            if (condition.RequiredQuantity > 0 && availableQuantity < condition.RequiredQuantity)
                return false;

            // 检查金额条件（如果设置了RequiredAmount）
            if (condition.RequiredAmount > 0 && totalAmount < condition.RequiredAmount)
                return false;

            // 如果既没有设置数量也没有设置金额条件，则认为条件不满足
            if (condition.RequiredQuantity <= 0 && condition.RequiredAmount <= 0)
                return false;
        }

        return true;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = int.MaxValue;

        // 计算每个组合条件的最大应用次数
        foreach (var condition in CombinationConditions)
        {
            var conditionMaxApplications = 0;

            if (condition.RequiredQuantity > 0)
            {
                // 按数量计算
                var availableQuantity = cart.GetAvailableProductQuantity(condition.ProductId);
                conditionMaxApplications = IsRepeatable
                    ? availableQuantity / condition.RequiredQuantity
                    : (availableQuantity >= condition.RequiredQuantity ? 1 : 0);
            }
            else if (condition.RequiredAmount > 0)
            {
                // 按金额计算
                var totalAmount = cart
                    .Items.Where(x => x.Product.Id == condition.ProductId)
                    .Sum(x => x.SubTotal);
                conditionMaxApplications = IsRepeatable
                    ? (int)(totalAmount / condition.RequiredAmount)
                    : (totalAmount >= condition.RequiredAmount ? 1 : 0);
            }
            else
            {
                conditionMaxApplications = 1;
            }

            maxApplications = Math.Min(maxApplications, conditionMaxApplications);
        }

        if (maxApplications == int.MaxValue)
            maxApplications = 1;

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyCombinationCashDiscount(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用组合减现促销";
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "应用组合减现促销失败");
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用组合减现促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyCombinationCashDiscount(
        ShoppingCart cart,
        int applicationCount
    )
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var discountRecords = new List<GiftItem>(); // 减现记录

        for (int app = 0; app < applicationCount; app++)
        {
            // 消耗组合条件商品（记录消耗但不修改购物车）
            var combinationConsumed = ConsumeCombinationProducts(cart);
            if (!combinationConsumed.Any())
                break;

            // 计算本次应用的减现金额
            var currentDiscountAmount = DiscountAmount;
            totalDiscountAmount += currentDiscountAmount;

            // 合并消耗记录
            foreach (var item in combinationConsumed)
            {
                var existingConsumed = consumedItems.FirstOrDefault(x =>
                    x.ProductId == item.ProductId
                );
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += item.Quantity;
                }
                else
                {
                    consumedItems.Add(item);
                }
            }

            // 创建减现记录
            var conditionDescription = string.Join(
                "、",
                CombinationConditions.Select(c =>
                    c.RequiredQuantity > 0
                        ? $"{c.ProductId}满{c.RequiredQuantity}件"
                        : $"{c.ProductId}满{c.RequiredAmount:C}"
                )
            );

            var description =
                $"组合减现：{conditionDescription}立减{currentDiscountAmount:C}（第{app + 1}次应用）";

            discountRecords.Add(CreateCashDiscountRecord(currentDiscountAmount, description));
        }

        // 应用减现到购物车
        var promotion = new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType
        };
        var allProductIds = CombinationConditions.Select(c => c.ProductId).ToList();
        ApplyCashDiscountToCart(cart, totalDiscountAmount, promotion, allProductIds);

        return (totalDiscountAmount, consumedItems, discountRecords);
    }

    /// <summary>
    /// 消耗组合条件商品
    /// </summary>
    private List<ConsumedItem> ConsumeCombinationProducts(ShoppingCart cart)
    {
        var consumedItems = new List<ConsumedItem>();

        foreach (var condition in CombinationConditions)
        {
            if (condition.RequiredQuantity > 0)
            {
                // 按数量消耗
                var requiredQuantity = condition.RequiredQuantity;
                var consumed = ConsumeConditionProducts(
                    cart,
                    new List<string> { condition.ProductId },
                    requiredQuantity
                );
                consumedItems.AddRange(consumed);
            }
            else if (condition.RequiredAmount > 0)
            {
                // 按金额消耗 - 消耗满足金额条件的商品
                var currentAmount = 0m;
                var targetAmount = condition.RequiredAmount;

                var availableItems = cart
                    .Items.Where(x =>
                        x.Product.Id == condition.ProductId && x.AvailableQuantity > 0
                    )
                    .OrderBy(x => x.UnitPrice) // 优先消耗价格低的商品
                    .ToList();

                foreach (var item in availableItems)
                {
                    if (currentAmount >= targetAmount)
                        break;

                    var remainingAmount = targetAmount - currentAmount;
                    var itemTotalValue = item.UnitPrice * item.AvailableQuantity;

                    int consumeQuantity;
                    if (itemTotalValue <= remainingAmount)
                    {
                        // 全部消耗
                        consumeQuantity = item.AvailableQuantity;
                    }
                    else
                    {
                        // 部分消耗，计算需要消耗多少件才能满足剩余金额
                        consumeQuantity = Math.Min(
                            (int)Math.Ceiling(remainingAmount / item.UnitPrice),
                            item.AvailableQuantity
                        );
                    }

                    if (consumeQuantity > 0)
                    {
                        // 记录消耗的商品
                        var existingConsumed = consumedItems.FirstOrDefault(x =>
                            x.ProductId == condition.ProductId
                        );
                        if (existingConsumed != null)
                        {
                            existingConsumed.Quantity += consumeQuantity;
                        }
                        else
                        {
                            consumedItems.Add(
                                new ConsumedItem
                                {
                                    ProductId = condition.ProductId,
                                    ProductName = item.Product.Name,
                                    Quantity = consumeQuantity,
                                    UnitPrice = item.UnitPrice
                                }
                            );
                        }

                        // 更新当前消耗的金额
                        currentAmount += item.UnitPrice * consumeQuantity;

                        // 从购物车中减少数量
                        //item.Quantity -= consumeQuantity;
                    }
                }

                // 检查是否满足金额条件
                if (currentAmount < targetAmount)
                {
                    // 如果没有满足金额条件，返回空列表表示失败
                    return new List<ConsumedItem>();
                }
            }
        }

        return consumedItems;
    }
}

/// <summary>
/// 组合减现条件
/// </summary>
public class CombinationDiscountCondition
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 所需数量
    /// </summary>
    public int RequiredQuantity { get; set; }

    /// <summary>
    /// 所需金额
    /// </summary>
    public decimal RequiredAmount { get; set; }
}
