### 测试改进的互斥性分析功能

### 1. 基础互斥性测试 - 验证详细的冲突分析
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "EXCLUSIVITY_ANALYSIS_TEST_001",
  "customerId": "CUSTOMER_001",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 2,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 50.00,
        "category": "服装"
      },
      "quantity": 4,
      "unitPrice": 50.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 30.00,
        "category": "家居"
      },
      "quantity": 6,
      "unitPrice": 30.00
    }
  ]
}

### 2. 高冲突场景测试 - 多个促销规则竞争同一商品
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "HIGH_CONFLICT_TEST",
  "customerId": "CUSTOMER_CONFLICT",
  "items": [
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 50.00,
        "category": "服装"
      },
      "quantity": 10,
      "unitPrice": 50.00
    }
  ]
}

### 3. 组合套餐互斥测试 - 验证组合促销的互斥性
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "BUNDLE_EXCLUSIVITY_TEST",
  "customerId": "CUSTOMER_BUNDLE",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 50.00,
        "category": "电子产品"
      },
      "quantity": 2,
      "unitPrice": 50.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 30.00,
        "category": "服装"
      },
      "quantity": 3,
      "unitPrice": 30.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 20.00,
        "category": "家居"
      },
      "quantity": 4,
      "unitPrice": 20.00
    }
  ]
}

### 4. VIP客户互斥测试 - 验证客户类型限制的互斥性
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "VIP_EXCLUSIVITY_TEST",
  "customerId": "VIP_CUSTOMER_001",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 3,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 50.00,
        "category": "服装"
      },
      "quantity": 2,
      "unitPrice": 50.00
    }
  ]
}

### 5. 分类促销互斥测试 - 验证分类促销的互斥性
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "CATEGORY_EXCLUSIVITY_TEST",
  "customerId": "CUSTOMER_CATEGORY",
  "items": [
    {
      "product": {
        "id": "B",
        "name": "时尚T恤",
        "price": 80.00,
        "category": "服装"
      },
      "quantity": 4,
      "unitPrice": 80.00
    },
    {
      "product": {
        "id": "D",
        "name": "运动鞋",
        "price": 200.00,
        "category": "服装"
      },
      "quantity": 2,
      "unitPrice": 200.00
    },
    {
      "product": {
        "id": "E",
        "name": "牛仔裤",
        "price": 150.00,
        "category": "服装"
      },
      "quantity": 3,
      "unitPrice": 150.00
    }
  ]
}

### 6. 高价值订单测试 - 验证总额折扣与其他促销的互斥性
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "HIGH_VALUE_EXCLUSIVITY_TEST",
  "customerId": "CUSTOMER_HIGH_VALUE",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 200.00,
        "category": "电子产品"
      },
      "quantity": 3,
      "unitPrice": 200.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 100.00,
        "category": "服装"
      },
      "quantity": 5,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 50.00,
        "category": "家居"
      },
      "quantity": 4,
      "unitPrice": 50.00
    }
  ]
}

### 7. 边界条件测试 - 最小数量商品的互斥性
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "BOUNDARY_EXCLUSIVITY_TEST",
  "customerId": "CUSTOMER_BOUNDARY",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 1,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 50.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 50.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 30.00,
        "category": "家居"
      },
      "quantity": 1,
      "unitPrice": 30.00
    }
  ]
}

### 8. 复杂叠加测试 - 验证多层次互斥性控制
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "COMPLEX_STACKING_TEST",
  "customerId": "CUSTOMER_COMPLEX",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 150.00,
        "category": "电子产品"
      },
      "quantity": 4,
      "unitPrice": 150.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 80.00,
        "category": "服装"
      },
      "quantity": 6,
      "unitPrice": 80.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 40.00,
        "category": "家居"
      },
      "quantity": 8,
      "unitPrice": 40.00
    }
  ]
}

### 9. 获取互斥性配置信息
GET http://localhost:5213/api/exclusivitytest/exclusivity-config

### 10. 比较有无互斥性限制的差异
POST http://localhost:5213/api/exclusivitytest/compare-with-without-exclusivity
Content-Type: application/json

{
  "id": "EXCLUSIVITY_COMPARE_DETAILED",
  "customerId": "CUSTOMER_COMPARE_DETAILED",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 2,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 50.00,
        "category": "服装"
      },
      "quantity": 4,
      "unitPrice": 50.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 30.00,
        "category": "家居"
      },
      "quantity": 6,
      "unitPrice": 30.00
    }
  ]
}

### 11. 测试互斥性功能总览
POST http://localhost:5213/api/exclusivitytest/test-exclusivity

### 12. 促销方案比较 - 验证互斥性对方案选择的影响
POST http://localhost:5213/api/promotionanalysis/compare-scenarios
Content-Type: application/json

{
  "cart": {
    "id": "SCENARIO_COMPARE_TEST",
    "customerId": "CUSTOMER_SCENARIO",
    "items": [
      {
        "product": {
          "id": "A",
          "name": "商品A",
          "price": 100.00,
          "category": "电子产品"
        },
        "quantity": 2,
        "unitPrice": 100.00
      },
      {
        "product": {
          "id": "B",
          "name": "商品B",
          "price": 50.00,
          "category": "服装"
        },
        "quantity": 3,
        "unitPrice": 50.00
      },
      {
        "product": {
          "id": "C",
          "name": "商品C",
          "price": 30.00,
          "category": "家居"
        },
        "quantity": 4,
        "unitPrice": 30.00
      }
    ]
  },
  "specificRuleIds": ["RULE001", "RULE002", "RULE003", "RULE004"]
}
