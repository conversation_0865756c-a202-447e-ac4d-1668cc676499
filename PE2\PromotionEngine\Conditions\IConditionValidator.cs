using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Conditions;

/// <summary>
/// 条件验证器接口
/// </summary>
public interface IConditionValidator
{
    /// <summary>
    /// 支持的条件类型
    /// </summary>
    ConditionType SupportedType { get; }

    /// <summary>
    /// 验证条件
    /// </summary>
    Task<ConditionResult> ValidateAsync(ConditionExpression expression, ShoppingCart cart);

    /// <summary>
    /// 估算验证时间
    /// </summary>
    long EstimateValidationTime(ConditionExpression expression);

    /// <summary>
    /// 获取优化建议
    /// </summary>
    List<string> GetOptimizationSuggestions(ConditionExpression expression);
}

/// <summary>
/// 商品数量条件验证器
/// </summary>
public sealed class ProductQuantityValidator : IConditionValidator
{
    public ConditionType SupportedType => ConditionType.ProductQuantity;

    public async Task<ConditionResult> ValidateAsync(ConditionExpression expression, ShoppingCart cart)
    {
        await Task.Delay(1).ConfigureAwait(false); // 模拟异步操作

        if (!expression.Parameters.TryGetValue("productIds", out var productIdsObj) ||
            !expression.Parameters.TryGetValue("requiredQuantity", out var requiredQuantityObj))
        {
            return new ConditionResult { IsSatisfied = false, Gap = "缺少必要参数: productIds 或 requiredQuantity" };
        }

        var productIds = (productIdsObj as IEnumerable<string>)?.ToList() ?? new List<string>();
        var requiredQuantity = Convert.ToInt32(requiredQuantityObj);

        var actualQuantity = productIds.Sum(id => cart.GetAvailableProductQuantity(id));
        var isSatisfied = actualQuantity >= requiredQuantity;

        return new ConditionResult
        {
            IsSatisfied = isSatisfied,
            Gap = isSatisfied ? string.Empty : $"需要 {requiredQuantity} 件，实际 {actualQuantity} 件，缺少 {requiredQuantity - actualQuantity} 件",
            ActualValue = actualQuantity,
            RequiredValue = requiredQuantity
        };
    }

    public long EstimateValidationTime(ConditionExpression expression)
    {
        var productCount = expression.Parameters.TryGetValue("productIds", out var productIdsObj) 
            ? ((productIdsObj as IEnumerable<string>)?.Count() ?? 0) 
            : 0;
        return Math.Max(1, productCount / 10); // 每10个商品约1ms
    }

    public List<string> GetOptimizationSuggestions(ConditionExpression expression)
    {
        var suggestions = new List<string>();
        
        if (expression.Parameters.TryGetValue("productIds", out var productIdsObj))
        {
            var productIds = (productIdsObj as IEnumerable<string>)?.ToList() ?? new List<string>();
            if (productIds.Count > 50)
            {
                suggestions.Add("商品ID数量过多，考虑使用分类条件替代");
            }
        }

        return suggestions;
    }
}

/// <summary>
/// 商品金额条件验证器
/// </summary>
public sealed class ProductAmountValidator : IConditionValidator
{
    public ConditionType SupportedType => ConditionType.ProductAmount;

    public async Task<ConditionResult> ValidateAsync(ConditionExpression expression, ShoppingCart cart)
    {
        await Task.Delay(1).ConfigureAwait(false);

        if (!expression.Parameters.TryGetValue("productIds", out var productIdsObj) ||
            !expression.Parameters.TryGetValue("requiredAmount", out var requiredAmountObj))
        {
            return new ConditionResult { IsSatisfied = false, Gap = "缺少必要参数: productIds 或 requiredAmount" };
        }

        var productIds = (productIdsObj as IEnumerable<string>)?.ToList() ?? new List<string>();
        var requiredAmount = Convert.ToDecimal(requiredAmountObj);

        var actualAmount = productIds.Sum(id => 
            cart.Items.Where(x => x.Product.Id == id && x.AvailableQuantity > 0)
                     .Sum(x => x.UnitPrice * x.AvailableQuantity));

        var isSatisfied = actualAmount >= requiredAmount;

        return new ConditionResult
        {
            IsSatisfied = isSatisfied,
            Gap = isSatisfied ? string.Empty : $"需要 {requiredAmount:C}，实际 {actualAmount:C}，缺少 {requiredAmount - actualAmount:C}",
            ActualValue = actualAmount,
            RequiredValue = requiredAmount
        };
    }

    public long EstimateValidationTime(ConditionExpression expression)
    {
        var productCount = expression.Parameters.TryGetValue("productIds", out var productIdsObj) 
            ? ((productIdsObj as IEnumerable<string>)?.Count() ?? 0) 
            : 0;
        return Math.Max(1, productCount / 8); // 金额计算稍微复杂一些
    }

    public List<string> GetOptimizationSuggestions(ConditionExpression expression)
    {
        var suggestions = new List<string>();
        
        if (expression.Parameters.TryGetValue("productIds", out var productIdsObj))
        {
            var productIds = (productIdsObj as IEnumerable<string>)?.ToList() ?? new List<string>();
            if (productIds.Count > 30)
            {
                suggestions.Add("商品ID数量过多，考虑预计算金额或使用分类条件");
            }
        }

        return suggestions;
    }
}

/// <summary>
/// 总数量条件验证器
/// </summary>
public sealed class TotalQuantityValidator : IConditionValidator
{
    public ConditionType SupportedType => ConditionType.TotalQuantity;

    public async Task<ConditionResult> ValidateAsync(ConditionExpression expression, ShoppingCart cart)
    {
        await Task.Delay(1).ConfigureAwait(false);

        if (!expression.Parameters.TryGetValue("requiredQuantity", out var requiredQuantityObj))
        {
            return new ConditionResult { IsSatisfied = false, Gap = "缺少必要参数: requiredQuantity" };
        }

        var requiredQuantity = Convert.ToInt32(requiredQuantityObj);
        var actualQuantity = cart.TotalQuantity;
        var isSatisfied = actualQuantity >= requiredQuantity;

        return new ConditionResult
        {
            IsSatisfied = isSatisfied,
            Gap = isSatisfied ? string.Empty : $"需要总数量 {requiredQuantity} 件，实际 {actualQuantity} 件",
            ActualValue = actualQuantity,
            RequiredValue = requiredQuantity
        };
    }

    public long EstimateValidationTime(ConditionExpression expression)
    {
        return 1; // 总数量验证很快
    }

    public List<string> GetOptimizationSuggestions(ConditionExpression expression)
    {
        return new List<string>(); // 总数量验证已经很优化了
    }
}

/// <summary>
/// 总金额条件验证器
/// </summary>
public sealed class TotalAmountValidator : IConditionValidator
{
    public ConditionType SupportedType => ConditionType.TotalAmount;

    public async Task<ConditionResult> ValidateAsync(ConditionExpression expression, ShoppingCart cart)
    {
        await Task.Delay(1).ConfigureAwait(false);

        if (!expression.Parameters.TryGetValue("requiredAmount", out var requiredAmountObj))
        {
            return new ConditionResult { IsSatisfied = false, Gap = "缺少必要参数: requiredAmount" };
        }

        var requiredAmount = Convert.ToDecimal(requiredAmountObj);
        var actualAmount = cart.TotalAmount;
        var isSatisfied = actualAmount >= requiredAmount;

        return new ConditionResult
        {
            IsSatisfied = isSatisfied,
            Gap = isSatisfied ? string.Empty : $"需要总金额 {requiredAmount:C}，实际 {actualAmount:C}",
            ActualValue = actualAmount,
            RequiredValue = requiredAmount
        };
    }

    public long EstimateValidationTime(ConditionExpression expression)
    {
        return 1; // 总金额验证很快
    }

    public List<string> GetOptimizationSuggestions(ConditionExpression expression)
    {
        return new List<string>(); // 总金额验证已经很优化了
    }
}

/// <summary>
/// 分类数量条件验证器
/// </summary>
public sealed class CategoryQuantityValidator : IConditionValidator
{
    public ConditionType SupportedType => ConditionType.CategoryQuantity;

    public async Task<ConditionResult> ValidateAsync(ConditionExpression expression, ShoppingCart cart)
    {
        await Task.Delay(1).ConfigureAwait(false);

        if (!expression.Parameters.TryGetValue("categoryIds", out var categoryIdsObj) ||
            !expression.Parameters.TryGetValue("requiredQuantity", out var requiredQuantityObj))
        {
            return new ConditionResult { IsSatisfied = false, Gap = "缺少必要参数: categoryIds 或 requiredQuantity" };
        }

        var categoryIds = (categoryIdsObj as IEnumerable<string>)?.ToList() ?? new List<string>();
        var requiredQuantity = Convert.ToInt32(requiredQuantityObj);

        var actualQuantity = cart.Items
            .Where(item => categoryIds.Contains(item.Product.CategoryId))
            .Sum(item => item.AvailableQuantity);

        var isSatisfied = actualQuantity >= requiredQuantity;

        return new ConditionResult
        {
            IsSatisfied = isSatisfied,
            Gap = isSatisfied ? string.Empty : $"分类需要 {requiredQuantity} 件，实际 {actualQuantity} 件",
            ActualValue = actualQuantity,
            RequiredValue = requiredQuantity
        };
    }

    public long EstimateValidationTime(ConditionExpression expression)
    {
        return 2; // 分类验证需要遍历所有商品
    }

    public List<string> GetOptimizationSuggestions(ConditionExpression expression)
    {
        var suggestions = new List<string>();
        
        if (expression.Parameters.TryGetValue("categoryIds", out var categoryIdsObj))
        {
            var categoryIds = (categoryIdsObj as IEnumerable<string>)?.ToList() ?? new List<string>();
            if (categoryIds.Count > 10)
            {
                suggestions.Add("分类ID数量过多，考虑使用层级分类或预计算");
            }
        }

        return suggestions;
    }
}

/// <summary>
/// 分类金额条件验证器
/// </summary>
public sealed class CategoryAmountValidator : IConditionValidator
{
    public ConditionType SupportedType => ConditionType.CategoryAmount;

    public async Task<ConditionResult> ValidateAsync(ConditionExpression expression, ShoppingCart cart)
    {
        await Task.Delay(1).ConfigureAwait(false);

        if (!expression.Parameters.TryGetValue("categoryIds", out var categoryIdsObj) ||
            !expression.Parameters.TryGetValue("requiredAmount", out var requiredAmountObj))
        {
            return new ConditionResult { IsSatisfied = false, Gap = "缺少必要参数: categoryIds 或 requiredAmount" };
        }

        var categoryIds = (categoryIdsObj as IEnumerable<string>)?.ToList() ?? new List<string>();
        var requiredAmount = Convert.ToDecimal(requiredAmountObj);

        var actualAmount = cart.Items
            .Where(item => categoryIds.Contains(item.Product.CategoryId) && item.AvailableQuantity > 0)
            .Sum(item => item.UnitPrice * item.AvailableQuantity);

        var isSatisfied = actualAmount >= requiredAmount;

        return new ConditionResult
        {
            IsSatisfied = isSatisfied,
            Gap = isSatisfied ? string.Empty : $"分类需要 {requiredAmount:C}，实际 {actualAmount:C}",
            ActualValue = actualAmount,
            RequiredValue = requiredAmount
        };
    }

    public long EstimateValidationTime(ConditionExpression expression)
    {
        return 3; // 分类金额验证需要更多计算
    }

    public List<string> GetOptimizationSuggestions(ConditionExpression expression)
    {
        var suggestions = new List<string>();
        
        if (expression.Parameters.TryGetValue("categoryIds", out var categoryIdsObj))
        {
            var categoryIds = (categoryIdsObj as IEnumerable<string>)?.ToList() ?? new List<string>();
            if (categoryIds.Count > 8)
            {
                suggestions.Add("分类ID数量过多，考虑预计算分类金额");
            }
        }

        return suggestions;
    }
}

/// <summary>
/// 会员等级条件验证器
/// </summary>
public sealed class MemberLevelValidator : IConditionValidator
{
    public ConditionType SupportedType => ConditionType.MemberLevel;

    public async Task<ConditionResult> ValidateAsync(ConditionExpression expression, ShoppingCart cart)
    {
        await Task.Delay(1).ConfigureAwait(false);

        if (!expression.Parameters.TryGetValue("requiredLevels", out var requiredLevelsObj))
        {
            return new ConditionResult { IsSatisfied = false, Gap = "缺少必要参数: requiredLevels" };
        }

        var requiredLevels = (requiredLevelsObj as IEnumerable<string>)?.ToList() ?? new List<string>();
        var isSatisfied = !string.IsNullOrEmpty(cart.MemberLevel) && requiredLevels.Contains(cart.MemberLevel);

        return new ConditionResult
        {
            IsSatisfied = isSatisfied,
            Gap = isSatisfied ? string.Empty : $"需要会员等级: {string.Join(",", requiredLevels)}，当前: {cart.MemberLevel ?? "非会员"}",
            ActualValue = cart.MemberLevel ?? "非会员",
            RequiredValue = string.Join(",", requiredLevels)
        };
    }

    public long EstimateValidationTime(ConditionExpression expression)
    {
        return 1; // 会员等级验证很快
    }

    public List<string> GetOptimizationSuggestions(ConditionExpression expression)
    {
        return new List<string>(); // 会员等级验证已经很优化了
    }
}

/// <summary>
/// 时间范围条件验证器
/// </summary>
public sealed class TimeRangeValidator : IConditionValidator
{
    public ConditionType SupportedType => ConditionType.TimeRange;

    public async Task<ConditionResult> ValidateAsync(ConditionExpression expression, ShoppingCart cart)
    {
        await Task.Delay(1).ConfigureAwait(false);

        if (!expression.Parameters.TryGetValue("startTime", out var startTimeObj) ||
            !expression.Parameters.TryGetValue("endTime", out var endTimeObj))
        {
            return new ConditionResult { IsSatisfied = false, Gap = "缺少必要参数: startTime 或 endTime" };
        }

        var startTime = Convert.ToDateTime(startTimeObj);
        var endTime = Convert.ToDateTime(endTimeObj);
        var currentTime = DateTime.Now;

        var isSatisfied = currentTime >= startTime && currentTime <= endTime;

        return new ConditionResult
        {
            IsSatisfied = isSatisfied,
            Gap = isSatisfied ? string.Empty : $"当前时间 {currentTime:yyyy-MM-dd HH:mm:ss} 不在有效期内 ({startTime:yyyy-MM-dd HH:mm:ss} - {endTime:yyyy-MM-dd HH:mm:ss})",
            ActualValue = currentTime,
            RequiredValue = $"{startTime:yyyy-MM-dd HH:mm:ss} - {endTime:yyyy-MM-dd HH:mm:ss}"
        };
    }

    public long EstimateValidationTime(ConditionExpression expression)
    {
        return 1; // 时间验证很快
    }

    public List<string> GetOptimizationSuggestions(ConditionExpression expression)
    {
        return new List<string>(); // 时间验证已经很优化了
    }
}
