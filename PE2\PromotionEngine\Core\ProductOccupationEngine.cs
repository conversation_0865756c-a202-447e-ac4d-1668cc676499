global using Microsoft.Extensions.Logging;
using PE2.Models;
using PE2.PromotionEngine.Models;
using System.Collections.Concurrent;

namespace PE2.PromotionEngine.Core;

/// <summary>
/// 商品占用引擎 - 优雅的商品占用和条件筛选解决方案
/// 核心设计：混合式商品表示 + 三阶段处理 + 精确占用管理
/// </summary>
public sealed class ProductOccupationEngine : IDisposable
{
    private readonly ILogger<ProductOccupationEngine> _logger;
    private readonly ConcurrentDictionary<string, ProductOccupationState> _occupationStates = new();
    private readonly SemaphoreSlim _operationLock = new(1, 1);
    private bool _disposed;

    public ProductOccupationEngine(ILogger<ProductOccupationEngine> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 创建商品占用会话 - 支持事务性操作
    /// </summary>
    public async Task<IOccupationSession> CreateSessionAsync(
        ProcessedCart cart, 
        string sessionId = null,
        CancellationToken cancellationToken = default)
    {
        await _operationLock.WaitAsync(cancellationToken).ConfigureAwait(false);
        
        try
        {
            sessionId ??= Guid.NewGuid().ToString("N");
            
            var session = new OccupationSession(
                sessionId,
                cart,
                this,
                _logger
            );

            await session.InitializeAsync(cancellationToken).ConfigureAwait(false);
            return session;
        }
        finally
        {
            _operationLock.Release();
        }
    }

    /// <summary>
    /// 获取商品的当前占用状态
    /// </summary>
    public ProductOccupationState GetOccupationState(string productId)
    {
        return _occupationStates.GetValueOrDefault(productId, new ProductOccupationState
        {
            ProductId = productId,
            TotalQuantity = 0,
            AvailableQuantity = 0,
            Reservations = []
        });
    }

    /// <summary>
    /// 更新商品占用状态
    /// </summary>
    internal void UpdateOccupationState(string productId, ProductOccupationState state)
    {
        _occupationStates.AddOrUpdate(productId, state, (_, _) => state);
    }

    /// <summary>
    /// 清理会话相关的占用状态
    /// </summary>
    internal void CleanupSession(string sessionId)
    {
        foreach (var kvp in _occupationStates)
        {
            var state = kvp.Value;
            state.Reservations.RemoveAll(r => r.SessionId == sessionId);
            
            if (!state.Reservations.Any())
            {
                _occupationStates.TryRemove(kvp.Key, out _);
            }
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _operationLock?.Dispose();
            _occupationStates.Clear();
            _disposed = true;
        }
    }
}

/// <summary>
/// 商品占用状态
/// </summary>
public sealed class ProductOccupationState
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public required string ProductId { get; init; }

    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalQuantity { get; set; }

    /// <summary>
    /// 可用数量（未被占用的）
    /// </summary>
    public int AvailableQuantity { get; set; }

    /// <summary>
    /// 预留记录列表
    /// </summary>
    public List<OccupationReservation> Reservations { get; set; } = [];

    /// <summary>
    /// 获取指定会话的已占用数量
    /// </summary>
    public int GetOccupiedQuantity(string sessionId)
    {
        return Reservations
            .Where(r => r.SessionId == sessionId)
            .Sum(r => r.Quantity);
    }

    /// <summary>
    /// 获取指定规则的已占用数量
    /// </summary>
    public int GetOccupiedQuantityByRule(string ruleId)
    {
        return Reservations
            .Where(r => r.RuleId == ruleId)
            .Sum(r => r.Quantity);
    }

    /// <summary>
    /// 检查是否可以预留指定数量
    /// </summary>
    public bool CanReserve(int quantity)
    {
        return AvailableQuantity >= quantity;
    }
}

/// <summary>
/// 占用预留记录
/// </summary>
public sealed class OccupationReservation
{
    /// <summary>
    /// 预留ID
    /// </summary>
    public string ReservationId { get; init; } = Guid.NewGuid().ToString("N");

    /// <summary>
    /// 会话ID
    /// </summary>
    public required string SessionId { get; init; }

    /// <summary>
    /// 规则ID
    /// </summary>
    public required string RuleId { get; init; }

    /// <summary>
    /// 规则名称
    /// </summary>
    public string RuleName { get; init; } = string.Empty;

    /// <summary>
    /// 预留类型
    /// </summary>
    public required ReservationType ReservationType { get; init; }

    /// <summary>
    /// 预留数量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 排他性级别
    /// </summary>
    public ProductExclusivityLevel ExclusivityLevel { get; init; } = ProductExclusivityLevel.None;

    /// <summary>
    /// 是否可以与其他促销叠加
    /// </summary>
    public bool CanStackWithOthers { get; init; } = true;

    /// <summary>
    /// 预留时间
    /// </summary>
    public DateTime ReservedAt { get; init; } = DateTime.UtcNow;

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; init; }

    /// <summary>
    /// 是否已过期
    /// </summary>
    public bool IsExpired => ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;
}

/// <summary>
/// 占用会话接口
/// </summary>
public interface IOccupationSession : IDisposable
{
    /// <summary>
    /// 会话ID
    /// </summary>
    string SessionId { get; }

    /// <summary>
    /// 购物车
    /// </summary>
    ProcessedCart Cart { get; }

    /// <summary>
    /// 尝试预留商品用于条件验证
    /// </summary>
    Task<ReservationResult> TryReserveForConditionAsync(
        string ruleId,
        string ruleName,
        List<string> productIds,
        int requiredQuantity,
        ProductExclusivityLevel exclusivityLevel = ProductExclusivityLevel.None,
        bool canStackWithOthers = true,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 尝试预留商品用于促销执行
    /// </summary>
    Task<ReservationResult> TryReserveForExecutionAsync(
        string ruleId,
        string ruleName,
        List<string> productIds,
        int requiredQuantity,
        ProductExclusivityLevel exclusivityLevel = ProductExclusivityLevel.None,
        bool canStackWithOthers = true,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 释放指定规则的所有预留
    /// </summary>
    Task ReleaseReservationsAsync(string ruleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 提交会话 - 将预留转换为实际占用
    /// </summary>
    Task<CommitResult> CommitAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 回滚会话 - 释放所有预留
    /// </summary>
    Task RollbackAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取会话状态报告
    /// </summary>
    SessionStateReport GetStateReport();
}

/// <summary>
/// 预留结果
/// </summary>
public sealed class ReservationResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccessful { get; init; }

    /// <summary>
    /// 预留ID列表
    /// </summary>
    public List<string> ReservationIds { get; init; } = [];

    /// <summary>
    /// 实际预留的商品详情
    /// </summary>
    public List<ReservedProductDetail> ReservedProducts { get; init; } = [];

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; init; } = string.Empty;

    /// <summary>
    /// 冲突信息
    /// </summary>
    public List<ConflictInfo> Conflicts { get; init; } = [];
}

/// <summary>
/// 预留的商品详情
/// </summary>
public sealed class ReservedProductDetail
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public required string ProductId { get; init; }

    /// <summary>
    /// 商品名称
    /// </summary>
    public string ProductName { get; init; } = string.Empty;

    /// <summary>
    /// 预留数量
    /// </summary>
    public int ReservedQuantity { get; init; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal UnitPrice { get; init; }

    /// <summary>
    /// 预留类型
    /// </summary>
    public ReservationType ReservationType { get; init; }
}

/// <summary>
/// 冲突信息
/// </summary>
public sealed class ConflictInfo
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public required string ProductId { get; init; }

    /// <summary>
    /// 冲突类型
    /// </summary>
    public ConflictType ConflictType { get; init; }

    /// <summary>
    /// 冲突描述
    /// </summary>
    public string Description { get; init; } = string.Empty;

    /// <summary>
    /// 冲突的规则ID
    /// </summary>
    public string ConflictingRuleId { get; init; } = string.Empty;
}

/// <summary>
/// 冲突类型
/// </summary>
public enum ConflictType
{
    /// <summary>
    /// 数量不足
    /// </summary>
    InsufficientQuantity,

    /// <summary>
    /// 排他性冲突
    /// </summary>
    ExclusivityConflict,

    /// <summary>
    /// 不允许叠加
    /// </summary>
    StackingNotAllowed,

    /// <summary>
    /// 已被其他规则占用
    /// </summary>
    AlreadyOccupied
}
