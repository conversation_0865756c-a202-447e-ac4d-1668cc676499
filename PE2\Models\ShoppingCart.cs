namespace PE2.Models;

/// <summary>
/// 购物车
/// </summary>
public class ShoppingCart
{
    /// <summary>
    /// 购物车ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 客户ID
    /// </summary>
    public string CustomerId { get; set; } = string.Empty;

    /// <summary>
    /// 购物车商品列表
    /// </summary>
    public List<CartItem> Items { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 商品总数量
    /// </summary>
    public int TotalQuantity => Items.Sum(x => x.Quantity);

    /// <summary>
    /// 商品总金额（未优惠前）
    /// </summary>
    public decimal TotalAmount => Items.Sum(x => x.SubTotal);

    /// <summary>
    /// 实际支付总金额（应用促销后）
    /// </summary>
    public decimal ActualTotalAmount => Items.Sum(x => x.ActualSubTotal);

    /// <summary>
    /// 总优惠金额
    /// </summary>
    public decimal TotalDiscountAmount => TotalAmount - ActualTotalAmount;

    /// <summary>
    /// 获取指定商品的总数量
    /// </summary>
    public int GetProductQuantity(string productId)
    {
        return Items.Where(x => x.Product.Id == productId).Sum(x => x.Quantity);
    }

    /// <summary>
    /// 获取指定商品的可用数量（未被促销消耗的）
    /// </summary>
    public int GetAvailableProductQuantity(string productId)
    {
        return Items.Where(x => x.Product.Id == productId).Sum(x => x.AvailableQuantity);
    }

    /// <summary>
    /// 获取指定分类的商品总数量
    /// </summary>
    public int GetCategoryQuantity(string category)
    {
        return Items.Where(x => x.Product.Category == category).Sum(x => x.Quantity);
    }

    /// <summary>
    /// 获取指定分类的可用数量
    /// </summary>
    public int GetAvailableCategoryQuantity(string category)
    {
        return Items.Where(x => x.Product.Category == category).Sum(x => x.AvailableQuantity);
    }

    /// <summary>
    /// 深度克隆购物车
    /// </summary>
    public ShoppingCart Clone()
    {
        return new ShoppingCart
        {
            Id = Id,
            CustomerId = CustomerId,
            Items = Items.Select(x => x.Clone()).ToList(),
            CreatedAt = CreatedAt,
            UpdatedAt = UpdatedAt
        };
    }

    /// <summary>
    /// 初始化所有商品的实际支付单价
    /// </summary>
    public void InitializeActualPrices()
    {
        foreach (var item in Items)
        {
            item.InitializeActualUnitPrice();
        }
    }

    /// <summary>
    /// 添加商品到购物车
    /// </summary>
    public void AddItem(Product product, int quantity, decimal? unitPrice = null)
    {
        var price = unitPrice ?? product.Price;
        var existingItem = Items.FirstOrDefault(x => x.Product.Id == product.Id && x.UnitPrice == price && !x.IsGift);

        if (existingItem != null)
        {
            existingItem.Quantity += quantity;
        }
        else
        {
            var newItem = new CartItem
            {
                Product = product,
                Quantity = quantity,
                UnitPrice = price,
                ActualUnitPrice = price
            };
            Items.Add(newItem);
        }

        UpdatedAt = DateTime.Now;
    }

    /// <summary>
    /// 添加赠品到购物车
    /// </summary>
    public void AddGiftItem(Product product, int quantity, string ruleId, string ruleName)
    {
        var giftItem = new CartItem
        {
            Product = product,
            Quantity = quantity,
            UnitPrice = product.Price,
            ActualUnitPrice = 0, // 赠品价格为0
            IsGift = true
        };

        giftItem.AddPromotionDetail(new ItemPromotionDetail
        {
            RuleId = ruleId,
            RuleName = ruleName,
            PromotionType = "Gift",
            DiscountAmount = product.Price * quantity,
            Description = $"赠品 - {ruleName}",
            IsGiftRelated = true
        });

        Items.Add(giftItem);
        UpdatedAt = DateTime.Now;
    }

    /// <summary>
    /// 移除商品
    /// </summary>
    public bool RemoveItem(string productId, int quantity = int.MaxValue)
    {
        var items = Items.Where(x => x.Product.Id == productId).ToList();
        var totalRemoved = 0;

        foreach (var item in items)
        {
            var toRemove = Math.Min(item.Quantity, quantity - totalRemoved);
            item.Quantity -= toRemove;
            totalRemoved += toRemove;

            if (quantity - totalRemoved <= 0) break;
        }

        Items.RemoveAll(x => x.Quantity <= 0);
        UpdatedAt = DateTime.Now;

        return totalRemoved > 0;
    }
}
