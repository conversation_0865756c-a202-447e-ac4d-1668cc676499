using PE2.Models;
using PE2.PromotionEngine.Calculators;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Rules;

namespace PE2.Services;

/// <summary>
/// 促销引擎服务
/// </summary>
public class PromotionEngineService
{
    private readonly IPromotionRuleService _ruleService;
    private readonly ILogger<PromotionEngineService> _logger;

    public PromotionEngineService(IPromotionRuleService ruleService, ILogger<PromotionEngineService> logger)
    {
        _ruleService = ruleService;
        _logger = logger;
    }

    /// <summary>
    /// 计算购物车的最优促销
    /// </summary>
    public async Task<PromotionResult> CalculateOptimalPromotionsAsync(ShoppingCart cart)
    {
        try
        {
            _logger.LogInformation("开始计算购物车 {CartId} 的最优促销", cart.Id);

            // 获取有效的促销规则
            var validRules = await _ruleService.GetValidRulesAsync();

            if (!validRules.Any())
            {
                _logger.LogWarning("没有找到有效的促销规则");
                return new PromotionResult
                {
                    OriginalCart = cart.Clone(),
                    ProcessedCart = cart.Clone(),
                    AlgorithmInfo = "没有有效的促销规则"
                };
            }

            // 创建促销计算器
            var calculator = new PromotionCalculator(validRules);

            // 计算最优促销组合
            var result = calculator.CalculateOptimalPromotions(cart);

            _logger.LogInformation("购物车 {CartId} 促销计算完成，原价: {OriginalAmount:C}, 优惠: {Discount:C}, 最终价格: {FinalAmount:C}",
                cart.Id, result.OriginalAmount, result.TotalDiscount, result.FinalAmount);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "计算购物车 {CartId} 促销时发生错误", cart.Id);

            return new PromotionResult
            {
                OriginalCart = cart.Clone(),
                ProcessedCart = cart.Clone(),
                AlgorithmInfo = $"计算错误: {ex.Message}",
                IsOptimal = false
            };
        }
    }

    /// <summary>
    /// 获取购物车的促销预览
    /// </summary>
    public async Task<List<PromotionPreview>> GetPromotionPreviewsAsync(ShoppingCart cart)
    {
        try
        {
            var validRules = await _ruleService.GetValidRulesAsync();
            var previews = new List<PromotionPreview>();

            foreach (var rule in validRules.OrderByDescending(r => r.Priority))
            {
                var preview = rule.GetPromotionPreview(cart);
                previews.Add(preview);
            }

            return previews;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取购物车 {CartId} 促销预览时发生错误", cart.Id);
            return new List<PromotionPreview>();
        }
    }

    /// <summary>
    /// 应用指定的促销规则
    /// </summary>
    public async Task<PromotionApplication> ApplySpecificPromotionAsync(ShoppingCart cart, string ruleId, int applicationCount = 1)
    {
        try
        {
            var rule = await _ruleService.GetRuleByIdAsync(ruleId);

            if (rule == null)
            {
                return new PromotionApplication
                {
                    RuleId = ruleId,
                    IsSuccessful = false,
                    ErrorMessage = "促销规则不存在"
                };
            }

            if (!rule.IsApplicable(cart, DateTime.Now))
            {
                return new PromotionApplication
                {
                    RuleId = ruleId,
                    RuleName = rule.Name,
                    IsSuccessful = false,
                    ErrorMessage = "促销规则不适用于当前购物车"
                };
            }

            var maxApplications = rule.CalculateMaxApplications(cart);
            if (applicationCount > maxApplications)
            {
                applicationCount = maxApplications;
            }

            if (applicationCount <= 0)
            {
                return new PromotionApplication
                {
                    RuleId = ruleId,
                    RuleName = rule.Name,
                    IsSuccessful = false,
                    ErrorMessage = "商品数量不足以应用此促销"
                };
            }

            return rule.ApplyPromotion(cart, applicationCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "应用促销规则 {RuleId} 时发生错误", ruleId);

            return new PromotionApplication
            {
                RuleId = ruleId,
                IsSuccessful = false,
                ErrorMessage = $"应用促销时发生错误: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// 验证促销组合的有效性
    /// </summary>
    public async Task<(bool IsValid, string ErrorMessage)> ValidatePromotionCombinationAsync(
        ShoppingCart cart, List<string> ruleIds)
    {
        try
        {
            var cartCopy = cart.Clone();
            var appliedRules = new List<string>();

            foreach (var ruleId in ruleIds)
            {
                var rule = await _ruleService.GetRuleByIdAsync(ruleId);

                if (rule == null)
                {
                    return (false, $"促销规则 {ruleId} 不存在");
                }

                // 检查互斥规则
                if (rule.ExclusiveRuleIds.Any(exclusiveId => appliedRules.Contains(exclusiveId)))
                {
                    var conflictRules = rule.ExclusiveRuleIds.Where(id => appliedRules.Contains(id));
                    return (false, $"促销规则 {ruleId} 与已应用的规则 {string.Join(", ", conflictRules)} 互斥");
                }

                if (!rule.IsApplicable(cartCopy, DateTime.Now))
                {
                    return (false, $"促销规则 {ruleId} 不适用于当前购物车状态");
                }

                var maxApplications = rule.CalculateMaxApplications(cartCopy);
                if (maxApplications <= 0)
                {
                    return (false, $"促销规则 {ruleId} 无法应用，商品数量不足");
                }

                // 尝试应用促销
                var application = rule.ApplyPromotion(cartCopy, 1);
                if (!application.IsSuccessful)
                {
                    return (false, $"促销规则 {ruleId} 应用失败: {application.ErrorMessage}");
                }

                appliedRules.Add(ruleId);
            }

            return (true, "促销组合有效");
        }
        catch (Exception ex)
        {
            return (false, $"验证促销组合时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取促销统计信息
    /// </summary>
    public async Task<PromotionStatistics> GetPromotionStatisticsAsync()
    {
        try
        {
            var allRules = await _ruleService.GetAllRulesAsync();
            var validRules = await _ruleService.GetValidRulesAsync();

            return new PromotionStatistics
            {
                TotalRules = allRules.Count,
                EnabledRules = allRules.Count(r => r.IsEnabled),
                ValidRules = validRules.Count,
                RulesByType = allRules.GroupBy(r => r.RuleType)
                    .ToDictionary(g => g.Key, g => g.Count()),
                ExpiredRules = allRules.Count(r => r.EndTime.HasValue && r.EndTime.Value < DateTime.Now),
                FutureRules = allRules.Count(r => r.StartTime.HasValue && r.StartTime.Value > DateTime.Now)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取促销统计信息时发生错误");
            return new PromotionStatistics();
        }
    }

    /// <summary>
    /// 获取购物车中每个商品的可用促销信息
    /// </summary>
    public async Task<List<ProductPromotionInfo>> GetAvailablePromotionsAsync(ShoppingCart cart)
    {
        try
        {
            var validRules = await _ruleService.GetValidRulesAsync();
            var productPromotions = new List<ProductPromotionInfo>();

            // 为每个商品分析可用的促销
            foreach (var item in cart.Items)
            {
                var productInfo = new ProductPromotionInfo
                {
                    ProductId = item.Product.Id,
                    ProductName = item.Product.Name,
                    CurrentQuantity = item.Quantity,
                    UnitPrice = item.UnitPrice,
                    AvailablePromotions = new List<AvailablePromotion>()
                };

                foreach (var rule in validRules.Where(r => r.IsApplicable(cart, DateTime.Now)))
                {
                    var preview = rule.GetPromotionPreview(cart);
                    if (preview.IsApplicable)
                    {
                        var availablePromotion = new AvailablePromotion
                        {
                            RuleId = rule.Id,
                            RuleName = rule.Name,
                            RuleType = rule.RuleType,
                            Description = rule.Description,
                            Priority = rule.Priority,
                            MaxApplications = rule.CalculateMaxApplications(cart),
                            EstimatedDiscount = preview.EstimatedDiscount,
                            RequiredConditions = GetRequiredConditions(rule, cart),
                            CanStack = rule.CanStackWithOthers,
                            IsRepeatable = rule.IsRepeatable
                        };

                        productInfo.AvailablePromotions.Add(availablePromotion);
                    }
                }

                productPromotions.Add(productInfo);
            }

            return productPromotions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取可用促销信息时发生错误");
            return new List<ProductPromotionInfo>();
        }
    }

    /// <summary>
    /// 应用用户选择的促销组合
    /// </summary>
    public async Task<PromotionResult> ApplySelectedPromotionsAsync(ShoppingCart cart, List<SelectedPromotion> selectedPromotions)
    {
        try
        {
            var cartCopy = cart.Clone();
            var appliedPromotions = new List<PromotionApplication>();
            var ignoredPromotions = new List<IgnoredPromotion>();

            foreach (var selected in selectedPromotions.Where(s => s.IsSelected))
            {
                var rule = await _ruleService.GetRuleByIdAsync(selected.RuleId);
                if (rule == null)
                {
                    ignoredPromotions.Add(new IgnoredPromotion
                    {
                        RuleId = selected.RuleId,
                        RuleName = "未知规则",
                        Reason = "规则不存在"
                    });
                    continue;
                }

                if (!rule.IsApplicable(cartCopy, DateTime.Now))
                {
                    ignoredPromotions.Add(new IgnoredPromotion
                    {
                        RuleId = selected.RuleId,
                        RuleName = rule.Name,
                        Reason = "规则不适用于当前购物车状态"
                    });
                    continue;
                }

                var maxApplications = rule.CalculateMaxApplications(cartCopy);
                var applicationCount = Math.Min(selected.ApplicationCount, maxApplications);

                if (applicationCount <= 0)
                {
                    ignoredPromotions.Add(new IgnoredPromotion
                    {
                        RuleId = selected.RuleId,
                        RuleName = rule.Name,
                        Reason = "商品数量不足"
                    });
                    continue;
                }

                var application = rule.ApplyPromotion(cartCopy, applicationCount);
                if (application.IsSuccessful)
                {
                    appliedPromotions.Add(application);
                }
                else
                {
                    ignoredPromotions.Add(new IgnoredPromotion
                    {
                        RuleId = selected.RuleId,
                        RuleName = rule.Name,
                        Reason = application.ErrorMessage ?? "应用失败"
                    });
                }
            }

            // 将 PromotionApplication 转换为 AppliedPromotion
            var convertedPromotions = appliedPromotions.Select(app => new AppliedPromotion
            {
                RuleId = app.RuleId,
                RuleName = app.RuleName,
                PromotionType = app.GetType().Name,
                DiscountAmount = app.DiscountAmount,
                ConsumedItems = app.ConsumedItems,
                GiftItems = app.GiftItems,
                ApplicationCount = app.ApplicationCount,
                Description = app.ErrorMessage ?? "用户选择的促销"
            }).ToList();

            return new PromotionResult
            {
                OriginalCart = cart,
                ProcessedCart = cartCopy,
                AppliedPromotions = convertedPromotions,
                IgnoredPromotions = ignoredPromotions,
                AlgorithmInfo = "用户选择的促销组合",
                IsOptimal = false // 用户选择的不一定是最优的
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "应用选择的促销时发生错误");
            return new PromotionResult
            {
                OriginalCart = cart,
                ProcessedCart = cart.Clone(),
                AppliedPromotions = new List<AppliedPromotion>(),
                IgnoredPromotions = new List<IgnoredPromotion>(),
                AlgorithmInfo = $"应用错误: {ex.Message}",
                IsOptimal = false
            };
        }
    }

    /// <summary>
    /// 获取促销规则的必要条件
    /// </summary>
    private List<string> GetRequiredConditions(PromotionRuleBase rule, ShoppingCart cart)
    {
        var conditions = new List<string>();

        // 这里可以根据不同的规则类型分析具体的条件
        // 暂时返回基本信息
        conditions.Add($"优先级: {rule.Priority}");

        if (rule.IsRepeatable)
        {
            conditions.Add("可重复应用");
        }

        if (!rule.CanStackWithOthers)
        {
            conditions.Add("不可与其他促销叠加");
        }

        return conditions;
    }
}


