using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Observability;
using PE2.PromotionEngine.Conditions;
using PE2.PromotionEngine.Inventory;
using PE2.PromotionEngine.Performance;
using PE2.PromotionEngine.Allocation;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Core;

/// <summary>
/// 新架构交换规则基类
/// </summary>
public abstract class NewExchangeRuleBase : NewPromotionRuleBase
{
    /// <summary>
    /// 构造函数
    /// </summary>
    protected NewExchangeRuleBase(
        ILogger logger,
        IObservabilityEngine observability,
        IConditionEngine conditionEngine,
        IInventoryManager inventoryManager,
        IPerformanceOptimizer performanceOptimizer,
        IAllocationEngine allocationEngine)
        : base(logger, observability, conditionEngine, inventoryManager, performanceOptimizer, allocationEngine)
    {
    }

    /// <summary>
    /// 规则类型
    /// </summary>
    public override string RuleType => "Exchange";

    /// <summary>
    /// 交换策略
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public ExchangeStrategy Strategy { get; init; } = ExchangeStrategy.SpecialPrice;

    /// <summary>
    /// 收益选择策略
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public BenefitSelectionStrategy BenefitSelection { get; init; } = BenefitSelectionStrategy.CustomerBenefit;

    /// <summary>
    /// 购买条件
    /// </summary>
    public required ExchangeBuyCondition BuyCondition { get; init; }

    /// <summary>
    /// 交换条件
    /// </summary>
    public required ExchangeCondition ExchangeCondition { get; init; }
}

/// <summary>
/// 新架构买免规则基类
/// </summary>
public abstract class NewBuyFreeRuleBase : NewPromotionRuleBase
{
    /// <summary>
    /// 构造函数
    /// </summary>
    protected NewBuyFreeRuleBase(
        ILogger logger,
        IObservabilityEngine observability,
        IConditionEngine conditionEngine,
        IInventoryManager inventoryManager,
        IPerformanceOptimizer performanceOptimizer,
        IAllocationEngine allocationEngine)
        : base(logger, observability, conditionEngine, inventoryManager, performanceOptimizer, allocationEngine)
    {
    }

    /// <summary>
    /// 规则类型
    /// </summary>
    public override string RuleType => "BuyFree";

    /// <summary>
    /// 免费商品选择策略
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public FreeItemSelectionStrategy FreeItemSelection { get; init; } = FreeItemSelectionStrategy.LowestPrice;

    /// <summary>
    /// 是否支持翻倍
    /// </summary>
    public bool SupportMultiplier { get; init; } = true;

    /// <summary>
    /// 最大翻倍次数
    /// </summary>
    public int MaxMultiplier { get; init; } = 10;
}

/// <summary>
/// 新架构现金折扣规则基类
/// </summary>
public abstract class NewCashDiscountRuleBase : NewPromotionRuleBase
{
    /// <summary>
    /// 构造函数
    /// </summary>
    protected NewCashDiscountRuleBase(
        ILogger logger,
        IObservabilityEngine observability,
        IConditionEngine conditionEngine,
        IInventoryManager inventoryManager,
        IPerformanceOptimizer performanceOptimizer,
        IAllocationEngine allocationEngine)
        : base(logger, observability, conditionEngine, inventoryManager, performanceOptimizer, allocationEngine)
    {
    }

    /// <summary>
    /// 规则类型
    /// </summary>
    public override string RuleType => "CashDiscount";

    /// <summary>
    /// 是否支持翻倍
    /// </summary>
    public bool SupportMultiplier { get; init; } = true;

    /// <summary>
    /// 最大翻倍次数
    /// </summary>
    public int MaxMultiplier { get; init; } = 10;
}

/// <summary>
/// 新架构折扣规则基类
/// </summary>
public abstract class NewDiscountRuleBase : NewPromotionRuleBase
{
    /// <summary>
    /// 构造函数
    /// </summary>
    protected NewDiscountRuleBase(
        ILogger logger,
        IObservabilityEngine observability,
        IConditionEngine conditionEngine,
        IInventoryManager inventoryManager,
        IPerformanceOptimizer performanceOptimizer,
        IAllocationEngine allocationEngine)
        : base(logger, observability, conditionEngine, inventoryManager, performanceOptimizer, allocationEngine)
    {
    }

    /// <summary>
    /// 规则类型
    /// </summary>
    public override string RuleType => "Discount";

    /// <summary>
    /// 折扣选择策略
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public DiscountSelectionStrategy DiscountSelection { get; init; } = DiscountSelectionStrategy.CustomerBenefit;

    /// <summary>
    /// 是否支持翻倍
    /// </summary>
    public bool SupportMultiplier { get; init; } = true;

    /// <summary>
    /// 最大翻倍次数
    /// </summary>
    public int MaxMultiplier { get; init; } = 10;
}

/// <summary>
/// 新架构特价规则基类
/// </summary>
public abstract class NewSpecialPriceRuleBase : NewPromotionRuleBase
{
    /// <summary>
    /// 构造函数
    /// </summary>
    protected NewSpecialPriceRuleBase(
        ILogger logger,
        IObservabilityEngine observability,
        IConditionEngine conditionEngine,
        IInventoryManager inventoryManager,
        IPerformanceOptimizer performanceOptimizer,
        IAllocationEngine allocationEngine)
        : base(logger, observability, conditionEngine, inventoryManager, performanceOptimizer, allocationEngine)
    {
    }

    /// <summary>
    /// 规则类型
    /// </summary>
    public override string RuleType => "SpecialPrice";

    /// <summary>
    /// 是否支持翻倍
    /// </summary>
    public bool SupportMultiplier { get; init; } = true;

    /// <summary>
    /// 最大翻倍次数
    /// </summary>
    public int MaxMultiplier { get; init; } = 10;
}

/// <summary>
/// 新架构买赠规则基类
/// </summary>
public abstract class NewBuyGiftRuleBase : NewPromotionRuleBase
{
    /// <summary>
    /// 构造函数
    /// </summary>
    protected NewBuyGiftRuleBase(
        ILogger logger,
        IObservabilityEngine observability,
        IConditionEngine conditionEngine,
        IInventoryManager inventoryManager,
        IPerformanceOptimizer performanceOptimizer,
        IAllocationEngine allocationEngine)
        : base(logger, observability, conditionEngine, inventoryManager, performanceOptimizer, allocationEngine)
    {
    }

    /// <summary>
    /// 规则类型
    /// </summary>
    public override string RuleType => "BuyGift";

    /// <summary>
    /// 赠品类型
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public GiftType GiftType { get; init; } = GiftType.Unified;

    /// <summary>
    /// 收益选择策略
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public BenefitSelectionStrategy BenefitSelection { get; init; } = BenefitSelectionStrategy.CustomerBenefit;

    /// <summary>
    /// 是否支持翻倍
    /// </summary>
    public bool SupportMultiplier { get; init; } = true;

    /// <summary>
    /// 最大翻倍次数
    /// </summary>
    public int MaxMultiplier { get; init; } = 10;
}

/// <summary>
/// 规则迁移辅助类
/// 提供从旧规则到新规则的转换功能
/// </summary>
public static class RuleMigrationHelper
{
    /// <summary>
    /// 将旧的PromotionApplication转换为新的AppliedPromotion
    /// </summary>
    public static AppliedPromotion ConvertToAppliedPromotion(PromotionApplication application, string ruleType)
    {
        return new AppliedPromotion
        {
            RuleId = application.RuleId,
            RuleName = application.RuleName,
            PromotionType = ruleType,
            DiscountAmount = application.DiscountAmount,
            ApplicationCount = application.ApplicationCount,
            ConsumedItems = application.ConsumedItems,
            GiftItems = application.GiftItems
        };
    }

    /// <summary>
    /// 将ShoppingCart转换为ProcessedCart
    /// </summary>
    public static ProcessedCart ConvertToProcessedCart(ShoppingCart cart)
    {
        var processedCart = new ProcessedCart
        {
            Id = cart.Id,
            CustomerId = cart.CustomerId,
            Channel = cart.Channel,
            CreatedAt = cart.CreatedAt,
            Items = cart.Items.Select(item => new ProcessedCartItem
            {
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                OriginalUnitPrice = item.UnitPrice,
                ActualUnitPrice = item.ActualUnitPrice,
                Quantity = item.Quantity,
                Category = item.Category,
                Brand = item.Brand,
                Tags = item.Tags,
                Properties = item.Properties,
                IsGift = item.IsGift,
                GiftSourceRuleId = item.GiftSourceRuleId,
                AppliedPromotions = []
            }).ToList()
        };

        return processedCart;
    }

    /// <summary>
    /// 验证规则迁移的一致性
    /// </summary>
    public static ValidationResult ValidateMigrationConsistency(
        PromotionRuleBase oldRule, 
        INewPromotionRule newRule)
    {
        var errors = new List<string>();
        var warnings = new List<string>();

        // 基本属性验证
        if (oldRule.Id != newRule.Id)
            errors.Add($"规则ID不一致: {oldRule.Id} vs {newRule.Id}");

        if (oldRule.Name != newRule.Name)
            warnings.Add($"规则名称不一致: {oldRule.Name} vs {newRule.Name}");

        if (oldRule.Priority != newRule.Priority)
            warnings.Add($"优先级不一致: {oldRule.Priority} vs {newRule.Priority}");

        if (oldRule.IsEnabled != newRule.IsEnabled)
            warnings.Add($"启用状态不一致: {oldRule.IsEnabled} vs {newRule.IsEnabled}");

        if (errors.Count > 0)
            return ValidationResult.Failure(errors.ToArray());

        if (warnings.Count > 0)
            return ValidationResult.SuccessWithWarnings(warnings.ToArray());

        return ValidationResult.Success();
    }
}
