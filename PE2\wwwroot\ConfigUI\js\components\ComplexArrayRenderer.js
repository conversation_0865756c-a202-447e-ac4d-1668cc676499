/**
 * 复杂数组字段渲染器
 * 专门处理array-complex类型字段，如组合条件、阶梯条件等
 */

const ComplexArrayRenderer = {
    name: 'ComplexArrayRenderer',
    components: {
        'product-selector': ProductSelector
    },
    props: {
        fieldName: {
            type: String,
            required: true
        },
        fieldConfig: {
            type: Object,
            required: true
        },
        modelValue: {
            type: Array,
            default: () => []
        },
        disabled: {
            type: Boolean,
            default: false
        }
    },
    emits: ['update:modelValue'],    setup(props, { emit }) {
        const { ref, computed, watch } = Vue;
        const { ElMessage } = ElementPlus;

        const currentValue = ref([]);
        const showAddDialog = ref(false);
        const editingIndex = ref(-1);
        const editingItem = ref({});

        // 字段元数据
        const metadata = computed(() => props.fieldConfig.metadata || {});
        const elementSchema = computed(() => {
            const schema = metadata.value.elementSchema || {};
            console.log('ComplexArrayRenderer elementSchema:', schema);
            console.log('elementSchema.fields:', schema.fields);
            return schema;
        });
        const defaultItem = computed(() => metadata.value.defaultItem || {});        // 中文标签映射
        const chineseLabels = {
            // 基本字段
            'productId': '商品ID',
            'productIds': '商品ID列表',
            'requiredQuantity': '必需数量',
            'requiredAmount': '必需金额',
            'minQuantity': '最小数量',
            'minAmount': '最小金额',
            'maxQuantity': '最大数量', 
            'maxAmount': '最大金额',
            'description': '描述',
            'name': '名称',
            
            // 特价相关
            'specialPrice': '特价',
            'specialPriceSelectionStrategy': '特价选择策略',
            
            // 折扣相关
            'discountRate': '折扣率',
            'discountAmount': '折扣金额',
            'discountSelectionStrategy': '折扣选择策略',
            
            // 兑换相关
            'exchangeProductIds': '兑换商品ID',
            'exchangeQuantity': '兑换数量',
            'addAmount': '加价金额',
            
            // 赠品相关
            'giftProductIds': '赠品商品ID',
            'giftQuantity': '赠品数量',
            'couponId': '优惠券ID',
            'couponName': '优惠券名称',
            'couponValue': '优惠券面值',
            'quantity': '数量',
            'validityDays': '有效天数',
            
            // 组合条件字段
            'combinationConditions': '组合条件',
            'buyConditions': '购买条件',
            'exchangeConditions': '兑换条件',
            'freeConditions': '免费条件',
            'giftConditions': '赠品条件',
            
            // 阶梯相关
            'discountTiers': '折扣阶梯',
            'specialPriceTiers': '特价阶梯',
            'giftTiers': '赠品阶梯',
            'progressiveTiers': '递进阶梯'
        };

        // 获取字段中文标签
        const getFieldLabel = (fieldName) => {
            return chineseLabels[fieldName] || fieldName;
        };        // 字段类型渲染配置
        const getFieldRenderConfig = (field) => {
            // 处理嵌套的类型结构
            let type = 'input';
            let isNullable = true;
            let elementType = null;
            
            if (typeof field.type === 'string') {
                type = field.type;
            } else if (typeof field.type === 'object' && field.type !== null) {
                type = field.type.type || 'input';
                isNullable = field.type.isNullable !== false;
                elementType = field.type.elementType;
            }
            
            return {
                type,
                isNullable,
                elementType,
                label: getFieldLabel(field.name),
                required: field.required || false,
                group: field.group || 'condition',
                description: field.description,
                default: field.default
            };
        };

        // 初始化值
        const initValue = () => {
            if (Array.isArray(props.modelValue)) {
                currentValue.value = [...props.modelValue];
            } else {
                currentValue.value = [];
            }
        };        // 根据类型获取默认值
        const getDefaultValueForType = (config) => {
            switch (config.type) {
                case 'input':
                case 'string':
                    return '';
                case 'number':
                case 'decimal':
                    return 0;
                case 'array-simple':
                    return [];
                case 'boolean':
                case 'switch':
                    return false;
                default:
                    return config.default || null;
            }
        };

        // 创建新项（修复版本，处理错误的默认值）
        const createNewItem = () => {
            let newItem = { ...defaultItem.value };
            
            // 修复默认值中的问题
            if (elementSchema.value.fields) {
                elementSchema.value.fields.forEach(field => {
                    const config = getFieldRenderConfig(field);
                    
                    // 如果字段是array-simple但默认值不是数组，则修复它
                    if (config.type === 'array-simple' && !Array.isArray(newItem[field.name])) {
                        newItem[field.name] = [];
                    }
                    
                    // 如果字段值未定义，设置默认值
                    if (newItem[field.name] === undefined) {
                        newItem[field.name] = getDefaultValueForType(config);
                    }
                });
            }
            
            return newItem;
        };

        // 添加新项
        const addItem = () => {
            console.log('addItem called');
            console.log('elementSchema:', elementSchema.value);
            console.log('elementSchema.fields:', elementSchema.value.fields);
            console.log('defaultItem:', defaultItem.value);
            const newItem = createNewItem();
            editingItem.value = newItem;
            editingIndex.value = -1;
            showAddDialog.value = true;
            console.log('showAddDialog set to true');
        };

        // 编辑项
        const editItem = (index) => {
            editingItem.value = { ...currentValue.value[index] };
            editingIndex.value = index;
            showAddDialog.value = true;
        };

        // 删除项
        const removeItem = (index) => {
            currentValue.value.splice(index, 1);
            emitUpdate();
        };

        // 保存编辑
        const saveEdit = () => {
            if (editingIndex.value >= 0) {
                // 编辑模式
                currentValue.value[editingIndex.value] = { ...editingItem.value };
            } else {
                // 新增模式
                currentValue.value.push({ ...editingItem.value });
            }
            
            showAddDialog.value = false;
            editingItem.value = {};
            editingIndex.value = -1;
            emitUpdate();
        };

        // 取消编辑
        const cancelEdit = () => {
            showAddDialog.value = false;
            editingItem.value = {};
            editingIndex.value = -1;
        };

        // 更新字段值
        const updateFieldValue = (fieldName, value) => {
            editingItem.value[fieldName] = value;
        };

        // 发出更新事件
        const emitUpdate = () => {
            emit('update:modelValue', [...currentValue.value]);
        };        // 获取项目显示标题
        const getItemTitle = (item, index) => {
            const schema = elementSchema.value;
            if (!schema.fields || schema.fields.length === 0) {
                return `项目 ${index + 1}`;
            }

            // 尝试使用最重要的字段作为标题
            const titleFields = ['productId', 'name', 'description'];
            for (const fieldName of titleFields) {
                const field = schema.fields.find(f => f.name === fieldName);
                if (field && item[fieldName] !== undefined && item[fieldName] !== null && item[fieldName] !== '') {
                    const label = getFieldLabel(fieldName);
                    const value = Array.isArray(item[fieldName]) ? item[fieldName].join(', ') : item[fieldName];
                    return `${label}: ${value}`;
                }
            }
            
            // 如果没有找到合适的标题字段，使用第一个有值的字段
            for (const field of schema.fields) {
                const value = item[field.name];
                if (value !== undefined && value !== null && value !== '') {
                    const label = getFieldLabel(field.name);
                    const displayValue = Array.isArray(value) ? value.join(', ') : value;
                    return `${label}: ${displayValue}`;
                }
            }
            
            // 使用类型名称加索引
            const typeName = schema.displayName || schema.typeName || '条件';
            return `${typeName} ${index + 1}`;
        };

        // 渲染简单数组字段
        const renderArraySimpleField = (field, value) => {
            if (!Array.isArray(value)) {
                value = [];
            }
            
            return {
                type: 'array-simple',
                value: value,
                label: getFieldLabel(field.name),
                placeholder: `请输入${getFieldLabel(field.name)}`
            };
        };

        // 添加数组项
        const addArrayItem = (fieldName) => {
            if (!editingItem.value[fieldName]) {
                editingItem.value[fieldName] = [];
            }
            editingItem.value[fieldName].push('');
        };

        // 删除数组项
        const removeArrayItem = (fieldName, index) => {
            if (editingItem.value[fieldName] && editingItem.value[fieldName].length > index) {
                editingItem.value[fieldName].splice(index, 1);
            }
        };

        // 更新数组项
        const updateArrayItem = (fieldName, index, value) => {
            if (!editingItem.value[fieldName]) {
                editingItem.value[fieldName] = [];
            }
            while (editingItem.value[fieldName].length <= index) {
                editingItem.value[fieldName].push('');
            }
            editingItem.value[fieldName][index] = value;
        };        // 初始化
        watch(() => props.modelValue, initValue, { immediate: true });

        return {
            currentValue,
            showAddDialog,
            editingIndex,
            editingItem,
            elementSchema,
            getFieldLabel,
            getFieldRenderConfig,
            addItem,
            editItem,
            removeItem,
            saveEdit,
            cancelEdit,
            updateFieldValue,
            getItemTitle,
            renderArraySimpleField,
            addArrayItem,
            removeArrayItem,
            updateArrayItem
        };
    },    template: `
        <div class="complex-array-renderer">
            
            <!-- 数组项列表 -->
            <div v-if="currentValue.length > 0" class="array-items">
                <el-card
                    v-for="(item, index) in currentValue"
                    :key="index"
                    class="array-item-card"
                    shadow="hover"
                >
                    <template #header>
                        <div class="item-header">
                            <span class="item-title">{{ getItemTitle(item, index) }}</span>
                            <div class="item-actions">
                                <el-button 
                                    type="primary" 
                                    size="small" 
                                    icon="Edit"
                                    @click="editItem(index)"
                                    :disabled="disabled"
                                >
                                    编辑
                                </el-button>
                                <el-button 
                                    type="danger" 
                                    size="small" 
                                    icon="Delete"
                                    @click="removeItem(index)"
                                    :disabled="disabled"
                                >
                                    删除
                                </el-button>
                            </div>
                        </div>
                    </template>
                    
                    <!-- 显示项目字段值 -->
                    <div class="item-content">
                        <el-descriptions :column="2" size="small">                            <el-descriptions-item
                                v-for="field in elementSchema.fields"
                                :key="field.name"
                                :label="getFieldLabel(field.name)"
                            >
                                <template v-if="getFieldRenderConfig(field).type === 'array-simple'">
                                    <el-tag 
                                        v-for="(val, idx) in (item[field.name] || [])"
                                        :key="idx"
                                        size="small"
                                        style="margin-right: 4px;"
                                    >
                                        {{ val }}
                                    </el-tag>
                                    <span v-if="!(item[field.name] || []).length" class="text-placeholder">暂无</span>
                                </template>
                                <span v-else>
                                    {{ item[field.name] || '-' }}
                                </span>
                            </el-descriptions-item>
                        </el-descriptions>
                    </div>
                </el-card>
            </div>
            
            <!-- 空状态 -->
            <el-empty 
                v-else 
                description="暂无数据"
                :image-size="100"
            />
            
            <!-- 添加按钮 -->
            <el-button 
                type="primary" 
                icon="Plus"
                @click="addItem"
                :disabled="disabled"
                style="width: 100%; margin-top: 16px;"
            >
                添加{{ elementSchema.displayName || fieldName }}
            </el-button>
            
            <!-- 编辑对话框 -->
            <el-dialog
                :model-value="showAddDialog"
                :title="editingIndex >= 0 ? '编辑条件' : '新增条件'"
                width="600px"
                @close="cancelEdit"
            >
                <el-form
                    :model="editingItem"
                    label-width="120px"
                    label-position="right"
                >
                    <el-form-item
                        v-for="field in elementSchema.fields"
                        :key="field.name"
                        :label="getFieldLabel(field.name)"
                        :required="field.required"                    >                        <!-- 商品选择器 (单选) -->
                        <product-selector
                            v-if="getFieldRenderConfig(field).type === 'product-selector-single' || 
                                 field.name === 'productId' || field.name.toLowerCase().includes('productid')"
                            :model-value="editingItem[field.name]"
                            @update:model-value="value => updateFieldValue(field.name, value)"
                            :placeholder="'请选择' + getFieldLabel(field.name)"
                            :disabled="false"
                        />
                        
                        <!-- 商品选择器 (多选) -->
                        <product-selector
                            v-else-if="getFieldRenderConfig(field).type === 'product-selector-multiple' || 
                                      field.name === 'productIds' || field.name.toLowerCase().includes('productids')"
                            :model-value="editingItem[field.name] || []"
                            @update:model-value="value => updateFieldValue(field.name, value)"
                            :placeholder="'请选择' + getFieldLabel(field.name)"
                            :multiple="true"
                            :disabled="false"
                        />
                        
                        <!-- 字符串输入 -->
                        <el-input
                            v-else-if="getFieldRenderConfig(field).type === 'input'"
                            v-model="editingItem[field.name]"
                            :placeholder="'请输入' + getFieldLabel(field.name)"
                        />
                        
                        <!-- 数字输入 -->
                        <el-input-number
                            v-else-if="getFieldRenderConfig(field).type === 'number'"
                            v-model="editingItem[field.name]"
                            :min="0"
                            :step="0.01"
                            style="width: 100%"
                        />
                        
                        <!-- 简单数组输入 -->
                        <div v-else-if="getFieldRenderConfig(field).type === 'array-simple'" class="array-simple-input">
                            <div 
                                v-for="(item, index) in (editingItem[field.name] || [])"
                                :key="index"
                                class="array-item-input"
                            >
                                <el-input
                                    :model-value="item"
                                    @update:model-value="value => updateArrayItem(field.name, index, value)"
                                    :placeholder="'请输入' + getFieldLabel(field.name)"
                                />
                                <el-button
                                    type="danger"
                                    size="small"
                                    icon="Delete"
                                    @click="removeArrayItem(field.name, index)"
                                />
                            </div>
                            <el-button
                                type="primary"
                                plain
                                icon="Plus"
                                @click="addArrayItem(field.name)"
                                style="width: 100%; margin-top: 8px;"
                            >
                                添加{{ getFieldLabel(field.name) }}
                            </el-button>
                        </div>
                        
                        <!-- 默认字符串输入 -->
                        <el-input
                            v-else
                            v-model="editingItem[field.name]"
                            :placeholder="'请输入' + getFieldLabel(field.name)"
                        />
                    </el-form-item>
                </el-form>
                
                <template #footer>
                    <el-button @click="cancelEdit">取消</el-button>
                    <el-button type="primary" @click="saveEdit">保存</el-button>
                </template>
            </el-dialog>
        </div>
    `
};

// 注册全局组件
if (typeof window !== 'undefined' && window.Vue) {
    window.ComplexArrayRenderer = ComplexArrayRenderer;
}
