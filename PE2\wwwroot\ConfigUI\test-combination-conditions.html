<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组合特价条件配置测试</title>
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3.3.8/dist/vue.global.js"></script>
    
    <!-- Element Plus UI Framework -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    
    <!-- Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.iife.min.js"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei';
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 32px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 16px;
            color: #303133;
        }
        .result-display {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 16px;
            margin-top: 16px;
        }
        .json-display {
            background: #2d3748;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 300px;
            overflow-y: auto;
            font-size: 12px;
        }
        .api-test-section {
            margin-top: 32px;
            border-top: 1px solid #eee;
            padding-top: 24px;
        }
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-indicator.success { background: #52c41a; }
        .status-indicator.error { background: #ff4d4f; }
        .status-indicator.loading { background: #1890ff; }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>组合特价条件配置 - 完整测试</h1>
            
            <div class="test-section">
                <div class="test-title">
                    <span class="status-indicator" :class="status"></span>
                    组合特价条件配置器
                </div>
                <complex-array-renderer
                    field-name="combinationConditions"
                    :field-config="fieldConfig"
                    :model-value="currentValue"
                    @update:model-value="updateValue">
                </complex-array-renderer>
                
                <div class="result-display">
                    <h3>当前配置值：</h3>
                    <div class="json-display">{{ formatJson(currentValue) }}</div>
                </div>
            </div>
            
            <div class="api-test-section">
                <div class="test-title">API元数据测试</div>
                <el-row :gutter="16">
                    <el-col :span="12">
                        <el-button @click="loadMetadata" type="primary" :loading="loading">
                            重新加载元数据
                        </el-button>
                        <el-button @click="testSaveRule" type="success" :loading="saving">
                            测试保存规则
                        </el-button>
                    </el-col>
                </el-row>
                
                <div class="result-display" v-if="apiResponse">
                    <h3>API响应：</h3>
                    <div class="json-display">{{ formatJson(apiResponse) }}</div>
                </div>
                
                <div class="result-display" v-if="metadataDetails">
                    <h3>字段元数据详情：</h3>
                    <div class="json-display">{{ formatJson(metadataDetails) }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载组件 -->
    <script src="./js/components/ComplexArrayRenderer.js"></script>
    
    <script>
        const { createApp } = Vue;
        
        const app = createApp({
            components: {
                'complex-array-renderer': ComplexArrayRenderer
            },
            setup() {
                const { ref, reactive, computed } = Vue;
                
                const loading = ref(false);
                const saving = ref(false);
                const apiResponse = ref(null);
                const metadataDetails = ref(null);
                
                // 字段配置 - 从API获取
                const fieldConfig = ref({
                    name: "combinationConditions",
                    label: "组合条件",
                    type: "array-complex",
                    required: false,
                    group: "condition",
                    metadata: {
                        elementSchema: {
                            typeName: "CombinationSpecialPriceCondition",
                            displayName: "组合特价条件",
                            fields: [
                                {
                                    name: "productId",
                                    label: "商品ID",
                                    type: { type: "input", isNullable: false },
                                    required: true,
                                    group: "condition"
                                },
                                {
                                    name: "requiredQuantity",
                                    label: "必需数量",
                                    type: { type: "number", isNullable: false },
                                    required: true,
                                    group: "condition"
                                },
                                {
                                    name: "requiredAmount",
                                    label: "必需金额",
                                    type: { type: "number", isNullable: false },
                                    required: false,
                                    group: "condition"
                                }
                            ]
                        },
                        defaultItem: {
                            productId: "",
                            requiredQuantity: 1,
                            requiredAmount: 0
                        }
                    }
                });
                
                // 当前值
                const currentValue = ref([
                    {
                        productId: "PROD001",
                        requiredQuantity: 2,
                        requiredAmount: 100.00
                    }
                ]);
                
                // 状态指示器
                const status = computed(() => {
                    if (loading.value || saving.value) return 'loading';
                    if (apiResponse.value && apiResponse.value.error) return 'error';
                    return 'success';
                });
                
                // 更新值
                const updateValue = (newValue) => {
                    currentValue.value = newValue;
                    console.log('Value updated:', newValue);
                };
                
                // 格式化JSON
                const formatJson = (obj) => {
                    return JSON.stringify(obj, null, 2);
                };
                
                // 加载元数据
                const loadMetadata = async () => {
                    loading.value = true;
                    try {
                        const response = await fetch('/api/promotion/metadata/types/CombinationSpecialPriceRule');
                        const data = await response.json();
                        apiResponse.value = data;
                        
                        // 查找组合条件字段
                        const combinationField = data.fields?.find(f => f.name === 'combinationConditions');
                        if (combinationField) {
                            fieldConfig.value = combinationField;
                            metadataDetails.value = combinationField.metadata;
                        }
                        
                        console.log('Metadata loaded:', data);
                    } catch (error) {
                        console.error('Load metadata failed:', error);
                        apiResponse.value = { error: error.message };
                    } finally {
                        loading.value = false;
                    }
                };
                
                // 测试保存规则
                const testSaveRule = async () => {
                    saving.value = true;
                    try {
                        const ruleData = {
                            ruleId: "test_rule_" + Date.now(),
                            ruleName: "测试组合特价规则",
                            ruleType: "CombinationSpecialPriceRule",
                            combinationConditions: currentValue.value,
                            specialPrice: 99.99,
                            isEnabled: true
                        };
                        
                        console.log('Saving rule:', ruleData);
                        
                        // 模拟保存 - 实际项目中应该调用保存API
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        
                        ElementPlus.ElMessage.success('规则保存成功！');
                        apiResponse.value = { success: true, ruleData };
                    } catch (error) {
                        console.error('Save rule failed:', error);
                        apiResponse.value = { error: error.message };
                        ElementPlus.ElMessage.error('规则保存失败：' + error.message);
                    } finally {
                        saving.value = false;
                    }
                };
                
                // 初始化加载元数据
                loadMetadata();
                
                return {
                    fieldConfig,
                    currentValue,
                    loading,
                    saving,
                    apiResponse,
                    metadataDetails,
                    status,
                    updateValue,
                    formatJson,
                    loadMetadata,
                    testSaveRule
                };
            }
        });
        
        // 注册Element Plus
        app.use(ElementPlus);
        
        // 注册Element Plus图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
        
        // 挂载应用
        app.mount('#app');
    </script>
</body>
</html>
