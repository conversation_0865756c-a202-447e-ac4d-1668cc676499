// 促销类型选择器组件
const PromotionTypeSelector = {
  props: {
    selectedType: String,
    selectedCategory: String
  },
  emits: ['type-selected'],
  template: `
    <div class="sidebar">
      <div class="sidebar-header">
        <h2>促销规则配置</h2>
      </div>
      <div class="promotion-categories">
        <div v-for="(category, key) in promotionTypes" :key="key" class="category-group">
          <div class="category-title">{{ category.name }}</div>
          <div 
            v-for="(type, typeKey) in category.types" 
            :key="typeKey"
            class="promotion-type-card"
            :class="{ selected: selectedType === typeKey }"
            @click="selectType(key, typeKey, type)"
          >
            <div class="type-icon">{{ category.icon }}</div>
            <div class="type-info">
              <h4>{{ type.name }}</h4>
              <p>{{ type.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,  data() {
    return {
      promotionTypes: window.PROMOTION_TYPES || {}
    };
  },
  methods: {
    selectType(categoryKey, typeKey, typeData) {
      this.$emit('type-selected', {
        category: categoryKey,
        type: typeKey,
        data: typeData
      });
    }
  }
};
