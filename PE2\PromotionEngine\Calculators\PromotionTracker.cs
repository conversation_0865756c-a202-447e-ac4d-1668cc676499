using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Calculators;

/// <summary>
/// 促销计算过程追踪器
/// </summary>
public class PromotionTracker
{
    private readonly List<CalculationStep> _steps;
    private int _stepCounter;

    public PromotionTracker()
    {
        _steps = new List<CalculationStep>();
        _stepCounter = 0;
    }

    /// <summary>
    /// 重置追踪器
    /// </summary>
    public void Reset()
    {
        _steps.Clear();
        _stepCounter = 0;
    }

    /// <summary>
    /// 添加计算步骤
    /// </summary>
    public void AddStep(StepType type, string description, object? data = null)
    {
        _stepCounter++;
        _steps.Add(new CalculationStep
        {
            StepNumber = _stepCounter,
            Type = type,
            Description = description,
            Data = data,
            Timestamp = DateTime.Now
        });
    }

    /// <summary>
    /// 获取所有计算步骤
    /// </summary>
    public List<CalculationStep> GetSteps()
    {
        return new List<CalculationStep>(_steps);
    }

    /// <summary>
    /// 获取指定类型的步骤
    /// </summary>
    public List<CalculationStep> GetStepsByType(StepType type)
    {
        return _steps.Where(s => s.Type == type).ToList();
    }

    /// <summary>
    /// 获取计算摘要
    /// </summary>
    public CalculationSummary GetSummary()
    {
        var summary = new CalculationSummary();

        var startStep = _steps.FirstOrDefault(s => s.Type == StepType.Start);
        var endStep = _steps.LastOrDefault(s => s.Type == StepType.Complete);

        if (startStep != null && endStep != null)
        {
            summary.TotalDuration = endStep.Timestamp - startStep.Timestamp;
        }

        summary.TotalSteps = _steps.Count;
        summary.RuleFilteringSteps = _steps.Count(s => s.Type == StepType.RuleFiltering);
        summary.ConditionCheckSteps = _steps.Count(s => s.Type == StepType.ConditionCheck);
        summary.PromotionApplicationSteps = _steps.Count(s => s.Type == StepType.PromotionApplication);
        summary.OptimizationSearchSteps = _steps.Count(s => s.Type == StepType.OptimizationSearch);
        summary.ResultComparisonSteps = _steps.Count(s => s.Type == StepType.ResultComparison);
        summary.ExclusivityCheckSteps = _steps.Count(s => s.Type == StepType.ExclusivityCheck);
        summary.ExclusivityConflictSteps = _steps.Count(s => s.Type == StepType.ExclusivityConflict);
        summary.PromotionExcludedSteps = _steps.Count(s => s.Type == StepType.PromotionExcluded);

        return summary;
    }

    /// <summary>
    /// 生成详细的计算报告
    /// </summary>
    public string GenerateDetailedReport()
    {
        var report = new System.Text.StringBuilder();
        report.AppendLine("=== 促销计算详细报告 ===");
        report.AppendLine();

        var summary = GetSummary();
        report.AppendLine($"计算总耗时: {summary.TotalDuration.TotalMilliseconds:F2} 毫秒");
        report.AppendLine($"总步骤数: {summary.TotalSteps}");
        report.AppendLine($"规则筛选步骤: {summary.RuleFilteringSteps}");
        report.AppendLine($"条件检查步骤: {summary.ConditionCheckSteps}");
        report.AppendLine($"促销应用步骤: {summary.PromotionApplicationSteps}");
        report.AppendLine($"优化搜索步骤: {summary.OptimizationSearchSteps}");
        report.AppendLine($"结果比较步骤: {summary.ResultComparisonSteps}");
        report.AppendLine($"互斥性检查步骤: {summary.ExclusivityCheckSteps}");
        report.AppendLine($"互斥冲突步骤: {summary.ExclusivityConflictSteps}");
        report.AppendLine($"促销排除步骤: {summary.PromotionExcludedSteps}");
        report.AppendLine();

        report.AppendLine("=== 详细步骤 ===");
        foreach (var step in _steps)
        {
            report.AppendLine($"[{step.StepNumber:D3}] {step.Timestamp:HH:mm:ss.fff} [{step.Type}] {step.Description}");

            if (step.Data != null)
            {
                var dataStr = FormatStepData(step.Data);
                if (!string.IsNullOrEmpty(dataStr))
                {
                    report.AppendLine($"     数据: {dataStr}");
                }
            }
        }

        return report.ToString();
    }

    /// <summary>
    /// 格式化步骤数据
    /// </summary>
    private string FormatStepData(object data)
    {
        return data switch
        {
            string str => str,
            int num => num.ToString(),
            decimal dec => dec.ToString("C"),
            DateTime dt => dt.ToString("yyyy-MM-dd HH:mm:ss"),
            IEnumerable<string> strings => string.Join(", ", strings),
            _ => data.ToString() ?? string.Empty
        };
    }
}

/// <summary>
/// 计算摘要
/// </summary>
public class CalculationSummary
{
    /// <summary>
    /// 总耗时
    /// </summary>
    public TimeSpan TotalDuration { get; set; }

    /// <summary>
    /// 总步骤数
    /// </summary>
    public int TotalSteps { get; set; }

    /// <summary>
    /// 规则筛选步骤数
    /// </summary>
    public int RuleFilteringSteps { get; set; }

    /// <summary>
    /// 条件检查步骤数
    /// </summary>
    public int ConditionCheckSteps { get; set; }

    /// <summary>
    /// 促销应用步骤数
    /// </summary>
    public int PromotionApplicationSteps { get; set; }

    /// <summary>
    /// 优化搜索步骤数
    /// </summary>
    public int OptimizationSearchSteps { get; set; }

    /// <summary>
    /// 结果比较步骤数
    /// </summary>
    public int ResultComparisonSteps { get; set; }

    /// <summary>
    /// 互斥性检查步骤数
    /// </summary>
    public int ExclusivityCheckSteps { get; set; }

    /// <summary>
    /// 互斥冲突步骤数
    /// </summary>
    public int ExclusivityConflictSteps { get; set; }

    /// <summary>
    /// 促销排除步骤数
    /// </summary>
    public int PromotionExcludedSteps { get; set; }
}
