[{"$type": "UnifiedGift", "id": "UNIFIED_GIFT_001", "name": "统一送赠品测试 - 买A送B（不翻倍）", "description": "购买A商品1件时，免费送1件B商品", "priority": 70, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "giftSelectionStrategy": "CustomerBenefit", "applicableProductIds": ["A"], "giftProductIds": ["B"], "minQuantity": 1, "minAmount": 0, "giftQuantity": 1, "isByAmount": false, "couponGifts": [], "memberOnly": false}, {"$type": "UnifiedGift", "id": "UNIFIED_GIFT_002", "name": "统一送赠品测试 - 买A送B（翻2倍）", "description": "购买A商品1件时，免费送1件B商品，支持翻倍", "priority": 75, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 2, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "giftSelectionStrategy": "CustomerBenefit", "applicableProductIds": ["A"], "giftProductIds": ["B"], "minQuantity": 1, "minAmount": 0, "giftQuantity": 1, "isByAmount": false, "couponGifts": [], "memberOnly": false}, {"$type": "TieredGift", "id": "TIERED_GIFT_001", "name": "梯度送赠品测试 - 梯度送（不翻倍）", "description": "购买A商品大于等于1件时，送1件B商品；购买A商品大于等于2件时，送2件C商品", "priority": 80, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "giftSelectionStrategy": "CustomerBenefit", "applicableProductIds": ["A"], "giftStrategy": "ByTier", "isByAmount": false, "memberOnly": false, "giftTiers": [{"minQuantity": 1, "minAmount": 0, "giftProductIds": ["B"], "giftQuantity": 1, "couponGifts": [], "description": "满1件送1件B商品"}, {"minQuantity": 2, "minAmount": 0, "giftProductIds": ["C"], "giftQuantity": 2, "couponGifts": [], "description": "满2件送2件C商品"}]}, {"$type": "TieredGift", "id": "TIERED_GIFT_002", "name": "梯度送赠品测试 - 全部送（翻2倍）", "description": "购买A商品大于等于1件时，送1件B商品；购买A商品大于等于2件时，送2件C商品，全部送策略", "priority": 85, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 2, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "giftSelectionStrategy": "CustomerBenefit", "applicableProductIds": ["A"], "giftStrategy": "AllTiers", "isByAmount": false, "memberOnly": false, "giftTiers": [{"minQuantity": 1, "minAmount": 0, "giftProductIds": ["B"], "giftQuantity": 1, "couponGifts": [], "description": "满1件送1件B商品"}, {"minQuantity": 2, "minAmount": 0, "giftProductIds": ["C"], "giftQuantity": 2, "couponGifts": [], "description": "满2件送2件C商品"}]}, {"$type": "CombinationGift", "id": "COMBINATION_GIFT_001", "name": "组合送赠品测试 - 买A+B送C（不翻倍）", "description": "购买A商品和B商品各1件时，送1件C商品", "priority": 90, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "giftSelectionStrategy": "CustomerBenefit", "giftProductIds": ["C"], "giftQuantity": 1, "couponGifts": [], "memberOnly": false, "combinationConditions": [{"productId": "A", "requiredQuantity": 1, "requiredAmount": 0}, {"productId": "B", "requiredQuantity": 1, "requiredAmount": 0}]}, {"$type": "CombinationGift", "id": "COMBINATION_GIFT_002", "name": "组合送赠品测试 - 买A+B送C（翻2倍）", "description": "购买A商品和B商品各1件时，送1件C商品，支持翻倍", "priority": 95, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 2, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "giftSelectionStrategy": "CustomerBenefit", "giftProductIds": ["C"], "giftQuantity": 1, "couponGifts": [], "memberOnly": false, "combinationConditions": [{"productId": "A", "requiredQuantity": 1, "requiredAmount": 0}, {"productId": "B", "requiredQuantity": 1, "requiredAmount": 0}]}, {"$type": "UnifiedGift", "id": "UNIFIED_GIFT_COUPON_001", "name": "统一送券测试 - 买A送券（仅限会员）", "description": "购买A商品1件时，送100元优惠券，仅限会员", "priority": 60, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "giftSelectionStrategy": "CustomerBenefit", "applicableProductIds": ["A"], "giftProductIds": [], "minQuantity": 1, "minAmount": 0, "giftQuantity": 0, "isByAmount": false, "memberOnly": true, "couponGifts": [{"couponId": "COUPON_100", "couponName": "100元优惠券", "couponValue": 100.0, "quantity": 1, "validityDays": 30, "description": "购买A商品送100元优惠券"}]}, {"$type": "UnifiedGift", "id": "UNIFIED_GIFT_AMOUNT_001", "name": "按金额统一送赠品测试 - 满1000元送B", "description": "购买A商品满1000元时，送1件B商品", "priority": 65, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "giftSelectionStrategy": "CustomerBenefit", "applicableProductIds": ["A"], "giftProductIds": ["B"], "minQuantity": 0, "minAmount": 1000.0, "giftQuantity": 1, "isByAmount": true, "couponGifts": [], "memberOnly": false}]