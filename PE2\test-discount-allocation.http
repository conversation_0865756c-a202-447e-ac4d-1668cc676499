### 折扣分摊和促销追踪功能测试

### 1. 基础折扣分摊测试 - A:1, B:2, C:5
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "DISCOUNT_TEST_001",
  "customerId": "CUSTOMER_001",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 50.00,
        "category": "电子产品",
        "barcode": "1234567890001",
        "brand": "品牌A"
      },
      "quantity": 1,
      "unitPrice": 50.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 30.00,
        "category": "服装",
        "barcode": "1234567890002",
        "brand": "品牌B"
      },
      "quantity": 2,
      "unitPrice": 30.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 20.00,
        "category": "家居",
        "barcode": "1234567890003",
        "brand": "品牌C"
      },
      "quantity": 5,
      "unitPrice": 20.00
    }
  ]
}

### 2. 复杂场景折扣分摊测试 - 多种商品和促销
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "DISCOUNT_TEST_002",
  "customerId": "VIP_CUSTOMER",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "高端手机",
        "price": 3000.00,
        "category": "电子产品"
      },
      "quantity": 2,
      "unitPrice": 3000.00
    },
    {
      "product": {
        "id": "B",
        "name": "时尚T恤",
        "price": 150.00,
        "category": "服装"
      },
      "quantity": 4,
      "unitPrice": 150.00
    },
    {
      "product": {
        "id": "C",
        "name": "家用电器",
        "price": 800.00,
        "category": "家居"
      },
      "quantity": 3,
      "unitPrice": 800.00
    },
    {
      "product": {
        "id": "D",
        "name": "运动鞋",
        "price": 500.00,
        "category": "服装"
      },
      "quantity": 2,
      "unitPrice": 500.00
    }
  ]
}

### 3. 买赠促销测试 - 验证赠品处理
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "GIFT_TEST_001",
  "customerId": "CUSTOMER_GIFT",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 50.00,
        "category": "电子产品"
      },
      "quantity": 2,
      "unitPrice": 50.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 30.00,
        "category": "服装"
      },
      "quantity": 3,
      "unitPrice": 30.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 20.00,
        "category": "家居"
      },
      "quantity": 6,
      "unitPrice": 20.00
    }
  ]
}

### 4. 促销方案比较测试
POST http://localhost:5213/api/promotionanalysis/compare-scenarios
Content-Type: application/json

{
  "cart": {
    "id": "COMPARE_TEST_001",
    "customerId": "CUSTOMER_COMPARE",
    "items": [
      {
        "product": {
          "id": "A",
          "name": "商品A",
          "price": 50.00,
          "category": "电子产品"
        },
        "quantity": 1,
        "unitPrice": 50.00
      },
      {
        "product": {
          "id": "B",
          "name": "商品B",
          "price": 30.00,
          "category": "服装"
        },
        "quantity": 2,
        "unitPrice": 30.00
      },
      {
        "product": {
          "id": "C",
          "name": "商品C",
          "price": 20.00,
          "category": "家居"
        },
        "quantity": 5,
        "unitPrice": 20.00
      }
    ]
  },
  "specificRuleIds": ["RULE001", "RULE002", "RULE003"]
}

### 5. 四舍五入测试 - 使用特殊价格
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "ROUNDING_TEST_001",
  "customerId": "CUSTOMER_ROUNDING",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 33.33,
        "category": "电子产品"
      },
      "quantity": 3,
      "unitPrice": 33.33
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 66.67,
        "category": "服装"
      },
      "quantity": 2,
        "unitPrice": 66.67
    }
  ]
}

### 6. 高价值订单测试 - 验证总额折扣分摊
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "HIGH_VALUE_TEST_001",
  "customerId": "VIP_CUSTOMER_001",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "奢侈品A",
        "price": 2000.00,
        "category": "电子产品"
      },
      "quantity": 1,
      "unitPrice": 2000.00
    },
    {
      "product": {
        "id": "B",
        "name": "奢侈品B",
        "price": 1500.00,
        "category": "服装"
      },
      "quantity": 2,
      "unitPrice": 1500.00
    },
    {
      "product": {
        "id": "C",
        "name": "奢侈品C",
        "price": 1000.00,
        "category": "家居"
      },
      "quantity": 1,
      "unitPrice": 1000.00
    }
  ]
}

### 7. 单商品多促销测试
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "MULTI_PROMO_TEST_001",
  "customerId": "CUSTOMER_MULTI",
  "items": [
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 30.00,
        "category": "服装"
      },
      "quantity": 10,
      "unitPrice": 30.00
    }
  ]
}

### 8. 空购物车测试
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "EMPTY_CART_TEST",
  "customerId": "CUSTOMER_EMPTY",
  "items": []
}

### 9. 获取原始促销计算结果（对比用）
POST http://localhost:5213/api/promotion/calculate
Content-Type: application/json

{
  "id": "ORIGINAL_TEST_001",
  "customerId": "CUSTOMER_ORIGINAL",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 50.00,
        "category": "电子产品"
      },
      "quantity": 1,
      "unitPrice": 50.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 30.00,
        "category": "服装"
      },
      "quantity": 2,
      "unitPrice": 30.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 20.00,
        "category": "家居"
      },
      "quantity": 5,
      "unitPrice": 20.00
    }
  ]
}

### 10. 验证促销规则状态
GET http://localhost:5213/api/promotionrule/valid
