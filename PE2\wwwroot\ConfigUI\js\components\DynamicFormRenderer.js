/**
 * 动态表单渲染器组件
 * 基于后端反射API返回的元数据动态生成促销规则配置表单
 * 支持所有促销类型的配置，实现完全的动态化
 */

const DynamicFormRenderer = {
    name: 'DynamicFormRenderer',
    props: {
        modelValue: {
            type: Object,
            default: () => ({})
        },
        promotionTypes: {
            type: Object,
            default: () => ({})
        },
        selectedCategory: String,
        selectedType: String,
        mode: {
            type: String,
            default: 'create', // create, edit, view
            validator: (value) => ['create', 'edit', 'view'].includes(value)
        }
    },
    emits: ['update:modelValue', 'submit', 'cancel'],
    setup(props, { emit }) {
        const { ref, computed, watch, nextTick } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        // 表单数据
        const formData = ref({});
        const formRef = ref(null);
        const loading = ref(false);
        const validationRules = ref({});

        // 当前选中的促销类型详细信息
        const currentTypeInfo = computed(() => {
            if (!props.selectedCategory || !props.selectedType) return null;
            
            const category = props.promotionTypes[props.selectedCategory];
            if (!category || !category.types) return null;
            
            return category.types[props.selectedType];
        });

        // 表单字段配置
        const formFields = computed(() => {
            if (!currentTypeInfo.value) return [];
            return currentTypeInfo.value.fields || [];
        });

        // 分组字段
        const groupedFields = computed(() => {
            const groups = {};
            formFields.value.forEach(field => {
                const groupName = field.group || '基本信息';
                if (!groups[groupName]) {
                    groups[groupName] = [];
                }
                groups[groupName].push(field);
            });
            return groups;
        });

        // 初始化表单数据
        const initFormData = () => {
            const newFormData = { ...props.modelValue };
            
            // 设置默认值
            formFields.value.forEach(field => {
                if (newFormData[field.name] === undefined) {
                    newFormData[field.name] = getFieldDefaultValue(field);
                }
            });

            // 确保必要的基础字段
            if (!newFormData.ruleId) {
                newFormData.ruleId = generateRuleId();
            }
            if (!newFormData.ruleName) {
                newFormData.ruleName = generateRuleName();
            }
            if (!newFormData.ruleType && currentTypeInfo.value) {
                newFormData.ruleType = currentTypeInfo.value.ruleType;
            }

            formData.value = newFormData;
            emit('update:modelValue', newFormData);
        };

        // 获取字段默认值
        const getFieldDefaultValue = (field) => {
            if (field.defaultValue !== undefined) {
                return field.defaultValue;
            }

            switch (field.type) {
                case 'string':
                case 'text':
                    return '';
                case 'number':
                case 'decimal':
                case 'integer':
                    return field.min || 0;
                case 'boolean':
                    return false;
                case 'array':
                    return [];
                case 'object':
                    return {};
                case 'date':
                case 'datetime':
                    return null;
                case 'select':
                case 'enum':
                    return field.options && field.options.length > 0 ? field.options[0].value : '';
                default:
                    return null;
            }
        };

        // 生成规则ID
        const generateRuleId = () => {
            const timestamp = Date.now();
            const random = Math.floor(Math.random() * 1000);
            return `rule_${timestamp}_${random}`;
        };

        // 生成规则名称
        const generateRuleName = () => {
            if (!currentTypeInfo.value) return '新促销规则';
            const typeName = currentTypeInfo.value.name;
            const timestamp = new Date().toLocaleString('zh-CN', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            }).replace(/[\/\s:]/g, '');
            return `${typeName}_${timestamp}`;
        };

        // 生成验证规则
        const generateValidationRules = () => {
            const rules = {};
            formFields.value.forEach(field => {
                const fieldRules = [];

                // 必填验证
                if (field.required) {
                    fieldRules.push({
                        required: true,
                        message: `${field.displayName || field.name}不能为空`,
                        trigger: ['blur', 'change']
                    });
                }

                // 类型验证
                switch (field.type) {
                    case 'number':
                    case 'decimal':
                    case 'integer':
                        fieldRules.push({
                            type: 'number',
                            message: `${field.displayName || field.name}必须是数字`,
                            trigger: ['blur', 'change']
                        });
                        
                        // 范围验证
                        if (field.min !== undefined || field.max !== undefined) {
                            fieldRules.push({
                                validator: (rule, value, callback) => {
                                    if (value === '' || value === null || value === undefined) {
                                        callback();
                                        return;
                                    }
                                    const num = Number(value);
                                    if (field.min !== undefined && num < field.min) {
                                        callback(new Error(`${field.displayName || field.name}不能小于${field.min}`));
                                        return;
                                    }
                                    if (field.max !== undefined && num > field.max) {
                                        callback(new Error(`${field.displayName || field.name}不能大于${field.max}`));
                                        return;
                                    }
                                    callback();
                                },
                                trigger: ['blur', 'change']
                            });
                        }
                        break;

                    case 'string':
                    case 'text':
                        // 长度验证
                        if (field.minLength || field.maxLength) {
                            fieldRules.push({
                                min: field.minLength || 0,
                                max: field.maxLength || Infinity,
                                message: `${field.displayName || field.name}长度应在${field.minLength || 0}-${field.maxLength || '无限制'}之间`,
                                trigger: ['blur', 'change']
                            });
                        }

                        // 正则验证
                        if (field.pattern) {
                            fieldRules.push({
                                pattern: new RegExp(field.pattern),
                                message: field.patternMessage || `${field.displayName || field.name}格式不正确`,
                                trigger: ['blur', 'change']
                            });
                        }
                        break;

                    case 'array':
                        // 数组长度验证
                        if (field.minItems !== undefined || field.maxItems !== undefined) {
                            fieldRules.push({
                                validator: (rule, value, callback) => {
                                    if (!Array.isArray(value)) {
                                        callback();
                                        return;
                                    }
                                    if (field.minItems !== undefined && value.length < field.minItems) {
                                        callback(new Error(`至少需要${field.minItems}项`));
                                        return;
                                    }
                                    if (field.maxItems !== undefined && value.length > field.maxItems) {
                                        callback(new Error(`最多允许${field.maxItems}项`));
                                        return;
                                    }
                                    callback();
                                },
                                trigger: ['blur', 'change']
                            });
                        }
                        break;
                }

                if (fieldRules.length > 0) {
                    rules[field.name] = fieldRules;
                }
            });

            validationRules.value = rules;
        };        // 渲染表单项
        const renderFormItem = (field) => {
            const { h } = Vue;
            const isReadonly = props.mode === 'view' || field.readonly;
            const fieldProps = {
                disabled: isReadonly,
                placeholder: field.placeholder || `请输入${field.displayName || field.name}`,
                ...field.props
            };

            switch (field.type) {
                case 'string':
                case 'text':
                    return h(ElInput, {
                        ...fieldProps,
                        modelValue: formData.value[field.name],
                        'onUpdate:modelValue': (value) => updateFieldValue(field.name, value),
                        type: field.subType === 'textarea' ? 'textarea' : 'text',
                        showWordLimit: !!field.maxLength,
                        maxlength: field.maxLength,
                        rows: field.rows || 3
                    });

                case 'number':
                case 'decimal':
                case 'integer':
                    return h(ElInputNumber, {
                        ...fieldProps,
                        modelValue: formData.value[field.name],
                        'onUpdate:modelValue': (value) => updateFieldValue(field.name, value),
                        min: field.min,
                        max: field.max,
                        step: field.step || (field.type === 'integer' ? 1 : 0.01),
                        precision: field.precision,
                        controlsPosition: 'right',
                        style: { width: '100%' }
                    });

                case 'boolean':
                    return h(ElSwitch, {
                        ...fieldProps,
                        modelValue: formData.value[field.name],
                        'onUpdate:modelValue': (value) => updateFieldValue(field.name, value),
                        activeText: field.activeText || '是',
                        inactiveText: field.inactiveText || '否'
                    });

                case 'select':
                case 'enum':
                    return h(ElSelect, {
                        ...fieldProps,
                        modelValue: formData.value[field.name],
                        'onUpdate:modelValue': (value) => updateFieldValue(field.name, value),
                        clearable: !field.required,
                        filterable: field.filterable !== false,
                        multiple: field.multiple
                    }, {
                        default: () => (field.options || []).map(option => 
                            h(ElOption, {
                                key: option.value,
                                label: option.label,
                                value: option.value,
                                disabled: option.disabled
                            })
                        )
                    });

                case 'date':
                    return h(ElDatePicker, {
                        ...fieldProps,
                        modelValue: formData.value[field.name],
                        'onUpdate:modelValue': (value) => updateFieldValue(field.name, value),
                        type: 'date',
                        format: 'YYYY-MM-DD',
                        valueFormat: 'YYYY-MM-DD',
                        style: { width: '100%' }
                    });

                case 'datetime':
                    return h(ElDatePicker, {
                        ...fieldProps,
                        modelValue: formData.value[field.name],
                        'onUpdate:modelValue': (value) => updateFieldValue(field.name, value),
                        type: 'datetime',
                        format: 'YYYY-MM-DD HH:mm:ss',
                        valueFormat: 'YYYY-MM-DD HH:mm:ss',
                        style: { width: '100%' }
                    });

                case 'array':
                    return renderArrayField(field);

                case 'object':
                    return renderObjectField(field);

                case 'product-selector':
                    return renderProductSelector(field);

                case 'rule-condition':
                    return renderRuleCondition(field);

                default:
                    return h(ElInput, {
                        ...fieldProps,
                        modelValue: formData.value[field.name],
                        'onUpdate:modelValue': (value) => updateFieldValue(field.name, value)
                    });
            }
        };        // 渲染数组字段
        const renderArrayField = (field) => {
            const { h } = Vue;
            const items = formData.value[field.name] || [];
            
            return h('div', { class: 'dynamic-array-field' }, [
                // 数组项列表
                items.map((item, index) => 
                    h('div', { 
                        key: index, 
                        class: 'array-item',
                        style: { 
                            display: 'flex', 
                            alignItems: 'center', 
                            marginBottom: '8px',
                            padding: '8px',
                            border: '1px solid var(--ant-border-color-base)',
                            borderRadius: 'var(--ant-border-radius-base)'
                        }
                    }, [
                        h('div', { style: { flex: 1, marginRight: '8px' } }, [
                            renderArrayItemField(field, item, index)
                        ]),
                        h('button', {
                            type: 'button',
                            class: 'el-button el-button--danger el-button--small is-circle',
                            onClick: () => removeArrayItem(field.name, index),
                            disabled: props.mode === 'view'
                        }, [
                            h('i', { class: 'el-icon-delete' })
                        ])
                    ])
                ),
                // 添加按钮
                props.mode !== 'view' && h('button', {
                    type: 'button',
                    class: 'el-button el-button--dashed',
                    onClick: () => addArrayItem(field.name, field),
                    style: { width: '100%', marginTop: '8px' }
                }, [
                    h('i', { class: 'el-icon-plus', style: { marginRight: '4px' } }),
                    `添加${field.displayName || field.name}`
                ])
            ]);
        };        // 渲染数组项字段
        const renderArrayItemField = (field, item, index) => {
            const { h } = Vue;
            const itemType = field.itemType || 'string';
            
            switch (itemType) {
                case 'string':
                    return h('input', {
                        class: 'el-input__inner',
                        value: item,
                        onInput: (e) => updateArrayItem(field.name, index, e.target.value),
                        placeholder: `请输入${field.displayName || field.name}`
                    });
                
                case 'number':
                    return h('input', {
                        class: 'el-input__inner',
                        type: 'number',
                        value: item,
                        onInput: (e) => updateArrayItem(field.name, index, Number(e.target.value)),
                        style: { width: '100%' }
                    });
                
                case 'object':
                    return renderObjectField({
                        ...field.itemSchema,
                        name: `${field.name}[${index}]`
                    }, item, (value) => updateArrayItem(field.name, index, value));
                
                default:
                    return h('input', {
                        class: 'el-input__inner',
                        value: item,
                        onInput: (e) => updateArrayItem(field.name, index, e.target.value)
                    });
            }
        };        // 渲染对象字段
        const renderObjectField = (field, value = null, onUpdate = null) => {
            const { h } = Vue;
            const objectValue = value || formData.value[field.name] || {};
            
            return h('textarea', {
                class: 'el-textarea__inner',
                value: JSON.stringify(objectValue, null, 2),
                onInput: (e) => {
                    try {
                        const parsed = JSON.parse(e.target.value);
                        if (onUpdate) {
                            onUpdate(parsed);
                        } else {
                            updateFieldValue(field.name, parsed);
                        }
                    } catch (error) {
                        // JSON解析错误，暂时不更新
                    }
                },
                placeholder: `请输入${field.displayName || field.name}的JSON配置`,
                rows: 6,
                style: { width: '100%', fontFamily: 'monospace' }
            });
        };

        // 渲染商品选择器
        const renderProductSelector = (field) => {
            const { h } = Vue;
            return h('div', { class: 'product-selector-placeholder' }, [
                h('textarea', {
                    class: 'el-textarea__inner',
                    value: JSON.stringify(formData.value[field.name] || [], null, 2),
                    onInput: (e) => {
                        try {
                            const parsed = JSON.parse(e.target.value);
                            updateFieldValue(field.name, parsed);
                        } catch (e) {
                            // 处理JSON解析错误
                        }
                    },
                    rows: 3,
                    placeholder: '商品选择器（JSON格式）',
                    style: { width: '100%', fontFamily: 'monospace' }
                })
            ]);
        };

        // 渲染规则条件
        const renderRuleCondition = (field) => {
            const { h } = Vue;
            return h('div', { class: 'rule-condition-placeholder' }, [
                h('textarea', {
                    class: 'el-textarea__inner',
                    value: JSON.stringify(formData.value[field.name] || {}, null, 2),
                    onInput: (e) => {
                        try {
                            const parsed = JSON.parse(e.target.value);
                            updateFieldValue(field.name, parsed);
                        } catch (e) {
                            // 处理JSON解析错误
                        }
                    },
                    rows: 4,
                    placeholder: '规则条件（JSON格式）',
                    style: { width: '100%', fontFamily: 'monospace' }
                })
            ]);
        };

        // 更新字段值
        const updateFieldValue = (fieldName, value) => {
            formData.value[fieldName] = value;
            emit('update:modelValue', { ...formData.value });
        };

        // 添加数组项
        const addArrayItem = (fieldName, field) => {
            const items = [...(formData.value[fieldName] || [])];
            items.push(getFieldDefaultValue({ type: field.itemType || 'string' }));
            updateFieldValue(fieldName, items);
        };

        // 删除数组项
        const removeArrayItem = (fieldName, index) => {
            const items = [...(formData.value[fieldName] || [])];
            items.splice(index, 1);
            updateFieldValue(fieldName, items);
        };

        // 更新数组项
        const updateArrayItem = (fieldName, index, value) => {
            const items = [...(formData.value[fieldName] || [])];
            items[index] = value;
            updateFieldValue(fieldName, items);
        };

        // 表单提交
        const handleSubmit = async () => {
            if (!formRef.value) return;
            
            try {
                await formRef.value.validate();
                emit('submit', { ...formData.value });
            } catch (error) {
                ElMessage.error('表单验证失败，请检查输入内容');
            }
        };

        // 表单重置
        const handleReset = () => {
            if (formRef.value) {
                formRef.value.resetFields();
            }
            initFormData();
        };

        // 监听属性变化
        watch(() => props.modelValue, (newValue) => {
            formData.value = { ...newValue };
        }, { deep: true });

        watch(() => [props.selectedCategory, props.selectedType], () => {
            nextTick(() => {
                initFormData();
                generateValidationRules();
            });
        });

        // 初始化
        Vue.onMounted(() => {
            initFormData();
            generateValidationRules();
        });

        return {
            formData,
            formRef,
            loading,
            validationRules,
            currentTypeInfo,
            groupedFields,
            renderFormItem,
            handleSubmit,
            handleReset
        };
    },
    template: `
        <div class="dynamic-form-renderer">
            <el-form
                ref="formRef"
                :model="formData"
                :rules="validationRules"
                label-width="120px"
                label-position="right"
                :disabled="mode === 'view'"
                v-loading="loading"
            >
                <!-- 表单标题 -->
                <div v-if="currentTypeInfo" class="form-header">
                    <h3>{{ currentTypeInfo.name }}</h3>
                    <p class="form-description">{{ currentTypeInfo.description }}</p>
                </div>

                <!-- 分组表单项 -->
                <template v-for="(fields, groupName) in groupedFields" :key="groupName">
                    <el-divider content-position="left">
                        <span class="group-title">{{ groupName }}</span>
                    </el-divider>
                    
                    <el-row :gutter="16">
                        <template v-for="field in fields" :key="field.name">
                            <el-col :span="field.span || 24">
                                <el-form-item
                                    :label="field.displayName || field.name"
                                    :prop="field.name"
                                    :required="field.required"
                                >
                                    <template #label>
                                        <span>{{ field.displayName || field.name }}</span>
                                        <el-tooltip
                                            v-if="field.description"
                                            :content="field.description"
                                            placement="top"
                                        >
                                            <el-icon style="margin-left: 4px; color: #999;">
                                                <QuestionFilled />
                                            </el-icon>
                                        </el-tooltip>
                                    </template>
                                    
                                    <component :is="renderFormItem(field)" />
                                </el-form-item>
                            </el-col>
                        </template>
                    </el-row>
                </template>

                <!-- 表单操作 -->
                <el-form-item v-if="mode !== 'view'" style="margin-top: 32px;">
                    <el-button type="primary" @click="handleSubmit" :loading="loading">
                        {{ mode === 'edit' ? '更新规则' : '创建规则' }}
                    </el-button>
                    <el-button @click="handleReset">重置</el-button>
                    <el-button @click="$emit('cancel')">取消</el-button>
                </el-form-item>
            </el-form>
        </div>
    `,
    style: `
        .dynamic-form-renderer {
            padding: 24px;
            background: var(--ant-component-background);
            border-radius: var(--ant-border-radius-base);
        }

        .form-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--ant-border-color-split);
        }

        .form-header h3 {
            margin: 0 0 8px 0;
            font-size: 18px;
            font-weight: 600;
            color: var(--ant-text-color);
        }

        .form-description {
            margin: 0;
            font-size: 14px;
            color: var(--ant-text-color-secondary);
        }

        .group-title {
            font-weight: 600;
            color: var(--ant-text-color);
        }

        .nested-form-item {
            margin-bottom: 16px;
        }

        .dynamic-array-field {
            width: 100%;
        }

        .array-item {
            background: #fafafa;
        }

        .dynamic-object-field {
            border: 1px solid var(--ant-border-color-base);
            border-radius: var(--ant-border-radius-base);
            padding: 16px;
            background: #fafafa;
        }

        .product-selector-placeholder,
        .rule-condition-placeholder {
            width: 100%;
        }
    `
};

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DynamicFormRenderer;
} else if (typeof window !== 'undefined') {
    window.DynamicFormRenderer = DynamicFormRenderer;
}
