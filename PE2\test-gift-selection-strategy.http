### 赠品选择策略测试

### 1. 客户利益最大化 - 统一送赠品（从B、C、D中选择价值最高的）
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "CUSTOMER_BENEFIT_TEST_001",
  "customerId": "CUSTOMER_001",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 1,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B（低价值）",
        "price": 50.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 50.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C（中价值）",
        "price": 80.00,
        "category": "家居"
      },
      "quantity": 1,
      "unitPrice": 80.00
    },
    {
      "product": {
        "id": "D",
        "name": "商品D（高价值）",
        "price": 120.00,
        "category": "数码"
      },
      "quantity": 1,
      "unitPrice": 120.00
    }
  ]
}

### 2. 商家利益最大化 - 统一送赠品（从B、C、D中选择价值最低的）
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "MERCHANT_BENEFIT_TEST_001",
  "customerId": "CUSTOMER_002",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 1,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B（低价值）",
        "price": 50.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 50.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C（中价值）",
        "price": 80.00,
        "category": "家居"
      },
      "quantity": 1,
      "unitPrice": 80.00
    },
    {
      "product": {
        "id": "D",
        "name": "商品D（高价值）",
        "price": 120.00,
        "category": "数码"
      },
      "quantity": 1,
      "unitPrice": 120.00
    }
  ]
}

### 3. 梯度赠品 - 客户利益最大化（选择价值最高的赠品）
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "GRADIENT_CUSTOMER_BENEFIT_TEST",
  "customerId": "CUSTOMER_003",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 2,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B（低价值）",
        "price": 30.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 30.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C（高价值）",
        "price": 90.00,
        "category": "家居"
      },
      "quantity": 1,
      "unitPrice": 90.00
    },
    {
      "product": {
        "id": "D",
        "name": "商品D（低价值）",
        "price": 40.00,
        "category": "数码"
      },
      "quantity": 1,
      "unitPrice": 40.00
    },
    {
      "product": {
        "id": "E",
        "name": "商品E（中价值）",
        "price": 60.00,
        "category": "运动"
      },
      "quantity": 1,
      "unitPrice": 60.00
    },
    {
      "product": {
        "id": "F",
        "name": "商品F（高价值）",
        "price": 100.00,
        "category": "美妆"
      },
      "quantity": 1,
      "unitPrice": 100.00
    }
  ]
}

### 4. 梯度赠品 - 商家利益最大化（选择价值最低的赠品）
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "GRADIENT_MERCHANT_BENEFIT_TEST",
  "customerId": "CUSTOMER_004",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 2,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B（低价值）",
        "price": 30.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 30.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C（高价值）",
        "price": 90.00,
        "category": "家居"
      },
      "quantity": 1,
      "unitPrice": 90.00
    },
    {
      "product": {
        "id": "D",
        "name": "商品D（低价值）",
        "price": 40.00,
        "category": "数码"
      },
      "quantity": 1,
      "unitPrice": 40.00
    },
    {
      "product": {
        "id": "E",
        "name": "商品E（中价值）",
        "price": 60.00,
        "category": "运动"
      },
      "quantity": 1,
      "unitPrice": 60.00
    },
    {
      "product": {
        "id": "F",
        "name": "商品F（高价值）",
        "price": 100.00,
        "category": "美妆"
      },
      "quantity": 1,
      "unitPrice": 100.00
    }
  ]
}

### 5. 组合赠品 - 客户利益最大化（从C、D、E中选择价值最高的）
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "COMBINATION_CUSTOMER_BENEFIT_TEST",
  "customerId": "CUSTOMER_005",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 1,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 80.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 80.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C（低价值）",
        "price": 40.00,
        "category": "家居"
      },
      "quantity": 1,
      "unitPrice": 40.00
    },
    {
      "product": {
        "id": "D",
        "name": "商品D（中价值）",
        "price": 70.00,
        "category": "数码"
      },
      "quantity": 1,
      "unitPrice": 70.00
    },
    {
      "product": {
        "id": "E",
        "name": "商品E（高价值）",
        "price": 110.00,
        "category": "运动"
      },
      "quantity": 1,
      "unitPrice": 110.00
    }
  ]
}

### 6. 组合赠品 - 商家利益最大化（从C、D、E中选择价值最低的）
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "COMBINATION_MERCHANT_BENEFIT_TEST",
  "customerId": "CUSTOMER_006",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 1,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 80.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 80.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C（低价值）",
        "price": 40.00,
        "category": "家居"
      },
      "quantity": 1,
      "unitPrice": 40.00
    },
    {
      "product": {
        "id": "D",
        "name": "商品D（中价值）",
        "price": 70.00,
        "category": "数码"
      },
      "quantity": 1,
      "unitPrice": 70.00
    },
    {
      "product": {
        "id": "E",
        "name": "商品E（高价值）",
        "price": 110.00,
        "category": "运动"
      },
      "quantity": 1,
      "unitPrice": 110.00
    }
  ]
}

### 7. 多赠品选择 - 客户利益最大化（从B、C、D、E中选择价值最高的2件）
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "MULTI_GIFT_CUSTOMER_BENEFIT_TEST",
  "customerId": "CUSTOMER_007",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 2,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B（低价值）",
        "price": 30.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 30.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C（中价值）",
        "price": 60.00,
        "category": "家居"
      },
      "quantity": 1,
      "unitPrice": 60.00
    },
    {
      "product": {
        "id": "D",
        "name": "商品D（高价值）",
        "price": 90.00,
        "category": "数码"
      },
      "quantity": 1,
      "unitPrice": 90.00
    },
    {
      "product": {
        "id": "E",
        "name": "商品E（最高价值）",
        "price": 120.00,
        "category": "运动"
      },
      "quantity": 1,
      "unitPrice": 120.00
    }
  ]
}

### 8. 多赠品选择 - 商家利益最大化（从B、C、D、E中选择价值最低的2件）
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "MULTI_GIFT_MERCHANT_BENEFIT_TEST",
  "customerId": "CUSTOMER_008",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 2,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B（低价值）",
        "price": 30.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 30.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C（中价值）",
        "price": 60.00,
        "category": "家居"
      },
      "quantity": 1,
      "unitPrice": 60.00
    },
    {
      "product": {
        "id": "D",
        "name": "商品D（高价值）",
        "price": 90.00,
        "category": "数码"
      },
      "quantity": 1,
      "unitPrice": 90.00
    },
    {
      "product": {
        "id": "E",
        "name": "商品E（最高价值）",
        "price": 120.00,
        "category": "运动"
      },
      "quantity": 1,
      "unitPrice": 120.00
    }
  ]
}

### 预期结果验证：
### 1. 客户利益最大化应该选择商品D（120元）作为赠品
### 2. 商家利益最大化应该选择商品B（50元）作为赠品
### 3. 梯度赠品客户利益最大化应该选择商品C（90元）和商品F（100元）
### 4. 梯度赠品商家利益最大化应该选择商品B（30元）和商品D（40元）
### 5. 组合赠品客户利益最大化应该选择商品E（110元）
### 6. 组合赠品商家利益最大化应该选择商品C（40元）
### 7. 多赠品客户利益最大化应该选择商品E（120元）和商品D（90元）
### 8. 多赠品商家利益最大化应该选择商品B（30元）和商品C（60元）
