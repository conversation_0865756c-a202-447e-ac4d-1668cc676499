using PE2.Models;
using PE2.PromotionEngine.Models;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Rules;

/// <summary>
/// 梯度送赠品促销规则
/// 针对某一类商品，满第一梯度送A商品，满第二梯度送B商品
/// </summary>
public class GradientGiftRule : PromotionRuleBase
{
    public override string RuleType => "GradientGift";

    /// <summary>
    /// 购买条件列表
    /// </summary>
    public List<BuyCondition> BuyConditions { get; set; } = new();

    /// <summary>
    /// 梯度赠品条件列表
    /// </summary>
    public List<GradientGiftCondition> GradientGiftConditions { get; set; } = new();

    /// <summary>
    /// 梯度赠送策略
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public GradientGiftStrategy GradientStrategy { get; set; } = GradientGiftStrategy.ByGradient;

    /// <summary>
    /// 赠品选择策略
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public GiftSelectionStrategy GiftSelectionStrategy { get; set; } = GiftSelectionStrategy.CustomerBenefit;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!BuyConditions.Any() || !GradientGiftConditions.Any())
            return false;

        // 使用择优算法检查是否能满足梯度条件
        var optimalPlan = FindOptimalGradientPlan(cart);
        return optimalPlan != null && optimalPlan.MaxApplications > 0;
    }

    /// <summary>
    /// 梯度促销计划
    /// </summary>
    private class GradientPromotionPlan
    {
        public int MaxApplications { get; set; }
        public List<ProductAllocation> ConsumedAllocations { get; set; } = new();
        public List<GradientGiftAllocation> GiftAllocations { get; set; } = new();
        public decimal TotalGiftValue { get; set; }
    }

    /// <summary>
    /// 商品分配
    /// </summary>
    private class ProductAllocation
    {
        public string ProductId { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalValue => Quantity * UnitPrice;
    }

    /// <summary>
    /// 梯度赠品分配
    /// </summary>
    private class GradientGiftAllocation : ProductAllocation
    {
        public int GradientLevel { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 找到最优的梯度促销计划
    /// 考虑所有可能的梯度组合和商品分配
    /// </summary>
    private GradientPromotionPlan? FindOptimalGradientPlan(ShoppingCart cart)
    {
        var allProducts = GetAllRelevantProducts(cart);
        if (!allProducts.Any()) return null;

        var bestPlan = new GradientPromotionPlan();
        var maxApplications = IsRepeatable ? (MaxApplications > 0 ? MaxApplications : 10) : 1;

        // 尝试不同的应用次数，找到最优方案
        for (int appCount = 1; appCount <= maxApplications; appCount++)
        {
            var plan = TryCreateGradientPlan(cart, allProducts, appCount);
            if (plan != null && plan.MaxApplications > bestPlan.MaxApplications)
            {
                bestPlan = plan;
            }
        }

        return bestPlan.MaxApplications > 0 ? bestPlan : null;
    }

    /// <summary>
    /// 获取所有相关的商品
    /// </summary>
    private List<ProductAllocation> GetAllRelevantProducts(ShoppingCart cart)
    {
        var allProductIds = new HashSet<string>();

        // 添加购买条件中的商品
        foreach (var condition in BuyConditions)
        {
            foreach (var productId in condition.ProductIds)
            {
                allProductIds.Add(productId);
            }
        }

        // 添加梯度赠品中的商品
        foreach (var gradient in GradientGiftConditions)
        {
            foreach (var productId in gradient.GiftProductIds)
            {
                allProductIds.Add(productId);
            }
        }

        var products = new List<ProductAllocation>();
        foreach (var productId in allProductIds)
        {
            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
            if (cartItem != null && cartItem.AvailableQuantity > 0)
            {
                products.Add(new ProductAllocation
                {
                    ProductId = productId,
                    Quantity = cartItem.AvailableQuantity,
                    UnitPrice = cartItem.UnitPrice
                });
            }
        }

        return products;
    }

    /// <summary>
    /// 尝试创建指定应用次数的梯度促销计划
    /// </summary>
    private GradientPromotionPlan? TryCreateGradientPlan(ShoppingCart cart, List<ProductAllocation> allProducts, int targetApplications)
    {
        var plan = new GradientPromotionPlan();
        var remainingProducts = allProducts.Select(p => new ProductAllocation
        {
            ProductId = p.ProductId,
            Quantity = p.Quantity,
            UnitPrice = p.UnitPrice
        }).ToList();

        int actualApplications = 0;

        for (int app = 0; app < targetApplications; app++)
        {
            // 找到当前可以应用的最高梯度
            var applicableGradient = FindApplicableGradient(remainingProducts);
            if (applicableGradient == null) break;

            // 消耗购买条件中的商品
            if (!TryConsumeGradientBuyConditions(remainingProducts, plan, applicableGradient))
                break;

            // 分配梯度赠品
            if (!TryAllocateGradientGifts(remainingProducts, plan, applicableGradient))
                break;

            actualApplications++;
        }

        plan.MaxApplications = actualApplications;
        return plan.MaxApplications > 0 ? plan : null;
    }

    /// <summary>
    /// 找到当前可以应用的梯度（考虑购买条件和赠品库存）
    /// </summary>
    private GradientGiftCondition? FindApplicableGradient(List<ProductAllocation> remainingProducts)
    {
        foreach (var buyCondition in BuyConditions)
        {
            var availableQuantity = remainingProducts
                .Where(p => buyCondition.ProductIds.Contains(p.ProductId))
                .Sum(p => p.Quantity);

            // 找到所有满足购买条件的梯度
            var applicableGradients = GradientGiftConditions
                .Where(g => availableQuantity >= g.RequiredQuantity)
                .OrderByDescending(g => g.GradientLevel)
                .ToList();

            // 检查每个梯度是否有足够的赠品库存
            foreach (var gradient in applicableGradients)
            {
                var hasEnoughGifts = gradient.GiftProductIds.Any(giftId =>
                    remainingProducts.Where(p => p.ProductId == giftId).Sum(p => p.Quantity) >= gradient.GiftQuantity);

                if (hasEnoughGifts)
                    return gradient;
            }
        }

        return null;
    }

    /// <summary>
    /// 消耗梯度购买条件中的商品
    /// </summary>
    private bool TryConsumeGradientBuyConditions(List<ProductAllocation> remainingProducts, GradientPromotionPlan plan, GradientGiftCondition gradient)
    {
        foreach (var buyCondition in BuyConditions)
        {
            var requiredQuantity = gradient.RequiredQuantity;
            var consumedThisCondition = new List<ProductAllocation>();

            // 按价格排序：商家利益最大化时优先消耗高价商品作为购买条件
            var candidateProducts = remainingProducts
                .Where(p => buyCondition.ProductIds.Contains(p.ProductId) && p.Quantity > 0)
                .OrderBy(p => GiftSelectionStrategy == GiftSelectionStrategy.MerchantBenefit ? -p.UnitPrice : p.UnitPrice)
                .ToList();

            foreach (var product in candidateProducts)
            {
                if (requiredQuantity <= 0) break;

                var consumeQuantity = Math.Min(product.Quantity, requiredQuantity);

                consumedThisCondition.Add(new ProductAllocation
                {
                    ProductId = product.ProductId,
                    Quantity = consumeQuantity,
                    UnitPrice = product.UnitPrice
                });

                product.Quantity -= consumeQuantity;
                requiredQuantity -= consumeQuantity;
            }

            if (requiredQuantity > 0)
                return false;

            plan.ConsumedAllocations.AddRange(consumedThisCondition);
        }

        return true;
    }

    /// <summary>
    /// 分配梯度赠品
    /// 修复：从剩余商品中选择赠品，确保不会选择已被购买条件消耗的商品
    /// </summary>
    private bool TryAllocateGradientGifts(List<ProductAllocation> remainingProducts, GradientPromotionPlan plan, GradientGiftCondition gradient)
    {
        var requiredGiftQuantity = gradient.GiftQuantity;
        var giftAllocations = new List<GradientGiftAllocation>();

        // 修复：从剩余商品中选择最优赠品
        var selectedGiftProducts = SelectOptimalGradientGiftProductsFromRemaining(
            gradient.GiftProductIds,
            remainingProducts,
            requiredGiftQuantity);

        foreach (var productId in selectedGiftProducts)
        {
            if (requiredGiftQuantity <= 0) break;

            var product = remainingProducts.FirstOrDefault(p => p.ProductId == productId && p.Quantity > 0);
            if (product == null) continue;

            var giftQuantity = Math.Min(product.Quantity, requiredGiftQuantity);

            giftAllocations.Add(new GradientGiftAllocation
            {
                ProductId = product.ProductId,
                Quantity = giftQuantity,
                UnitPrice = product.UnitPrice,
                GradientLevel = gradient.GradientLevel,
                Description = gradient.Description
            });

            product.Quantity -= giftQuantity;
            requiredGiftQuantity -= giftQuantity;
            plan.TotalGiftValue += giftQuantity * product.UnitPrice;
        }

        if (requiredGiftQuantity > 0)
            return false;

        plan.GiftAllocations.AddRange(giftAllocations);
        return true;
    }

    /// <summary>
    /// 从剩余商品中选择最优的梯度赠品商品
    /// 修复后的核心方法：只从未被消耗的剩余商品中选择赠品
    /// </summary>
    private List<string> SelectOptimalGradientGiftProductsFromRemaining(List<string> candidateProductIds, List<ProductAllocation> remainingProducts, int requiredQuantity)
    {
        if (!candidateProductIds.Any())
            return new List<string>();

        // 只考虑剩余商品中的候选商品
        var availableGiftProducts = remainingProducts
            .Where(p => candidateProductIds.Contains(p.ProductId) && p.Quantity > 0)
            .ToList();

        if (!availableGiftProducts.Any())
            return new List<string>();

        // 根据策略排序
        var sortedProducts = GiftSelectionStrategy switch
        {
            GiftSelectionStrategy.CustomerBenefit => availableGiftProducts.OrderByDescending(p => p.UnitPrice),
            GiftSelectionStrategy.MerchantBenefit => availableGiftProducts.OrderBy(p => p.UnitPrice),
            _ => availableGiftProducts.OrderByDescending(p => p.UnitPrice)
        };

        var selectedProducts = new List<string>();
        var remainingQuantity = requiredQuantity;

        foreach (var product in sortedProducts)
        {
            if (remainingQuantity <= 0) break;

            if (product.Quantity > 0)
            {
                selectedProducts.Add(product.ProductId);
                remainingQuantity--;
            }
        }

        return selectedProducts;
    }

    /// <summary>
    /// 检查特定梯度的赠品库存是否充足（已废弃，由择优算法替代）
    /// </summary>
    private bool CheckGradientGiftStock(ShoppingCart cart, GradientGiftCondition gradient)
    {
        if (gradient.GiftProductIds.Count > 1)
        {
            // 多选一：只要有一个赠品有库存就可以
            return gradient.GiftProductIds.Any(id =>
                cart.Items.Where(x => x.Product.Id == id).Sum(x => x.Quantity) > 0);
        }
        else
        {
            // 单一赠品：必须有足够库存
            var giftProductId = gradient.GiftProductIds.FirstOrDefault();
            if (string.IsNullOrEmpty(giftProductId)) return false;

            var availableGiftQuantity = cart.Items
                .Where(x => x.Product.Id == giftProductId)
                .Sum(x => x.Quantity);

            return availableGiftQuantity >= gradient.GiftQuantity;
        }
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        var optimalPlan = FindOptimalGradientPlan(cart);
        return optimalPlan?.MaxApplications ?? 0;
    }

    /// <summary>
    /// 根据赠品库存计算最大应用次数
    /// </summary>
    private int CalculateMaxApplicationsByGiftStock(ShoppingCart cart, List<GradientGiftCondition> gradients)
    {
        var minApplications = int.MaxValue;

        foreach (var gradient in gradients)
        {
            var maxForThisGradient = CalculateMaxApplicationsForSingleGradient(cart, gradient);
            minApplications = Math.Min(minApplications, maxForThisGradient);
        }

        return minApplications == int.MaxValue ? int.MaxValue : minApplications;
    }

    /// <summary>
    /// 计算单个梯度的最大应用次数（基于赠品库存）
    /// </summary>
    private int CalculateMaxApplicationsForSingleGradient(ShoppingCart cart, GradientGiftCondition gradient)
    {
        if (gradient.GiftProductIds.Count > 1)
        {
            // 多选一的情况：只要有一个赠品有库存就可以应用
            var hasAnyStock = gradient.GiftProductIds.Any(id =>
                cart.Items.Where(x => x.Product.Id == id).Sum(x => x.Quantity) > 0);
            return hasAnyStock ? int.MaxValue : 0;
        }
        else
        {
            // 单一赠品：根据库存数量计算
            var giftProductId = gradient.GiftProductIds.FirstOrDefault();
            if (string.IsNullOrEmpty(giftProductId)) return 0;

            var availableStock = cart.Items
                .Where(x => x.Product.Id == giftProductId)
                .Sum(x => x.Quantity);

            return gradient.GiftQuantity > 0 ? availableStock / gradient.GiftQuantity : 0;
        }
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyGradientGiftPromotion(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用梯度送赠品促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = $"应用梯度送赠品促销时发生错误: {ex.Message}";
        }

        return application;
    }

    /// <summary>
    /// 应用梯度送赠品促销
    /// 修复：使用择优算法确保最优的商品分配和正确的赠品选择
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyGradientGiftPromotion(ShoppingCart cart, int applicationCount)
    {
        // 使用择优算法获取最优促销计划
        var optimalPlan = FindOptimalGradientPlan(cart);
        if (optimalPlan == null || optimalPlan.MaxApplications == 0)
        {
            return (0m, new List<ConsumedItem>(), new List<GiftItem>());
        }

        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var giftItems = new List<GiftItem>();

        // 实际应用次数取最小值
        var actualApplications = Math.Min(applicationCount, optimalPlan.MaxApplications);

        // 根据择优算法的结果构建消耗商品列表
        foreach (var allocation in optimalPlan.ConsumedAllocations.Take(actualApplications))
        {
            var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == allocation.ProductId);
            if (existingConsumed != null)
            {
                existingConsumed.Quantity += allocation.Quantity;
            }
            else
            {
                var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == allocation.ProductId);
                consumedItems.Add(new ConsumedItem
                {
                    ProductId = allocation.ProductId,
                    ProductName = cartItem?.Product.Name ?? allocation.ProductId,
                    Quantity = allocation.Quantity,
                    UnitPrice = allocation.UnitPrice
                });
            }
        }

        // 根据择优算法的结果构建赠品列表
        foreach (var allocation in optimalPlan.GiftAllocations.Take(actualApplications))
        {
            var giftValue = allocation.Quantity * allocation.UnitPrice;
            totalDiscountAmount += giftValue;

            var existingGift = giftItems.FirstOrDefault(x => x.ProductId == allocation.ProductId);
            if (existingGift != null)
            {
                existingGift.Quantity += allocation.Quantity;
                existingGift.Value += giftValue;
            }
            else
            {
                var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == allocation.ProductId);
                var strategyDescription = GiftSelectionStrategy == GiftSelectionStrategy.CustomerBenefit
                    ? "客户利益最大化选择"
                    : "商家利益最大化选择";

                giftItems.Add(new GiftItem
                {
                    ProductId = allocation.ProductId,
                    ProductName = cartItem?.Product.Name ?? allocation.ProductId,
                    Quantity = allocation.Quantity,
                    Value = giftValue,
                    Description = $"梯度{allocation.GradientLevel}: {allocation.Description} - {strategyDescription}（择优算法）"
                });
            }
        }

        // 从购物车中扣除消耗的商品
        foreach (var consumed in consumedItems)
        {
            var remainingQuantity = consumed.Quantity;
            var cartItems = cart.Items.Where(x => x.Product.Id == consumed.ProductId).ToList();

            foreach (var cartItem in cartItems)
            {
                if (remainingQuantity <= 0) break;

                var deductQuantity = Math.Min(cartItem.Quantity, remainingQuantity);
                cartItem.Quantity -= deductQuantity;
                remainingQuantity -= deductQuantity;
            }
        }

        cart.Items.RemoveAll(x => x.Quantity <= 0);
        return (totalDiscountAmount, consumedItems, giftItems);
    }

    // 旧的逻辑代码已删除，使用择优算法替代

    // 旧的废弃代码已删除，使用择优算法替代

    /// <summary>
    /// 根据策略选择最优的赠品商品（已废弃）
    /// 此方法存在Bug：没有考虑购买条件已消耗的商品
    /// 已被择优算法中的 SelectOptimalGradientGiftProductsFromRemaining 方法替代
    /// </summary>
    [Obsolete("此方法存在逻辑错误，请使用择优算法")]
    private List<string> SelectOptimalGiftProducts(List<string> candidateProductIds, ShoppingCart cart, int requiredQuantity)
    {
        // 此方法已废弃，不应再使用
        throw new InvalidOperationException("此方法已废弃，存在逻辑错误。请使用择优算法中的 SelectOptimalGradientGiftProductsFromRemaining 方法。");
    }
}
