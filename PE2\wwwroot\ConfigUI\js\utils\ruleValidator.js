// 规则验证器 - 提供智能验证和建议
const RuleValidator = {
  
  // 验证规则完整性和合理性
  validateRule(ruleData) {
    const errors = [];
    const warnings = [];
    const suggestions = [];
    
    // 基础字段验证
    if (!ruleData.id) {
      errors.push('规则ID不能为空');
    } else if (!/^[a-zA-Z0-9_-]+$/.test(ruleData.id)) {
      errors.push('规则ID只能包含字母、数字、下划线和连字符');
    }
    
    if (!ruleData.name) {
      errors.push('规则名称不能为空');
    } else if (ruleData.name.length > 100) {
      warnings.push('规则名称过长，建议控制在100字符以内');
    }
    
    // 时间验证
    if (ruleData.startTime && ruleData.endTime) {
      const start = new Date(ruleData.startTime);
      const end = new Date(ruleData.endTime);
      if (start >= end) {
        errors.push('生效时间必须早于失效时间');
      }
      
      // 检查是否设置了过短的有效期
      const duration = end - start;
      const oneDay = 24 * 60 * 60 * 1000;
      if (duration < oneDay) {
        warnings.push('促销活动时间较短（少于1天），请确认是否正确');
      }
    }
    
    // 优先级建议
    if (ruleData.priority === undefined || ruleData.priority === null) {
      suggestions.push('建议设置规则优先级以控制执行顺序');
    } else if (ruleData.priority < 0) {
      warnings.push('优先级为负数，该规则将具有较低优先级');
    }
    
    // 业务逻辑验证
    if (ruleData.$type) {
      const businessValidation = this.validateBusinessLogic(ruleData);
      errors.push(...businessValidation.errors);
      warnings.push(...businessValidation.warnings);
      suggestions.push(...businessValidation.suggestions);
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      score: this.calculateRuleScore(ruleData, errors, warnings)
    };
  },
  
  // 业务逻辑验证
  validateBusinessLogic(ruleData) {
    const errors = [];
    const warnings = [];
    const suggestions = [];
    
    switch (ruleData.$type) {
      case 'UnifiedGift':
        return this.validateUnifiedGift(ruleData);
      case 'BundleOffer':
        return this.validateBundleOffer(ruleData);
      case 'VolumeDiscount':
        return this.validateVolumeDiscount(ruleData);
      case 'PercentageDiscount':
        return this.validatePercentageDiscount(ruleData);
      default:
        suggestions.push('未识别的促销类型，请检查配置');
    }
    
    return { errors, warnings, suggestions };
  },
  
  // 买赠规则验证
  validateUnifiedGift(ruleData) {
    const errors = [];
    const warnings = [];
    const suggestions = [];
    
    // 购买条件验证
    if (!ruleData.buyConditions || ruleData.buyConditions.length === 0) {
      errors.push('买赠规则必须设置购买条件');
    } else {
      ruleData.buyConditions.forEach((condition, index) => {
        if (!condition.productIds || condition.productIds.length === 0) {
          errors.push(`购买条件${index + 1}：必须选择商品`);
        }
        
        if (!condition.requiredQuantity && !condition.requiredAmount) {
          errors.push(`购买条件${index + 1}：必须设置所需数量或金额`);
        }
        
        if (condition.requiredQuantity && condition.requiredQuantity <= 0) {
          errors.push(`购买条件${index + 1}：所需数量必须大于0`);
        }
        
        if (condition.requiredAmount && condition.requiredAmount <= 0) {
          errors.push(`购买条件${index + 1}：所需金额必须大于0`);
        }
      });
    }
    
    // 赠品条件验证
    if (!ruleData.giftConditions || ruleData.giftConditions.length === 0) {
      errors.push('买赠规则必须设置赠品条件');
    } else {
      ruleData.giftConditions.forEach((condition, index) => {
        if (!condition.giftProductIds || condition.giftProductIds.length === 0) {
          errors.push(`赠品条件${index + 1}：必须选择赠品`);
        }
        
        if (!condition.giftQuantity || condition.giftQuantity <= 0) {
          errors.push(`赠品条件${index + 1}：赠品数量必须大于0`);
        }
      });
    }
    
    // 业务建议
    if (ruleData.buyConditions && ruleData.giftConditions) {
      const buyProducts = new Set();
      const giftProducts = new Set();
      
      ruleData.buyConditions.forEach(condition => {
        condition.productIds?.forEach(id => buyProducts.add(id));
      });
      
      ruleData.giftConditions.forEach(condition => {
        condition.giftProductIds?.forEach(id => giftProducts.add(id));
      });
      
      // 检查是否有重复商品
      const overlap = [...buyProducts].filter(id => giftProducts.has(id));
      if (overlap.length > 0) {
        warnings.push('购买商品和赠品中存在相同商品，可能造成逻辑混乱');
      }
    }
    
    return { errors, warnings, suggestions };
  },
  
  // 套餐规则验证
  validateBundleOffer(ruleData) {
    const errors = [];
    const warnings = [];
    const suggestions = [];
    
    if (!ruleData.bundleProducts || ruleData.bundleProducts.length < 2) {
      errors.push('套餐规则至少需要2个商品');
    }
    
    if (!ruleData.bundlePrice || ruleData.bundlePrice <= 0) {
      errors.push('套餐价格必须大于0');
    }
    
    return { errors, warnings, suggestions };
  },
  
  // 满量折扣验证
  validateVolumeDiscount(ruleData) {
    const errors = [];
    const warnings = [];
    const suggestions = [];
    
    if (!ruleData.discountTiers || ruleData.discountTiers.length === 0) {
      errors.push('满量折扣必须设置折扣阶梯');
    } else {
      // 验证阶梯合理性
      const sortedTiers = [...ruleData.discountTiers].sort((a, b) => a.minQuantity - b.minQuantity);
      
      for (let i = 0; i < sortedTiers.length; i++) {
        const tier = sortedTiers[i];
        
        if (tier.minQuantity <= 0) {
          errors.push(`折扣阶梯${i + 1}：最小数量必须大于0`);
        }
        
        if (tier.discountRate <= 0 || tier.discountRate > 100) {
          errors.push(`折扣阶梯${i + 1}：折扣率必须在0-100之间`);
        }
        
        // 检查阶梯递增合理性
        if (i > 0 && tier.discountRate <= sortedTiers[i-1].discountRate) {
          warnings.push(`折扣阶梯${i + 1}：折扣力度应该随数量增加而提升`);
        }
      }
    }
    
    return { errors, warnings, suggestions };
  },
  
  // 百分比折扣验证
  validatePercentageDiscount(ruleData) {
    const errors = [];
    const warnings = [];
    const suggestions = [];
    
    if (!ruleData.discountPercentage || ruleData.discountPercentage <= 0 || ruleData.discountPercentage > 100) {
      errors.push('折扣百分比必须在0-100之间');
    }
    
    if (ruleData.discountPercentage > 50) {
      warnings.push('折扣力度较大，请确认是否正确');
    }
    
    return { errors, warnings, suggestions };
  },
  
  // 计算规则质量评分
  calculateRuleScore(ruleData, errors, warnings) {
    let score = 100;
    
    // 错误扣分
    score -= errors.length * 20;
    
    // 警告扣分
    score -= warnings.length * 5;
    
    // 完整性加分
    if (ruleData.description && ruleData.description.length > 10) score += 5;
    if (ruleData.startTime && ruleData.endTime) score += 5;
    if (ruleData.priority !== undefined && ruleData.priority !== null) score += 3;
    
    return Math.max(0, Math.min(100, score));
  },
  
  // 获取优化建议
  getOptimizationSuggestions(ruleData) {
    const suggestions = [];
    
    // 性能优化建议
    if (ruleData.productIds && ruleData.productIds.length > 100) {
      suggestions.push({
        type: 'performance',
        message: '商品数量较多，考虑使用商品分类或标签来优化性能'
      });
    }
    
    // 用户体验建议
    if (!ruleData.description || ruleData.description.length < 10) {
      suggestions.push({
        type: 'ux',
        message: '建议添加详细的规则描述，便于用户理解'
      });
    }
    
    // 运营建议
    if (!ruleData.maxApplications || ruleData.maxApplications === 0) {
      suggestions.push({
        type: 'business',
        message: '考虑设置最大应用次数以控制促销成本'
      });
    }
    
    return suggestions;
  }
};

// 导出到全局
window.RuleValidator = RuleValidator;
