using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.ExchangeRules;

/// <summary>
/// 组合定额减换购规则测试
/// 测试场景：购买A、B商品各大于等于1件时，可以减1元购买C商品
/// </summary>
public class CombinationDiscountAmountExchangeRuleTests(ITestOutputHelper output) : TestBase(output)
{
    #region 基本功能测试

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("Priority", "High")]
    public async Task Apply_BasicCombinationDiscountAmountExchange_ShouldApplyCorrectly()
    {
        // Arrange - 创建基本组合定额减换购规则：买A+B享C商品减1元
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_BuyAB_1Yuan_OffC();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 1), // A商品50元
                (TestDataGenerator.CreateProductB(), 1), // B商品30元
                (TestDataGenerator.CreateProductC(), 1) // C商品20元
            ]
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "应用前购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "基本组合定额减换购");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证C商品实际支付价格为19元（20-1）
        var productCItem = result.ProcessedCart.Items.First(x => x.Product.Id == "C");
        AssertAmountEqual(19.00m, productCItem.ActualUnitPrice, "C商品实际支付价格应为19元");

        // 验证总优惠金额
        AssertAmountEqual(1.00m, result.TotalDiscount, "总优惠应为1元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("Priority", "High")]
    public async Task Apply_DifferentDiscountAmounts_ShouldApplyCorrectly()
    {
        // Arrange - 测试不同减免金额
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_BuyAB_5Yuan_OffC();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 1),
                (TestDataGenerator.CreateProductB(), 1),
                (TestDataGenerator.CreateProductC(), 1) // 20元商品
            ]
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "不同减免金额测试购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "不同减免金额测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证C商品实际支付价格为15元（20-5）
        var productCItem = result.ProcessedCart.Items.First(x => x.Product.Id == "C");
        AssertAmountEqual(15.00m, productCItem.ActualUnitPrice, "C商品实际支付价格应为15元");

        // 验证总优惠金额
        AssertAmountEqual(5.00m, result.TotalDiscount, "总优惠应为5元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("Priority", "Medium")]
    public async Task Apply_QuantityBasedCombination_ShouldApplyCorrectly()
    {
        // Arrange - 基于数量的组合条件
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_QuantityBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 2), // 满足A商品数量条件
                (TestDataGenerator.CreateProductB(), 2), // 满足B商品数量条件
                (TestDataGenerator.CreateProductC(), 1)
            ]
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "基于数量的组合测试购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "基于数量的组合定额减换购");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证有优惠产生
        Assert.True(result.TotalDiscount > 0, "应该有优惠产生");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("Priority", "Medium")]
    public async Task Apply_AmountBasedCombination_ShouldApplyCorrectly()
    {
        // Arrange - 基于金额的组合条件
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_AmountBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 3), // 150元，满足A商品金额条件
                (TestDataGenerator.CreateProductB(), 2), // 60元，满足B商品金额条件
                (TestDataGenerator.CreateProductC(), 1)
            ]
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "基于金额的组合测试购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "基于金额的组合定额减换购");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证有优惠产生
        Assert.True(result.TotalDiscount > 0, "应该有优惠产生");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 条件不满足测试

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("Priority", "High")]
    public async Task Apply_MissingFirstProduct_ShouldNotApply()
    {
        // Arrange - 缺少第一个组合商品
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_BuyAB_1Yuan_OffC();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductB(), 1), // 只有B商品，没有A商品
                (TestDataGenerator.CreateProductC(), 1)
            ]
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "缺少第一个组合商品的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "缺少第一个组合商品场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "缺少组合商品时应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("Priority", "High")]
    public async Task Apply_MissingSecondProduct_ShouldNotApply()
    {
        // Arrange - 缺少第二个组合商品
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_BuyAB_1Yuan_OffC();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 1), // 只有A商品，没有B商品
                (TestDataGenerator.CreateProductC(), 1)
            ]
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "缺少第二个组合商品的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "缺少第二个组合商品场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "缺少组合商品时应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("Priority", "High")]
    public async Task Apply_NoExchangeProductInCart_ShouldNotApply()
    {
        // Arrange - 购物车中没有换购商品
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_BuyAB_1Yuan_OffC();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 1),
                (TestDataGenerator.CreateProductB(), 1)
                // 没有C商品
            ]
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "没有换购商品的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "没有换购商品场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "没有换购商品时应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("Priority", "High")]
    public async Task Apply_InsufficientCombinationQuantity_ShouldNotApply()
    {
        // Arrange - 组合商品数量不足
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_QuantityBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 1), // 不满足数量条件（需要2件）
                (TestDataGenerator.CreateProductB(), 2),
                (TestDataGenerator.CreateProductC(), 1)
            ]
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "组合商品数量不足的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "组合商品数量不足场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "组合商品数量不足时应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("Priority", "High")]
    public async Task Apply_InsufficientCombinationAmount_ShouldNotApply()
    {
        // Arrange - 组合商品金额不足
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_AmountBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 1), // 50元，不满足金额条件
                (TestDataGenerator.CreateProductB(), 1), // 30元，不满足金额条件
                (TestDataGenerator.CreateProductC(), 1)
            ]
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "组合商品金额不足的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "组合商品金额不足场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "组合商品金额不足时应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("Priority", "Medium")]
    public async Task Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_BuyAB_1Yuan_OffC();
        var cart = TestDataGenerator.CreateEmptyCart();

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "空购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "空购物车场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "空购物车应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("Priority", "Medium")]
    public async Task Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_Disabled();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_011",
            "CUSTOMER_011",
            [
                (TestDataGenerator.CreateProductA(), 1),
                (TestDataGenerator.CreateProductB(), 1),
                (TestDataGenerator.CreateProductC(), 1)
            ]
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "禁用规则测试购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "禁用规则场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "规则已禁用");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "禁用规则应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("Priority", "High")]
    public async Task Apply_DiscountExceedsProductPrice_ShouldLimitToProductPrice()
    {
        // Arrange - 减免金额超过商品价格
        var rule =
            TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_BuyAB_50Yuan_OffC();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_012",
            "CUSTOMER_012",
            [
                (TestDataGenerator.CreateProductA(), 1),
                (TestDataGenerator.CreateProductB(), 1),
                (TestDataGenerator.CreateProductC(), 1) // C商品20元，但减免50元
            ]
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "减免金额超过商品价格的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "减免金额超过商品价格场景");
        LogPromotionResultDetails(result);

        // 验证规则被应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证折扣被限制为商品价格
        AssertAmountEqual(20.00m, result.TotalDiscount, "折扣应被限制为商品价格");

        // 验证C商品实际支付价格为0元
        var productCItem = result.ProcessedCart.Items.First(x => x.Product.Id == "C");
        AssertAmountEqual(0m, productCItem.ActualUnitPrice, "C商品实际支付价格应为0元");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("Priority", "Medium")]
    public async Task Apply_ZeroDiscountAmount_ShouldNotApply()
    {
        // Arrange - 0减免金额（无效配置）
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_ZeroDiscount();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_013",
            "CUSTOMER_013",
            [
                (TestDataGenerator.CreateProductA(), 1),
                (TestDataGenerator.CreateProductB(), 1),
                (TestDataGenerator.CreateProductC(), 1)
            ]
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "零减免金额的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "零减免金额场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "无实际优惠");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "零减免金额应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("Priority", "Medium")]
    public async Task Apply_NegativeDiscountAmount_ShouldNotApply()
    {
        // Arrange - 负减免金额（无效配置）
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_NegativeDiscount();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 1),
                (TestDataGenerator.CreateProductB(), 1),
                (TestDataGenerator.CreateProductC(), 1)
            ]
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "负减免金额的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        AssertPromotionResult(result, "负减免金额场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "无效配置");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "负减免金额应无优惠");

        // 验证购物车一致性
        AssertCartConsistency(result.ProcessedCart);
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("TestType", "EdgeCase")]
    public async Task Apply_ExactCombinationRequirements_ShouldApplyCorrectly()
    {
        // Arrange - 刚好满足组合条件
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_BuyAB_1Yuan_OffC();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 1), // 刚好1件
                (TestDataGenerator.CreateProductB(), 1), // 刚好1件
                (TestDataGenerator.CreateProductC(), 1)
            ]
        );
        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result.AppliedPromotions);

        Output.WriteLine($"刚好满足组合条件：总折扣 {result.TotalDiscount}元");
    }

    #endregion

    #region 可重复性测试

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("TestType", "Repeatability")]
    public async Task Apply_RepeatableRule_ShouldApplyMultipleTimes()
    {
        // Arrange - 可重复的组合定额减换购规则
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_Repeatable();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 3), // 3件A商品
                (TestDataGenerator.CreateProductB(), 3), // 3件B商品
                (TestDataGenerator.CreateProductC(), 3) // 3件C商品可换购
            ]
        );
        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result.AppliedPromotions);
        Assert.True(result.TotalDiscount > 0);

        // 验证多次应用
        Assert.True(result.AppliedPromotions.Count > 1, "可重复规则应该能应用多次");

        Output.WriteLine($"可重复规则应用次数：{result.AppliedPromotions.Count}，总折扣：{result.TotalDiscount}元");
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("TestType", "Repeatability")]
    public async Task Apply_NonRepeatableRule_ShouldApplyOnlyOnce()
    {
        // Arrange - 不可重复的组合定额减换购规则
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_NonRepeatable();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 5), // 足够多的A商品
                (TestDataGenerator.CreateProductB(), 5), // 足够多的B商品
                (TestDataGenerator.CreateProductC(), 5) // 足够多的C商品
            ]
        );
        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.NotNull(result);
        if (result.AppliedPromotions.Any())
        {
            Assert.Equal(1, result.AppliedPromotions.Count);
        }

        Output.WriteLine(
            $"不可重复规则应用次数：{result.AppliedPromotions.Count}，总折扣：{result.TotalDiscount}元"
        );
    }

    #endregion

    #region 复杂场景测试

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("TestType", "ComplexScenario")]
    public async Task Apply_MultipleExchangeOptions_ShouldSelectOptimal()
    {
        // Arrange - 多种换购选择
        var rule =
            TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_MultipleExchangeOptions();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 1),
                (TestDataGenerator.CreateProductB(), 1),
                (TestDataGenerator.CreateProductC(), 1), // 20元
                (TestDataGenerator.CreateProductD(), 1), // 100元
                (TestDataGenerator.CreateProductE(), 1) // 15元
            ]
        );

        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.NotNull(result);
        if (result.AppliedPromotions.Any())
        {
            Assert.True(result.TotalDiscount > 0);

            // 验证选择了最优的换购商品（客户利益最大化应选择高价商品）
            var productDItem = result.ProcessedCart.Items.First(x => x.Product.Id == "D");
            Assert.True(productDItem.ActualUnitPrice < productDItem.UnitPrice, "高价商品应该被选为换购商品");
        }

        Output.WriteLine($"多种换购选择场景：总折扣 {result.TotalDiscount}元");
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("TestType", "ComplexScenario")]
    public async Task Apply_MerchantBenefitStrategy_ShouldSelectLowPriceProduct()
    {
        // Arrange - 商家利益最大化策略
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_MerchantBenefit();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 1),
                (TestDataGenerator.CreateProductB(), 1),
                (TestDataGenerator.CreateProductC(), 1), // 20元
                (TestDataGenerator.CreateProductD(), 1), // 100元
                (TestDataGenerator.CreateProductE(), 1) // 15元
            ]
        );

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.NotNull(result);
        if (result.AppliedPromotions.Any())
        {
            Assert.True(result.TotalDiscount > 0);

            // 验证选择了低价商品（商家利益最大化）
            var productEItem = result.ProcessedCart.Items.First(x => x.Product.Id == "E");
            Assert.True(productEItem.ActualUnitPrice < productEItem.UnitPrice, "低价商品应该被选为换购商品");
        }

        Output.WriteLine($"商家利益最大化策略：总折扣 {result.TotalDiscount}元");
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("TestType", "ComplexScenario")]
    public async Task Apply_ComplexCombinationConditions_ShouldApplyCorrectly()
    {
        // Arrange - 复杂组合条件
        var rule =
            TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_ComplexConditions();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 2), // 满足数量条件
                (TestDataGenerator.CreateProductB(), 3), // 90元，满足金额条件
                (TestDataGenerator.CreateProductC(), 1)
            ]
        );
        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result.AppliedPromotions);

        Output.WriteLine($"复杂组合条件：总折扣 {result.TotalDiscount}元");
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("TestType", "Integration")]
    public async Task Apply_WithOtherPromotions_ShouldHandleCorrectly()
    {
        // Arrange - 与其他促销规则交互
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_BuyAB_1Yuan_OffC();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 2),
                (TestDataGenerator.CreateProductB(), 2),
                (TestDataGenerator.CreateProductC(), 2)
            ]
        );
        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result.AppliedPromotions);

        Output.WriteLine($"与其他促销交互：总折扣 {result.TotalDiscount}元");
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("TestType", "ComplexScenario")]
    public async Task Apply_ThreeProductCombination_ShouldApplyCorrectly()
    {
        // Arrange - 三商品组合
        var rule =
            TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_ThreeProductCombo();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 1),
                (TestDataGenerator.CreateProductB(), 1),
                (TestDataGenerator.CreateProductC(), 1),
                (TestDataGenerator.CreateProductD(), 1) // 换购商品
            ]
        );
        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result.AppliedPromotions);

        Output.WriteLine($"三商品组合：总折扣 {result.TotalDiscount}元");
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("TestType", "EdgeCase")]
    public async Task Apply_SmallDiscountAmount_ShouldApplyCorrectly()
    {
        // Arrange - 小额减免测试
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_SmallDiscount();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 1),
                (TestDataGenerator.CreateProductB(), 1),
                (TestDataGenerator.CreateProductC(), 1)
            ]
        );
        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result.AppliedPromotions);
        Assert.True(result.TotalDiscount > 0 && result.TotalDiscount < 1);

        Output.WriteLine($"小额减免：总折扣 {result.TotalDiscount}元");
    }

    #endregion

    #region 配置验证测试

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("TestType", "Configuration")]
    public async Task Apply_InvalidConfiguration_ShouldHandleGracefully()
    {
        // Arrange - 无效配置
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_InvalidConfig();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 1),
                (TestDataGenerator.CreateProductB(), 1),
                (TestDataGenerator.CreateProductC(), 1)
            ]
        );
        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0, result.TotalDiscount);

        Output.WriteLine("无效配置规则不应生效");
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("TestType", "EdgeCase")]
    public async Task Apply_EmptyCombinationConditions_ShouldNotApply()
    {
        // Arrange - 空组合条件
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_EmptyConditions();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 1),
                (TestDataGenerator.CreateProductB(), 1),
                (TestDataGenerator.CreateProductC(), 1)
            ]
        );
        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0, result.TotalDiscount);

        Output.WriteLine("空组合条件规则不应生效");
    }

    [Fact]
    [Trait("Category", "CombinationDiscountAmountExchange")]
    [Trait("TestType", "EdgeCase")]
    public async Task Apply_LargeDiscountAmount_ShouldHandleCorrectly()
    {
        // Arrange - 大额减免测试
        var rule = TestDataGenerator.CreateCombinationDiscountAmountExchangeRule_LargeDiscount();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006",
            "CUSTOMER_006",
            [
                (TestDataGenerator.CreateProductA(), 1),
                (TestDataGenerator.CreateProductB(), 1),
                (TestDataGenerator.CreateProductD(), 1) // 100元商品
            ]
        );
        TestPromotionRuleService.Rules = [rule];

        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result.AppliedPromotions);
        Assert.True(result.TotalDiscount > 0);

        Output.WriteLine($"大额减免：总折扣 {result.TotalDiscount}元");
    }

    #endregion
}
