using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.CashOffRules;

/// <summary>
/// 梯度减现规则单元测试
/// 测试梯度减现规则在各种场景下的正确性
/// </summary>
public class GradientCashDiscountRuleTests : TestBase
{
    public GradientCashDiscountRuleTests(ITestOutputHelper output) : base(output)
    {
    }

    #region 基础功能测试

    /// <summary>
    /// 测试第一梯度减现 - 满足最低条件
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Basic")]
    public async Task Apply_FirstTier_ShouldApplyCorrectDiscount()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateGradientCashDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_001", "CUSTOMER_001",
            (TestDataGenerator.CreateProductA(), 1) // 1件A商品，满足第一梯度条件（满1件减10元）
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "第一梯度减现");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(rule.Id, result.AppliedPromotions[0].RuleId);
        Assert.Equal(10.00m, result.TotalDiscount); // 第一梯度减10元
        Assert.Equal(40.00m, result.FinalAmount); // 原价50元，减10元
    }

    /// <summary>
    /// 测试第二梯度减现 - 满足中等条件
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Basic")]
    public async Task Apply_SecondTier_ShouldApplyCorrectDiscount()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateGradientCashDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_002", "CUSTOMER_002",
            (TestDataGenerator.CreateProductA(), 2) // 2件A商品，满足第二梯度条件（满2件减30元）
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "第二梯度减现");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(rule.Id, result.AppliedPromotions[0].RuleId);
        Assert.Equal(30.00m, result.TotalDiscount); // 第二梯度减30元
        Assert.Equal(70.00m, result.FinalAmount); // 原价100元，减30元
    }

    /// <summary>
    /// 测试第三梯度减现 - 满足最高条件
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Basic")]
    public async Task Apply_ThirdTier_ShouldApplyCorrectDiscount()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateGradientCashDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_003", "CUSTOMER_003",
            (TestDataGenerator.CreateProductA(), 3) // 3件A商品，满足第三梯度条件（满3件减70元）
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "第三梯度减现");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(rule.Id, result.AppliedPromotions[0].RuleId);
        Assert.Equal(70.00m, result.TotalDiscount); // 第三梯度减70元
        Assert.Equal(80.00m, result.FinalAmount); // 原价150元，减70元
    }

    /// <summary>
    /// 测试基于金额的梯度减现
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Basic")]
    public async Task Apply_AmountBasedTiers_ShouldApplyCorrectDiscount()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateGradientCashDiscountRule_AmountBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_004", "CUSTOMER_004",
            (TestDataGenerator.CreateProductA(), 5) // 5件A商品，总价250元，满足满200元减50元条件
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "基于金额的梯度减现");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(rule.Id, result.AppliedPromotions[0].RuleId);
        Assert.Equal(50.00m, result.TotalDiscount); // 满200元减50元
        Assert.Equal(200.00m, result.FinalAmount); // 原价250元，减50元
    }

    #endregion

    #region 梯度选择逻辑测试

    /// <summary>
    /// 测试梯度之间的选择 - 应选择最高适用梯度
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "TierSelection")]
    public async Task Apply_BetweenTiers_ShouldSelectHighestApplicableTier()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateGradientCashDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_005", "CUSTOMER_005",
            (TestDataGenerator.CreateProductA(), 2), // 2件A商品，可选择第一梯度或第二梯度
            (TestDataGenerator.CreateProductA(), 1)  // 再加1件，总共3件，应选择第三梯度
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "梯度选择逻辑");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(70.00m, result.TotalDiscount); // 应选择第三梯度减70元，而非第二梯度减30元
    }

    /// <summary>
    /// 测试超出最高梯度 - 应用最高梯度
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "TierSelection")]
    public async Task Apply_ExceedHighestTier_ShouldApplyHighestTier()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateGradientCashDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006", "CUSTOMER_006",
            (TestDataGenerator.CreateProductA(), 10) // 10件A商品，超出最高梯度条件
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "超出最高梯度");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(70.00m, result.TotalDiscount); // 应用最高梯度减70元
    }

    #endregion

    #region 条件不满足测试

    /// <summary>
    /// 测试数量不足 - 不应用规则
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Insufficient")]
    public async Task Apply_InsufficientQuantity_ShouldNotApply()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateGradientCashDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_007", "CUSTOMER_007",
            (TestDataGenerator.CreateProductB(), 2) // B商品不在适用范围内
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "数量不足场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0m, result.TotalDiscount);
        Assert.Equal(60.00m, result.FinalAmount); // 原价60元，无折扣
    }

    /// <summary>
    /// 测试无适用商品 - 不应用规则
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Insufficient")]
    public async Task Apply_NoApplicableProducts_ShouldNotApply()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateGradientCashDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_008", "CUSTOMER_008",
            (TestDataGenerator.CreateProductC(), 5) // C商品不在适用范围内
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "无适用商品场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0m, result.TotalDiscount);
        Assert.Equal(100.00m, result.FinalAmount); // 原价100元，无折扣
    }

    #endregion

    #region 重复应用测试

    /// <summary>
    /// 测试可重复应用的梯度规则
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Multiple")]
    public async Task Apply_RepeatableGradientRule_ShouldApplyMultipleTimes()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateGradientCashDiscountRule_Repeatable();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_009", "CUSTOMER_009",
            (TestDataGenerator.CreateProductA(), 6) // 6件A商品，可应用2次第三梯度（每3件减70元）
        );

        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "可重复应用的梯度规则");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(rule.Id, result.AppliedPromotions[0].RuleId);
        Assert.Equal(140.00m, result.TotalDiscount); // 2次第三梯度，每次减70元，共减140元
        Assert.Equal(160.00m, result.FinalAmount); // 原价300元，减140元
    }

    /// <summary>
    /// 测试不可重复应用的梯度规则
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Multiple")]
    public async Task Apply_NonRepeatableGradientRule_ShouldApplyOnce()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateGradientCashDiscountRule_NonRepeatable();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_010", "CUSTOMER_010",
            (TestDataGenerator.CreateProductA(), 6) // 6件A商品，但只能应用一次最高梯度
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "不可重复应用的梯度规则");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(rule.Id, result.AppliedPromotions[0].RuleId);
        Assert.Equal(70.00m, result.TotalDiscount); // 只应用一次最高梯度减70元
        Assert.Equal(230.00m, result.FinalAmount); // 原价300元，减70元
    }

    #endregion

    #region 边界条件测试

    /// <summary>
    /// 测试空购物车 - 不应用规则
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Boundary")]
    public async Task Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateGradientCashDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateEmptyCart();

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "空购物车场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0m, result.TotalDiscount);
        Assert.Equal(0m, result.FinalAmount);
    }

    /// <summary>
    /// 测试禁用规则 - 不应用规则
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Boundary")]
    public async Task Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateGradientCashDiscountRule_Disabled();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_011", "CUSTOMER_011",
            (TestDataGenerator.CreateProductA(), 3)
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "禁用规则场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0m, result.TotalDiscount);
        Assert.Equal(150.00m, result.FinalAmount);
    }

    #endregion

    #region 复杂场景测试

    /// <summary>
    /// 测试多商品混合梯度场景
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Complex")]
    public async Task Apply_MultipleProductsGradient_ShouldApplyCorrectly()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateGradientCashDiscountRule_MultipleProducts();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_012", "CUSTOMER_012",
            (TestDataGenerator.CreateProductA(), 2), // A商品在适用范围内
            (TestDataGenerator.CreateProductB(), 2), // B商品在适用范围内
            (TestDataGenerator.CreateProductC(), 1)  // C商品不在适用范围内
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "多商品混合梯度场景");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(rule.Id, result.AppliedPromotions[0].RuleId);
        Assert.Equal(30.00m, result.TotalDiscount); // A+B商品共4件，满足第二梯度减30元
        Assert.Equal(150.00m, result.FinalAmount); // 原价180元，减30元
    }

    #endregion

    #region 性能测试

    /// <summary>
    /// 测试大购物车性能
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Performance")]
    public async Task Apply_LargeCart_ShouldPerformWell()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateGradientCashDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateLargeTestCart(100); // 大量商品的购物车

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        stopwatch.Stop();

        // Assert - 验证结果
        AssertPromotionResult(result, "大购物车性能测试");
        Assert.True(stopwatch.ElapsedMilliseconds < 1000, "处理时间应少于1秒");
        Output.WriteLine($"处理时间: {stopwatch.ElapsedMilliseconds}ms");
    }

    #endregion

    #region 配置验证测试

    /// <summary>
    /// 测试空梯度配置 - 优雅处理
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Configuration")]
    public async Task Apply_EmptyTiers_ShouldHandleGracefully()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateGradientCashDiscountRule_EmptyTiers();
        var cart = TestDataGenerator.CreateSimpleTestCart();

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "空梯度配置场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0m, result.TotalDiscount);
    }

    /// <summary>
    /// 测试无效梯度配置 - 优雅处理
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Configuration")]
    public async Task Apply_InvalidTierConfiguration_ShouldHandleGracefully()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateGradientCashDiscountRule_InvalidConfig();
        var cart = TestDataGenerator.CreateSimpleTestCart();

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "无效梯度配置场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0m, result.TotalDiscount);
    }

    #endregion
}