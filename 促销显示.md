需求文档：购物车商品促销展示与选择功能

版本： 1.0 日期： 2025年6月7日 负责人： [待填写]

1. 引言

本文档旨在详细描述电商平台购物车页面内，商品级别促销信息的展示与用户选择功能的具体需求。目标是提升用户购物体验，帮助用户清晰了解并自主选择商品可享受的优惠，从而提高转化率和用户满意度。

2. 目标用户

电商平台购物用户。

3. 核心需求描述

在电商平台的购物车页面，当用户查看购物车中的商品时，系统需要清晰地展示每个商品当前可用的促销方案。用户应能直观地看到每个促销方案的条件是否已经满足，并且能够通过下拉列表的形式为该商品自主选择一个希望应用的促销。

4. 用户故事 (User Stories)

US001：作为一名普通购物用户， 我希望在购物车里直接看到我的商品有哪些可用的优惠活动，以及这些优惠是否已经满足条件，以便于 我能快速了解能省多少钱，而不需要到处寻找或记忆复杂的规则。
US002：作为一名精打细算的购物用户， 当一个商品有多个可用优惠时，我希望能自己选择应用哪个优惠，以便于 我能根据自己的需求选择最划算或最合适的那个（比如选择折扣最大的，或者选择送我更想要的赠品）。
US003：作为一名追求效率的购物用户， 我希望在我调整购物车商品（如修改数量）后，相关的促销信息和满足状态能立即更新，以便于 我能实时看到最新的优惠情况并做出决策。
US004：作为一名普通购物用户， 我希望选择促销的过程简单明了，选择后能立刻看到价格变化，以便于 我能确认优惠已生效。
5. 功能性需求 (Functional Requirements)

5.1. 促销信息展示

FR001：促销方案获取
系统应能根据购物车内每个商品的ID、分类、品牌、用户标签（如会员等级）等信息，实时从促销引擎或促销规则库中查询并获取该商品当前所有可用的促销方案列表。
查询应考虑促销的有效期、适用范围、库存（如赠品库存）等。
FR002：促销信息内容
在购物车商品列表的每个商品项旁，应展示以下促销信息（根据实际情况可调整）：
促销标签/角标： 简短醒目的标签，如“满减”、“折扣”、“赠品”、“买赠”。
促销名称/描述： 清晰描述促销内容，例如：“满2件享8折”、“满100减10元”、“买1送1（赠送同款商品）”、“购买即赠价值¥20小风扇”。
促销类型： （内部标记，也可选择性展示）如：满减、满折、满赠、单品折扣、赠品促销等。
优惠详情： 具体优惠的额度或内容，例如：“可减¥10”、“享8折优惠”、“赠送[赠品名称]”。
生效条件： 清晰说明享受此优惠需要满足的条件，例如：“本商品购买满2件”、“订单金额满¥199（本商品参与凑单）”、“需购买指定商品A”。
有效期： （可选，若为短期促销建议展示）例如：“有效期至 YYYY-MM-DD”。
FR003：促销信息关联与布局
每个商品的可用促销信息应清晰地展示在该商品行的下方或右侧专属区域，确保用户能明确促销与商品的对应关系。
若一个商品有多个可用促销，应以列表形式或可展开/折叠形式展示。
5.2. 促销条件满足状态指示

FR004：条件满足状态判断与展示
系统需实时判断每个可用促销方案的条件是否已基于当前购物车状态（商品数量、组合等）被满足。
应通过明确的视觉元素向用户展示条件满足状态：
已满足： 使用醒目的颜色（如绿色）、图标（如✓）或文字提示（如“已满足，可选择”）。
未满足： 使用不同的颜色（如灰色或橙色）、图标或文字提示，并尽可能清晰地指示差距，例如：“未满足（还差1件）”、“未满足（还差¥20可享）”。
不可用/冲突： （如果适用）对于因与其他已选促销冲突而不可选的促销，也应有明确提示。
FR005：状态实时更新
当用户在购物车中进行以下操作时，系统必须实时重新计算并更新所有相关商品的促销满足状态及优惠信息：
增加/减少商品数量
从购物车删除商品
添加新商品到购物车（如果影响凑单类促销）
更改商品规格（如果规格影响促销）
更新应平滑进行，避免页面闪烁或加载过慢。
5.3. 用户选择促销

FR006：促销选择方式
对于每个商品，如果其有多个“已满足”或“即将满足”（用户可通过简单操作如增加数量即满足）的可用促销，系统应提供一个下拉列表（或其他清晰的选择控件，如下拉菜单按钮），允许用户从中选择一个希望应用的促销。
下拉列表中应清晰展示促销的简要描述和优惠力度。
默认情况下，如果只有一个促销满足条件，可考虑自动应用或仍需用户确认。
FR007：促销应用规则（单商品）
默认规则： 一个商品在同一时间原则上只能应用一个商品级别的促销方案。如果后续需求允许叠加，需另行定义叠加规则。
默认选择：
方案一（推荐）： 不默认选择。如果多个促销可用，提示用户“有X个优惠可选”，并引导用户打开下拉列表进行选择。
方案二： 默认选择优惠力度最大的一个（需明确优惠力度比较逻辑，如折扣金额、折扣率等）。
方案三： 默认选择优先级最高的促销（需后台配置促销优先级）。
用户选择后，该选择应被记录，并在当前会话中保持，直至用户更改或结算。
FR008：选择后界面响应
用户从下拉列表中选择一个促销后：
下拉列表收起，并显示当前选中的促销名称或核心优惠信息。
商品的小计价格应立即更新，以反映应用该促销后的价格。
购物车总计的优惠金额和应付金额也应同步更新。
可以有轻微的视觉反馈，如高亮选中的促销或短暂的“已应用”提示。
FR009：更改与取消促销
用户应能够方便地更改已选的促销。通过再次点击促销选择区域或下拉列表，可以重新选择其他可用促销。
用户应能够取消已选的促销，恢复到不应用任何促销的状态（或系统默认促销状态，如果有）。下拉列表中可以提供一个“不使用优惠”或“取消选择”的选项。
更改或取消后，价格信息需实时更新。
5.4. 价格计算与显示

FR010：价格实时计算
当用户选择、更改或取消商品促销时，系统必须实时、准确地重新计算以下价格信息：
该商品的优惠金额。
该商品的小计金额（应用促销后）。
整个购物车的商品总额、总优惠金额、应付总额。
FR011：价格清晰展示
商品原价、优惠金额、促销后价格应清晰展示在商品行。
购物车底部的结算信息区域，应清晰汇总所有商品优惠，展示总的优惠金额。
5.5. 促销规则与优先级 (初步)

FR012：促销互斥与叠加（本期范围）
本期需求主要关注单个商品级别的促销选择。默认情况下，商品级促销之间是互斥的，用户只能选择一个。
暂不详细考虑商品促销与订单促销（如全场满减、优惠券）之间的复杂叠加与互斥逻辑，但系统设计时应预留接口或考虑未来扩展性。若订单促销已应用，商品促销的选择不应导致订单促销失效（除非规则明确定义）。
FR013：促销推荐逻辑（可选）
如果一个商品有多个满足条件的促销可供选择，系统可考虑在下拉列表中对“最优”或“推荐”的促销进行标记（例如，标记“省最多”、“推荐”）。推荐逻辑需单独定义（如按优惠金额、折扣率、或业务运营需求排序）。
6. 非功能性需求 (Non-Functional Requirements)

NFR001：性能
购物车页面加载时，促销信息的获取和展示不应显著增加页面加载时间（例如，促销信息异步加载，首屏商品信息优先）。
用户选择促销后，价格和促销状态的更新响应时间应在1秒以内，确保流畅体验。
NFR002：易用性
促销信息的展示应直观易懂，用户无需过多思考即可理解优惠内容和条件。
促销选择操作应简单、便捷，符合用户常规操作习惯。
错误提示和引导信息应清晰友好。
NFR003：准确性
促销条件的判断必须100%准确。
应用促销后的价格计算必须100%准确。
促销信息的展示（如有效期、规则描述）必须与后台配置一致。
NFR004：可扩展性
促销规则和类型的增加不应导致前端需要大规模重构。系统设计应考虑未来新促销类型的接入。
7. UI/UX 设计初步建议

7.1. 布局建议：
在购物车每个商品项的下方，增加一个“促销活动”区域。
如果无可用促销，则此区域可不显示或提示“暂无可用优惠”。
如果有一个或多个可用促销：
默认展示最显著的一个或几个促销的标签/简述。
提供一个“查看更多优惠”或直接是一个下拉箭头图标，点击后展开/弹出下拉列表显示所有可用促销。
每个促销条目应包含：促销描述、满足状态（及未满足时的差距提示）、选择控件（如单选按钮或直接点击条目选择）。
7.2. 视觉引导：
使用不同颜色和图标区分促销的“已满足”、“未满足”、“已选中”状态。
例如：绿色✓表示已满足，橙色!表示未满足但接近，灰色表示未满足且差距较大。
选中的促销应有明显高亮或背景色变化。
优惠金额或节省的金额使用醒目颜色（如红色或橙色）突出显示。
7.3. 交互流程简要说明：
用户进入购物车，系统加载商品列表及各商品可用促销信息和初始满足状态。
用户查看某商品，可见其下方的促销信息。
若有多个促销，用户点击下拉箭头/按钮，展开促销列表。
列表中清晰展示各促销详情及满足状态。
用户点击选择一个“已满足”的促销。
列表收起（或保持展开但选中项高亮），商品价格、购物车总价实时更新。选中的促销信息在商品旁固定显示。
用户可再次点击，更改选择或取消选择。
用户修改商品数量，所有相关促销状态和价格实时更新。


9. 待讨论/未来考虑 (可选)

多种商品促销叠加的复杂规则。
商品促销与订单级促销（优惠券、全场活动）的叠加与互斥优先级详细规则。
促销凑单的智能提示与引导（例如，“再买XX元，即可享受YY优惠”）。