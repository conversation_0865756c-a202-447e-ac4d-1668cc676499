using System.Text.Json.Serialization;
using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.SpecialPriceRules;

/// <summary>
/// 特价促销规则基类
/// 核心原则：通过修改商品的ActualUnitPrice来实现特价效果
/// </summary>
public abstract class BaseSpecialPriceRule : PromotionRuleBase
{
    /// <summary>
    /// 特价商品选择策略：客户利益最大化 vs 商家利益最大化
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public BenefitSelectionStrategy SpecialPriceSelectionStrategy { get; set; } =
        BenefitSelectionStrategy.CustomerBenefit;

    /// <summary>
    /// 验证特价商品是否在购物车中
    /// </summary>
    protected bool ValidateSpecialPriceProductsInCart(ShoppingCart cart, List<string> productIds) =>
        cart.Items.Any(x => x.Quantity > 0 && productIds.Contains(x.Product.Id));

    /// <summary>
    /// 计算商品的总数量
    /// </summary>
    protected int CalculateTotalQuantity(ShoppingCart cart, List<string> productIds)
    {
        return productIds.Sum(id => cart.GetAvailableProductQuantity(id));
    }

    /// <summary>
    /// 计算商品的总金额
    /// </summary>
    protected decimal CalculateTotalAmount(ShoppingCart cart, List<string> productIds)
    {
        return productIds.Sum(id => cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));
    }

    /// <summary>
    /// 应用特价到指定商品项
    /// </summary>
    protected void ApplySpecialPriceToCartItem(
        CartItem cartItem,
        decimal specialPrice,
        AppliedPromotion promotion,
        string description
    )
    {
        var originalPrice = cartItem.UnitPrice;
        var discountAmount = originalPrice - specialPrice;

        // 应用特价
        cartItem.ActualUnitPrice = specialPrice;

        // 记录促销详情
        var promotionDetail = new ItemPromotionDetail
        {
            RuleId = promotion.RuleId,
            RuleName = promotion.RuleName,
            PromotionType = promotion.PromotionType,
            DiscountAmount = discountAmount * cartItem.Quantity,
            Description = description,
            IsGiftRelated = false
        };

        cartItem.AddPromotionDetail(promotionDetail);
    }

    /// <summary>
    /// 根据策略排序商品（用于优化特价分配）
    /// </summary>
    protected List<CartItem> SortItemsByStrategy(List<CartItem> items)
    {
        return SpecialPriceSelectionStrategy switch
        {
            BenefitSelectionStrategy.CustomerBenefit
                => items.OrderByDescending(x => x.UnitPrice).ToList(), // 客户利益：优先给高价商品特价
            BenefitSelectionStrategy.MerchantBenefit => items.OrderBy(x => x.UnitPrice).ToList(), // 商家利益：优先给低价商品特价
            _ => items.OrderByDescending(x => x.UnitPrice).ToList()
        };
    }

    /// <summary>
    /// 创建特价记录（作为GiftItem记录，但实际是特价）
    /// </summary>
    protected GiftItem CreateSpecialPriceRecord(
        string productId,
        string productName,
        int quantity,
        decimal discountAmount,
        string description
    )
    {
        return new GiftItem
        {
            ProductId = productId,
            ProductName = productName,
            Quantity = quantity,
            Value = discountAmount,
            Description = description
        };
    }
}
