[{"$type": "BuyXGetY", "id": "UNIFIED_GIFT_001", "name": "购买A商品1件送1件B商品", "description": "统一送赠品：购买A商品1件时，免费送1件B商品", "priority": 20, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftType": "UnifiedGift", "gradientStrategy": "ByGradient", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1}], "giftConditions": [{"productIds": ["B"], "giftQuantity": 1}], "combinationConditions": [], "gradientGiftConditions": []}, {"$type": "BuyXGetY", "id": "UNIFIED_GIFT_002", "name": "购买A商品2件送2件B商品（翻倍）", "description": "统一送赠品：购买A商品2件时，免费送2件B商品（活动设置为翻倍）", "priority": 20, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 2, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftType": "UnifiedGift", "gradientStrategy": "ByGradient", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1}], "giftConditions": [{"productIds": ["B"], "giftQuantity": 1}], "combinationConditions": [], "gradientGiftConditions": []}, {"$type": "BuyXGetY", "id": "GRADIENT_GIFT_001", "name": "梯度送赠品（按梯度送）", "description": "购买A商品>=1件送1件B，>=2件送2件C（按梯度送，不翻倍）", "priority": 25, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftType": "GradientGift", "gradientStrategy": "ByGradient", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1}], "giftConditions": [], "combinationConditions": [], "gradientGiftConditions": [{"gradientLevel": 1, "requiredQuantity": 1, "requiredAmount": 0, "giftProductIds": ["B"], "giftQuantity": 1, "description": "购买1件A送1件B"}, {"gradientLevel": 2, "requiredQuantity": 2, "requiredAmount": 0, "giftProductIds": ["C"], "giftQuantity": 2, "description": "购买2件A送2件C"}]}, {"$type": "BuyXGetY", "id": "GRADIENT_GIFT_002", "name": "梯度送赠品（全部送）", "description": "购买A商品>=1件送1件B，>=2件送2件C（全部送，翻倍）", "priority": 25, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 2, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftType": "GradientGift", "gradientStrategy": "SendAll", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1}], "giftConditions": [], "combinationConditions": [], "gradientGiftConditions": [{"gradientLevel": 1, "requiredQuantity": 1, "requiredAmount": 0, "giftProductIds": ["B"], "giftQuantity": 1, "description": "购买1件A送1件B"}, {"gradientLevel": 2, "requiredQuantity": 2, "requiredAmount": 0, "giftProductIds": ["C"], "giftQuantity": 2, "description": "购买2件A送2件C"}]}, {"$type": "BuyXGetY", "id": "COMBINATION_GIFT_001", "name": "组合送赠品（不翻倍）", "description": "购买A商品和B商品各1件时，送1件C商品", "priority": 30, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": false, "maxApplications": 1, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftType": "CombinationGift", "gradientStrategy": "ByGradient", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "buyConditions": [], "giftConditions": [{"productIds": ["C"], "giftQuantity": 1}], "combinationConditions": [{"productId": "A", "requiredQuantity": 1, "minAmount": 0}, {"productId": "B", "requiredQuantity": 1, "minAmount": 0}], "gradientGiftConditions": []}, {"$type": "BuyXGetY", "id": "COMBINATION_GIFT_002", "name": "组合送赠品（翻倍）", "description": "购买A商品和B商品各1件时，送1件C商品（可翻倍）", "priority": 30, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 2, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": false, "productExclusivity": "Exclusive", "giftType": "CombinationGift", "gradientStrategy": "ByGradient", "isByAmount": false, "minAmount": 0, "giftSameProduct": false, "buyConditions": [], "giftConditions": [{"productIds": ["C"], "giftQuantity": 1}], "combinationConditions": [{"productId": "A", "requiredQuantity": 1, "minAmount": 0}, {"productId": "B", "requiredQuantity": 1, "minAmount": 0}], "gradientGiftConditions": []}, {"$type": "BuyXGetY", "id": "AMOUNT_BASED_GIFT", "name": "按金额送赠品", "description": "购买A商品满1000元，送1件B商品", "priority": 15, "isEnabled": true, "startTime": "2024-01-01T00:00:00", "endTime": "2025-12-31T23:59:59", "isRepeatable": true, "maxApplications": 0, "applicableCustomerTypes": [], "exclusiveRuleIds": [], "canStackWithOthers": true, "productExclusivity": "None", "giftType": "UnifiedGift", "gradientStrategy": "ByGradient", "isByAmount": true, "minAmount": 1000, "giftSameProduct": false, "buyConditions": [{"productIds": ["A"], "requiredQuantity": 1}], "giftConditions": [{"productIds": ["B"], "giftQuantity": 1}], "combinationConditions": [], "gradientGiftConditions": []}]