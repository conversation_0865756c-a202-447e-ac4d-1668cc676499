using PE2.PromotionEngine.Rules;
using PE2.PromotionEngine.Rules.BuyFreeRules;
using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.BuyFreeRules;

/// <summary>
/// 商品买免规则测试类
/// 测试 ProductBuyFreeRule 的买免计算和应用逻辑
/// </summary>
public class ProductBuyFreeRuleTests(ITestOutputHelper output) : TestBase(output)
{
    #region 基础买免功能测试

    [Fact]
    [Trait("Category", "ProductBuyFree")]
    [Trait("Priority", "High")]
    public void Apply_ValidBuyFreeScenario_ShouldApplyCorrectly()
    {
        // Arrange - 买3免1：买3件A免1件A（不翻倍）
        var rule = new ProductBuyFreeRule
        {
            Id = "BUY_FREE_001",
            Name = "商品买免测试 - 买3免1（不翻倍）",
            Description = "购买A商品3件时，免1件",
            Priority = 70,
            IsEnabled = true,
            IsRepeatable = false,
            MaxApplications = 1,
            FreeItemSelectionStrategy = BenefitSelectionStrategy.MerchantBenefit,
            ApplicableProductIds = ["A"],
            BuyQuantity = 3,
            FreeQuantity = 1
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            (TestDataGenerator.CreateProductA(), 4) // 4件A，买3免1，应免费1件
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "买免测试购物车");

        var originalTotal = cart.Items.Sum(i => i.UnitPrice * i.Quantity); // 4*50 = 200元
        var expectedFreeAmount = TestDataGenerator.CreateProductA().Price; // 免费1件A = 50元

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "买3免1");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证总优惠金额
        AssertAmountEqual(expectedFreeAmount, result.TotalDiscount, "应免费1件A商品价值50元");

        // 验证购物车中有免费商品
        var cartAfter = result.ProcessedCart;
        var freeItems = result.AppliedPromotions[0].GiftItems;//cartAfter.Items.Where(i => i.ActualUnitPrice == 0).ToList();
        Assert.NotEmpty(freeItems);

        var totalFreeQuantity = freeItems.Sum(i => i.Quantity);
        Assert.Equal(1, totalFreeQuantity);

        // 验证最终总金额
        var finalTotal = cartAfter.Items.Sum(i => i.ActualUnitPrice * i.Quantity);
        AssertAmountEqual(originalTotal - expectedFreeAmount, finalTotal, "最终总金额应减少免费商品价值");
    }

    [Fact]
    [Trait("Category", "ProductBuyFree")]
    [Trait("Priority", "High")]
    public void Apply_InsufficientQuantityToBuy_ShouldNotApply()
    {
        // Arrange - 数量不足：只有2件A，需要买3件
        var rule = new ProductBuyFreeRule
        {
            Id = "BUY_FREE_001",
            Name = "商品买免测试 - 买3免1",
            Priority = 70,
            IsEnabled = true,
            ApplicableProductIds = ["A"],
            BuyQuantity = 3,
            FreeQuantity = 1
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_002",
            (TestDataGenerator.CreateProductA(), 2) // 只有2件A，不满足买3的条件
        );

        LogCartDetails(cart, "数量不足的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "数量不足场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "数量不足时应无优惠");
    }

    [Fact]
    [Trait("Category", "ProductBuyFree")]
    [Trait("Priority", "High")]
    public void Apply_ExactRequiredQuantity_ShouldNotApply()
    {
        // Arrange - 恰好满足买条件但无额外商品免费：3件A
        var rule = new ProductBuyFreeRule
        {
            Id = "BUY_FREE_001",
            Name = "商品买免测试 - 买3免1",
            Priority = 70,
            IsEnabled = true,
            ApplicableProductIds = ["A"],
            BuyQuantity = 3,
            FreeQuantity = 1
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_003",
            (TestDataGenerator.CreateProductA(), 3) // 恰好3件A，买3免1，但没有额外的A可免费
        );

        LogCartDetails(cart, "恰好满足条件的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "恰好满足条件");
        LogPromotionResultDetails(result);

        // 买3免1需要至少4件商品（3件付费+1件免费）
        // 如果只有3件，无法应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");
    }

    #endregion

    #region 多次应用测试

    [Fact]
    [Trait("Category", "ProductBuyFree")]
    [Trait("Priority", "High")]
    public void Apply_MultipleApplications_ShouldApplyMultipleTimes()
    {
        // Arrange - 多次应用：买3免1，支持翻倍
        var rule = new ProductBuyFreeRule
        {
            Id = "BUY_FREE_002",
            Name = "商品买免测试 - 买3免1（翻2倍）",
            Priority = 75,
            IsEnabled = true,
            IsRepeatable = true,
            MaxApplications = 2,
            ApplicableProductIds = ["A"],
            BuyQuantity = 3,
            FreeQuantity = 1
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_004",
            (TestDataGenerator.CreateProductA(), 8) // 8件A，可以买6免2
        );

        LogCartDetails(cart, "多次应用测试购物车");

        var expectedFreeQuantity = 2; // 应免费2件
        var expectedFreeAmount = expectedFreeQuantity * TestDataGenerator.CreateProductA().Price;

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "多次应用");
        LogPromotionResultDetails(result);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证总优惠金额
        AssertAmountEqual(expectedFreeAmount, result.TotalDiscount, $"应免费{expectedFreeQuantity}件A商品");

        // 验证免费商品数量
        //var cartAfter = result.ProcessedCart;
        //var freeItems = cartAfter.Items.Where(i => i.ActualUnitPrice == 0).ToList();
        //var totalFreeQuantity = freeItems.Sum(i => i.Quantity);
        var totalFreeQuantity = result.AppliedPromotions[0].ApplicationCount; // 应用次数应为2
        Assert.Equal(expectedFreeQuantity, totalFreeQuantity);
    }

    [Fact]
    [Trait("Category", "ProductBuyFree")]
    [Trait("Priority", "Medium")]
    public void Apply_MaxApplicationLimit_ShouldRespectLimit()
    {
        // Arrange - 限制最大应用次数：买3免1，最多应用1次
        var rule = new ProductBuyFreeRule
        {
            Id = "BUY_FREE_001",
            Name = "商品买免测试 - 买3免1（不翻倍）",
            Priority = 70,
            IsEnabled = true,
            IsRepeatable = false,
            MaxApplications = 1,
            ApplicableProductIds = ["A"],
            BuyQuantity = 3,
            FreeQuantity = 1
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_005",
            (TestDataGenerator.CreateProductA(), 10) // 10件A，理论上可以买9免3，但限制为买3免1
        );

        LogCartDetails(cart, "最大应用次数限制测试购物车");

        var expectedFreeQuantity = 1; // 限制为最多免费1件
        var expectedFreeAmount = expectedFreeQuantity * TestDataGenerator.CreateProductA().Price;

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "最大应用次数限制");
        LogPromotionResultDetails(result);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证只应用了1次
        AssertAmountEqual(expectedFreeAmount, result.TotalDiscount, "应受最大应用次数限制");

        //var cartAfter = result.ProcessedCart;
        //var freeItems = cartAfter.Items.Where(i => i.ActualUnitPrice == 0).ToList();
        //var totalFreeQuantity = freeItems.Sum(i => i.Quantity);
        var totalFreeQuantity = result.AppliedPromotions[0].ApplicationCount; // 应用次数应为1
        Assert.Equal(expectedFreeQuantity, totalFreeQuantity);
    }

    #endregion

    #region 客户利益最大化测试

    [Fact]
    [Trait("Category", "ProductBuyFree")]
    [Trait("Priority", "Medium")]
    public void Apply_CustomerBenefitStrategy_ShouldFreeHighestPriceItems()
    {
        // Arrange - 客户利益最大化：优先免费高价商品
        var rule = new ProductBuyFreeRule
        {
            Id = "BUY_FREE_003",
            Name = "商品买免测试 - 客户利益最大化",
            Priority = 65,
            IsEnabled = true,
            FreeItemSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit,
            ApplicableProductIds = ["A", "B"],
            BuyQuantity = 3,
            FreeQuantity = 1
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_006",
            (TestDataGenerator.CreateProductA(), 2), // A商品50元
            (TestDataGenerator.CreateProductB(), 2)  // B商品30元
        );

        LogCartDetails(cart, "客户利益最大化测试购物车");

        // 应该免费A商品（价格更高）
        var expectedFreeAmount = TestDataGenerator.CreateProductA().Price;

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "客户利益最大化");
        LogPromotionResultDetails(result);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证免费了高价商品A
        AssertAmountEqual(expectedFreeAmount, result.TotalDiscount, "应免费高价商品A");

        //var cartAfter = result.ProcessedCart;
        //var freeItemA = cartAfter.Items.FirstOrDefault(i => i.Product.Id == "A" && i.ActualUnitPrice == 0);
        //Assert.NotNull(freeItemA);
        //Assert.Equal(1, freeItemA.Quantity);
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "ProductBuyFree")]
    [Trait("Priority", "Low")]
    public void Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange
        var rule = new ProductBuyFreeRule
        {
            Id = "BUY_FREE_001",
            Name = "商品买免测试",
            Priority = 70,
            IsEnabled = true,
            ApplicableProductIds = ["A"],
            BuyQuantity = 3,
            FreeQuantity = 1
        };

        var cart = TestDataGenerator.CreateEmptyCart();

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "空购物车");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertAmountEqual(0m, result.TotalDiscount, "空购物车应无优惠");
    }

    [Fact]
    [Trait("Category", "ProductBuyFree")]
    [Trait("Priority", "Low")]
    public void Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange
        var rule = new ProductBuyFreeRule
        {
            Id = "BUY_FREE_001",
            Name = "商品买免测试",
            Priority = 70,
            IsEnabled = false, // 禁用规则
            ApplicableProductIds = ["A"],
            BuyQuantity = 3,
            FreeQuantity = 1
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_009",
            (TestDataGenerator.CreateProductA(), 4)
        );

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "禁用规则");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");
        AssertAmountEqual(0m, result.TotalDiscount, "禁用规则应无优惠");
    }

    #endregion

    #region 性能测试

    [Fact]
    [Trait("Category", "ProductBuyFree")]
    [Trait("Priority", "Low")]
    public void Apply_LargeCartWithBuyFree_ShouldPerformWell()
    {
        // Arrange - 大型购物车
        var rule = new ProductBuyFreeRule
        {
            Id = "BUY_FREE_PERFORMANCE",
            Name = "性能测试买免规则",
            Priority = 70,
            IsEnabled = true,
            IsRepeatable = true,
            ApplicableProductIds = ["A"],
            BuyQuantity = 3,
            FreeQuantity = 1
        };

        var cart = TestDataGenerator.CreateLargeTestCart(1000); // 1000个商品项

        Output.WriteLine($"性能测试: {cart.Items.Count} 个商品项");

        TestPromotionRuleService.Rules = [rule];
        // Act & Assert
        var executionTime = MeasureExecutionTime(() =>
        {
            var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;
            AssertPromotionResult(result, "性能测试");
        });

        // 性能断言：买免计算应在合理时间内完成
        AssertPerformance(executionTime, TimeSpan.FromMilliseconds(500), "买免规则计算");
    }

    #endregion

    #region 规则配置验证测试

    [Fact]
    [Trait("Category", "ProductBuyFree")]
    [Trait("Priority", "Medium")]
    public void ValidateRule_ValidConfiguration_ShouldPass()
    {
        // Arrange
        var rule = new ProductBuyFreeRule
        {
            Id = "BUY_FREE_001",
            Name = "商品买免测试",
            Priority = 70,
            IsEnabled = true,
            ApplicableProductIds = ["A"],
            BuyQuantity = 3,
            FreeQuantity = 1
        };

        // Act & Assert
        ValidateRuleBasicProperties(rule);

        // 验证买免规则特有属性
        Assert.NotNull(rule.ApplicableProductIds);
        Assert.NotEmpty(rule.ApplicableProductIds);
        Assert.True(rule.BuyQuantity > 0, "买数量或按金额条件必须有效");
        Assert.True(rule.FreeQuantity > 0, "免费数量必须大于0");

        foreach (var productId in rule.ApplicableProductIds)
        {
            Assert.False(string.IsNullOrEmpty(productId), "适用商品ID不能为空");
        }

        Output.WriteLine("商品买免规则配置验证通过 ✓");
    }

    #endregion
}