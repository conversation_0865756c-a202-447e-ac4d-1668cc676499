using System.Diagnostics;
using System.Text.Json.Serialization;
using Microsoft.Extensions.Logging;
using PE2.Models;
using PE2.PromotionEngine.Core;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.new.Core.Enums;
using PE2.PromotionEngine.new.Core.Interfaces;
using PE2.PromotionEngine.new.Core.Models;

namespace PE2.PromotionEngine.new.Core.Base;

/// <summary>
/// 新架构促销规则基类
/// 集成商品占用管理、条件执行分离和排他性管理功能
/// </summary>
public abstract class PromotionRuleBase : IPromotionRule
{
    protected readonly ILogger? _logger;
    
    protected PromotionRuleBase(ILogger? logger = null)
    {
        _logger = logger;
    }
    
    #region 基本属性
    
    /// <summary>
    /// 规则ID
    /// </summary>
    public string Id { get; set; } = string.Empty;
    
    /// <summary>
    /// 规则名称
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// 规则描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// 规则类型
    /// </summary>
    public abstract string RuleType { get; }
    
    /// <summary>
    /// 促销类型
    /// </summary>
    public abstract PromotionType PromotionType { get; }
    
    /// <summary>
    /// 优先级（数字越大优先级越高）
    /// </summary>
    public int Priority { get; set; } = 0;
    
    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;
    
    /// <summary>
    /// 生效时间
    /// </summary>
    public DateTime? StartTime { get; set; }
    
    /// <summary>
    /// 失效时间
    /// </summary>
    public DateTime? EndTime { get; set; }
    
    /// <summary>
    /// 是否可重复应用
    /// </summary>
    public bool IsRepeatable { get; set; } = true;
    
    /// <summary>
    /// 最大应用次数（0表示无限制）
    /// </summary>
    public int MaxApplications { get; set; } = 1;
    
    /// <summary>
    /// 适用的客户类型
    /// </summary>
    public List<string> ApplicableCustomerTypes { get; set; } = new();
    
    /// <summary>
    /// 排斥的规则ID列表（互斥规则）
    /// </summary>
    public List<string> ExclusiveRuleIds { get; set; } = new();
    
    /// <summary>
    /// 是否可与其他促销叠加
    /// </summary>
    public bool CanStackWithOthers { get; set; } = true;
    
    /// <summary>
    /// 商品互斥级别
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public ProductExclusivityLevel ProductExclusivity { get; set; } = ProductExclusivityLevel.None;
    
    /// <summary>
    /// 规则状态
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public RuleStatus Status { get; set; } = RuleStatus.Enabled;
    
    #endregion
    
    #region 核心方法实现
    
    /// <summary>
    /// 检查规则是否适用于指定购物车
    /// </summary>
    public virtual bool IsApplicable(ShoppingCart cart, DateTime checkTime)
    {
        if (!IsEnabled || Status != RuleStatus.Enabled)
            return false;
        
        if (!IsInValidPeriod(checkTime))
            return false;
        
        return CheckConditions(cart);
    }
    
    /// <summary>
    /// 检查规则是否在有效期内
    /// </summary>
    public virtual bool IsInValidPeriod(DateTime checkTime)
    {
        if (StartTime.HasValue && checkTime < StartTime.Value)
            return false;
        
        if (EndTime.HasValue && checkTime > EndTime.Value)
            return false;
        
        return true;
    }
    
    /// <summary>
    /// 计算可应用的最大次数
    /// </summary>
    public abstract int CalculateMaxApplications(ShoppingCart cart);
    
    #endregion
    
    #region 商品占用管理集成
    
    /// <summary>
    /// 使用占用会话验证条件
    /// </summary>
    public virtual async Task<ValidationResult> ValidateConditionsAsync(
        ShoppingCart cart, 
        IOccupationSession session, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger?.LogDebug("开始验证促销条件，规则ID: {RuleId}, 购物车ID: {CartId}", Id, cart.Id);
            
            // 基础验证
            if (!IsApplicable(cart, DateTime.UtcNow))
            {
                return ValidationResult.Failure("规则不适用于当前购物车");
            }
            
            // 获取占用请求
            var occupationRequests = GetOccupationRequests(cart);
            
            // 尝试占用商品（条件验证阶段）
            var conditionRequests = occupationRequests
                .Where(r => r.ReservationType == ReservationType.Condition)
                .ToList();
            
            if (conditionRequests.Any())
            {
                var canOccupy = await TryOccupyProductsAsync(session, conditionRequests, cancellationToken)
                    .ConfigureAwait(false);
                
                if (!canOccupy)
                {
                    return ValidationResult.Failure("无法占用所需商品进行条件验证");
                }
            }
            
            // 执行具体的条件验证逻辑
            var validationResult = await ValidateSpecificConditionsAsync(cart, session, cancellationToken)
                .ConfigureAwait(false);
            
            stopwatch.Stop();
            
            _logger?.LogDebug("促销条件验证完成，规则ID: {RuleId}, 结果: {IsValid}, 耗时: {ElapsedMs}ms", 
                Id, validationResult.IsValid, stopwatch.ElapsedMilliseconds);
            
            return validationResult with { ElapsedMilliseconds = stopwatch.ElapsedMilliseconds };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger?.LogError(ex, "促销条件验证失败，规则ID: {RuleId}", Id);
            return ValidationResult.Failure($"验证过程中发生错误: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 使用占用会话执行促销
    /// </summary>
    public virtual async Task<ExecutionResult> ExecutePromotionAsync(
        ShoppingCart cart, 
        IOccupationSession session, 
        int applicationCount = 1,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger?.LogDebug("开始执行促销，规则ID: {RuleId}, 应用次数: {ApplicationCount}", Id, applicationCount);
            
            // 获取执行阶段的占用请求
            var occupationRequests = GetOccupationRequests(cart);
            var executionRequests = occupationRequests
                .Where(r => r.ReservationType == ReservationType.Execution)
                .ToList();
            
            // 尝试占用商品（执行阶段）
            if (executionRequests.Any())
            {
                var canOccupy = await TryOccupyProductsAsync(session, executionRequests, cancellationToken)
                    .ConfigureAwait(false);
                
                if (!canOccupy)
                {
                    return ExecutionResult.Failure("无法占用所需商品进行促销执行");
                }
            }
            
            // 执行具体的促销逻辑
            var executionResult = await ExecuteSpecificPromotionAsync(cart, session, applicationCount, cancellationToken)
                .ConfigureAwait(false);
            
            stopwatch.Stop();
            
            _logger?.LogDebug("促销执行完成，规则ID: {RuleId}, 成功: {IsSuccessful}, 折扣: {DiscountAmount}, 耗时: {ElapsedMs}ms", 
                Id, executionResult.IsSuccessful, executionResult.DiscountAmount, stopwatch.ElapsedMilliseconds);
            
            return executionResult with { ElapsedMilliseconds = stopwatch.ElapsedMilliseconds };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger?.LogError(ex, "促销执行失败，规则ID: {RuleId}", Id);
            return ExecutionResult.Failure($"执行过程中发生错误: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 获取规则需要占用的商品信息
    /// </summary>
    public abstract List<OccupationRequest> GetOccupationRequests(ShoppingCart cart);
    
    #endregion
    
    #region 抽象方法
    
    /// <summary>
    /// 检查促销条件是否满足
    /// </summary>
    protected abstract bool CheckConditions(ShoppingCart cart);
    
    /// <summary>
    /// 验证具体的促销条件
    /// </summary>
    protected abstract Task<ValidationResult> ValidateSpecificConditionsAsync(
        ShoppingCart cart, 
        IOccupationSession session, 
        CancellationToken cancellationToken);
    
    /// <summary>
    /// 执行具体的促销逻辑
    /// </summary>
    protected abstract Task<ExecutionResult> ExecuteSpecificPromotionAsync(
        ShoppingCart cart, 
        IOccupationSession session, 
        int applicationCount,
        CancellationToken cancellationToken);
    
    #endregion
    
    #region 兼容性方法
    
    /// <summary>
    /// 传统方式应用促销（向后兼容）
    /// </summary>
    public abstract PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1);
    
    #endregion
    
    #region 辅助方法
    
    /// <summary>
    /// 尝试占用商品
    /// </summary>
    protected virtual async Task<bool> TryOccupyProductsAsync(
        IOccupationSession session,
        List<OccupationRequest> requests,
        CancellationToken cancellationToken)
    {
        foreach (var request in requests)
        {
            var success = await session.TryReserveForConditionAsync(
                request.ProductId,
                request.Quantity,
                Id,
                ProductExclusivity,
                cancellationToken).ConfigureAwait(false);
            
            if (!success)
            {
                return false;
            }
        }
        
        return true;
    }
    
    #endregion
}
