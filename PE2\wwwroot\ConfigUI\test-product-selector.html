<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品选择器测试页面</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    
    <style>
        .test-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>商品选择器测试页面</h1>
            
            <div class="test-section">
                <h2>单选商品测试</h2>
                <product-selector
                    :model-value="singleProductId"
                    @update:model-value="value => singleProductId = value"
                    placeholder="请选择单个商品"
                />
                
                <div style="margin-top: 10px;">
                    <strong>选中的商品ID:</strong> {{ singleProductId }}
                </div>
            </div>
            
            <div class="test-section">
                <h2>多选商品测试</h2>
                <product-selector
                    :model-value="multipleProductIds"
                    @update:model-value="value => multipleProductIds = value"
                    placeholder="请选择多个商品"
                    :multiple="true"
                />
                
                <div style="margin-top: 10px;">
                    <strong>选中的商品IDs:</strong> {{ JSON.stringify(multipleProductIds) }}
                </div>
            </div>
            
            <div class="test-section">
                <h2>禁用状态测试</h2>
                <product-selector
                    :model-value="['A', 'B']"
                    placeholder="禁用状态的选择器"
                    :multiple="true"
                    :disabled="true"
                />
            </div>
        </div>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.min.js"></script>
    
    <!-- 商品选择器组件 -->
    <script src="js/components/ProductSelector.js"></script>

    <script>
        const { createApp, ref } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                const singleProductId = ref('');
                const multipleProductIds = ref([]);

                return {
                    singleProductId,
                    multipleProductIds
                };
            }
        })
        .use(ElementPlus)
        .component('ProductSelector', ProductSelector)
        .mount('#app');
    </script>
</body>
</html>
