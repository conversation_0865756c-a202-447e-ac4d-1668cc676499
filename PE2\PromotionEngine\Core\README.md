# 促销编排器 (PromotionOrchestrator) 实现文档

## 概述

PromotionOrchestrator 是促销引擎重构第三阶段的核心组件，负责统一协调各个引擎组件，提供完整的促销计算流程编排和协调功能。

## 架构设计

### 核心组件集成

PromotionOrchestrator 整合了以下引擎组件：

1. **ObservabilityEngine** - 可观测性引擎，提供计算过程追踪和性能监控
2. **ConditionEngine** - 条件引擎，统一处理促销规则条件验证
3. **InventoryManager** - 库存管理器，处理商品数量预留和冲突解决
4. **PerformanceOptimizer** - 性能优化器，提供规则预筛选和组合优化
5. **AllocationEngine** - 分配引擎，处理精确的折扣分配算法

### 文件结构

```
PE2/PromotionEngine/Core/
├── PromotionOrchestrator.cs          # 主要实现文件
├── PromotionOrchestrator.Helpers.cs  # 辅助方法部分类
├── PromotionOrchestrator.Monitoring.cs # 监控和清理部分类
├── IPromotionOrchestrator.cs         # 接口定义
└── README.md                         # 本文档
```

## 主要功能

### 1. 完整促销计算流程 (`CalculatePromotionsAsync`)

执行7个阶段的促销计算流程：

1. **预分析和性能优化** (0-20%)
2. **规则预筛选** (20-40%)
3. **库存预留** (40-50%)
4. **条件验证** (50-60%)
5. **促销计算** (60-80%)
6. **折扣分配** (80-90%)
7. **结果验证和优化** (90-100%)

### 2. 促销预分析 (`PreAnalyzePromotionsAsync`)

- 分析购物车特征
- 评估规则适用性
- 预估优化潜力
- 识别潜在冲突

### 3. 兼容性验证 (`ValidatePromotionCompatibilityAsync`)

- 检查促销规则间的兼容性
- 识别互斥规则
- 提供冲突解决建议

### 4. 促销回滚 (`RollbackPromotionAsync`)

- 回滚库存预留
- 清理计算结果
- 提供完整的回滚追踪

### 5. 状态查询 (`GetCalculationStatusAsync`)

- 实时查询计算进度
- 提供详细的状态信息
- 支持并发计算状态管理

## 技术特性

### 并发控制

- 使用 `SemaphoreSlim` 控制最大并发计算数
- 默认并发数：`Environment.ProcessorCount * 4`
- 支持动态调整并发限制

### 内存管理

- 集成 ObjectPool<T> 模式减少GC压力
- 自动清理过期数据
- 内存使用监控和优化

### 性能监控

- 详细的性能指标收集
- 操作耗时统计
- 内存使用追踪
- 缓存命中率监控

### 错误处理

- 完整的异常处理机制
- 自动回滚失败的操作
- 详细的错误日志记录

## 配置选项

### 基本配置

```csharp
services.AddPromotionEngine(options =>
{
    options.MaxConcurrentCalculations = 16;
    options.ResultRetentionTime = TimeSpan.FromHours(2);
    options.EnablePerformanceMonitoring = true;
    options.EnableDetailedLogging = false;
});
```

### 缓存配置

```csharp
options.Cache.DefaultExpiration = TimeSpan.FromMinutes(30);
options.Cache.MaxCacheItems = 10000;
options.Cache.EnableDistributedCache = false;
```

### 内存池配置

```csharp
options.MemoryPool.StringBuilderPoolSize = 100;
options.MemoryPool.ListPoolSize = 50;
options.MemoryPool.DictionaryPoolSize = 50;
options.MemoryPool.EnableMemoryMonitoring = true;
```

## 使用示例

### 基本使用

```csharp
// 注入依赖
var orchestrator = serviceProvider.GetRequiredService<IPromotionOrchestrator>();

// 创建计算请求
var request = new PromotionCalculationRequest
{
    Id = Guid.NewGuid().ToString(),
    Cart = cart,
    AvailableRules = rules,
    Mode = CalculationMode.Automatic,
    Target = OptimizationTarget.MaxDiscount
};

// 执行计算
var result = await orchestrator.CalculatePromotionsAsync(request);

// 处理结果
if (result.IsSuccessful)
{
    Console.WriteLine($"优惠金额: {result.OptimalResult.TotalDiscount:C}");
    Console.WriteLine($"最终金额: {result.OptimalResult.FinalAmount:C}");
}
```

### 预分析使用

```csharp
// 执行预分析
var analysis = await orchestrator.PreAnalyzePromotionsAsync(cart, rules);

// 查看分析结果
Console.WriteLine($"适用规则数: {analysis.RuleApplicability.Count}");
Console.WriteLine($"优化复杂度: {analysis.OptimizationPotential.OptimizationComplexity}");
```

## 性能指标

### 目标性能

- **计算时间减少**: 60%（相比原有实现）
- **内存使用优化**: 50%（通过ObjectPool和缓存优化）
- **并发处理能力**: 支持高并发POS场景
- **响应时间**: 95%的计算在500ms内完成

### 监控指标

- 平均计算时间
- 最大/最小计算时间
- 成功率统计
- 缓存命中率
- 内存使用峰值
- 并发计算数

## 扩展性

### 自定义计算模式

可以通过实现自定义的计算逻辑来扩展PromotionOrchestrator：

```csharp
// 在 ExecutePromotionCalculationAsync 中添加新的计算模式
if (request.Mode == CalculationMode.Custom)
{
    result = await CalculateCustomPromotionsAsync(request.Cart, validatedRules, traceId);
}
```

### 自定义分配策略

可以通过AllocationEngine添加新的分配策略：

```csharp
var allocationRequest = new AllocationRequest
{
    Strategy = AllocationStrategy.CustomStrategy,
    // 其他配置...
};
```

## 测试建议

### 单元测试

1. `TestCalculatePromotionsAsync` - 测试完整促销计算流程
2. `TestPreAnalyzePromotionsAsync` - 测试促销预分析功能
3. `TestValidatePromotionCompatibilityAsync` - 测试促销兼容性验证
4. `TestRollbackPromotionAsync` - 测试促销回滚功能
5. `TestGetCalculationStatusAsync` - 测试计算状态查询
6. `TestPerformanceOptimization` - 测试性能优化效果
7. `TestConcurrentCalculations` - 测试并发计算处理
8. `TestErrorHandlingAndRecovery` - 测试错误处理和恢复

### 性能测试

使用 BenchmarkDotNet 进行性能基准测试：

```csharp
[Benchmark]
public async Task<PromotionCalculationResult> BenchmarkPromotionCalculation()
{
    return await _orchestrator.CalculatePromotionsAsync(_testRequest);
}
```

## 下一步计划

1. **规则迁移**: 将现有的24个促销规则迁移到新架构
2. **性能验证**: 验证60%计算时间减少和50%内存优化目标
3. **压力测试**: 在高并发POS场景下进行压力测试
4. **监控集成**: 集成APM工具进行生产环境监控

## 注意事项

1. **线程安全**: 所有公共方法都是线程安全的
2. **资源管理**: 使用 `using` 语句或手动调用 `Dispose()` 释放资源
3. **异常处理**: 所有异常都会被捕获并记录，不会影响系统稳定性
4. **配置验证**: 启动时会验证所有配置参数的有效性

## 联系信息

如有问题或建议，请联系促销引擎开发团队。
