using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.DiscountRules;

/// <summary>
/// 循环递增折扣规则 [OK]
/// 针对某一类商品，第1件X折，第2件Y折，第三件Z折，依次循环类推
///场景案例：A商品吊牌、零售价均为1000元，购买第一件打0.9折，第二件打0.8折，第三件打0.7折，
///          第四件打0.9折，第五件打0.8折，第六件打0.7折，依次循环。
///购买3件A：1000*0.9+1000*0.8+1000*0.7=2400元
///购买4件A：1000*0.9+1000*0.8+1000*0.7+1000*0.9=3300元
///购买5件A：1000*0.9+1000*0.8+1000*0.7+1000*0.9+1000*0.8=4100元
///备注：若优惠方式为客户利益最大化，则优先取最大金额的商品进行最大优惠的折扣；若优惠方式为商户利益最大化，则优先取最小金额的商品进行最大优惠。
/// </summary>
public class CyclicDiscountRule : BaseDiscountRule
{
    public override string RuleType => "CyclicDiscount";

    /// <summary>
    /// 适用的商品ID列表
    /// </summary>
    public List<string> ApplicableProductIds { get; set; } = [];

    /// <summary>
    /// 循环折扣列表（按顺序循环应用）
    /// </summary>
    public List<CyclicDiscountTier> CyclicTiers { get; set; } = new();

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!ApplicableProductIds.Any() || !CyclicTiers.Any())
            return false;

        // 验证商品是否在购物车中
        if (!ValidateDiscountProductsInCart(cart, ApplicableProductIds))
            return false;

        // 检查是否有足够的商品数量
        var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);
        return totalQuantity >= 1;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        // 循环递增折扣通常只应用一次，因为会对所有商品按循环顺序应用不同折扣
        return 1;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyCyclicDiscount(cart);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用循环递增折扣促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用循环递增折扣促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyCyclicDiscount(ShoppingCart cart)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var discountRecords = new List<GiftItem>(); // 折扣记录

        // 获取所有适用的商品项
        var applicableItems = cart.Items
            .Where(x => ApplicableProductIds.Contains(x.Product.Id) && x.Quantity > 0)
            .ToList();

        if (!applicableItems.Any())
            return (0m, consumedItems, discountRecords);

        // 创建优化的循环折扣排序
        var expandedItems = CreateOptimalCyclicDiscountOrder(applicableItems);

        // 按循环折扣规则应用折扣
        var processedItems = new Dictionary<CartItem, (int ProcessedQuantity, decimal TotalDiscount, List<decimal> DiscountRates)>();

        for (int itemIndex = 0; itemIndex < expandedItems.Count; itemIndex++)
        {
            var (item, unitPrice) = expandedItems[itemIndex];

            // 确定当前商品应用的循环折扣梯度
            var tierIndex = itemIndex % CyclicTiers.Count;
            var tier = CyclicTiers[tierIndex];

            var discountedPrice = unitPrice * tier.DiscountRate;
            var discountAmount = unitPrice - discountedPrice;

            if (discountAmount > 0)
            {
                totalDiscountAmount += discountAmount;

                // 记录处理的商品信息
                if (!processedItems.TryGetValue(item, out (int ProcessedQuantity, decimal TotalDiscount, List<decimal> DiscountRates) processed))
                {
                    processed = (0, 0m, new List<decimal>());
                    processedItems[item] = processed;
                }

                processed.ProcessedQuantity++;
                processed.TotalDiscount += discountAmount;
                processed.DiscountRates.Add(tier.DiscountRate);
                processedItems[item] = processed;
            }
        }

        // 应用折扣到购物车项并记录详情
        foreach (var kvp in processedItems)
        {
            var item = kvp.Key;
            var (processedQuantity, totalDiscount, discountRates) = kvp.Value;

            if (processedQuantity > 0 && totalDiscount > 0)
            {
                // 计算平均折扣率（用于显示）
                var avgDiscountRate = discountRates.Average();

                // 计算新的实际单价
                var newActualUnitPrice = item.UnitPrice - (totalDiscount / processedQuantity);
                item.ActualUnitPrice = Math.Max(0, newActualUnitPrice);

                var strategyDescription = DiscountSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                    ? "客户利益最大化"
                    : "商家利益最大化";

                // 构建循环折扣描述
                var cycleDescription = string.Join("→", CyclicTiers.Select(t => $"{t.DiscountRate:P1}"));
                var description = $"循环递增折扣：{cycleDescription}循环，平均{avgDiscountRate:P1}折，节省{totalDiscount:C}（{strategyDescription}）";

                // 记录促销详情
                var promotionDetail = new ItemPromotionDetail
                {
                    RuleId = Id,
                    RuleName = Name,
                    PromotionType = RuleType,
                    DiscountAmount = totalDiscount,
                    Description = description,
                    IsGiftRelated = false
                };

                item.AddPromotionDetail(promotionDetail);

                // 记录折扣
                discountRecords.Add(CreateDiscountRecord(
                    item.Product.Id,
                    item.Product.Name,
                    processedQuantity,
                    totalDiscount,
                    description
                ));

                // 记录消耗的商品
                var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == item.Product.Id);
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += processedQuantity;
                }
                else
                {
                    consumedItems.Add(new ConsumedItem
                    {
                        ProductId = item.Product.Id,
                        ProductName = item.Product.Name,
                        Quantity = processedQuantity,
                        UnitPrice = item.UnitPrice
                    });
                }
            }
        }

        return (totalDiscountAmount, consumedItems, discountRecords);
    }

    /// <summary>
    /// 创建优化的循环折扣排序
    /// 根据梯度折扣率的大小来优化商品排序
    /// </summary>
    private List<(CartItem Item, decimal UnitPrice)> CreateOptimalCyclicDiscountOrder(List<CartItem> applicableItems)
    {
        // 1. 展开所有商品
        var allItems = applicableItems.SelectMany(item =>
            Enumerable.Repeat((item, item.UnitPrice), item.Quantity)).ToList();

        if (!allItems.Any()) return new List<(CartItem Item, decimal UnitPrice)>();

        // 2. 生成折扣序列并创建位置索引
        var discountPositions = Enumerable.Range(0, allItems.Count)
            .Select(i => new
            {
                Rate = CyclicTiers[i % CyclicTiers.Count].DiscountRate,
                Position = i
            }).ToList();

        // 3. 根据策略排序并配对
        var sortedItems = allItems.OrderByDescending(x => x.UnitPrice).ToList();
        var sortedDiscounts = DiscountSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
            ? discountPositions.OrderBy(x => x.Rate).ToList()      // 客户利益：最大折扣给最贵商品
            : discountPositions.OrderByDescending(x => x.Rate).ToList(); // 商户利益：最小折扣给最贵商品

        // 4. 配对并按原始位置排序返回
        return sortedItems.Zip(sortedDiscounts, (item, discount) => new { item, discount.Position })
            .OrderBy(x => x.Position)
            .Select(x => x.item)
            .ToList();
    }

}

/// <summary>
/// 循环折扣梯度
/// </summary>
public class CyclicDiscountTier
{
    /// <summary>
    /// 折扣率（0.9表示9折）
    /// </summary>
    public decimal DiscountRate { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}
