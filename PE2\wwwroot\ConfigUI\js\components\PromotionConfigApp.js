// 主应用组件
const PromotionConfigApp = {
  template: `
    <div class="promotion-config-container">
      <promotion-type-selector 
        :selectedType="selectedType"
        :selectedCategory="selectedCategory"
        @type-selected="onTypeSelected"
      />
      
      <div class="main-content">
        <div class="content-header">
          <h1>{{ currentTypeData ? currentTypeData.name : '促销规则配置' }}</h1>
          <div class="header-actions">
            <button class="add-btn" @click="loadFromJson">导入JSON</button>
            <button class="add-btn" @click="createNew" :disabled="!selectedType">新建规则</button>
            <button class="add-btn" @click="saveRule" :disabled="!hasValidData">保存规则</button>
          </div>
        </div>
        
        <div class="content-body">
          <div class="form-container">
            <div v-if="!selectedType" class="empty-state">
              <h3>请选择促销类型</h3>
              <p>从左侧选择一种促销类型开始配置规则</p>
            </div>
            
            <dynamic-form 
              v-else
              :fields="currentTypeData.fields"
              :ruleType="selectedType"
              v-model="formData"
              @form-changed="onFormChanged"
            />
          </div>
          
          <json-preview 
            :data="formData"
            :title="selectedType ? currentTypeData.name + ' JSON' : 'JSON预览'"
          />
        </div>
      </div>
      
      <!-- 隐藏的文件上传输入框 -->
      <input 
        type="file" 
        ref="fileInput" 
        accept=".json"
        style="display: none"
        @change="onFileSelected"
      />
    </div>
  `,
  data() {
    return {
      selectedType: '',
      selectedCategory: '',
      currentTypeData: null,
      formData: {},
      savedRules: [] // 存储已保存的规则
    };
  },
  computed: {
    hasValidData() {
      return this.formData && 
             this.formData.$type && 
             this.formData.id && 
             this.formData.name;
    }
  },
  methods: {
    onTypeSelected(selection) {
      this.selectedCategory = selection.category;
      this.selectedType = selection.type;
      this.currentTypeData = selection.data;
      
      // 如果是新选择的类型，创建新的表单数据
      if (!this.formData.$type || this.formData.$type !== selection.type) {
        this.createNew();
      }
    },
    
    onFormChanged(newData) {
      this.formData = { ...newData };
    },
    
    createNew() {
      if (!this.currentTypeData) return;
      
      // 创建新的表单数据，设置默认值
      const newData = {
        $type: this.selectedType,
        id: '',
        name: '',
        description: '',
        priority: 0,
        isEnabled: true,
        startTime: '',
        endTime: '',
        isRepeatable: true,
        maxApplications: 0,
        applicableCustomerTypes: [],
        exclusiveRuleIds: [],
        canStackWithOthers: false,
        productExclusivity: 'Exclusive'
      };
      
      // 根据字段配置设置默认值
      this.currentTypeData.fields.forEach(field => {
        if (field.default !== undefined) {
          newData[field.name] = field.default;
        } else if (field.type === 'array') {
          newData[field.name] = [];
        }
      });
      
      this.formData = newData;
    },
    
    saveRule() {
      if (!this.hasValidData) {
        this.showNotification('请填写必要的字段（规则类型、ID、名称）', 'error');
        return;
      }
      
      // 验证ID唯一性
      const existingRule = this.savedRules.find(rule => rule.id === this.formData.id);
      if (existingRule) {
        if (!confirm('已存在相同ID的规则，是否覆盖？')) {
          return;
        }
        // 删除旧规则
        this.savedRules = this.savedRules.filter(rule => rule.id !== this.formData.id);
      }
      
      // 保存规则
      this.savedRules.push({ ...this.formData });
      this.saveToLocalStorage();
      
      this.showNotification('规则保存成功！', 'success');
    },
    
    loadFromJson() {
      this.$refs.fileInput.click();
    },
    
    onFileSelected(event) {
      const file = event.target.files[0];
      if (!file) return;
      
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const jsonData = JSON.parse(e.target.result);
          
          // 验证JSON格式
          if (!jsonData.$type) {
            this.showNotification('无效的JSON格式：缺少$type字段', 'error');
            return;
          }
          
          // 查找对应的类型配置
          let foundType = null;
          let foundCategory = null;
          
          for (const [categoryKey, category] of Object.entries(PROMOTION_TYPES)) {
            for (const [typeKey, typeData] of Object.entries(category.types)) {
              if (typeKey === jsonData.$type) {
                foundType = typeKey;
                foundCategory = categoryKey;
                this.currentTypeData = typeData;
                break;
              }
            }
            if (foundType) break;
          }
          
          if (!foundType) {
            this.showNotification(`不支持的促销类型：${jsonData.$type}`, 'error');
            return;
          }
          
          // 设置选中的类型并加载数据
          this.selectedType = foundType;
          this.selectedCategory = foundCategory;
          this.formData = { ...jsonData };
          
          this.showNotification('JSON导入成功！', 'success');
          
        } catch (error) {
          this.showNotification('JSON格式错误：' + error.message, 'error');
        }
      };
      
      reader.readAsText(file);
      // 清空文件输入框
      event.target.value = '';
    },
    
    saveToLocalStorage() {
      try {
        localStorage.setItem('promotionRules', JSON.stringify(this.savedRules));
      } catch (error) {
        console.error('保存到本地存储失败:', error);
      }
    },
    
    loadFromLocalStorage() {
      try {
        const saved = localStorage.getItem('promotionRules');
        if (saved) {
          this.savedRules = JSON.parse(saved);
        }
      } catch (error) {
        console.error('从本地存储加载失败:', error);
        this.savedRules = [];
      }
    },
    
    showNotification(message, type) {
      // 创建通知元素
      const notification = document.createElement('div');
      notification.className = `notification ${type}`;
      notification.textContent = message;
      
      document.body.appendChild(notification);
      
      // 3秒后移除通知
      setTimeout(() => {
        if (document.body.contains(notification)) {
          document.body.removeChild(notification);
        }
      }, 3000);
    }
  },
  
  mounted() {
    this.loadFromLocalStorage();
  }
};
