namespace PE2.Models;

/// <summary>
/// 选择的促销请求
/// </summary>
public class SelectedPromotionsRequest
{
    /// <summary>
    /// 购物车
    /// </summary>
    public ShoppingCart Cart { get; set; } = new();

    /// <summary>
    /// 用户选择的促销列表
    /// </summary>
    public List<SelectedPromotion> SelectedPromotions { get; set; } = new();
}

/// <summary>
/// 用户选择的促销
/// </summary>
public class SelectedPromotion
{
    /// <summary>
    /// 促销规则ID
    /// </summary>
    public string RuleId { get; set; } = string.Empty;

    /// <summary>
    /// 应用次数
    /// </summary>
    public int ApplicationCount { get; set; } = 1;

    /// <summary>
    /// 是否选中
    /// </summary>
    public bool IsSelected { get; set; } = true;
}

/// <summary>
/// 商品促销信息
/// </summary>
public class ProductPromotionInfo
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 商品名称
    /// </summary>
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// 当前数量
    /// </summary>
    public int CurrentQuantity { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// 可用的促销列表
    /// </summary>
    public List<AvailablePromotion> AvailablePromotions { get; set; } = new();
}

/// <summary>
/// 可用的促销
/// </summary>
public class AvailablePromotion
{
    /// <summary>
    /// 规则ID
    /// </summary>
    public string RuleId { get; set; } = string.Empty;

    /// <summary>
    /// 规则名称
    /// </summary>
    public string RuleName { get; set; } = string.Empty;

    /// <summary>
    /// 规则类型
    /// </summary>
    public string RuleType { get; set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// 最大应用次数
    /// </summary>
    public int MaxApplications { get; set; }

    /// <summary>
    /// 预估折扣金额
    /// </summary>
    public decimal EstimatedDiscount { get; set; }

    /// <summary>
    /// 必要条件
    /// </summary>
    public List<string> RequiredConditions { get; set; } = new();

    /// <summary>
    /// 是否可以叠加
    /// </summary>
    public bool CanStack { get; set; }

    /// <summary>
    /// 是否可重复
    /// </summary>
    public bool IsRepeatable { get; set; }

    /// <summary>
    /// 条件满足状态
    /// </summary>
    public PromotionConditionStatus ConditionStatus { get; set; } = PromotionConditionStatus.NotMet;

    /// <summary>
    /// 条件差距描述
    /// </summary>
    public string ConditionGap { get; set; } = string.Empty;
}

/// <summary>
/// 促销条件满足状态
/// </summary>
public enum PromotionConditionStatus
{
    /// <summary>
    /// 已满足
    /// </summary>
    Met = 0,

    /// <summary>
    /// 未满足
    /// </summary>
    NotMet = 1,

    /// <summary>
    /// 接近满足
    /// </summary>
    NearlyMet = 2,

    /// <summary>
    /// 冲突/不可用
    /// </summary>
    Conflicted = 3
}

/// <summary>
/// 促销统计信息
/// </summary>
public class PromotionStatistics
{
    /// <summary>
    /// 总规则数
    /// </summary>
    public int TotalRules { get; set; }

    /// <summary>
    /// 启用的规则数
    /// </summary>
    public int EnabledRules { get; set; }

    /// <summary>
    /// 有效的规则数
    /// </summary>
    public int ValidRules { get; set; }

    /// <summary>
    /// 按类型分组的规则数
    /// </summary>
    public Dictionary<string, int> RulesByType { get; set; } = new();

    /// <summary>
    /// 已过期的规则数
    /// </summary>
    public int ExpiredRules { get; set; }

    /// <summary>
    /// 未来生效的规则数
    /// </summary>
    public int FutureRules { get; set; }
}
