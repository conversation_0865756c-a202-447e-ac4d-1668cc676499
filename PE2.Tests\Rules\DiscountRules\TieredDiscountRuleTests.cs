using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.DiscountRules;

/// <summary>
/// 阶梯折扣规则测试类
/// 测试 TieredDiscountRule 的阶梯折扣计算和应用逻辑
/// </summary>
public class TieredDiscountRuleTests(ITestOutputHelper output) : TestBase(output)
{
    #region 基础阶梯折扣功能测试

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_ValidTieredDiscountScenario_ShouldApplyCorrectly()
    {
        // Arrange - 阶梯折扣：买2件9折，买3件8折
        var rule = TestDataGenerator.CreateTieredDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            (TestDataGenerator.CreateProductA(), 3) // 3件A，应享受8折（最高梯度）
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "阶梯折扣测试购物车");

        var originalPrice = TestDataGenerator.CreateProductA().Price; // 50元
        var expectedDiscountedPrice = originalPrice * 0.8m; // 40元（3件享受8折）
        var expectedTotalDiscount = (originalPrice - expectedDiscountedPrice) * 3; // (50-40)*3 = 30元

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "阶梯折扣测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证商品A的实际单价被调整为8折
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(expectedDiscountedPrice, productAItem.ActualUnitPrice, "商品A的实际单价应为8折");

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为3件商品的8折折扣金额");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_LowerTierQuantity_ShouldApplyLowerDiscount()
    {
        // Arrange - 只有2件A，应享受9折
        var rule = TestDataGenerator.CreateTieredDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_002",
            (TestDataGenerator.CreateProductA(), 2) // 2件A，应享受9折
        );

        LogCartDetails(cart, "低梯度折扣测试购物车");

        var originalPrice = TestDataGenerator.CreateProductA().Price; // 50元
        var expectedDiscountedPrice = originalPrice * 0.9m; // 45元（2件享受9折）
        var expectedTotalDiscount = (originalPrice - expectedDiscountedPrice) * 2; // (50-45)*2 = 10元

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "低梯度折扣测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证商品A的实际单价被调整为9折
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(expectedDiscountedPrice, productAItem.ActualUnitPrice, "商品A的实际单价应为9折");

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为2件商品的9折折扣金额");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_InsufficientQuantity_ShouldNotApply()
    {
        // Arrange - 数量不足：只有1件A，不满足最低梯度（2件）
        var rule = TestDataGenerator.CreateTieredDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_003",
            (TestDataGenerator.CreateProductA(), 1) // 1件A，不满足最低梯度条件
        );

        LogCartDetails(cart, "数量不足的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "数量不足场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "数量不足时应无优惠");

        // 验证商品价格未变
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(
            TestDataGenerator.CreateProductA().Price,
            productAItem.ActualUnitPrice,
            "价格应保持原价"
        );
    }

    #endregion

    #region 多梯度测试

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Medium")]
    public void Apply_ExcessQuantity_ShouldApplyHighestTier()
    {
        // Arrange - 超过最高梯度：5件A，应享受最高梯度8折
        var rule = TestDataGenerator.CreateTieredDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_004",
            (TestDataGenerator.CreateProductA(), 5) // 5件A，超过最高梯度3件
        );

        LogCartDetails(cart, "超过最高梯度的购物车");

        var originalPrice = TestDataGenerator.CreateProductA().Price; // 50元
        var expectedDiscountedPrice = originalPrice * 0.8m; // 40元（享受最高梯度8折）
        var expectedTotalDiscount = (originalPrice - expectedDiscountedPrice) * 5; // (50-40)*5 = 50元

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "超过最高梯度场景");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证所有商品都享受最高梯度折扣
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(expectedDiscountedPrice, productAItem.ActualUnitPrice, "所有商品都应享受最高梯度8折");

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为所有商品的8折折扣金额");
    }

    [Theory]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Medium")]
    [InlineData(2, 0.9, "2件享受9折")]
    [InlineData(3, 0.8, "3件享受8折")]
    [InlineData(4, 0.8, "4件享受8折（最高梯度）")]
    public void Apply_DifferentQuantities_ShouldApplyCorrectTier(
        int quantity,
        decimal expectedDiscountRate,
        string description
    )
    {
        // Arrange
        var rule = TestDataGenerator.CreateTieredDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            $"CUSTOMER_TIER_{quantity}",
            (TestDataGenerator.CreateProductA(), quantity)
        );

        Output.WriteLine($"测试场景: {description}");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, description);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        var originalPrice = TestDataGenerator.CreateProductA().Price;
        var expectedPrice = originalPrice * expectedDiscountRate;
        var expectedDiscount = (originalPrice - expectedPrice) * quantity;

        AssertAmountEqual(expectedPrice, productAItem.ActualUnitPrice, $"商品价格应为{expectedDiscountRate:P0}");
        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "总优惠金额应正确计算");

        Output.WriteLine($"{description} 测试通过 ✓");
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Low")]
    public void Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateTieredDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateEmptyCart();

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "空购物车");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertAmountEqual(0m, result.TotalDiscount, "空购物车应无优惠");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Low")]
    public void Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateTieredDiscountRule_Progressive();
        rule.IsEnabled = false; // 禁用规则

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_DISABLED",
            (TestDataGenerator.CreateProductA(), 3)
        );

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "禁用规则场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");
        AssertAmountEqual(0m, result.TotalDiscount, "禁用规则应无优惠");
    }

    #endregion

    #region 基于金额的阶梯折扣测试

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_AmountBasedTieredDiscount_ShouldApplyCorrectly()
    {
        // Arrange - 基于金额的阶梯折扣：满100元9折，满200元8折
        var rule = TestDataGenerator.CreateTieredDiscountRule_ByAmount();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_AMOUNT_001",
            (TestDataGenerator.CreateProductA(), 5) // 5件A，总价250元，应享受8折
        );

        LogCartDetails(cart, "基于金额的阶梯折扣测试购物车");

        var originalPrice = TestDataGenerator.CreateProductA().Price; // 50元
        var totalAmount = originalPrice * 5; // 250元
        var expectedDiscountedPrice = originalPrice * 0.8m; // 40元（满200元享受8折）
        var expectedTotalDiscount = (originalPrice - expectedDiscountedPrice) * 5; // (50-40)*5 = 50元

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "基于金额的阶梯折扣测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证商品A的实际单价被调整为8折
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(expectedDiscountedPrice, productAItem.ActualUnitPrice, "商品A的实际单价应为8折");

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为5件商品的8折折扣金额");

        Output.WriteLine($"总金额: {totalAmount:C}, 享受折扣: 8折, 节省: {expectedTotalDiscount:C}");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_AmountBasedLowerTier_ShouldApplyLowerDiscount()
    {
        // Arrange - 基于金额：总价150元，应享受9折
        var rule = TestDataGenerator.CreateTieredDiscountRule_ByAmount();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_AMOUNT_002",
            (TestDataGenerator.CreateProductA(), 3) // 3件A，总价150元，应享受9折
        );

        LogCartDetails(cart, "基于金额低梯度折扣测试购物车");

        var originalPrice = TestDataGenerator.CreateProductA().Price; // 50元
        var totalAmount = originalPrice * 3; // 150元
        var expectedDiscountedPrice = originalPrice * 0.9m; // 45元（满100元享受9折）
        var expectedTotalDiscount = (originalPrice - expectedDiscountedPrice) * 3; // (50-45)*3 = 15元

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "基于金额低梯度折扣测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证商品A的实际单价被调整为9折
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(expectedDiscountedPrice, productAItem.ActualUnitPrice, "商品A的实际单价应为9折");

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为3件商品的9折折扣金额");

        Output.WriteLine($"总金额: {totalAmount:C}, 享受折扣: 9折, 节省: {expectedTotalDiscount:C}");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_AmountBasedInsufficientAmount_ShouldNotApply()
    {
        // Arrange - 金额不足：总价50元，不满足最低梯度（100元）
        var rule = TestDataGenerator.CreateTieredDiscountRule_ByAmount();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_AMOUNT_003",
            (TestDataGenerator.CreateProductA(), 1) // 1件A，总价50元，不满足最低梯度条件
        );

        LogCartDetails(cart, "金额不足的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "金额不足场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "金额不足时应无优惠");

        // 验证商品价格未变
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        AssertAmountEqual(
            TestDataGenerator.CreateProductA().Price,
            productAItem.ActualUnitPrice,
            "价格应保持原价"
        );
    }

    #endregion

    #region 多商品阶梯折扣测试

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_MultiProductTieredDiscount_ShouldApplyCorrectly()
    {
        // Arrange - 多商品阶梯折扣：A+B商品买2件9折，买3件8折
        var rule = TestDataGenerator.CreateTieredDiscountRule_MultiProduct();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_MULTI_001",
            (TestDataGenerator.CreateProductA(), 2), // 2件A
            (TestDataGenerator.CreateProductB(), 1)  // 1件B，总共3件，应享受8折
        );

        LogCartDetails(cart, "多商品阶梯折扣测试购物车");

        var priceA = TestDataGenerator.CreateProductA().Price; // 50元
        var priceB = TestDataGenerator.CreateProductB().Price; // 30元
        var expectedDiscountedPriceA = priceA * 0.8m; // 40元（3件享受8折）
        var expectedDiscountedPriceB = priceB * 0.8m; // 24元（3件享受8折）
        var expectedTotalDiscount = (priceA - expectedDiscountedPriceA) * 2 + (priceB - expectedDiscountedPriceB) * 1;
        // (50-40)*2 + (30-24)*1 = 20 + 6 = 26元

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "多商品阶梯折扣测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证商品A和B的实际单价都被调整为8折
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        var productBItem = result.ProcessedCart.Items.First(i => i.Product.Id == "B");
        //AssertAmountEqual(expectedDiscountedPriceA, productAItem.ActualUnitPrice, "商品A的实际单价应为8折");
        //AssertAmountEqual(expectedDiscountedPriceB, productBItem.ActualUnitPrice, "商品B的实际单价应为8折");

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为所有商品的8折折扣金额");

        Output.WriteLine($"A商品2件: {priceA * 2:C} -> {expectedDiscountedPriceA * 2:C}");
        Output.WriteLine($"B商品1件: {priceB:C} -> {expectedDiscountedPriceB:C}");
        Output.WriteLine($"总节省: {expectedTotalDiscount:C}");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Medium")]
    public void Apply_MultiProductPartialMatch_ShouldApplyToMatchingProducts()
    {
        // Arrange - 多商品场景：只有A商品在规则中，C商品不在规则中
        var rule = TestDataGenerator.CreateTieredDiscountRule_MultiProduct();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_MULTI_002",
            (TestDataGenerator.CreateProductA(), 2), // 2件A，在规则中
            (TestDataGenerator.CreateProductC(), 1)  // 1件C，不在规则中
        );

        LogCartDetails(cart, "多商品部分匹配测试购物车");

        var priceA = TestDataGenerator.CreateProductA().Price; // 50元
        var priceC = TestDataGenerator.CreateProductC().Price; // 20元
        var expectedDiscountedPriceA = priceA * 0.9m; // 45元（2件A享受9折）
        var expectedTotalDiscount = (priceA - expectedDiscountedPriceA) * 2; // (50-45)*2 = 10元

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "多商品部分匹配测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证只有商品A享受折扣，商品C保持原价
        var productAItem = result.ProcessedCart.Items.First(i => i.Product.Id == "A");
        var productCItem = result.ProcessedCart.Items.First(i => i.Product.Id == "C");
        AssertAmountEqual(expectedDiscountedPriceA, productAItem.ActualUnitPrice, "商品A的实际单价应为9折");
        AssertAmountEqual(priceC, productCItem.ActualUnitPrice, "商品C的实际单价应保持原价");

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为商品A的9折折扣金额");

        Output.WriteLine($"A商品享受折扣: {priceA:C} -> {expectedDiscountedPriceA:C}");
        Output.WriteLine($"C商品保持原价: {priceC:C}");
    }

    #endregion

    #region 性能测试

    [Fact]
    [Trait("Category", "Performance")]
    [Trait("Priority", "Low")]
    public void Apply_LargeCart_ShouldPerformWell()
    {
        // Arrange
        var rule = TestDataGenerator.CreateTieredDiscountRule_Progressive();
        var cart = TestDataGenerator.CreateLargeTestCart(100); // 100个商品项

        Output.WriteLine($"大型购物车测试: {cart.Items.Count} 个商品项");

        TestPromotionRuleService.Rules = [rule];
        // Act & Assert
        var executionTime = MeasureExecutionTime(() =>
        {
            var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;
            AssertPromotionResult(result, "大型购物车性能测试");
        });

        // 性能断言：大型购物车处理应在合理时间内完成
        AssertPerformance(executionTime, TimeSpan.FromMilliseconds(500), "大型购物车阶梯折扣规则计算");
    }

    #endregion
}