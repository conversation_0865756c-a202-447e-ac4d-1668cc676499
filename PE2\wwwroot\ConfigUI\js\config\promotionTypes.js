// 促销类型配置定义 - 与后端C#模型完全一致
const PROMOTION_TYPES = {
  // 买赠规则
  buyGift: {
    name: '买赠规则',
    icon: '�',
    types: {
      'UnifiedGift': {
        name: '统一买赠',
        description: '购买指定商品满足条件时赠送商品',
        fields: [
          // 基础字段（与PromotionRuleBase一致）
          { name: 'id', label: '规则ID', type: 'input', required: true },
          { name: 'name', label: '规则名称', type: 'input', required: true },
          { name: 'description', label: '规则描述', type: 'textarea' },
          { name: 'priority', label: '优先级', type: 'number', default: 0 },
          { name: 'isEnabled', label: '是否启用', type: 'switch', default: true },
          { name: 'startTime', label: '生效时间', type: 'datetime' },
          { name: 'endTime', label: '失效时间', type: 'datetime' },
          { name: 'isRepeatable', label: '是否可重复', type: 'switch', default: true },
          { name: 'maxApplications', label: '最大应用次数', type: 'number', default: 0 },
          { name: 'canStackWithOthers', label: '是否可与其他促销叠加', type: 'switch', default: true },
          { name: 'productExclusivity', label: '商品互斥级别', type: 'select', default: 'None', options: [
            { value: 'None', label: '无互斥' },
            { value: 'SameType', label: '同类型互斥' },
            { value: 'All', label: '全部互斥' }
          ]},
          { name: 'applicableCustomerTypes', label: '适用客户类型', type: 'array-simple' },
          { name: 'exclusiveRuleIds', label: '互斥规则ID', type: 'array-simple' },
          
          // 买赠特有字段
          {
            name: 'buyConditions',
            label: '购买条件',
            type: 'array',
            fields: [
              { name: 'productIds', label: '商品列表', type: 'product-selector' },
              { name: 'requiredQuantity', label: '所需数量', type: 'number' },
              { name: 'requiredAmount', label: '所需金额', type: 'number' }
            ]
          },
          {
            name: 'giftConditions',
            label: '赠品条件',
            type: 'array',
            fields: [
              { name: 'giftProductIds', label: '赠品列表', type: 'product-selector' },
              { name: 'giftQuantity', label: '赠品数量', type: 'number' },
              { name: 'description', label: '描述', type: 'input' }
            ]
          }
        ]
      },
      'TieredGift': {
        name: '阶梯买赠',
        description: '根据购买数量或金额不同层级赠送不同商品',
        fields: [
          { name: 'id', label: '规则ID', type: 'input', required: true },
          { name: 'name', label: '规则名称', type: 'input', required: true },
          { name: 'description', label: '规则描述', type: 'textarea' },
          { name: 'priority', label: '优先级', type: 'number', default: 0 },
          { name: 'isEnabled', label: '是否启用', type: 'switch', default: true },
          { name: 'startTime', label: '生效时间', type: 'datetime' },
          { name: 'endTime', label: '失效时间', type: 'datetime' },
          { name: 'isRepeatable', label: '是否可重复', type: 'switch', default: true },
          { name: 'maxApplications', label: '最大应用次数', type: 'number', default: 0 },
          { name: 'canStackWithOthers', label: '是否可与其他促销叠加', type: 'switch', default: true },
          { name: 'productExclusivity', label: '商品互斥级别', type: 'select', default: 'None', options: [
            { value: 'None', label: '无互斥' },
            { value: 'SameType', label: '同类型互斥' },
            { value: 'All', label: '全部互斥' }
          ]},
          {
            name: 'tiers',
            label: '阶梯配置',
            type: 'array',
            fields: [
              { name: 'minQuantity', label: '最小数量', type: 'number' },
              { name: 'maxQuantity', label: '最大数量', type: 'number' },
              { name: 'giftProductIds', label: '赠品列表', type: 'product-selector' },
              { name: 'giftQuantity', label: '赠品数量', type: 'number' }
            ]
          }
        ]
      }
    }
  },

  // 换购规则
  exchange: {
    name: '换购规则',
    icon: '🔄',
    types: {
      'UnifiedSpecialPriceExchange': {
        name: '统一特价换购',
        description: '满足条件时以特价换购商品',
        fields: [
          { name: 'id', label: '规则ID', type: 'input', required: true },
          { name: 'name', label: '规则名称', type: 'input', required: true },
          { name: 'description', label: '规则描述', type: 'textarea' },
          { name: 'priority', label: '优先级', type: 'number', default: 0 },
          { name: 'isEnabled', label: '是否启用', type: 'switch', default: true },
          { name: 'startTime', label: '生效时间', type: 'datetime' },
          { name: 'endTime', label: '失效时间', type: 'datetime' },
          { name: 'isRepeatable', label: '是否可重复', type: 'switch', default: true },
          { name: 'maxApplications', label: '最大应用次数', type: 'number', default: 0 },
          { name: 'canStackWithOthers', label: '是否可与其他促销叠加', type: 'switch', default: true },
          { name: 'productExclusivity', label: '商品互斥级别', type: 'select', default: 'None', options: [
            { value: 'None', label: '无互斥' },
            { value: 'SameType', label: '同类型互斥' },
            { value: 'All', label: '全部互斥' }
          ]},
          {
            name: 'buyConditions',
            label: '购买条件',
            type: 'array',
            fields: [
              { name: 'productIds', label: '商品列表', type: 'product-selector' },
              { name: 'requiredQuantity', label: '所需数量', type: 'number' },
              { name: 'requiredAmount', label: '所需金额', type: 'number' }
            ]
          },
          {
            name: 'exchangeConditions',
            label: '换购条件',
            type: 'array',
            fields: [
              { name: 'exchangeProductIds', label: '换购商品列表', type: 'product-selector' },
              { name: 'exchangeQuantity', label: '换购数量', type: 'number' },
              { name: 'specialPrice', label: '特价', type: 'number' }
            ]
          }
        ]
      },
      'UnifiedDiscountExchange': {
        name: '统一折扣换购',
        description: '满足条件时以折扣价换购商品',
        fields: [
          { name: 'id', label: '规则ID', type: 'input', required: true },
          { name: 'name', label: '规则名称', type: 'input', required: true },
          { name: 'description', label: '规则描述', type: 'textarea' },
          { name: 'priority', label: '优先级', type: 'number', default: 0 },
          { name: 'isEnabled', label: '是否启用', type: 'switch', default: true },
          { name: 'startTime', label: '生效时间', type: 'datetime' },
          { name: 'endTime', label: '失效时间', type: 'datetime' },
          { name: 'isRepeatable', label: '是否可重复', type: 'switch', default: true },
          { name: 'maxApplications', label: '最大应用次数', type: 'number', default: 0 },
          { name: 'canStackWithOthers', label: '是否可与其他促销叠加', type: 'switch', default: true },
          { name: 'productExclusivity', label: '商品互斥级别', type: 'select', default: 'None', options: [
            { value: 'None', label: '无互斥' },
            { value: 'SameType', label: '同类型互斥' },
            { value: 'All', label: '全部互斥' }
          ]},
          {
            name: 'buyConditions',
            label: '购买条件',
            type: 'array',
            fields: [
              { name: 'productIds', label: '商品列表', type: 'product-selector' },
              { name: 'requiredQuantity', label: '所需数量', type: 'number' },
              { name: 'requiredAmount', label: '所需金额', type: 'number' }
            ]
          },
          {
            name: 'exchangeConditions',
            label: '换购条件',
            type: 'array',
            fields: [
              { name: 'exchangeProductIds', label: '换购商品列表', type: 'product-selector' },
              { name: 'exchangeQuantity', label: '换购数量', type: 'number' },
              { name: 'discountRate', label: '折扣率', type: 'number', step: 0.01 }
            ]
          }
        ]
      }
    }
  },

  // 买免规则
  buyFree: {
    name: '买免规则',
    icon: '🆓',
    types: {
      'ProductBuyFree': {
        name: '商品买免',
        description: '购买指定商品享受免费优惠',
        fields: [
          { name: 'id', label: '规则ID', type: 'input', required: true },
          { name: 'name', label: '规则名称', type: 'input', required: true },
          { name: 'description', label: '规则描述', type: 'textarea' },
          { name: 'priority', label: '优先级', type: 'number', default: 0 },
          { name: 'isEnabled', label: '是否启用', type: 'switch', default: true },
          { name: 'startTime', label: '生效时间', type: 'datetime' },
          { name: 'endTime', label: '失效时间', type: 'datetime' },
          { name: 'isRepeatable', label: '是否可重复', type: 'switch', default: true },
          { name: 'maxApplications', label: '最大应用次数', type: 'number', default: 0 },
          { name: 'canStackWithOthers', label: '是否可与其他促销叠加', type: 'switch', default: true },
          { name: 'productExclusivity', label: '商品互斥级别', type: 'select', default: 'None', options: [
            { value: 'None', label: '无互斥' },
            { value: 'SameType', label: '同类型互斥' },
            { value: 'All', label: '全部互斥' }
          ]},
          {
            name: 'buyConditions',
            label: '购买条件',
            type: 'array',
            fields: [
              { name: 'productIds', label: '商品列表', type: 'product-selector' },
              { name: 'requiredQuantity', label: '所需数量', type: 'number' }
            ]
          },
          {
            name: 'freeConditions',
            label: '免费条件',
            type: 'array',
            fields: [
              { name: 'freeProductIds', label: '免费商品列表', type: 'product-selector' },
              { name: 'freeQuantity', label: '免费数量', type: 'number' }
            ]
          }
        ]
      }
    }
  },

  // 减现规则
  cashDiscount: {
    name: '减现规则',
    icon: '💰',
    types: {
      'UnifiedCashDiscount': {
        name: '统一减现',
        description: '满足条件时立减指定金额',
        fields: [
          { name: 'id', label: '规则ID', type: 'input', required: true },
          { name: 'name', label: '规则名称', type: 'input', required: true },
          { name: 'description', label: '规则描述', type: 'textarea' },
          { name: 'priority', label: '优先级', type: 'number', default: 0 },
          { name: 'isEnabled', label: '是否启用', type: 'switch', default: true },
          { name: 'startTime', label: '生效时间', type: 'datetime' },
          { name: 'endTime', label: '失效时间', type: 'datetime' },
          { name: 'isRepeatable', label: '是否可重复', type: 'switch', default: true },
          { name: 'maxApplications', label: '最大应用次数', type: 'number', default: 0 },
          { name: 'canStackWithOthers', label: '是否可与其他促销叠加', type: 'switch', default: true },
          { name: 'productExclusivity', label: '商品互斥级别', type: 'select', default: 'None', options: [
            { value: 'None', label: '无互斥' },
            { value: 'SameType', label: '同类型互斥' },
            { value: 'All', label: '全部互斥' }
          ]},
          {
            name: 'conditions',
            label: '触发条件',
            type: 'array',
            fields: [
              { name: 'productIds', label: '商品列表', type: 'product-selector' },
              { name: 'minAmount', label: '最小金额', type: 'number' }
            ]
          },
          { name: 'discountAmount', label: '减现金额', type: 'number' }
        ]
      },
      'GradientCashDiscount': {
        name: '阶梯减现',
        description: '根据购买金额不同层级享受不同减现优惠',
        fields: [
          { name: 'id', label: '规则ID', type: 'input', required: true },
          { name: 'name', label: '规则名称', type: 'input', required: true },
          { name: 'description', label: '规则描述', type: 'textarea' },
          { name: 'priority', label: '优先级', type: 'number', default: 0 },
          { name: 'isEnabled', label: '是否启用', type: 'switch', default: true },
          { name: 'startTime', label: '生效时间', type: 'datetime' },
          { name: 'endTime', label: '失效时间', type: 'datetime' },
          { name: 'isRepeatable', label: '是否可重复', type: 'switch', default: true },
          { name: 'maxApplications', label: '最大应用次数', type: 'number', default: 0 },
          { name: 'canStackWithOthers', label: '是否可与其他促销叠加', type: 'switch', default: true },
          { name: 'productExclusivity', label: '商品互斥级别', type: 'select', default: 'None', options: [
            { value: 'None', label: '无互斥' },
            { value: 'SameType', label: '同类型互斥' },
            { value: 'All', label: '全部互斥' }
          ]},
          {
            name: 'tiers',
            label: '阶梯配置',
            type: 'array',
            fields: [
              { name: 'minAmount', label: '最小金额', type: 'number' },
              { name: 'maxAmount', label: '最大金额', type: 'number' },
              { name: 'discountAmount', label: '减现金额', type: 'number' }
            ]
          }
        ]
      }
    }
  },

  // 打折规则
  discount: {
    name: '打折规则',
    icon: '🏷️',
    types: {
      'UnifiedDiscount': {
        name: '统一打折',
        description: '指定商品享受统一折扣',
        fields: [
          { name: 'id', label: '规则ID', type: 'input', required: true },
          { name: 'name', label: '规则名称', type: 'input', required: true },
          { name: 'description', label: '规则描述', type: 'textarea' },
          { name: 'priority', label: '优先级', type: 'number', default: 0 },
          { name: 'isEnabled', label: '是否启用', type: 'switch', default: true },
          { name: 'startTime', label: '生效时间', type: 'datetime' },
          { name: 'endTime', label: '失效时间', type: 'datetime' },
          { name: 'isRepeatable', label: '是否可重复', type: 'switch', default: true },
          { name: 'maxApplications', label: '最大应用次数', type: 'number', default: 0 },
          { name: 'canStackWithOthers', label: '是否可与其他促销叠加', type: 'switch', default: true },
          { name: 'productExclusivity', label: '商品互斥级别', type: 'select', default: 'None', options: [
            { value: 'None', label: '无互斥' },
            { value: 'SameType', label: '同类型互斥' },
            { value: 'All', label: '全部互斥' }
          ]},
          {
            name: 'conditions',
            label: '适用条件',
            type: 'array',
            fields: [
              { name: 'productIds', label: '商品列表', type: 'product-selector' },
              { name: 'minQuantity', label: '最小数量', type: 'number' }
            ]
          },
          { name: 'discountRate', label: '折扣率', type: 'number', step: 0.01 }
        ]
      },
      'TieredDiscount': {
        name: '阶梯打折',
        description: '根据购买数量享受不同折扣',
        fields: [
          { name: 'id', label: '规则ID', type: 'input', required: true },
          { name: 'name', label: '规则名称', type: 'input', required: true },
          { name: 'description', label: '规则描述', type: 'textarea' },
          { name: 'priority', label: '优先级', type: 'number', default: 0 },
          { name: 'isEnabled', label: '是否启用', type: 'switch', default: true },
          { name: 'startTime', label: '生效时间', type: 'datetime' },
          { name: 'endTime', label: '失效时间', type: 'datetime' },
          { name: 'isRepeatable', label: '是否可重复', type: 'switch', default: true },
          { name: 'maxApplications', label: '最大应用次数', type: 'number', default: 0 },
          { name: 'canStackWithOthers', label: '是否可与其他促销叠加', type: 'switch', default: true },
          { name: 'productExclusivity', label: '商品互斥级别', type: 'select', default: 'None', options: [
            { value: 'None', label: '无互斥' },
            { value: 'SameType', label: '同类型互斥' },
            { value: 'All', label: '全部互斥' }
          ]},
          {
            name: 'tiers',
            label: '阶梯配置',
            type: 'array',
            fields: [
              { name: 'minQuantity', label: '最小数量', type: 'number' },
              { name: 'maxQuantity', label: '最大数量', type: 'number' },
              { name: 'discountRate', label: '折扣率', type: 'number', step: 0.01 }
            ]
          }
        ]
      }
    }  },

  // 特价规则
  specialPrice: {
    name: '特价规则',
    icon: '💸',
    types: {
      'UnifiedSpecialPrice': {
        name: '统一特价',
        description: '指定商品统一特价销售',
        fields: [
          // 基础字段
          { name: 'id', label: '规则ID', type: 'input', required: true },
          { name: 'name', label: '规则名称', type: 'input', required: true },
          { name: 'description', label: '规则描述', type: 'textarea' },
          { name: 'priority', label: '优先级', type: 'number', default: 0 },
          { name: 'isEnabled', label: '是否启用', type: 'switch', default: true },
          { name: 'startTime', label: '生效时间', type: 'datetime' },
          { name: 'endTime', label: '失效时间', type: 'datetime' },
          { name: 'isRepeatable', label: '是否可重复', type: 'switch', default: true },
          { name: 'maxApplications', label: '最大应用次数', type: 'number', default: 0 },
          { name: 'canStackWithOthers', label: '是否可与其他促销叠加', type: 'switch', default: true },
          { name: 'productExclusivity', label: '商品互斥级别', type: 'select', default: 'None', options: [
            { value: 'None', label: '无互斥' },
            { value: 'SameType', label: '同类型互斥' },
            { value: 'All', label: '全部互斥' }
          ]},
          
          // 业务字段
          {
            name: 'conditions',
            label: '特价条件',
            type: 'array',
            fields: [
              { name: 'productIds', label: '商品列表', type: 'product-selector' },
              { name: 'specialPrice', label: '特价', type: 'number' },
              { name: 'maxQuantity', label: '最大数量', type: 'number', default: 0, placeholder: '0表示无限制' }
            ]
          }
        ]
      }
    }
  }
};

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { PROMOTION_TYPES };
} else if (typeof window !== 'undefined') {
  window.PROMOTION_TYPES = PROMOTION_TYPES;
  console.log('✅ PROMOTION_TYPES 已加载到 window 对象', PROMOTION_TYPES);
}
