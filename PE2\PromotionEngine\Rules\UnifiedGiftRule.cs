using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules;

/// <summary>
/// 统一送赠品促销规则
/// 满X件或X元，赠送某类商品Z件
/// </summary>
public class UnifiedGiftRule : PromotionRuleBase
{
    public override string RuleType => "UnifiedGift";

    /// <summary>
    /// 购买条件列表
    /// </summary>
    public List<BuyCondition> BuyConditions { get; set; } = new();

    /// <summary>
    /// 赠品条件列表
    /// </summary>
    public List<GiftCondition> GiftConditions { get; set; } = new();

    /// <summary>
    /// 是否按金额条件（而非数量条件）
    /// </summary>
    public bool IsByAmount { get; set; } = false;

    /// <summary>
    /// 最小金额要求（当IsByAmount为true时使用）
    /// </summary>
    public decimal MinAmount { get; set; } = 0;

    /// <summary>
    /// 是否赠送相同商品
    /// </summary>
    public bool GiftSameProduct { get; set; } = false;

    /// <summary>
    /// 赠品选择策略
    /// </summary>
    public GiftSelectionStrategy GiftSelectionStrategy { get; set; } = GiftSelectionStrategy.CustomerBenefit;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!BuyConditions.Any() || !GiftConditions.Any())
            return false;

        // 检查购买条件
        foreach (var condition in BuyConditions)
        {
            if (IsByAmount)
            {
                var totalAmount = condition.ProductIds.Sum(id =>
                    cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));
                if (totalAmount < MinAmount)
                    return false;
            }
            else
            {
                var availableQuantity = condition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                if (availableQuantity < condition.RequiredQuantity)
                    return false;
            }
        }

        // 检查赠品库存
        return CheckGiftStock(cart);
    }

    /// <summary>
    /// 检查赠品库存是否充足
    /// </summary>
    private bool CheckGiftStock(ShoppingCart cart)
    {
        if (GiftSameProduct)
        {
            // 同商品买赠：需要确保有足够的商品可以赠送
            foreach (var buyCondition in BuyConditions)
            {
                var totalRequired = buyCondition.RequiredQuantity;
                foreach (var giftCondition in GiftConditions)
                {
                    totalRequired += giftCondition.GiftQuantity;
                }

                var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                if (availableQuantity < totalRequired)
                    return false;
            }
        }
        else
        {
            // 不同商品赠品：检查赠品库存
            foreach (var giftCondition in GiftConditions)
            {
                if (giftCondition.ProductIds.Count > 1)
                {
                    // 多选一：只要有一个有库存即可
                    var hasStock = giftCondition.ProductIds.Any(id => cart.GetAvailableProductQuantity(id) > 0);
                    if (!hasStock) return false;
                }
                else
                {
                    // 单一赠品：必须有足够库存
                    var availableQuantity = giftCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                    if (availableQuantity < giftCondition.GiftQuantity)
                        return false;
                }
            }
        }

        return true;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = int.MaxValue;

        // 根据购买条件计算最大应用次数
        foreach (var condition in BuyConditions)
        {
            if (IsByAmount)
            {
                var totalAmount = condition.ProductIds.Sum(id =>
                    cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));
                var maxByAmount = (int)(totalAmount / MinAmount);
                maxApplications = Math.Min(maxApplications, maxByAmount);
            }
            else
            {
                var availableQuantity = condition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                var maxByCondition = availableQuantity / condition.RequiredQuantity;
                maxApplications = Math.Min(maxApplications, maxByCondition);
            }
        }

        // 根据赠品库存计算最大应用次数
        var maxByGiftStock = CalculateMaxApplicationsByGiftStock(cart);
        maxApplications = Math.Min(maxApplications, maxByGiftStock);

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications == int.MaxValue ? 0 : maxApplications;
    }

    /// <summary>
    /// 根据赠品库存计算最大应用次数
    /// </summary>
    private int CalculateMaxApplicationsByGiftStock(ShoppingCart cart)
    {
        if (GiftSameProduct)
        {
            // 同商品买赠的库存限制
            var minApplications = int.MaxValue;
            foreach (var buyCondition in BuyConditions)
            {
                var totalRequired = buyCondition.RequiredQuantity;
                foreach (var giftCondition in GiftConditions)
                {
                    totalRequired += giftCondition.GiftQuantity;
                }

                var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                var maxByGift = availableQuantity / totalRequired;
                minApplications = Math.Min(minApplications, maxByGift);
            }
            return minApplications == int.MaxValue ? 0 : minApplications;
        }
        else
        {
            // 不同商品赠品的库存限制
            var minApplications = int.MaxValue;
            foreach (var giftCondition in GiftConditions)
            {
                if (giftCondition.ProductIds.Count > 1)
                {
                    // 多选一：通常不受库存限制
                    continue;
                }
                else
                {
                    // 单一赠品：根据库存计算
                    var availableQuantity = giftCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                    var maxByThisGift = availableQuantity / giftCondition.GiftQuantity;
                    minApplications = Math.Min(minApplications, maxByThisGift);
                }
            }
            return minApplications == int.MaxValue ? int.MaxValue : minApplications;
        }
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyUnifiedGiftPromotion(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用统一送赠品促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = $"应用统一送赠品促销时发生错误: {ex.Message}";
        }

        return application;
    }

    /// <summary>
    /// 应用统一送赠品促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyUnifiedGiftPromotion(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var giftItems = new List<GiftItem>();

        for (int app = 0; app < applicationCount; app++)
        {
            // 消耗购买条件中的商品
            foreach (var buyCondition in BuyConditions)
            {
                var remainingQuantity = buyCondition.RequiredQuantity;

                foreach (var productId in buyCondition.ProductIds)
                {
                    if (remainingQuantity <= 0) break;

                    var availableItems = cart.Items
                        .Where(x => x.Product.Id == productId && x.AvailableQuantity > 0)
                        .ToList();

                    foreach (var item in availableItems)
                    {
                        if (remainingQuantity <= 0) break;

                        var consumeQuantity = Math.Min(item.AvailableQuantity, remainingQuantity);

                        // 记录消耗的商品
                        var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == productId);
                        if (existingConsumed != null)
                        {
                            existingConsumed.Quantity += consumeQuantity;
                        }
                        else
                        {
                            consumedItems.Add(new ConsumedItem
                            {
                                ProductId = productId,
                                ProductName = item.Product.Name,
                                Quantity = consumeQuantity,
                                UnitPrice = item.UnitPrice
                            });
                        }

                        item.Quantity -= consumeQuantity;
                        remainingQuantity -= consumeQuantity;
                    }
                }
            }

            // 处理赠品
            foreach (var giftCondition in GiftConditions)
            {
                var giftQuantity = giftCondition.GiftQuantity;
                var selectedProductIds = giftCondition.ProductIds.Count > 1
                    ? SelectOptimalGiftProducts(giftCondition.ProductIds, cart, giftQuantity)
                    : giftCondition.ProductIds;

                foreach (var productId in selectedProductIds)
                {
                    if (giftQuantity <= 0) break;

                    var productItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
                    if (productItem != null)
                    {
                        var actualGiftQuantity = Math.Min(giftQuantity, 1);
                        var giftValue = actualGiftQuantity * productItem.UnitPrice;

                        totalDiscountAmount += giftValue;

                        var existingGift = giftItems.FirstOrDefault(x => x.ProductId == productId);
                        if (existingGift != null)
                        {
                            existingGift.Quantity += actualGiftQuantity;
                            existingGift.Value += giftValue;
                        }
                        else
                        {
                            var strategyDescription = GiftSelectionStrategy == GiftSelectionStrategy.CustomerBenefit
                                ? "客户利益最大化选择"
                                : "商家利益最大化选择";

                            giftItems.Add(new GiftItem
                            {
                                ProductId = productId,
                                ProductName = productItem.Product.Name,
                                Quantity = actualGiftQuantity,
                                Value = giftValue,
                                Description = $"统一赠品 - {strategyDescription}"
                            });
                        }

                        giftQuantity -= actualGiftQuantity;
                    }
                }
            }
        }

        // 清理数量为0的商品项
        cart.Items.RemoveAll(x => x.Quantity <= 0);

        return (totalDiscountAmount, consumedItems, giftItems);
    }

    /// <summary>
    /// 根据策略选择最优的赠品商品
    /// </summary>
    private List<string> SelectOptimalGiftProducts(List<string> candidateProductIds, ShoppingCart cart, int requiredQuantity)
    {
        if (!candidateProductIds.Any())
            return new List<string>();

        var productPrices = new List<(string ProductId, decimal Price)>();

        foreach (var productId in candidateProductIds)
        {
            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
            if (cartItem != null)
            {
                productPrices.Add((productId, cartItem.Product.Price));
            }
        }

        if (!productPrices.Any())
            return new List<string>();

        var sortedProducts = GiftSelectionStrategy switch
        {
            GiftSelectionStrategy.CustomerBenefit => productPrices.OrderByDescending(p => p.Price),
            GiftSelectionStrategy.MerchantBenefit => productPrices.OrderBy(p => p.Price),
            _ => productPrices.OrderByDescending(p => p.Price)
        };

        var selectedProducts = new List<string>();
        var remainingQuantity = requiredQuantity;

        foreach (var (productId, _) in sortedProducts)
        {
            if (remainingQuantity <= 0) break;

            var availableQuantity = cart.Items
                .Where(x => x.Product.Id == productId)
                .Sum(x => x.Quantity);

            if (availableQuantity > 0)
            {
                selectedProducts.Add(productId);
                remainingQuantity--;
            }
        }

        return selectedProducts;
    }
}
