global using Microsoft.Extensions.Logging;
global using Microsoft.Extensions.Caching.Memory;
global using Microsoft.Extensions.DependencyInjection;
using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Conditions;
using PE2.PromotionEngine.Observability;

namespace PE2.PromotionEngine.Tests;

/// <summary>
/// 条件引擎测试类
/// </summary>
public class ConditionEngineTests
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ConditionEngine _conditionEngine;
    private readonly ObservabilityEngine _observabilityEngine;

    public ConditionEngineTests()
    {
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole());
        services.AddMemoryCache();
        
        _serviceProvider = services.BuildServiceProvider();
        
        var logger = _serviceProvider.GetRequiredService<ILogger<ConditionEngine>>();
        var memoryCache = _serviceProvider.GetRequiredService<IMemoryCache>();
        var obsLogger = _serviceProvider.GetRequiredService<ILogger<ObservabilityEngine>>();
        
        _observabilityEngine = new ObservabilityEngine(obsLogger);
        _conditionEngine = new ConditionEngine(logger, _observabilityEngine, memoryCache);
    }

    /// <summary>
    /// 测试商品数量条件验证
    /// </summary>
    public async Task TestProductQuantityCondition()
    {
        // 准备测试数据
        var cart = CreateTestCart();
        var condition = new ConditionExpression
        {
            Id = "test-product-quantity",
            Type = ConditionType.ProductQuantity,
            Parameters = new Dictionary<string, object>
            {
                { "productIds", new List<string> { "P001", "P002" } },
                { "requiredQuantity", 5 }
            },
            IsCacheable = true
        };

        // 执行验证
        var result = await _conditionEngine.ValidateAsync(condition, cart);

        // 验证结果
        Console.WriteLine($"条件验证结果: {result.IsSatisfied}");
        Console.WriteLine($"实际值: {result.ActualValue}");
        Console.WriteLine($"需要值: {result.RequiredValue}");
        Console.WriteLine($"缺口: {result.Gap}");
        Console.WriteLine($"验证时间: {result.ValidationTimeMs}ms");

        // 测试缓存
        var cachedResult = await _conditionEngine.ValidateAsync(condition, cart);
        Console.WriteLine($"缓存验证时间: {cachedResult.ValidationTimeMs}ms");
    }

    /// <summary>
    /// 测试复合条件验证
    /// </summary>
    public async Task TestCompositeCondition()
    {
        var cart = CreateTestCart();
        
        // 创建复合条件：(商品数量 >= 3) AND (总金额 >= 100)
        var condition = new ConditionExpression
        {
            Id = "test-composite",
            Type = ConditionType.Custom,
            Operator = LogicalOperator.And,
            SubConditions = new List<ConditionExpression>
            {
                new ConditionExpression
                {
                    Id = "sub-quantity",
                    Type = ConditionType.ProductQuantity,
                    Parameters = new Dictionary<string, object>
                    {
                        { "productIds", new List<string> { "P001" } },
                        { "requiredQuantity", 3 }
                    }
                },
                new ConditionExpression
                {
                    Id = "sub-amount",
                    Type = ConditionType.TotalAmount,
                    Parameters = new Dictionary<string, object>
                    {
                        { "requiredAmount", 100m }
                    }
                }
            }
        };

        var result = await _conditionEngine.ValidateAsync(condition, cart);
        
        Console.WriteLine($"复合条件验证结果: {result.IsSatisfied}");
        Console.WriteLine($"缺口: {result.Gap}");
    }

    /// <summary>
    /// 测试批量条件验证
    /// </summary>
    public async Task TestBatchValidation()
    {
        var cart = CreateTestCart();
        var conditions = new Dictionary<string, ConditionExpression>
        {
            {
                "quantity-check",
                new ConditionExpression
                {
                    Id = "quantity-check",
                    Type = ConditionType.TotalQuantity,
                    Parameters = new Dictionary<string, object> { { "requiredQuantity", 5 } }
                }
            },
            {
                "amount-check",
                new ConditionExpression
                {
                    Id = "amount-check",
                    Type = ConditionType.TotalAmount,
                    Parameters = new Dictionary<string, object> { { "requiredAmount", 150m } }
                }
            },
            {
                "member-check",
                new ConditionExpression
                {
                    Id = "member-check",
                    Type = ConditionType.MemberLevel,
                    Parameters = new Dictionary<string, object> 
                    { 
                        { "requiredLevels", new List<string> { "Gold", "Platinum" } } 
                    }
                }
            }
        };

        var results = await _conditionEngine.ValidateBatchAsync(conditions, cart);
        
        Console.WriteLine("批量验证结果:");
        foreach (var kvp in results)
        {
            Console.WriteLine($"  {kvp.Key}: {kvp.Value.IsSatisfied} - {kvp.Value.Gap}");
        }
    }

    /// <summary>
    /// 测试条件分析
    /// </summary>
    public async Task TestConditionAnalysis()
    {
        var cart = CreateTestCart();
        var condition = new ConditionExpression
        {
            Id = "analysis-test",
            Type = ConditionType.CategoryQuantity,
            Parameters = new Dictionary<string, object>
            {
                { "categoryIds", new List<string> { "CAT001", "CAT002", "CAT003" } },
                { "requiredQuantity", 8 }
            }
        };

        var analysis = await _conditionEngine.AnalyzeAsync(condition, cart);
        
        Console.WriteLine("条件分析结果:");
        Console.WriteLine($"  复杂度评分: {analysis.ComplexityScore}");
        Console.WriteLine($"  预估验证时间: {analysis.EstimatedValidationTimeMs}ms");
        Console.WriteLine($"  实际验证时间: {analysis.ActualValidationTimeMs}ms");
        Console.WriteLine($"  优化建议: {string.Join(", ", analysis.OptimizationSuggestions)}");
        Console.WriteLine($"  需要的数据字段: {string.Join(", ", analysis.RequiredDataFields)}");
    }

    /// <summary>
    /// 测试条件编译
    /// </summary>
    public void TestConditionCompilation()
    {
        var condition = new ConditionExpression
        {
            Id = "compile-test",
            Type = ConditionType.TotalAmount,
            Parameters = new Dictionary<string, object> { { "requiredAmount", 200m } }
        };

        var compiled = _conditionEngine.CompileCondition(condition);
        
        Console.WriteLine("条件编译结果:");
        Console.WriteLine($"  编译成功: {compiled.IsValid}");
        Console.WriteLine($"  编译时间: {compiled.CompiledAt}");
        
        if (compiled.IsValid)
        {
            var cart = CreateTestCart();
            var result = compiled.ValidationFunction(cart);
            Console.WriteLine($"  编译后验证结果: {result.IsSatisfied}");
        }
    }

    /// <summary>
    /// 运行所有测试
    /// </summary>
    public async Task RunAllTests()
    {
        Console.WriteLine("=== 条件引擎测试开始 ===\n");

        try
        {
            Console.WriteLine("1. 测试商品数量条件验证");
            await TestProductQuantityCondition();
            Console.WriteLine();

            Console.WriteLine("2. 测试复合条件验证");
            await TestCompositeCondition();
            Console.WriteLine();

            Console.WriteLine("3. 测试批量条件验证");
            await TestBatchValidation();
            Console.WriteLine();

            Console.WriteLine("4. 测试条件分析");
            await TestConditionAnalysis();
            Console.WriteLine();

            Console.WriteLine("5. 测试条件编译");
            TestConditionCompilation();
            Console.WriteLine();

            Console.WriteLine("=== 所有测试完成 ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"测试执行失败: {ex.Message}");
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        }
        finally
        {
            _conditionEngine.Dispose();
            _observabilityEngine.Dispose();
        }
    }

    /// <summary>
    /// 创建测试购物车
    /// </summary>
    private static ShoppingCart CreateTestCart()
    {
        var cart = new ShoppingCart
        {
            Id = "test-cart",
            MemberId = "M001",
            MemberLevel = "Silver",
            Items = new List<CartItem>
            {
                new CartItem
                {
                    Product = new Product { Id = "P001", Name = "商品1", CategoryId = "CAT001" },
                    Quantity = 3,
                    AvailableQuantity = 3,
                    UnitPrice = 50m
                },
                new CartItem
                {
                    Product = new Product { Id = "P002", Name = "商品2", CategoryId = "CAT002" },
                    Quantity = 2,
                    AvailableQuantity = 2,
                    UnitPrice = 30m
                },
                new CartItem
                {
                    Product = new Product { Id = "P003", Name = "商品3", CategoryId = "CAT001" },
                    Quantity = 1,
                    AvailableQuantity = 1,
                    UnitPrice = 80m
                }
            }
        };

        return cart;
    }
}

/// <summary>
/// 测试程序入口
/// </summary>
public class Program
{
    public static async Task Main(string[] args)
    {
        var tests = new ConditionEngineTests();
        await tests.RunAllTests();
        
        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
}
