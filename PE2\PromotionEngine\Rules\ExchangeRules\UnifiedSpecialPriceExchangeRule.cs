using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.ExchangeRules;

/// <summary>
/// 统一特价换购规则
/// 针对某类商品，满X件或X元，加Y元，换购一件C类商品
/// 场景：购买A商品1件时，若再增加1元可换购B商品，若增加2元可换购C商品
/// </summary>
public class UnifiedSpecialPriceExchangeRule : BaseExchangeRule
{
    public override string RuleType => "UnifiedSpecialPriceExchange";
    /// <summary>
    /// 购买条件列表
    /// </summary>
    public List<ExchangeBuyCondition> BuyConditions { get; set; } = new();

    /// <summary>
    /// 换购条件列表（支持梯度换购）
    /// </summary>
    public List<SpecialPriceExchangeCondition> ExchangeConditions { get; set; } = new();

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!BuyConditions.Any() || !ExchangeConditions.Any())
            return false;

        // 检查购买条件
        if (!CheckBuyConditions(cart))
            return false;

        // 检查换购商品是否在购物车中（POS系统核心要求）
        var allExchangeProductIds = ExchangeConditions
            .SelectMany(c => c.ExchangeProductIds)
            .Distinct()
            .ToList();

        return ValidateExchangeProductsInCart(cart, allExchangeProductIds);
    }

    /// <summary>
    /// 检查购买条件
    /// </summary>
    private bool CheckBuyConditions(ShoppingCart cart)
    {
        foreach (var buyCondition in BuyConditions)
        {
            var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
            var totalAmount = buyCondition.ProductIds.Sum(id =>
                cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));

            if (buyCondition.RequiredQuantity > 0 && availableQuantity < buyCondition.RequiredQuantity)
                return false;

            if (buyCondition.RequiredAmount > 0 && totalAmount < buyCondition.RequiredAmount)
                return false;
        }

        return true;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = 0;

        foreach (var buyCondition in BuyConditions)
        {
            var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));

            if (buyCondition.RequiredQuantity > 0)
            {
                var maxByQuantity = IsRepeatable
                    ? availableQuantity / buyCondition.RequiredQuantity
                    : (availableQuantity >= buyCondition.RequiredQuantity ? 1 : 0);

                maxApplications = Math.Max(maxApplications, maxByQuantity);
            }
            else
            {
                maxApplications = Math.Max(maxApplications, 1);
            }
        }

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplySpecialPriceExchange(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用特价换购促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用特价换购促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplySpecialPriceExchange(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var exchangeItems = new List<GiftItem>(); // 换购商品记录为GiftItem，但实际是换购

        for (int app = 0; app < applicationCount; app++)
        {
            foreach (var buyCondition in BuyConditions)
            {
                // 消耗购买条件商品
                if (!ConsumeBuyConditionProducts(cart, buyCondition, consumedItems))
                    continue;

                // 根据策略选择换购商品
                var applicableExchanges = GetApplicableExchangeConditions();

                foreach (var exchange in applicableExchanges)
                {
                    var selectedExchangeProducts = SelectOptimalExchangeProducts(
                        exchange.ExchangeProductIds, cart, exchange.ExchangeQuantity);

                    foreach (var productId in selectedExchangeProducts)
                    {
                        var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
                        if (cartItem == null || cartItem.Quantity <= 0) continue;

                        // 计算换购折扣（原价 - 加价金额）
                        var originalPrice = cartItem.UnitPrice;
                        var exchangePrice = exchange.AddAmount;
                        var discountAmount = originalPrice - exchangePrice;

                        if (discountAmount > 0)
                        {
                            totalDiscountAmount += discountAmount;

                            var strategyDescription = ExchangeSelectionStrategy == ExchangeSelectionStrategy.CustomerBenefit
                                ? "客户利益最大化"
                                : "商家利益最大化";

                            exchangeItems.Add(new GiftItem
                            {
                                ProductId = productId,
                                ProductName = cartItem.Product.Name,
                                Quantity = 1,
                                Value = discountAmount,
                                Description = $"特价换购：加{exchange.AddAmount:C}换购，节省{discountAmount:C}（{strategyDescription}）"
                            });

                            // 标记商品为换购商品（修改实际支付价格）
                            cartItem.ActualUnitPrice = exchangePrice;
                        }
                    }
                }
            }
        }

        return (totalDiscountAmount, consumedItems, exchangeItems);
    }

    /// <summary>
    /// 消耗购买条件商品
    /// </summary>
    private bool ConsumeBuyConditionProducts(ShoppingCart cart, ExchangeBuyCondition buyCondition, List<ConsumedItem> consumedItems)
    {
        var remainingQuantity = buyCondition.RequiredQuantity;

        foreach (var productId in buyCondition.ProductIds)
        {
            if (remainingQuantity <= 0) break;

            var availableItems = cart.Items
                .Where(x => x.Product.Id == productId && x.AvailableQuantity > 0)
                .ToList();

            foreach (var item in availableItems)
            {
                if (remainingQuantity <= 0) break;

                var consumeQuantity = Math.Min(item.AvailableQuantity, remainingQuantity);

                var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == productId);
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += consumeQuantity;
                }
                else
                {
                    consumedItems.Add(new ConsumedItem
                    {
                        ProductId = productId,
                        ProductName = item.Product.Name,
                        Quantity = consumeQuantity,
                        UnitPrice = item.UnitPrice
                    });
                }

                item.Quantity -= consumeQuantity;
                remainingQuantity -= consumeQuantity;
            }
        }

        return remainingQuantity <= 0;
    }

    /// <summary>
    /// 获取适用的换购条件
    /// </summary>
    private List<SpecialPriceExchangeCondition> GetApplicableExchangeConditions()
    {
        return ExchangeStrategy switch
        {
            ExchangeStrategy.ByGradient => ExchangeConditions.Take(1).ToList(), // 梯度换购：只取第一个
            ExchangeStrategy.AllExchange => ExchangeConditions.ToList(), // 全部换购：取所有
            _ => ExchangeConditions.Take(1).ToList()
        };
    }
}

/// <summary>
/// 换购购买条件
/// </summary>
public class ExchangeBuyCondition
{
    /// <summary>
    /// 商品ID列表
    /// </summary>
    public List<string> ProductIds { get; set; } = new();

    /// <summary>
    /// 所需数量
    /// </summary>
    public int RequiredQuantity { get; set; }

    /// <summary>
    /// 所需金额
    /// </summary>
    public decimal RequiredAmount { get; set; }
}

/// <summary>
/// 特价换购条件
/// </summary>
public class SpecialPriceExchangeCondition
{
    /// <summary>
    /// 换购商品ID列表
    /// </summary>
    public List<string> ExchangeProductIds { get; set; } = new();

    /// <summary>
    /// 换购数量
    /// </summary>
    public int ExchangeQuantity { get; set; } = 1;

    /// <summary>
    /// 加价金额
    /// </summary>
    public decimal AddAmount { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}
