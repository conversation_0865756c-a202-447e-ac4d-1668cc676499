using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.SpecialPriceRules;

/// <summary>
/// 组合特价规则 [OK]
/// 某A类商品满X件并且某B类商品满X件，特价N元
/// 场景案例： A、B商品各购买大于等于1件时，特价600元。  
/// 促销设置为不翻倍，A、B吊牌价、零售价各为1000；A、B各买1件，应付金额为600； 
/// 促销设置为不翻倍，A、B吊牌价、零售价各为1000；A买2件，B买1件，应付金额为1000 * (600 /（1000 * 2) * 3 = 900；
/// 促销设置为翻倍，A、B吊牌价、零售价各为1000；购买A、B商品各3件，应付金额为600 * 3 = 1800。
/// 备注：如果是大于等于 或者大于 都不考虑翻倍，只有等于时才考虑翻倍
/// </summary>
public class CombinationSpecialPriceRule : BaseSpecialPriceRule
{
    public override string RuleType => "CombinationSpecialPrice";

    /// <summary>
    /// 组合购买条件列表
    /// </summary>
    public List<CombinationSpecialPriceCondition> CombinationConditions { get; set; } = new();

    /// <summary>
    /// 特价金额
    /// </summary>
    public decimal SpecialPrice { get; set; }

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!CombinationConditions.Any())
            return false;

        // 验证组合商品是否在购物车中
        var allProductIds = CombinationConditions.Select(c => c.ProductId).ToList();
        if (!ValidateSpecialPriceProductsInCart(cart, allProductIds))
            return false;

        // 检查组合购买条件
        return CheckCombinationConditions(cart);
    }

    /// <summary>
    /// 检查组合购买条件
    /// </summary>
    private bool CheckCombinationConditions(ShoppingCart cart)
    {
        foreach (var condition in CombinationConditions)
        {
            var availableQuantity = cart.GetAvailableProductQuantity(condition.ProductId);
            var totalAmount = cart.Items
                .Where(x => x.Product.Id == condition.ProductId)
                .Sum(x => x.SubTotal);

            if (condition.RequiredQuantity > 0 && availableQuantity < condition.RequiredQuantity)
                return false;

            if (condition.RequiredAmount > 0 && totalAmount < condition.RequiredAmount)
                return false;
        }

        return true;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyCombinationSpecialPrice(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用组合特价促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用组合特价促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyCombinationSpecialPrice(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var specialPriceRecords = new List<GiftItem>();

        var promotion = new AppliedPromotion { RuleId = Id, RuleName = Name, PromotionType = RuleType };
        var strategyDesc = SpecialPriceSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit ? "客户利益最大化" : "商家利益最大化";

        // 计算实际允许的应用次数
        var allowedApplications = MaxApplications > 0 ? Math.Min(applicationCount, MaxApplications) : applicationCount;
        var processedByProduct = new Dictionary<string, int>();

        // 应用完整组合
        var actualApplications = 0;
        for (int app = 0; app < allowedApplications; app++)
        {
            var (isValid, participatingItems, totalOriginal) = CalculateParticipatingItems(cart, processedByProduct);
            if (!isValid) break;

            actualApplications++;
            var discount = Math.Max(0, totalOriginal - SpecialPrice);
            totalDiscountAmount += discount;

            // 应用组合特价
            ApplyPricingToItems(participatingItems, SpecialPrice, totalOriginal, promotion,
                $"组合特价：{GetCombinationDescription()}，特价{SpecialPrice:C}（第{app + 1}次，{strategyDesc}）",
                consumedItems, specialPriceRecords);

            // 更新已处理数量
            foreach (var (item, quantity) in participatingItems)
                processedByProduct[item.Product.Id] = processedByProduct.GetValueOrDefault(item.Product.Id, 0) + quantity;

            if (!IsRepeatable) break;
        }

        // 处理余量商品
        ProcessRemainingItems(cart, processedByProduct, actualApplications, allowedApplications, promotion, strategyDesc,
            ref totalDiscountAmount, consumedItems, specialPriceRecords);

        return (totalDiscountAmount, consumedItems, specialPriceRecords);
    }

    /// <summary>
    /// 处理剩余商品（区分翻倍范围内和超出范围的商品）
    /// </summary>
    private void ProcessRemainingItems(ShoppingCart cart, Dictionary<string, int> processedByProduct,
        int actualApplications, int allowedApplications, AppliedPromotion promotion, string strategyDesc,
        ref decimal totalDiscount, List<ConsumedItem> consumedItems, List<GiftItem> records)
    {
        // 计算组合内商品总数和组合单价
        var combinationItemCount = CombinationConditions.Sum(c => c.RequiredQuantity > 0 ? c.RequiredQuantity : 1);
        var combinationUnitPrice = SpecialPrice / combinationItemCount;

        foreach (var condition in CombinationConditions)
        {
            var availableItems = cart.Items.Where(x => x.Product.Id == condition.ProductId && x.Quantity > 0).ToList();
            var processedQty = processedByProduct.GetValueOrDefault(condition.ProductId, 0);

            // 计算各种数量
            var totalQty = availableItems.Sum(x => x.Quantity);
            var requiredPerCombination = condition.RequiredQuantity > 0 ? condition.RequiredQuantity : 1;

            // 翻倍范围内的余量数量（可以享受组合单价）
            var withinLimitQty = 0;
            if (IsRepeatable && actualApplications < allowedApplications)
            {
                var maxPossibleCombinations = allowedApplications;
                var maxRequiredForThisProduct = maxPossibleCombinations * requiredPerCombination;
                withinLimitQty = Math.Max(0, Math.Min(totalQty, maxRequiredForThisProduct) - processedQty);
            }

            // 超出翻倍范围的数量（原价）
            var beyondLimitQty = Math.Max(0, totalQty - processedQty - withinLimitQty);

            // 处理翻倍范围内的余量（使用组合单价）
            if (withinLimitQty > 0)
            {
                ProcessItemsWithSpecialPrice(availableItems, processedQty, withinLimitQty, combinationUnitPrice,
                    promotion, $"翻倍范围内余量：{withinLimitQty}件按组合单价{combinationUnitPrice:C}（{strategyDesc}）",
                    ref totalDiscount, consumedItems, records);
            }

            // 处理超出翻倍范围的商品（原价，无折扣）
            if (beyondLimitQty > 0)
            {
                ProcessItemsWithOriginalPrice(availableItems, processedQty + withinLimitQty, beyondLimitQty,
                    promotion, $"超出翻倍范围：{beyondLimitQty}件按原价（{strategyDesc}）",
                    consumedItems, records);
            }
        }
    }

    /// <summary>
    /// 用特殊价格处理商品
    /// </summary>
    private void ProcessItemsWithSpecialPrice(List<CartItem> availableItems, int skipQty, int targetQty,
        decimal specialUnitPrice, AppliedPromotion promotion, string description,
        ref decimal totalDiscount, List<ConsumedItem> consumedItems, List<GiftItem> records)
    {
        var processedQty = 0;
        var currentSkip = skipQty;

        foreach (var item in SortItemsByStrategy(availableItems))
        {
            if (processedQty >= targetQty) break;

            // 跳过已处理的部分
            if (currentSkip > 0)
            {
                var skipFromThis = Math.Min(currentSkip, item.Quantity);
                currentSkip -= skipFromThis;

                var remainingFromThis = item.Quantity - skipFromThis;
                if (remainingFromThis > 0)
                {
                    var useQty = Math.Min(remainingFromThis, targetQty - processedQty);
                    ApplyItemPricing(item, useQty, specialUnitPrice, promotion, description,
                        ref totalDiscount, consumedItems, records);
                    processedQty += useQty;
                }
            }
            else
            {
                var useQty = Math.Min(item.Quantity, targetQty - processedQty);
                ApplyItemPricing(item, useQty, specialUnitPrice, promotion, description,
                    ref totalDiscount, consumedItems, records);
                processedQty += useQty;
            }
        }
    }

    /// <summary>
    /// 用原价处理商品（无折扣）
    /// </summary>
    private void ProcessItemsWithOriginalPrice(List<CartItem> availableItems, int skipQty, int targetQty,
        AppliedPromotion promotion, string description, List<ConsumedItem> consumedItems, List<GiftItem> records)
    {
        var processedQty = 0;
        var currentSkip = skipQty;

        foreach (var item in SortItemsByStrategy(availableItems))
        {
            if (processedQty >= targetQty) break;

            // 跳过已处理的部分
            if (currentSkip > 0)
            {
                var skipFromThis = Math.Min(currentSkip, item.Quantity);
                currentSkip -= skipFromThis;

                var remainingFromThis = item.Quantity - skipFromThis;
                if (remainingFromThis > 0)
                {
                    var useQty = Math.Min(remainingFromThis, targetQty - processedQty);
                    // 原价处理，无折扣，只记录消耗
                    AddOrUpdateConsumed(consumedItems, item.Product.Id, item.Product.Name, useQty, item.UnitPrice);
                    processedQty += useQty;
                }
            }
            else
            {
                var useQty = Math.Min(item.Quantity, targetQty - processedQty);
                // 原价处理，无折扣，只记录消耗
                AddOrUpdateConsumed(consumedItems, item.Product.Id, item.Product.Name, useQty, item.UnitPrice);
                processedQty += useQty;
            }
        }
    }

    /// <summary>
    /// 应用商品定价
    /// </summary>
    private void ApplyItemPricing(CartItem item, int useQty, decimal specialUnitPrice, AppliedPromotion promotion,
        string description, ref decimal totalDiscount, List<ConsumedItem> consumedItems, List<GiftItem> records)
    {
        var originalAmt = item.UnitPrice * useQty;
        var specialAmt = specialUnitPrice * useQty;
        var discount = Math.Max(0, originalAmt - specialAmt);

        if (discount > 0)
        {
            totalDiscount += discount;
            UpdateItemPricing(item, useQty, specialUnitPrice, promotion, description);
            records.Add(CreateSpecialPriceRecord(item.Product.Id, item.Product.Name, useQty, discount,
                $"{description}，{useQty}件节省{discount:C}"));
        }

        AddOrUpdateConsumed(consumedItems, item.Product.Id, item.Product.Name, useQty, item.UnitPrice);
    }

    /// <summary>
    /// 计算参与组合的商品
    /// </summary>
    private (bool IsValid, List<(CartItem Item, int Quantity)> ParticipatingItems, decimal TotalOriginal)
        CalculateParticipatingItems(ShoppingCart cart, Dictionary<string, int> processedByProduct)
    {
        var participatingItems = new List<(CartItem Item, int Quantity)>();
        var totalOriginal = 0m;

        foreach (var condition in CombinationConditions)
        {
            var availableItems = cart.Items.Where(x => x.Product.Id == condition.ProductId && x.Quantity > 0).ToList();
            if (!availableItems.Any()) return (false, null, 0);

            var totalQty = availableItems.Sum(x => x.Quantity);
            var processedQty = processedByProduct.GetValueOrDefault(condition.ProductId, 0);
            var requiredQty = condition.RequiredQuantity > 0 ? condition.RequiredQuantity : 1;

            if (totalQty - processedQty < requiredQty) return (false, null, 0);

            // 选择参与商品
            var needQty = requiredQty;
            var skipQty = processedQty;

            foreach (var item in SortItemsByStrategy(availableItems))
            {
                if (needQty <= 0) break;

                if (skipQty > 0)
                {
                    var skipFromThis = Math.Min(skipQty, item.Quantity);
                    skipQty -= skipFromThis;
                    var remainingFromThis = item.Quantity - skipFromThis;

                    if (remainingFromThis > 0)
                    {
                        var useQty = Math.Min(remainingFromThis, needQty);
                        participatingItems.Add((item, useQty));
                        totalOriginal += item.UnitPrice * useQty;
                        needQty -= useQty;
                    }
                }
                else
                {
                    var useQty = Math.Min(item.Quantity, needQty);
                    participatingItems.Add((item, useQty));
                    totalOriginal += item.UnitPrice * useQty;
                    needQty -= useQty;
                }
            }

            if (needQty > 0) return (false, null, 0);
        }

        return (true, participatingItems, totalOriginal);
    }

    /// <summary>
    /// 应用定价到商品组
    /// </summary>
    private void ApplyPricingToItems(List<(CartItem Item, int Quantity)> items, decimal targetPrice, decimal originalTotal,
        AppliedPromotion promotion, string description, List<ConsumedItem> consumedItems, List<GiftItem> records)
    {
        foreach (var (item, quantity) in items)
        {
            var itemOriginal = item.UnitPrice * quantity;
            var itemTarget = Math.Round(targetPrice * itemOriginal / originalTotal, 2);
            var newUnitPrice = itemTarget / quantity;
            var discount = itemOriginal - itemTarget;

            UpdateItemPricing(item, quantity, newUnitPrice, promotion, description);
            AddOrUpdateConsumed(consumedItems, item.Product.Id, item.Product.Name, quantity, item.UnitPrice);

            if (discount > 0)
            {
                records.Add(CreateSpecialPriceRecord(item.Product.Id, item.Product.Name, quantity, discount,
                    $"{description}，{quantity}件节省{discount:C}"));
            }
        }
    }

    /// <summary>
    /// 更新商品定价
    /// </summary>
    private void UpdateItemPricing(CartItem item, int affectedQty, decimal newUnitPrice, AppliedPromotion promotion, string desc)
    {
        var totalQty = item.Quantity;
        var unaffectedQty = totalQty - affectedQty;

        item.ActualUnitPrice = (affectedQty * newUnitPrice + unaffectedQty * item.UnitPrice) / totalQty;

        var discount = (item.UnitPrice - newUnitPrice) * affectedQty;
        item.AddPromotionDetail(new ItemPromotionDetail
        {
            RuleId = promotion.RuleId,
            RuleName = promotion.RuleName,
            PromotionType = promotion.PromotionType,
            DiscountAmount = discount,
            Description = $"{desc}（{affectedQty}件特价，{unaffectedQty}件原价）",
            IsGiftRelated = false
        });
    }

    /// <summary>
    /// 添加或更新消耗商品
    /// </summary>
    private void AddOrUpdateConsumed(List<ConsumedItem> list, string productId, string productName, int qty, decimal unitPrice)
    {
        var existing = list.FirstOrDefault(x => x.ProductId == productId);
        if (existing != null)
            existing.Quantity += qty;
        else
            list.Add(new ConsumedItem { ProductId = productId, ProductName = productName, Quantity = qty, UnitPrice = unitPrice });
    }

    /// <summary>
    /// 获取组合描述
    /// </summary>
    private string GetCombinationDescription() =>
        string.Join("、", CombinationConditions.Select(c =>
            c.RequiredQuantity > 0 ? $"{c.ProductId}满{c.RequiredQuantity}件" : $"{c.ProductId}满{c.RequiredAmount:C}"));

    /// <summary>
    /// 计算最大应用次数
    /// </summary>
    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart)) return 0;

        var maxApps = int.MaxValue;

        foreach (var condition in CombinationConditions)
        {
            var items = cart.Items.Where(x => x.Product.Id == condition.ProductId && x.Quantity > 0);
            if (!items.Any()) return 0;

            var requiredQty = condition.RequiredQuantity > 0 ? condition.RequiredQuantity : 1;
            var totalQty = items.Sum(x => x.Quantity);
            var conditionMax = IsRepeatable ? totalQty / requiredQty : (totalQty >= requiredQty ? 1 : 0);

            maxApps = Math.Min(maxApps, conditionMax);
        }

        if (maxApps == int.MaxValue) maxApps = 1;
        return MaxApplications > 0 ? Math.Min(maxApps, MaxApplications) : maxApps;
    }

}

/// <summary>
/// 组合特价条件
/// </summary>
public class CombinationSpecialPriceCondition
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 所需数量
    /// </summary>
    public int RequiredQuantity { get; set; }

    /// <summary>
    /// 所需金额
    /// </summary>
    public decimal RequiredAmount { get; set; }
}