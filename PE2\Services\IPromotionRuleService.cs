﻿using PE2.PromotionEngine.Rules;

namespace PE2.Services
{
    public interface IPromotionRuleService
    {
        Task<bool> AddRuleAsync(PromotionRuleBase rule);
        Task<bool> DeleteRuleAsync(string ruleId);
        Task<List<PromotionRuleBase>> GetAllRulesAsync();
        Task<List<PromotionRuleBase>> GetEnabledRulesAsync();
        Task<PromotionRuleBase?> GetRuleByIdAsync(string ruleId);
        Task<List<PromotionRuleBase>> GetValidRulesAsync(DateTime? checkTime = null);
        Task ReloadRulesAsync();
        Task<bool> SetRuleEnabledAsync(string ruleId, bool enabled);
        Task<bool> UpdateRuleAsync(PromotionRuleBase rule);
        Task<(bool IsValid, string ErrorMessage)> ValidateRulesFileAsync();
    }
}