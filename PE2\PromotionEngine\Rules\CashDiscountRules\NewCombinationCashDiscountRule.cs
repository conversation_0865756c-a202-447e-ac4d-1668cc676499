using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Core;
using PE2.PromotionEngine.Observability;
using PE2.PromotionEngine.Conditions;
using PE2.PromotionEngine.Inventory;
using PE2.PromotionEngine.Performance;
using PE2.PromotionEngine.Allocation;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Rules.CashDiscountRules;

/// <summary>
/// 新架构组合减现规则
/// 某A类商品满X件或Y元并且某B类商品满X件或Y元，立减N元
/// 集成新架构的所有基础设施组件，支持.NET 9.0语法和异步模式
/// </summary>
public sealed class NewCombinationCashDiscountRule : NewCashDiscountRuleBase, INewPromotionRule
{
    /// <summary>
    /// 构造函数
    /// </summary>
    public NewCombinationCashDiscountRule(
        ILogger<NewCombinationCashDiscountRule> logger,
        IObservabilityEngine observability,
        IConditionEngine conditionEngine,
        IInventoryManager inventoryManager,
        IPerformanceOptimizer performanceOptimizer,
        IAllocationEngine allocationEngine,
        CombinationCashDiscountConfiguration configuration)
        : base(logger, observability, conditionEngine, inventoryManager, performanceOptimizer, allocationEngine)
    {
        ArgumentNullException.ThrowIfNull(configuration);
        
        Id = configuration.Id;
        Name = configuration.Name;
        Description = configuration.Description;
        Priority = configuration.Priority;
        IsEnabled = configuration.IsEnabled;
        StartTime = configuration.StartTime;
        EndTime = configuration.EndTime;
        IsRepeatable = configuration.IsRepeatable;
        MaxApplications = configuration.MaxApplications;
        ApplicableCustomerTypes = configuration.ApplicableCustomerTypes;
        ExclusiveRuleIds = configuration.ExclusiveRuleIds;
        CanStackWithOthers = configuration.CanStackWithOthers;
        ProductExclusivity = configuration.ProductExclusivity;
        CombinationConditions = configuration.CombinationConditions;
        DiscountAmount = configuration.DiscountAmount;
    }

    /// <summary>
    /// 规则类型
    /// </summary>
    public override string RuleType => "CombinationCashDiscount";

    /// <summary>
    /// 组合购买条件列表
    /// </summary>
    public List<CombinationCashDiscountCondition> CombinationConditions { get; init; } = [];

    /// <summary>
    /// 减现金额
    /// </summary>
    public decimal DiscountAmount { get; init; }

    /// <summary>
    /// 异步检查促销条件是否满足
    /// </summary>
    protected override async Task<bool> CheckConditionsAsync(ShoppingCart cart, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!CombinationConditions.Any())
                return false;

            // 验证组合商品是否在购物车中
            var allProductIds = CombinationConditions.SelectMany(c => c.ProductIds).Distinct().ToList();
            if (!await ValidateCashDiscountProductsInCartAsync(cart, allProductIds, cancellationToken).ConfigureAwait(false))
                return false;

            // 检查每个组合条件是否满足
            foreach (var condition in CombinationConditions)
            {
                var conditionResult = await ConditionEngine.ValidateConditionAsync(condition, cart, cancellationToken).ConfigureAwait(false);
                if (!conditionResult.IsValid)
                    return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "检查组合减现条件时发生异常: {RuleId}", Id);
            return false;
        }
    }

    /// <summary>
    /// 异步计算可应用的最大次数
    /// </summary>
    public override async Task<int> CalculateMaxApplicationsAsync(ShoppingCart cart, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!await CheckConditionsAsync(cart, cancellationToken).ConfigureAwait(false))
                return 0;

            // 使用性能优化器计算最大应用次数
            var optimizationContext = new OptimizationContext
            {
                Cart = cart,
                Rules = [this],
                Target = OptimizationTarget.MaximizeApplications
            };

            var result = await PerformanceOptimizer.OptimizeRuleCombinationAsync(optimizationContext, cancellationToken).ConfigureAwait(false);

            // 计算基于组合条件的最大应用次数
            var maxApplications = CombinationConditions
                .Select(condition =>
                {
                    if (condition.IsByAmount)
                    {
                        var totalAmount = condition.ProductIds.Sum(productId =>
                            cart.Items.Where(item => item.ProductId == productId).Sum(item => item.SubTotal));
                        return IsRepeatable
                            ? (int)(totalAmount / condition.MinAmount)
                            : (totalAmount >= condition.MinAmount ? 1 : 0);
                    }
                    else
                    {
                        var totalQuantity = condition.ProductIds.Sum(productId =>
                            cart.Items.Where(item => item.ProductId == productId).Sum(item => item.Quantity));
                        return IsRepeatable
                            ? totalQuantity / condition.MinQuantity
                            : (totalQuantity >= condition.MinQuantity ? 1 : 0);
                    }
                })
                .DefaultIfEmpty(1)
                .Min();

            return ApplyApplicationLimits(maxApplications);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "计算组合减现最大应用次数时发生异常: {RuleId}", Id);
            return 0;
        }
    }

    /// <summary>
    /// 异步应用促销规则
    /// </summary>
    public override async Task<AppliedPromotion> ApplyPromotionAsync(
        ProcessedCart cart, 
        int applicationCount = 1, 
        string? traceId = null,
        CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        
        try
        {
            // 记录开始追踪
            if (!string.IsNullOrEmpty(traceId))
            {
                Observability.TrackCalculationStep(traceId, new CalculationStep
                {
                    Id = Guid.NewGuid().ToString("N"),
                    TraceId = traceId,
                    StepType = StepType.PromotionApplication,
                    Timestamp = startTime,
                    Description = $"开始应用组合减现规则: {Name}",
                    RuleId = Id,
                    Data = new Dictionary<string, object>
                    {
                        ["applicationCount"] = applicationCount,
                        ["cartItemCount"] = cart.Items.Count,
                        ["combinationConditionsCount"] = CombinationConditions.Count,
                        ["discountAmount"] = DiscountAmount
                    }
                });
            }

            // 预留库存
            var reservationId = await ReserveInventoryAsync(cart, applicationCount, cancellationToken).ConfigureAwait(false);

            try
            {
                // 应用组合减现逻辑
                var (discountAmount, consumedItems, discountRecords) = await ApplyCombinationCashDiscountAsync(
                    cart, applicationCount, traceId, cancellationToken).ConfigureAwait(false);

                var appliedPromotion = CreateAppliedPromotion(discountAmount, applicationCount, consumedItems, discountRecords);

                // 记录促销应用追踪
                TrackPromotionApplication(traceId, appliedPromotion);

                // 使用分配引擎进行精确的折扣分配
                if (discountAmount > 0)
                {
                    var allProductIds = CombinationConditions.SelectMany(c => c.ProductIds).Distinct().ToList();
                    var allocationRequest = new AllocationRequest
                    {
                        TotalDiscount = discountAmount,
                        Items = cart.Items.Where(item => allProductIds.Contains(item.ProductId)).ToList(),
                        Strategy = AllocationStrategy.ProportionalByValue,
                        RoundingStrategy = RoundingStrategy.RoundToNearest
                    };

                    var allocationResult = await AllocationEngine.AllocateDiscountAsync(allocationRequest, cancellationToken).ConfigureAwait(false);
                    
                    // 应用分配结果到购物车
                    ApplyAllocationToCart(cart, allocationResult);
                }

                return appliedPromotion;
            }
            finally
            {
                // 释放库存预留
                if (!string.IsNullOrEmpty(reservationId))
                {
                    await InventoryManager.ReleaseReservationAsync(reservationId, cancellationToken).ConfigureAwait(false);
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "应用组合减现规则时发生异常: {RuleId}", Id);
            
            return CreateAppliedPromotion(0, 0, [], []);
        }
        finally
        {
            // 记录性能指标
            var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
            Logger.LogDebug("组合减现规则执行完成: {RuleId}, 耗时: {ExecutionTime}ms", Id, executionTime);
        }
    }

    /// <summary>
    /// 应用应用次数限制
    /// </summary>
    private int ApplyApplicationLimits(int maxApplications)
    {
        if (!IsRepeatable)
        {
            maxApplications = Math.Min(maxApplications, 1);
        }
        else if (MaxApplications > 0)
        {
            maxApplications = Math.Min(maxApplications, MaxApplications);
        }

        return maxApplications;
    }

    /// <summary>
    /// 验证现金折扣商品是否在购物车中
    /// </summary>
    private async Task<bool> ValidateCashDiscountProductsInCartAsync(
        ShoppingCart cart, 
        List<string> productIds, 
        CancellationToken cancellationToken)
    {
        try
        {
            foreach (var productId in productIds)
            {
                var hasProduct = cart.Items.Any(item => item.ProductId == productId && item.Quantity > 0);
                if (!hasProduct)
                    return false;

                // 验证库存可用性
                var available = await InventoryManager.GetAvailableQuantityAsync(productId, cancellationToken).ConfigureAwait(false);
                if (available <= 0)
                    return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "验证现金折扣商品时发生异常: {RuleId}", Id);
            return false;
        }
    }

    /// <summary>
    /// 预留库存
    /// </summary>
    private async Task<string?> ReserveInventoryAsync(ProcessedCart cart, int applicationCount, CancellationToken cancellationToken)
    {
        try
        {
            var requiredProducts = new List<(string ProductId, int Quantity)>();

            // 计算需要预留的商品数量
            foreach (var condition in CombinationConditions)
            {
                if (!condition.IsByAmount && condition.MinQuantity > 0)
                {
                    var requiredQuantity = condition.MinQuantity * applicationCount;
                    foreach (var productId in condition.ProductIds)
                    {
                        requiredProducts.Add((productId, requiredQuantity));
                    }
                }
            }

            if (!requiredProducts.Any())
                return null;

            var reservationRequest = new ReservationRequest
            {
                ReservationId = Guid.NewGuid().ToString("N"),
                ProductQuantities = requiredProducts.GroupBy(p => p.ProductId)
                    .ToDictionary(g => g.Key, g => g.Sum(p => p.Quantity)),
                Priority = ReservationPriority.High,
                ExpirationTime = DateTime.UtcNow.AddMinutes(5),
                Source = $"Rule_{Id}"
            };

            var result = await InventoryManager.ReserveProductsAsync(reservationRequest, cancellationToken).ConfigureAwait(false);
            return result.IsSuccessful ? reservationRequest.ReservationId : null;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "预留库存时发生异常: {RuleId}", Id);
            return null;
        }
    }

    /// <summary>
    /// 应用组合减现逻辑
    /// </summary>
    private async Task<(decimal totalDiscount, List<ConsumedItem> consumed, List<GiftItem> discountRecords)> ApplyCombinationCashDiscountAsync(
        ProcessedCart cart,
        int applicationCount,
        string? traceId,
        CancellationToken cancellationToken)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var discountRecords = new List<GiftItem>();

        try
        {
            var processedByProduct = new Dictionary<string, int>();

            for (int app = 0; app < applicationCount; app++)
            {
                // 验证组合条件
                if (!await CheckCombinationConditionsAsync(cart, processedByProduct, cancellationToken).ConfigureAwait(false))
                    break;

                // 消耗组合条件商品
                var consumedInThisApplication = await ConsumeCombinationConditionProductsAsync(cart, processedByProduct, cancellationToken).ConfigureAwait(false);
                consumedItems.AddRange(consumedInThisApplication);

                // 计算本次应用的减现金额
                var currentDiscountAmount = DiscountAmount;
                totalDiscountAmount += currentDiscountAmount;

                // 创建减现记录
                var description = $"组合减现：满足组合条件立减{currentDiscountAmount:C}（第{app + 1}次应用）";
                discountRecords.Add(CreateCashDiscountRecord(currentDiscountAmount, description));

                if (!IsRepeatable)
                    break;
            }

            return (totalDiscountAmount, consumedItems, discountRecords);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "应用组合减现逻辑时发生异常: {RuleId}", Id);
            return (0, [], []);
        }
    }

    /// <summary>
    /// 检查组合条件
    /// </summary>
    private async Task<bool> CheckCombinationConditionsAsync(
        ProcessedCart cart,
        Dictionary<string, int> processedByProduct,
        CancellationToken cancellationToken)
    {
        try
        {
            foreach (var condition in CombinationConditions)
            {
                if (condition.IsByAmount)
                {
                    var availableAmount = condition.ProductIds.Sum(productId =>
                    {
                        var cartItems = cart.Items.Where(item => item.ProductId == productId);
                        var processedQuantity = processedByProduct.GetValueOrDefault(productId, 0);
                        return cartItems.Sum(item => Math.Max(0, item.Quantity - processedQuantity) * item.OriginalUnitPrice);
                    });

                    if (availableAmount < condition.MinAmount)
                        return false;
                }
                else
                {
                    var availableQuantity = condition.ProductIds.Sum(productId =>
                    {
                        var cartQuantity = cart.Items.Where(item => item.ProductId == productId).Sum(item => item.Quantity);
                        var processedQuantity = processedByProduct.GetValueOrDefault(productId, 0);
                        return Math.Max(0, cartQuantity - processedQuantity);
                    });

                    if (availableQuantity < condition.MinQuantity)
                        return false;
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "检查组合条件时发生异常: {RuleId}", Id);
            return false;
        }
    }

    /// <summary>
    /// 消耗组合条件商品
    /// </summary>
    private async Task<List<ConsumedItem>> ConsumeCombinationConditionProductsAsync(
        ProcessedCart cart,
        Dictionary<string, int> processedByProduct,
        CancellationToken cancellationToken)
    {
        var consumedItems = new List<ConsumedItem>();

        try
        {
            foreach (var condition in CombinationConditions)
            {
                var consumed = ConsumeSingleConditionProducts(cart, condition, processedByProduct);
                consumedItems.AddRange(consumed);
            }

            return consumedItems;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "消耗组合条件商品时发生异常: {RuleId}", Id);
            return [];
        }
    }

    /// <summary>
    /// 消耗单个条件的商品
    /// </summary>
    private List<ConsumedItem> ConsumeSingleConditionProducts(
        ProcessedCart cart,
        CombinationCashDiscountCondition condition,
        Dictionary<string, int> processedByProduct)
    {
        var consumedItems = new List<ConsumedItem>();

        if (condition.IsByAmount)
        {
            // 按金额消耗，需要达到最小金额要求
            var remainingAmount = condition.MinAmount;

            foreach (var productId in condition.ProductIds)
            {
                if (remainingAmount <= 0) break;

                var cartItems = cart.Items.Where(item => item.ProductId == productId).ToList();
                foreach (var cartItem in cartItems)
                {
                    if (remainingAmount <= 0) break;

                    var processedQuantity = processedByProduct.GetValueOrDefault(productId, 0);
                    var availableQuantity = Math.Max(0, cartItem.Quantity - processedQuantity);

                    if (availableQuantity > 0)
                    {
                        var itemValue = cartItem.OriginalUnitPrice;
                        var consumeQuantity = Math.Min(availableQuantity, (int)Math.Ceiling(remainingAmount / itemValue));

                        consumedItems.Add(new ConsumedItem
                        {
                            ProductId = productId,
                            ProductName = cartItem.ProductName,
                            Quantity = consumeQuantity,
                            UnitPrice = cartItem.OriginalUnitPrice
                        });

                        processedByProduct[productId] = processedByProduct.GetValueOrDefault(productId, 0) + consumeQuantity;
                        remainingAmount -= consumeQuantity * itemValue;
                    }
                }
            }
        }
        else
        {
            // 按数量消耗
            var remainingQuantity = condition.MinQuantity;

            foreach (var productId in condition.ProductIds)
            {
                if (remainingQuantity <= 0) break;

                var cartItems = cart.Items.Where(item => item.ProductId == productId).ToList();
                foreach (var cartItem in cartItems)
                {
                    if (remainingQuantity <= 0) break;

                    var processedQuantity = processedByProduct.GetValueOrDefault(productId, 0);
                    var availableQuantity = Math.Max(0, cartItem.Quantity - processedQuantity);

                    if (availableQuantity > 0)
                    {
                        var consumeQuantity = Math.Min(availableQuantity, remainingQuantity);

                        consumedItems.Add(new ConsumedItem
                        {
                            ProductId = productId,
                            ProductName = cartItem.ProductName,
                            Quantity = consumeQuantity,
                            UnitPrice = cartItem.OriginalUnitPrice
                        });

                        processedByProduct[productId] = processedByProduct.GetValueOrDefault(productId, 0) + consumeQuantity;
                        remainingQuantity -= consumeQuantity;
                    }
                }
            }
        }

        return consumedItems;
    }

    /// <summary>
    /// 创建现金折扣记录
    /// </summary>
    private GiftItem CreateCashDiscountRecord(decimal discountAmount, string description)
    {
        return new GiftItem
        {
            ProductId = "CASH_DISCOUNT",
            ProductName = description,
            Quantity = 1,
            UnitPrice = -discountAmount,
            OriginalUnitPrice = 0
        };
    }

    /// <summary>
    /// 应用分配结果到购物车
    /// </summary>
    private static void ApplyAllocationToCart(ProcessedCart cart, AllocationResult allocationResult)
    {
        foreach (var allocation in allocationResult.Allocations)
        {
            var cartItem = cart.Items.FirstOrDefault(item => item.ProductId == allocation.ProductId);
            if (cartItem != null)
            {
                cartItem.ActualUnitPrice = Math.Max(0, cartItem.ActualUnitPrice - allocation.AllocatedDiscount / cartItem.Quantity);
            }
        }
    }
}

/// <summary>
/// 组合减现规则配置
/// </summary>
public sealed class CombinationCashDiscountConfiguration
{
    public required string Id { get; init; }
    public required string Name { get; init; }
    public string Description { get; init; } = string.Empty;
    public int Priority { get; init; } = 0;
    public bool IsEnabled { get; init; } = true;
    public DateTime? StartTime { get; init; }
    public DateTime? EndTime { get; init; }
    public bool IsRepeatable { get; init; } = true;
    public int MaxApplications { get; init; } = 1;
    public List<string> ApplicableCustomerTypes { get; init; } = [];
    public List<string> ExclusiveRuleIds { get; init; } = [];
    public bool CanStackWithOthers { get; init; } = true;
    public ProductExclusivityLevel ProductExclusivity { get; init; } = ProductExclusivityLevel.None;
    public List<CombinationCashDiscountCondition> CombinationConditions { get; init; } = [];
    public decimal DiscountAmount { get; init; }
}

/// <summary>
/// 组合减现条件定义
/// </summary>
public sealed class CombinationCashDiscountCondition
{
    /// <summary>
    /// 适用的商品ID列表
    /// </summary>
    public List<string> ProductIds { get; init; } = [];

    /// <summary>
    /// 最小数量要求
    /// </summary>
    public int MinQuantity { get; init; }

    /// <summary>
    /// 最小金额要求
    /// </summary>
    public decimal MinAmount { get; init; }

    /// <summary>
    /// 是否按金额计算（如果为true，则使用MinAmount；否则使用MinQuantity）
    /// </summary>
    public bool IsByAmount => MinAmount > 0;
}
