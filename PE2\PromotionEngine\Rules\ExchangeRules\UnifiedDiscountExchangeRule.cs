using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.ExchangeRules;

/// <summary>
/// 统一打折换购规则 [OK]
/// 针对某类商品，满X件或X元，可Y折换购一件C类商品
/// 场景案例：购买A商品大于等于1件时，可以0.9折换购B商品。
/// A、B商品吊牌价、零售价都为1000元；购买A商品和B商品各1件时，应收金额为1000+1000*0.9=1900元（B商品为0.9折换购）
/// 备注：1.如果 购买条件和换购商品条件范围有重复，就需要考虑 客户利益最大化 或 商家利益最大化 的选择。  
///       2.换购条件商品如果作为条件商品，就不能作为换购商品参与计算了（占位）。
/// </summary>
public class UnifiedDiscountExchangeRule : BaseExchangeRule
{
    public override string RuleType => "UnifiedDiscountExchange";

    /// <summary>
    /// 购买条件列表
    /// </summary>
    public List<ExchangeBuyCondition> BuyConditions { get; set; } = new();

    /// <summary>
    /// 打折换购条件列表
    /// </summary>
    public List<DiscountExchangeCondition> ExchangeConditions { get; set; } = new();

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!BuyConditions.Any() || !ExchangeConditions.Any())
            return false;

        // 检查购买条件
        if (!CheckBuyConditions(cart))
            return false;

        // 检查换购商品是否在购物车中（POS系统核心要求）
        var allExchangeProductIds = ExchangeConditions
            .SelectMany(c => c.ExchangeProductIds)
            .Distinct()
            .ToList();

        return ValidateExchangeProductsInCart(cart, allExchangeProductIds);
    }

    /// <summary>
    /// 检查购买条件
    /// </summary>
    private bool CheckBuyConditions(ShoppingCart cart)
    {
        return BuyConditions.All(buyCondition =>
            IsBuyConditionSatisfied(cart, buyCondition));
    }

    /// <summary>
    /// 检查单个购买条件是否满足
    /// </summary>
    private bool IsBuyConditionSatisfied(ShoppingCart cart, ExchangeBuyCondition buyCondition)
    {
        var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
        var totalAmount = buyCondition.ProductIds.Sum(id =>
            cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));

        return (buyCondition.RequiredQuantity <= 0 || availableQuantity >= buyCondition.RequiredQuantity) &&
               (buyCondition.RequiredAmount <= 0 || totalAmount >= buyCondition.RequiredAmount);
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = BuyConditions
            .Select(buyCondition => CalculateMaxApplicationsForBuyCondition(cart, buyCondition))
            .Max();

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    /// <summary>
    /// 计算单个购买条件的最大应用次数
    /// </summary>
    private int CalculateMaxApplicationsForBuyCondition(ShoppingCart cart, ExchangeBuyCondition buyCondition)
    {
        var maxApplications = 0;
        var virtualCart = CreateVirtualCart(cart);

        while (CanApplyOnce(virtualCart, buyCondition))
        {
            // 模拟一次完整的促销应用
            SimulateOneApplication(virtualCart, buyCondition);
            maxApplications++;

            if (!IsRepeatable)
                break;
        }

        return maxApplications;
    }

    /// <summary>
    /// 检查是否可以应用一次促销
    /// </summary>
    private bool CanApplyOnce(ShoppingCart cart, ExchangeBuyCondition buyCondition)
    {
        return CanSatisfyBuyCondition(cart, buyCondition) &&
               HasAvailableExchangeProducts(cart, buyCondition);
    }

    /// <summary>
    /// 模拟一次促销应用
    /// </summary>
    private void SimulateOneApplication(ShoppingCart cart, ExchangeBuyCondition buyCondition)
    {
        // 消费购买条件商品
        var consumedForBuy = ConsumeBuyConditionProducts(cart, buyCondition);

        // 消费换购商品（排除已用于购买条件的商品）
        ConsumeExchangeProducts(cart, buyCondition, consumedForBuy);
    }

    /// <summary>
    /// 创建虚拟购物车副本
    /// </summary>
    private ShoppingCart CreateVirtualCart(ShoppingCart originalCart)
    {
        var virtualCart = new ShoppingCart();
        foreach (var item in originalCart.Items)
        {
            virtualCart.Items.Add(new CartItem
            {
                Product = item.Product,
                Quantity = item.AvailableQuantity,
                UnitPrice = item.UnitPrice,
                ActualUnitPrice = item.ActualUnitPrice
            });
        }
        return virtualCart;
    }

    /// <summary>
    /// 检查是否能满足购买条件
    /// </summary>
    private bool CanSatisfyBuyCondition(ShoppingCart cart, ExchangeBuyCondition buyCondition)
    {
        if (buyCondition.RequiredQuantity > 0)
        {
            var availableQuantity = buyCondition.ProductIds.Sum(id =>
                cart.Items.Where(x => x.Product.Id == id).Sum(x => x.Quantity));
            return availableQuantity >= buyCondition.RequiredQuantity;
        }

        if (buyCondition.RequiredAmount > 0)
        {
            var availableAmount = buyCondition.ProductIds.Sum(id =>
                cart.Items.Where(x => x.Product.Id == id).Sum(x => x.Quantity * x.UnitPrice));
            return availableAmount >= buyCondition.RequiredAmount;
        }

        return true;
    }

    /// <summary>
    /// 检查是否有可用的换购商品
    /// </summary>
    private bool HasAvailableExchangeProducts(ShoppingCart cart, ExchangeBuyCondition buyCondition)
    {
        // 创建临时购物车来模拟消费购买条件后的状态
        var tempCart = CreateVirtualCart(cart);
        var consumedForBuy = ConsumeBuyConditionProducts(tempCart, buyCondition);

        // 检查是否还有换购商品可用
        return GetApplicableExchangeConditions()
            .SelectMany(exchange => exchange.ExchangeProductIds)
            .Any(exchangeProductId =>
            {
                var cartItem = tempCart.Items.FirstOrDefault(x => x.Product.Id == exchangeProductId);
                return cartItem != null && cartItem.Quantity > 0;
            });
    }

    /// <summary>
    /// 消费购买条件商品并返回消费记录
    /// </summary>
    private List<(string ProductId, int Quantity)> ConsumeBuyConditionProducts(ShoppingCart cart, ExchangeBuyCondition buyCondition)
    {
        var consumed = new List<(string ProductId, int Quantity)>();

        if (buyCondition.RequiredQuantity > 0)
        {
            consumed.AddRange(ConsumeByQuantity(cart, buyCondition));
        }
        else if (buyCondition.RequiredAmount > 0)
        {
            consumed.AddRange(ConsumeByAmount(cart, buyCondition));
        }

        return consumed;
    }

    /// <summary>
    /// 按数量消费商品
    /// </summary>
    private List<(string ProductId, int Quantity)> ConsumeByQuantity(ShoppingCart cart, ExchangeBuyCondition buyCondition)
    {
        var consumed = new List<(string ProductId, int Quantity)>();
        var remainingQuantity = buyCondition.RequiredQuantity;

        foreach (var productId in buyCondition.ProductIds)
        {
            if (remainingQuantity <= 0) break;

            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
            if (cartItem?.Quantity > 0)
            {
                var consumeQuantity = Math.Min(cartItem.Quantity, remainingQuantity);
                cartItem.Quantity -= consumeQuantity;
                consumed.Add((productId, consumeQuantity));
                remainingQuantity -= consumeQuantity;
            }
        }

        return consumed;
    }

    /// <summary>
    /// 按金额消费商品
    /// </summary>
    private List<(string ProductId, int Quantity)> ConsumeByAmount(ShoppingCart cart, ExchangeBuyCondition buyCondition)
    {
        var consumed = new List<(string ProductId, int Quantity)>();
        var remainingAmount = buyCondition.RequiredAmount;

        foreach (var productId in buyCondition.ProductIds)
        {
            if (remainingAmount <= 0) break;

            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
            if (cartItem?.Quantity > 0)
            {
                var maxAffordableQuantity = (int)Math.Floor(remainingAmount / cartItem.UnitPrice);
                var consumeQuantity = Math.Min(cartItem.Quantity, maxAffordableQuantity);

                if (consumeQuantity > 0)
                {
                    var consumeAmount = consumeQuantity * cartItem.UnitPrice;
                    cartItem.Quantity -= consumeQuantity;
                    consumed.Add((productId, consumeQuantity));
                    remainingAmount -= consumeAmount;
                }
            }
        }

        return consumed;
    }

    /// <summary>
    /// 消费换购商品
    /// </summary>
    private void ConsumeExchangeProducts(ShoppingCart cart, ExchangeBuyCondition buyCondition,
        List<(string ProductId, int Quantity)> consumedForBuy)
    {
        var applicableExchanges = GetApplicableExchangeConditions();

        foreach (var exchange in applicableExchanges)
        {
            var consumedCount = 0;

            foreach (var exchangeProductId in exchange.ExchangeProductIds)
            {
                if (consumedCount >= exchange.ExchangeQuantity) break;

                var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == exchangeProductId);
                if (cartItem?.Quantity > 0)
                {
                    cartItem.Quantity--;
                    consumedCount++;
                }
            }

            if (consumedCount > 0) break; // 成功消费了换购商品
        }
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyDiscountExchange(cart, applicationCount);
            application.DiscountAmount = result.DiscountAmount;
            application.ConsumedItems = result.ConsumedItems;
            application.GiftItems = result.ExchangeItems;
            application.IsSuccessful = result.DiscountAmount > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用打折换购促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用打折换购促销
    /// </summary>
    private (decimal DiscountAmount, List<ConsumedItem> ConsumedItems, List<GiftItem> ExchangeItems)
        ApplyDiscountExchange(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var exchangeItems = new List<GiftItem>();

        for (int app = 0; app < applicationCount; app++)
        {
            var applied = false;

            foreach (var buyCondition in BuyConditions)
            {
                if (TryApplyOnce(cart, buyCondition, consumedItems, exchangeItems, ref totalDiscountAmount))
                {
                    applied = true;
                    break;
                }
            }

            if (!applied) break;
        }

        return (totalDiscountAmount, consumedItems, exchangeItems);
    }

    /// <summary>
    /// 尝试应用一次促销
    /// </summary>
    private bool TryApplyOnce(ShoppingCart cart, ExchangeBuyCondition buyCondition,
        List<ConsumedItem> consumedItems, List<GiftItem> exchangeItems, ref decimal totalDiscountAmount)
    {
        if (!CanApplyOnce(cart, buyCondition))
            return false;

        // 消费购买条件商品
        var buyConditionConsumed = ConsumeBuyConditionProductsWithTracking(cart, buyCondition, consumedItems);
        if (!buyConditionConsumed.Any())
            return false;

        // 应用换购优惠
        return ApplyExchangeDiscount(cart, buyCondition, buyConditionConsumed, exchangeItems, ref totalDiscountAmount);
    }

    /// <summary>
    /// 消费购买条件商品并记录到consumedItems
    /// </summary>
    private List<(string ProductId, int Quantity)> ConsumeBuyConditionProductsWithTracking(
        ShoppingCart cart, ExchangeBuyCondition buyCondition, List<ConsumedItem> consumedItems)
    {
        var consumed = ConsumeBuyConditionProducts(cart, buyCondition);

        // 记录消费详情
        foreach (var (productId, quantity) in consumed)
        {
            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
            if (cartItem != null)
            {
                var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == productId);
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += quantity;
                }
                else
                {
                    consumedItems.Add(new ConsumedItem
                    {
                        ProductId = productId,
                        ProductName = cartItem.Product.Name,
                        Quantity = quantity,
                        UnitPrice = cartItem.UnitPrice
                    });
                }
            }
        }

        return consumed;
    }

    /// <summary>
    /// 应用换购折扣
    /// </summary>
    private bool ApplyExchangeDiscount(ShoppingCart cart, ExchangeBuyCondition buyCondition,
        List<(string ProductId, int Quantity)> buyConditionConsumed, List<GiftItem> exchangeItems, ref decimal totalDiscountAmount)
    {
        var applicableExchanges = GetApplicableExchangeConditions();
        var applied = false;

        foreach (var exchange in applicableExchanges)
        {
            var availableProducts = GetAvailableExchangeProducts(cart, exchange, buyConditionConsumed);
            if (!availableProducts.Any()) continue;

            var selectedProducts = SelectOptimalExchangeProducts(availableProducts, cart, exchange.ExchangeQuantity);

            foreach (var productId in selectedProducts.Take(exchange.ExchangeQuantity))
            {
                if (ApplyDiscountToProduct(cart, productId, exchange, exchangeItems, ref totalDiscountAmount))
                {
                    applied = true;
                }
            }

            if (applied) break;
        }

        return applied;
    }

    /// <summary>
    /// 获取可用的换购商品（排除已用于购买条件的商品）
    /// </summary>
    private List<string> GetAvailableExchangeProducts(ShoppingCart cart, DiscountExchangeCondition exchange,
        List<(string ProductId, int Quantity)> buyConditionConsumed)
    {
        return exchange.ExchangeProductIds
            .Where(exchangeProductId =>
            {
                var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == exchangeProductId);
                return cartItem?.Quantity > 0;
            })
            .ToList();
    }

    /// <summary>
    /// 为单个商品应用折扣
    /// </summary>
    private bool ApplyDiscountToProduct(ShoppingCart cart, string productId, DiscountExchangeCondition exchange,
        List<GiftItem> exchangeItems, ref decimal totalDiscountAmount)
    {
        var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
        if (cartItem?.Quantity <= 0) return false;

        var originalPrice = cartItem.UnitPrice;
        var discountedPrice = CalculateDiscountExchange(originalPrice, exchange.DiscountRate);
        var discountAmount = originalPrice - discountedPrice;

        if (discountAmount <= 0) return false;

        totalDiscountAmount += discountAmount;

        var strategyDescription = ExchangeSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
            ? "客户利益最大化" : "商家利益最大化";

        exchangeItems.Add(new GiftItem
        {
            ProductId = productId,
            ProductName = cartItem.Product.Name,
            Quantity = 1,
            Value = discountAmount,
            Description = $"打折换购：{exchange.DiscountRate:P1}折换购，节省{discountAmount:C}（{strategyDescription}）"
        });

        // 添加促销详情
        cartItem.AddPromotionDetail(new ItemPromotionDetail
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = "打折换购",
            DiscountAmount = discountAmount,
            Description = $"打折换购：{exchange.DiscountRate:P1}折",
            IsGiftRelated = false
        });

        cartItem.ActualUnitPrice = discountedPrice;
        return true;
    }

    /// <summary>
    /// 获取适用的换购条件
    /// </summary>
    private List<DiscountExchangeCondition> GetApplicableExchangeConditions()
    {
        return ExchangeStrategy switch
        {
            ExchangeStrategy.ByGradient => ExchangeConditions.Take(1).ToList(),
            ExchangeStrategy.AllExchange => ExchangeConditions.ToList(),
            _ => ExchangeConditions.Take(1).ToList()
        };
    }
}

/// <summary>
/// 打折换购条件
/// </summary>
public class DiscountExchangeCondition
{
    /// <summary>
    /// 换购商品ID列表
    /// </summary>
    public List<string> ExchangeProductIds { get; set; } = [];

    /// <summary>
    /// 换购数量
    /// </summary>
    public int ExchangeQuantity { get; set; } = 1;

    /// <summary>
    /// 折扣率（0.9表示9折）
    /// </summary>
    public decimal DiscountRate { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}