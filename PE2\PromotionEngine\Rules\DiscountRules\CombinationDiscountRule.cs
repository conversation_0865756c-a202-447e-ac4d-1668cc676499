using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.DiscountRules;

/// <summary>
/// 组合折扣规则 [OK]
/// 某A类商品满X件或X元并且某B类商品满T件或Y元，打Z折
/// 场景案例：A、B商品吊牌、零售价均为1000元，C商品吊牌、零售价为100元；购买A商品和B商品各大于等于1件，优惠C商品，C商品打9折。
/// 购买1件A、1件B、1件C：1000+1000+100*0.9=2090元
/// 购买2件A、2件B、2件C：1000*2+1000*2+100*2*0.9=4180元
/// 备注：折扣促销不需要考虑翻倍次数，但是需要考虑组合条件商品如果和折扣商品重复时，需要排除条件商品数量产品不参与折扣
/// </summary>
public class CombinationDiscountRule : BaseDiscountRule
{
    public override string RuleType => "CombinationDiscount";

    /// <summary>
    /// 组合购买条件列表
    /// </summary>
    public List<CombinationCondition> CombinationConditions { get; set; } = [];

    /// <summary>
    /// 享受折扣的商品ID列表
    /// </summary>
    public List<string> DiscountProductIds { get; set; } = [];

    /// <summary>
    /// 折扣率（0.9表示9折）
    /// </summary>
    public decimal DiscountRate { get; set; }

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!CombinationConditions.Any() || !DiscountProductIds.Any())
            return false;

        // 验证组合商品是否在购物车中
        var allConditionProductIds = CombinationConditions.SelectMany(c => c.ProductIds).ToList();
        if (!ValidateDiscountProductsInCart(cart, allConditionProductIds))
            return false;

        // 验证折扣商品是否在购物车中
        if (!ValidateDiscountProductsInCart(cart, DiscountProductIds))
            return false;

        // 检查组合购买条件和折扣商品可用性
        return CheckCombinationConditionsAndAvailability(cart);
    }

    /// <summary>
    /// 检查组合购买条件和折扣商品可用性
    /// </summary>
    private bool CheckCombinationConditionsAndAvailability(ShoppingCart cart)
    {
        // 创建虚拟购物车副本来模拟消耗
        var virtualCart = CreateVirtualCart(cart);

        // 检查组合购买条件并虚拟消耗
        var virtualConsumed = SimulateCombinationConsumption(virtualCart);
        if (!virtualConsumed.Any())
            return false;

        // 检查在消耗条件商品后，是否还有足够的折扣商品

        var availableQuantity = DiscountProductIds.Sum(x =>
            virtualCart.GetAvailableProductQuantity(x)
        );
        return availableQuantity > 0;
    }

    /// <summary>
    /// 创建虚拟购物车副本
    /// </summary>
    private ShoppingCart CreateVirtualCart(ShoppingCart originalCart)
    {
        var virtualCart = new ShoppingCart();
        foreach (var item in originalCart.Items)
        {
            virtualCart.Items.Add(
                new CartItem
                {
                    Product = item.Product,
                    Quantity = item.AvailableQuantity, // 使用可用数量
                    UnitPrice = item.UnitPrice
                }
            );
        }
        return virtualCart;
    }

    /// <summary>
    /// 模拟组合条件消耗（不修改原购物车）
    /// </summary>
    private List<ConsumedItem> SimulateCombinationConsumption(ShoppingCart virtualCart)
    {
        var consumedItems = new List<ConsumedItem>();

        foreach (var condition in CombinationConditions)
        {
            if (condition.RequiredQuantity > 0)
            {
                // 按数量消耗
                var requiredQuantity = condition.RequiredQuantity;
                var consumed = ConsumeConditionProducts(
                    virtualCart,
                    condition.ProductIds,
                    requiredQuantity
                );
                if (consumed.Sum(c => c.Quantity) < requiredQuantity)
                    return new List<ConsumedItem>(); // 条件不满足
                consumedItems.AddRange(consumed);
            }
            else if (condition.RequiredAmount > 0)
            {
                // 按金额消耗
                var consumed = ConsumeConditionProductsByAmount(virtualCart, condition);
                var totalAmount = consumed.Sum(c => c.Quantity * c.UnitPrice);
                if (totalAmount < condition.RequiredAmount)
                    return new List<ConsumedItem>(); // 条件不满足
                consumedItems.AddRange(consumed);
            }
        }

        return consumedItems;
    }

    /// <summary>
    /// 按金额消耗条件商品
    /// </summary>
    private List<ConsumedItem> ConsumeConditionProductsByAmount(
        ShoppingCart cart,
        CombinationCondition condition
    )
    {
        var consumedItems = new List<ConsumedItem>();
        var currentAmount = 0m;
        var targetAmount = condition.RequiredAmount;

        var availableItems = cart
            .Items.Where(x =>
                condition.ProductIds.Contains(x.Product.Id) && x.AvailableQuantity > 0
            )
            .OrderBy(x => x.UnitPrice) // 优先消耗价格低的商品
            .ToList();

        foreach (var item in availableItems)
        {
            if (currentAmount >= targetAmount)
                break;

            var remainingAmount = targetAmount - currentAmount;
            var itemTotalValue = item.UnitPrice * item.AvailableQuantity;

            int consumeQuantity;
            if (itemTotalValue <= remainingAmount)
            {
                // 全部消耗
                consumeQuantity = item.AvailableQuantity;
            }
            else
            {
                // 部分消耗，计算需要消耗多少件才能满足剩余金额
                consumeQuantity = Math.Min(
                    (int)Math.Ceiling(remainingAmount / item.UnitPrice),
                    item.AvailableQuantity
                );
            }

            if (consumeQuantity > 0)
            {
                // 记录消耗的商品
                var existingConsumed = consumedItems.FirstOrDefault(x =>
                    x.ProductId == item.Product.Id
                );
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += consumeQuantity;
                }
                else
                {
                    consumedItems.Add(
                        new ConsumedItem
                        {
                            ProductId = item.Product.Id,
                            ProductName = item.Product.Name,
                            Quantity = consumeQuantity,
                            UnitPrice = item.UnitPrice
                        }
                    );
                }

                // 更新当前消耗的金额
                currentAmount += item.UnitPrice * consumeQuantity;

                // 从虚拟购物车中减少数量
                item.Quantity -= consumeQuantity;
            }
        }

        return consumedItems;
    }


    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        // 创建虚拟购物车来计算最大应用次数
        var virtualCart = CreateVirtualCart(cart);
        var maxApplications = 1;

        if (!IsRepeatable)
        {
            return maxApplications;
        }

        // 尝试应用促销，直到无法再应用
        while (maxApplications < MaxApplications)
        {
            // 检查是否还能满足组合条件
            var consumed = SimulateCombinationConsumption(virtualCart);
            if (!consumed.Any())
                break;

            // 检查是否还有足够的折扣商品
            var hasDiscountItems = false;
            foreach (var discountProductId in DiscountProductIds)
            {
                var cartItem = virtualCart.Items.FirstOrDefault(x =>
                    x.Product.Id == discountProductId
                );
                if (cartItem != null)
                {
                    var consumedQuantity = consumed
                        .Where(c => c.ProductId == discountProductId)
                        .Sum(c => c.Quantity);

                    if (cartItem.AvailableQuantity > consumedQuantity)
                    {
                        hasDiscountItems = true;
                        break;
                    }
                }
            }

            if (!hasDiscountItems)
                break;

            // 应用一次促销后，从虚拟购物车中移除消耗的商品
            foreach (var consumedItem in consumed)
            {
                var cartItem = virtualCart.Items.FirstOrDefault(x =>
                    x.Product.Id == consumedItem.ProductId
                );
                if (cartItem != null)
                {
                    cartItem.Quantity -= consumedItem.Quantity;
                }
            }

            // 移除享受折扣的商品
            foreach (var discountProductId in DiscountProductIds)
            {
                var cartItem = virtualCart.Items.FirstOrDefault(x =>
                    x.Product.Id == discountProductId
                );
                if (cartItem != null && cartItem.AvailableQuantity > 0)
                {
                    // 假设所有可用的折扣商品都被消耗
                    cartItem.Quantity = 0;
                }
            }

            maxApplications++;
        }

        return Math.Min(maxApplications, MaxApplications);
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyCombinationDiscount(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用组合折扣促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用组合折扣促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyCombinationDiscount(
        ShoppingCart cart,
        int applicationCount
    )
    {
        var totalDiscountAmount = 0m;
        var allConsumedItems = new List<ConsumedItem>();
        var discountRecords = new List<GiftItem>(); // 折扣记录

        for (int app = 0; app < applicationCount; app++)
        {
            // 消耗组合条件商品（实际消耗购物车商品）
            var combinationConsumed = ConsumeCombinationProducts(cart);
            if (!combinationConsumed.Any())
                break;

            // 获取享受折扣的商品项（排除已被消耗的商品）
            var discountItems = GetAvailableDiscountItems(cart, combinationConsumed);
            if (!discountItems.Any())
            {
                // 如果没有可打折的商品，需要回滚消耗
                RollbackConsumption(cart, combinationConsumed);
                break;
            }

            // 根据策略排序商品
            var sortedItems = SortItemsByStrategy(discountItems);

            var promotion = new AppliedPromotion
            {
                RuleId = Id,
                RuleName = Name,
                PromotionType = RuleType
            };

            // 对折扣商品应用折扣
            var appDiscountAmount = 0m;
            foreach (var discountItem in sortedItems)
            {
                if (discountItem.Quantity <= 0)
                    continue;

                // 找到原始购物车中对应的商品项
                var originalCartItem = cart.Items.FirstOrDefault(x => x.Product.Id == discountItem.Product.Id);
                if (originalCartItem == null)
                    continue;

                var originalPrice = discountItem.UnitPrice;
                var discountedPrice = originalPrice * DiscountRate;
                var discountAmount = (originalPrice - discountedPrice) * discountItem.Quantity;

                if (discountAmount > 0)
                {
                    appDiscountAmount += discountAmount;

                    var strategyDescription =
                        DiscountSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                            ? "客户利益最大化"
                            : "商家利益最大化";

                    var conditionDescription = string.Join(
                        "、",
                        CombinationConditions.Select(c =>
                            c.RequiredQuantity > 0
                                ? $"{string.Join(',', c.ProductIds)}满{c.RequiredQuantity}件"
                                : $"{string.Join(',', c.ProductIds)}满{c.RequiredAmount:C}"
                        )
                    );

                    var description =
                        $"组合折扣：{conditionDescription}享{DiscountRate:P1}折，节省{discountAmount:C}（{strategyDescription}）";

                    // 应用折扣到原始购物车商品项
                    ApplyDiscountToCartItem(originalCartItem, DiscountRate, promotion, description);

                    // 记录折扣
                    discountRecords.Add(
                        CreateDiscountRecord(
                            discountItem.Product.Id,
                            discountItem.Product.Name,
                            discountItem.Quantity,
                            discountAmount,
                            description
                        )
                    );

                    // 从原始购物车中消耗折扣商品数量
                    originalCartItem.Quantity -= discountItem.Quantity;
                }
            }

            totalDiscountAmount += appDiscountAmount;

            // 合并消耗记录
            foreach (var item in combinationConsumed)
            {
                var existingConsumed = allConsumedItems.FirstOrDefault(x =>
                    x.ProductId == item.ProductId
                );
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += item.Quantity;
                }
                else
                {
                    allConsumedItems.Add(item);
                }
            }
        }

        return (totalDiscountAmount, allConsumedItems, discountRecords);
    }

    /// <summary>
    /// 获取可享受折扣的商品项（排除被条件消耗的商品）
    /// </summary>
    private List<CartItem> GetAvailableDiscountItems(
        ShoppingCart cart,
        List<ConsumedItem> consumedItems
    )
    {
        var discountItems = new List<CartItem>();

        foreach (var productId in DiscountProductIds)
        {
            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
            if (cartItem == null || cartItem.AvailableQuantity <= 0)
                continue;

            // 注意：cartItem.AvailableQuantity 已经是在ConsumeCombinationProducts中被消耗后的剩余数量
            // 所以这里直接使用AvailableQuantity即可，不需要再减去消耗量
            var availableForDiscount = cartItem.AvailableQuantity;

            if (availableForDiscount > 0)
            {
                // 创建一个临时的CartItem来表示可打折的数量
                var discountItem = new CartItem
                {
                    Product = cartItem.Product,
                    Quantity = availableForDiscount,
                    UnitPrice = cartItem.UnitPrice
                };

                discountItems.Add(discountItem);
            }
        }

        return discountItems;
    }

    /// <summary>
    /// 回滚消耗（当无法应用折扣时）
    /// </summary>
    private void RollbackConsumption(ShoppingCart cart, List<ConsumedItem> consumedItems)
    {
        foreach (var consumed in consumedItems)
        {
            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == consumed.ProductId);
            if (cartItem != null)
            {
                cartItem.Quantity += consumed.Quantity;
            }
        }
    }

    /// <summary>
    /// 消耗组合条件商品
    /// </summary>
    private List<ConsumedItem> ConsumeCombinationProducts(ShoppingCart cart)
    {
        var consumedItems = new List<ConsumedItem>();

        foreach (var condition in CombinationConditions)
        {
            if (condition.RequiredQuantity > 0)
            {
                // 按数量消耗
                var requiredQuantity = condition.RequiredQuantity;
                var consumed = ConsumeConditionProducts(
                    cart,
                    condition.ProductIds,
                    requiredQuantity
                );
                if (consumed.Sum(c => c.Quantity) < requiredQuantity)
                {
                    // 条件不满足，回滚已消耗的商品
                    RollbackConsumption(cart, consumedItems);
                    return new List<ConsumedItem>();
                }
                consumedItems.AddRange(consumed);
            }
            else if (condition.RequiredAmount > 0)
            {
                // 按金额消耗
                var consumed = ConsumeConditionProductsByAmount(cart, condition);
                var totalAmount = consumed.Sum(c => c.Quantity * c.UnitPrice);
                if (totalAmount < condition.RequiredAmount)
                {
                    // 条件不满足，回滚已消耗的商品
                    RollbackConsumption(cart, consumedItems);
                    return new List<ConsumedItem>();
                }
                consumedItems.AddRange(consumed);
            }
        }

        return consumedItems;
    }

    /// <summary>
    /// 消耗购买条件商品（实际从购物车中减少数量）
    /// 重写基类方法以支持实际消耗
    /// </summary>
    protected new List<ConsumedItem> ConsumeConditionProducts(
        ShoppingCart cart,
        List<string> productIds,
        int requiredQuantity
    )
    {
        var consumedItems = new List<ConsumedItem>();
        var remainingQuantity = requiredQuantity;

        foreach (var productId in productIds)
        {
            if (remainingQuantity <= 0)
                break;

            var cartItems = cart
                .Items.Where(x => x.Product.Id == productId && x.AvailableQuantity > 0)
                .ToList();

            foreach (var cartItem in cartItems)
            {
                if (remainingQuantity <= 0)
                    break;

                var consumeQuantity = Math.Min(cartItem.AvailableQuantity, remainingQuantity);
                if (consumeQuantity <= 0)
                    continue;

                var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == productId);
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += consumeQuantity;
                }
                else
                {
                    consumedItems.Add(
                        new ConsumedItem
                        {
                            ProductId = productId,
                            ProductName = cartItem.Product.Name,
                            Quantity = consumeQuantity,
                            UnitPrice = cartItem.UnitPrice
                        }
                    );
                }

                // 实际从购物车中减少数量
                cartItem.Quantity -= consumeQuantity;
                remainingQuantity -= consumeQuantity;
            }
        }

        return consumedItems;
    }
}

/// <summary>
/// 组合条件
/// </summary>
public class CombinationCondition
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public List<string> ProductIds { get; set; } = [];

    /// <summary>
    /// 所需数量
    /// </summary>
    public int RequiredQuantity { get; set; }

    /// <summary>
    /// 所需金额
    /// </summary>
    public decimal RequiredAmount { get; set; }
}
