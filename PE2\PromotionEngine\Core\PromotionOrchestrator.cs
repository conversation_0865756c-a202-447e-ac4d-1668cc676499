global using Microsoft.Extensions.Logging;
global using Microsoft.Extensions.DependencyInjection;
global using System.Diagnostics;
using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Observability;
using PE2.PromotionEngine.Conditions;
using PE2.PromotionEngine.Inventory;
using PE2.PromotionEngine.Performance;
using PE2.PromotionEngine.Allocation;
using PE2.PromotionEngine.Calculators;

namespace PE2.PromotionEngine.Core;

/// <summary>
/// 促销编排器实现 - 统一协调各个引擎组件
/// 整合ObservabilityEngine、ConditionEngine、InventoryManager、PerformanceOptimizer、AllocationEngine
/// 提供完整的促销计算流程编排和协调功能
/// </summary>
/// <remarks>
/// 建议单元测试：
/// 1. TestCalculatePromotionsAsync - 测试完整促销计算流程
/// 2. TestPreAnalyzePromotionsAsync - 测试促销预分析功能
/// 3. TestValidatePromotionCompatibilityAsync - 测试促销兼容性验证
/// 4. TestRollbackPromotionAsync - 测试促销回滚功能
/// 5. TestGetCalculationStatusAsync - 测试计算状态查询
/// 6. TestPerformanceOptimization - 测试性能优化效果
/// 7. TestConcurrentCalculations - 测试并发计算处理
/// 8. TestErrorHandlingAndRecovery - 测试错误处理和恢复
/// </remarks>
public sealed class PromotionOrchestrator : IPromotionOrchestrator, IDisposable
{
    private readonly ILogger<PromotionOrchestrator> _logger;
    private readonly IObservabilityEngine _observability;
    private readonly IConditionEngine _conditionEngine;
    private readonly IInventoryManager _inventoryManager;
    private readonly IPerformanceOptimizer _performanceOptimizer;
    private readonly IAllocationEngine _allocationEngine;
    private readonly IServiceProvider _serviceProvider;
    
    // 计算状态跟踪
    private readonly ConcurrentDictionary<string, CalculationStatus> _calculationStatuses;
    private readonly ConcurrentDictionary<string, PromotionCalculationResult> _calculationResults;
    
    // 性能统计
    private readonly ConcurrentDictionary<string, OrchestrationMetrics> _performanceMetrics;
    private readonly Timer _metricsCleanupTimer;
    
    // 配置参数
    private readonly TimeSpan _resultRetentionTime = TimeSpan.FromHours(2);
    private readonly int _maxConcurrentCalculations = Environment.ProcessorCount * 4;
    private readonly SemaphoreSlim _calculationSemaphore;
    
    private bool _disposed;

    /// <summary>
    /// 初始化促销编排器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="observability">可观测性引擎</param>
    /// <param name="conditionEngine">条件引擎</param>
    /// <param name="inventoryManager">库存管理器</param>
    /// <param name="performanceOptimizer">性能优化器</param>
    /// <param name="allocationEngine">分配引擎</param>
    /// <param name="serviceProvider">服务提供者</param>
    public PromotionOrchestrator(
        ILogger<PromotionOrchestrator> logger,
        IObservabilityEngine observability,
        IConditionEngine conditionEngine,
        IInventoryManager inventoryManager,
        IPerformanceOptimizer performanceOptimizer,
        IAllocationEngine allocationEngine,
        IServiceProvider serviceProvider)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _observability = observability ?? throw new ArgumentNullException(nameof(observability));
        _conditionEngine = conditionEngine ?? throw new ArgumentNullException(nameof(conditionEngine));
        _inventoryManager = inventoryManager ?? throw new ArgumentNullException(nameof(inventoryManager));
        _performanceOptimizer = performanceOptimizer ?? throw new ArgumentNullException(nameof(performanceOptimizer));
        _allocationEngine = allocationEngine ?? throw new ArgumentNullException(nameof(allocationEngine));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        
        _calculationStatuses = new ConcurrentDictionary<string, CalculationStatus>();
        _calculationResults = new ConcurrentDictionary<string, PromotionCalculationResult>();
        _performanceMetrics = new ConcurrentDictionary<string, OrchestrationMetrics>();
        
        _calculationSemaphore = new SemaphoreSlim(_maxConcurrentCalculations, _maxConcurrentCalculations);
        
        // 启动定时清理任务
        _metricsCleanupTimer = new Timer(CleanupExpiredData, null, TimeSpan.FromMinutes(10), TimeSpan.FromMinutes(10));
        
        _logger.LogInformation("促销编排器已初始化，最大并发计算数: {MaxConcurrent}", _maxConcurrentCalculations);
    }

    /// <summary>
    /// 执行完整的促销计算流程
    /// </summary>
    /// <param name="request">促销计算请求</param>
    /// <returns>促销计算结果</returns>
    public async Task<PromotionCalculationResult> CalculatePromotionsAsync(PromotionCalculationRequest request)
    {
        if (request == null)
        {
            throw new ArgumentNullException(nameof(request));
        }

        var calculationId = request.Id;
        var stopwatch = Stopwatch.StartNew();
        
        // 初始化计算状态
        var status = new CalculationStatus
        {
            CalculationId = calculationId,
            State = CalculationState.Running,
            CurrentPhase = "Started",
            ProgressPercentage = 0
        };
        _calculationStatuses.TryAdd(calculationId, status);

        var traceId = _observability.StartCalculationTrace(request.Cart, "PromotionOrchestrator");
        
        try
        {
            await _calculationSemaphore.WaitAsync().ConfigureAwait(false);
            
            _observability.TrackCalculationStep(traceId, "开始促销编排计算", new {
                CalculationId = calculationId,
                CartId = request.Cart.Id,
                Mode = request.Mode.ToString(),
                RuleCount = request.AvailableRules.Count,
                Target = request.Target.ToString()
            });

            // 阶段1：预分析和性能优化 (0-20%)
            status.State = CalculationState.Running;
            status.CurrentPhase = "PreAnalysis";
            status.ProgressPercentage = 5;
            
            var preAnalysis = await PreAnalyzePromotionsAsync(request.Cart, request.AvailableRules).ConfigureAwait(false);
            
            // 阶段2：规则预筛选 (20-40%)
            status.CurrentPhase = "RuleFiltering";
            status.ProgressPercentage = 20;

            var filteredRules = await _performanceOptimizer.PrefilterRulesAsync(request.AvailableRules, request.Cart).ConfigureAwait(false);

            _observability.TrackRuleFiltering(traceId, request.AvailableRules, filteredRules, "性能优化预筛选");

            // 阶段3：库存预留 (40-50%)
            status.CurrentPhase = "InventoryReservation";
            status.ProgressPercentage = 40;

            var inventorySnapshot = await ReserveInventoryForCalculationAsync(request.Cart, filteredRules, calculationId).ConfigureAwait(false);

            // 阶段4：条件验证 (50-60%)
            status.CurrentPhase = "ConditionValidation";
            status.ProgressPercentage = 50;

            var validatedRules = await ValidateRuleConditionsAsync(filteredRules, request.Cart, traceId).ConfigureAwait(false);

            // 阶段5：促销计算 (60-80%)
            status.CurrentPhase = "PromotionCalculation";
            status.ProgressPercentage = 60;

            var calculationResult = await ExecutePromotionCalculationAsync(request, validatedRules, traceId).ConfigureAwait(false);

            // 阶段6：折扣分配 (80-90%)
            status.CurrentPhase = "DiscountAllocation";
            status.ProgressPercentage = 80;

            await ProcessDiscountAllocationAsync(calculationResult, traceId).ConfigureAwait(false);

            // 阶段7：结果验证和优化 (90-100%)
            status.CurrentPhase = "ResultValidation";
            status.ProgressPercentage = 90;
            
            await ValidateAndOptimizeResultAsync(calculationResult, traceId).ConfigureAwait(false);
            
            // 完成计算
            stopwatch.Stop();
            calculationResult.Statistics.TotalCalculationTimeMs = stopwatch.ElapsedMilliseconds;
            calculationResult.CalculationId = calculationId;
            
            // 更新状态
            status.State = CalculationState.Completed;
            status.CurrentPhase = "Completed";
            status.ProgressPercentage = 100;
            
            // 缓存结果
            _calculationResults.TryAdd(calculationId, calculationResult);
            
            // 记录性能指标
            RecordOrchestrationMetric("FullCalculation", stopwatch.ElapsedMilliseconds, request.AvailableRules.Count);
            
            _observability.TrackCalculationStep(traceId, "促销编排计算完成", new {
                IsSuccessful = calculationResult.IsSuccessful,
                TotalDiscount = calculationResult.OptimalResult?.TotalDiscount ?? 0,
                ElapsedMs = stopwatch.ElapsedMilliseconds,
                AppliedPromotionsCount = calculationResult.OptimalResult?.AppliedPromotions.Count ?? 0
            });

            _logger.LogInformation("促销编排计算完成: {CalculationId}, 耗时{ElapsedMs}ms, 优惠{Discount:C}",
                calculationId, stopwatch.ElapsedMilliseconds, calculationResult.OptimalResult?.TotalDiscount ?? 0);

            return calculationResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "促销编排计算过程发生异常: {CalculationId}", calculationId);

            status.State = CalculationState.Failed;
            status.CurrentPhase = "Failed";
            status.ProgressPercentage = 100;
            
            return new PromotionCalculationResult
            {
                CalculationId = calculationId,
                IsSuccessful = false,
                ProcessedCart = new ProcessedCart(request.Cart),
                Statistics = new CalculationStatistics
                {
                    TotalCalculationTimeMs = stopwatch.ElapsedMilliseconds,
                    ErrorMessage = ex.Message
                }
            };
        }
        finally
        {
            _calculationSemaphore.Release();
            _observability.EndCalculationTrace(traceId, null);
            
            // 释放库存预留
            try
            {
                await _inventoryManager.ReleaseReservationAsync(calculationId).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "释放库存预留时发生异常: {CalculationId}", calculationId);
            }
        }
    }

    /// <summary>
    /// 执行促销预分析
    /// </summary>
    /// <param name="cart">购物车</param>
    /// <param name="rules">促销规则列表</param>
    /// <returns>促销预分析结果</returns>
    public async Task<PromotionPreAnalysis> PreAnalyzePromotionsAsync(ShoppingCart cart, List<PromotionRuleBase> rules)
    {
        var traceId = _observability.StartCalculationTrace(cart, "PreAnalysis");
        
        try
        {
            var analysis = new PromotionPreAnalysis
            {
                CartId = cart.Id,
                AnalysisTime = DateTime.Now
            };

            // 分析购物车特征
            analysis.CartCharacteristics = AnalyzeCartCharacteristics(cart);
            
            // 分析规则适用性
            analysis.RuleApplicability = await AnalyzeRuleApplicabilityAsync(cart, rules).ConfigureAwait(false);
            
            // 预估优化潜力
            analysis.OptimizationPotential = await EstimateOptimizationPotentialAsync(cart, rules).ConfigureAwait(false);
            
            // 识别潜在冲突
            analysis.PotentialConflicts = IdentifyPotentialConflicts(rules);
            
            _observability.TrackCalculationStep(traceId, "促销预分析完成", analysis);
            
            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "促销预分析过程发生异常");
            throw;
        }
        finally
        {
            _observability.EndCalculationTrace(traceId, null);
        }
    }

    /// <summary>
    /// 验证促销兼容性
    /// </summary>
    /// <param name="promotionIds">促销ID列表</param>
    /// <param name="cart">购物车</param>
    /// <returns>兼容性验证结果</returns>
    public async Task<CompatibilityResult> ValidatePromotionCompatibilityAsync(List<string> promotionIds, ShoppingCart cart)
    {
        var traceId = _observability.StartCalculationTrace(cart, "CompatibilityValidation");
        
        try
        {
            var result = new CompatibilityResult
            {
                IsCompatible = true,
                ConflictingPromotions = new List<string>(),
                CompatibilityIssues = new List<string>()
            };

            // 实现兼容性验证逻辑
            // 这里需要根据具体的业务规则来实现
            
            _observability.TrackCalculationStep(traceId, "促销兼容性验证完成", result);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "促销兼容性验证过程发生异常");
            throw;
        }
        finally
        {
            _observability.EndCalculationTrace(traceId, null);
        }
    }

    /// <summary>
    /// 执行促销回滚
    /// </summary>
    /// <param name="calculationId">计算ID</param>
    /// <returns>回滚结果</returns>
    public async Task<RollbackResult> RollbackPromotionAsync(string calculationId)
    {
        var traceId = _observability.StartCalculationTrace(new ShoppingCart(), "PromotionRollback");

        try
        {
            var result = new RollbackResult
            {
                CalculationId = calculationId,
                IsSuccessful = false
            };

            // 检查计算是否存在
            if (!_calculationResults.TryGetValue(calculationId, out var calculationResult))
            {
                result.ErrorMessage = $"未找到计算ID为 {calculationId} 的计算结果";
                return result;
            }

            // 回滚库存预留
            var inventoryRollback = await _inventoryManager.RollbackReservationsAsync(calculationId).ConfigureAwait(false);
            if (!inventoryRollback.IsSuccessful)
            {
                result.ErrorMessage = $"库存回滚失败: {inventoryRollback.ErrorMessage}";
                return result;
            }

            // 清理计算结果
            _calculationResults.TryRemove(calculationId, out _);
            _calculationStatuses.TryRemove(calculationId, out _);

            result.IsSuccessful = true;
            result.RollbackTime = DateTime.Now;

            _observability.TrackCalculationStep(traceId, "促销回滚完成", result);

            _logger.LogInformation("促销回滚完成: {CalculationId}", calculationId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "促销回滚过程发生异常: {CalculationId}", calculationId);
            return new RollbackResult
            {
                CalculationId = calculationId,
                IsSuccessful = false,
                ErrorMessage = ex.Message
            };
        }
        finally
        {
            _observability.EndCalculationTrace(traceId, null);
        }
    }

    /// <summary>
    /// 获取促销计算状态
    /// </summary>
    /// <param name="calculationId">计算ID</param>
    /// <returns>计算状态</returns>
    public async Task<CalculationStatus> GetCalculationStatusAsync(string calculationId)
    {
        await Task.Delay(1).ConfigureAwait(false);

        if (_calculationStatuses.TryGetValue(calculationId, out var status))
        {
            return status;
        }

        return new CalculationStatus
        {
            CalculationId = calculationId,
            State = CalculationState.NotFound,
            CurrentPhase = "NotFound",
            ProgressPercentage = 0
        };
    }
