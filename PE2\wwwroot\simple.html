<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能购物车 - 简化版</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
            color: white;
            padding: 24px 32px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .main-content {
            padding: 32px;
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 32px;
        }
        
        .products-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 24px;
        }
        
        .cart-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 24px;
            position: sticky;
            top: 20px;
            height: fit-content;
        }
          .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 12px;
            margin-top: 20px;
        }
        
        .product-card {
            background: white;
            border-radius: 8px;
            padding: 12px;
            border: 1px solid #e8e8e8;
            transition: all 0.3s ease;
        }
        
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .product-image {
            width: 100%;
            height: 80px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            margin-bottom: 8px;
        }
          .product-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 3px;
            line-height: 1.3;
        }
        
        .product-brand {
            color: #666;
            font-size: 12px;
            margin-bottom: 6px;
        }
        
        .product-price {
            font-size: 16px;
            color: #1890ff;
            font-weight: 600;
            margin-bottom: 8px;
        }
          .btn {
            background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #40a9ff 0%, #9254de 100%);
            transform: translateY(-1px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .cart-item {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border: 1px solid #e8e8e8;
        }
        
        .cart-item-name {
            font-weight: 600;
            margin-bottom: 8px;
        }
          .cart-item-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .quantity-control {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .quantity-btn {
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            width: 28px;
            height: 28px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .quantity-btn:hover:not(:disabled) {
            background: #e6f7ff;
            border-color: #1890ff;
        }
        
        .quantity-btn:disabled {
            background: #f5f5f5;
            border-color: #d9d9d9;
            color: #bfbfbf;
            cursor: not-allowed;
        }
        
        .remove-btn {
            background: #ff4d4f;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }
        
        .remove-btn:hover:not(:disabled) {
            background: #ff7875;
        }
        
        .remove-btn:disabled {
            background: #f5f5f5;
            color: #bfbfbf;
            cursor: not-allowed;
        }
        
        .cart-item-promotions {
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid #f0f0f0;
        }

        .promotion-selector {
            margin-top: 8px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e8e8e8;
        }

        .promotion-selector-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
            font-weight: 600;
            color: #666;
        }

        .promotion-dropdown {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 12px;
            background: white;
            cursor: pointer;
        }

        .promotion-dropdown:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .promotion-status {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: 600;
            margin-left: 4px;
        }

        .promotion-status.met {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .promotion-status.not-met {
            background: #fff2e8;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }

        .promotion-status.nearly-met {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }

        .promotion-status.conflicted {
            background: #fff1f0;
            color: #ff4d4f;
            border: 1px solid #ffa39e;
        }

        .promotion-suggestion {
            background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
            border: 1px solid #91d5ff;
            border-radius: 6px;
            padding: 12px;
            margin: 12px 0;
            font-size: 12px;
        }

        .promotion-suggestion-title {
            font-weight: 600;
            color: #1890ff;
            margin-bottom: 4px;
        }

        .promotion-suggestion-content {
            color: #666;
            line-height: 1.4;
        }

        .promotion-suggestion-action {
            margin-top: 8px;
        }

        .suggestion-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s;
        }

        .suggestion-btn:hover {
            background: #40a9ff;
        }

        .promotion-benefits {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 4px;
            padding: 8px;
            margin-top: 8px;
            font-size: 11px;
        }

        .promotion-benefits-title {
            font-weight: 600;
            color: #52c41a;
            margin-bottom: 4px;
        }

        .promotion-benefits-list {
            color: #666;
            line-height: 1.3;
        }
        
        .promotion-tag {
            display: inline-block;
            background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            margin-right: 4px;
            margin-bottom: 2px;
        }
        
        .promotion-tag.discount {
            background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
        }
        
        .promotion-tag.gift {
            background: linear-gradient(135deg, #faad14 0%, #d48806 100%);
        }
        
        .used-promotions-section {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            border: 1px solid #e8e8e8;
        }
        
        .promotion-detail {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;
        }
        
        .promotion-detail:last-child {
            margin-bottom: 0;
        }
        
        .promotion-title {
            font-weight: 600;
            color: #389e0d;
            margin-bottom: 4px;
            font-size: 14px;
        }
        
        .promotion-description {
            color: #666;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .promotion-savings {
            color: #ff4d4f;
            font-weight: 600;
            font-size: 14px;
            margin-top: 4px;
        }
        
        .price-summary {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
        }
        
        .price-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .total-price {
            font-size: 18px;
            font-weight: 600;
            color: #ff4d4f;
            border-top: 1px solid #e8e8e8;
            padding-top: 8px;
        }
        
        .promotion-card {
            background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
            color: white;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .empty-cart {
            text-align: center;
            padding: 40px 20px;
            color: #999;
        }
        
        .search-section {
            margin-bottom: 20px;
        }
        
        .search-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .product-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🛒 智能购物车</h1>
            <p style="margin: 8px 0 0 0; opacity: 0.9;">简化版 - 促销优惠系统</p>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Products Section -->
            <div class="products-section">
                <h2>🛍️ 商品列表</h2>
                
                <!-- Search -->
                <div class="search-section">
                    <input type="text" class="search-input" placeholder="搜索商品..." id="searchInput">
                </div>
                
                <!-- Loading -->
                <div id="loading" class="loading" style="display: none;">
                    加载中...
                </div>
                
                <!-- Products Grid -->
                <div class="product-grid" id="productsGrid">
                    <!-- 产品将通过 JavaScript 动态加载 -->
                </div>
            </div>

            <!-- Cart Section -->
            <div class="cart-section">
                <h2>🛒 购物车 <span id="cartBadge">0</span></h2>
                
                <!-- Empty Cart -->
                <div id="emptyCart" class="empty-cart">
                    <div style="font-size: 48px; margin-bottom: 16px;">🛒</div>
                    <p>购物车还是空的</p>
                    <p style="font-size: 14px;">快去选购您喜欢的商品吧！</p>
                </div>
                
                <!-- Cart Items -->
                <div id="cartItems" style="display: none;">
                    <!-- 购物车项目将通过 JavaScript 动态生成 -->
                </div>
                
                <!-- Promotion Suggestions -->
                <div id="promotionSuggestions"></div>

                <!-- Promotion Info -->
                <div id="promotionInfo"></div>
                
                <!-- Price Summary -->
                <div id="priceSummary" class="price-summary" style="display: none;">
                    <div class="price-row">
                        <span>商品总价:</span>
                        <span id="totalAmount">¥0.00</span>
                    </div>
                    <div class="price-row" id="discountRow" style="display: none;">
                        <span>优惠金额:</span>
                        <span id="discountAmount" style="color: #52c41a;">-¥0.00</span>
                    </div>
                    <div class="price-row total-price">
                        <span>实付金额:</span>
                        <span id="actualAmount">¥0.00</span>
                    </div>
                </div>
                
                <!-- Used Promotions Section -->
                <div id="usedPromotions" class="used-promotions-section" style="display: none;">
                    <h3 style="margin: 0 0 12px 0; font-size: 16px; color: #389e0d;">
                        🎯 已应用的优惠活动
                    </h3>
                    <div id="promotionsContainer">
                        <!-- 已使用的促销信息将通过 JavaScript 动态生成 -->
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div id="actionButtons" style="display: none; margin-top: 16px;">
                    <button class="btn" onclick="calculatePromotions()" id="calculateBtn">
                        🧮 计算优惠
                    </button>
                    <button class="btn" onclick="resetPromotionMode()" id="resetPromotionBtn" style="display: none; margin-top: 8px; background: #faad14;">
                        🔄 重置促销模式
                    </button>
                    <button class="btn" onclick="clearCart()" style="margin-top: 8px; background: #ff4d4f;">
                        🗑️ 清空购物车
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let products = [];
        let cart = {
            id: generateId(),
            customerId: 'customer001',
            memberId: 'member001', // 添加会员ID支持送券功能
            items: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        let promotionResult = null;
        let availablePromotions = []; // 可用促销信息
        let selectedPromotions = new Map(); // 用户选择的促销 Map<productId, ruleId>
        let isManualPromotionMode = false; // 是否为手动促销模式

        // 工具函数
        function generateId() {
            return Math.random().toString(36).substr(2, 9);
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 1000;
                animation: slideIn 0.3s ease;
                background: ${type === 'success' ? '#52c41a' : type === 'error' ? '#ff4d4f' : '#1890ff'};
            `;
            messageDiv.textContent = message;
            document.body.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }

        // 加载商品数据
        async function loadProducts() {
            const loading = document.getElementById('loading');
            loading.style.display = 'block';
            
            try {
                // 模拟 API 调用或使用实际 API
                const response = await fetch('/api/product');
                if (response.ok) {
                    products = await response.json();
                } else {
                    // 使用备用数据
                    products = [
                        { id: 'A', name: 'iPhone 15 Pro', price: 7999, category: '电子产品', brand: 'Apple', barcode: '001', isActive: true },
                        { id: 'B', name: 'MacBook Air', price: 8999, category: '电子产品', brand: 'Apple', barcode: '002', isActive: true },
                        { id: 'C', name: 'iPad Pro', price: 6799, category: '电子产品', brand: 'Apple', barcode: '003', isActive: true },
                        { id: 'D', name: 'AirPods Pro', price: 1999, category: '电子产品', brand: 'Apple', barcode: '004', isActive: true },
                        { id: 'E', name: '可口可乐', price: 3.5, category: '饮料', brand: 'Coca-Cola', barcode: '005', isActive: true },
                        { id: 'F', name: '百事可乐', price: 3, category: '饮料', brand: 'Pepsi', barcode: '006', isActive: true },
                        { id: 'G', name: '矿泉水', price: 2, category: '饮料', brand: '农夫山泉', barcode: '007', isActive: true },
                        { id: 'H', name: '笔记本', price: 15, category: '文具', brand: '晨光', barcode: '008', isActive: true }
                    ];
                }
                
                renderProducts();
                showMessage('商品加载成功', 'success');
            } catch (error) {
                console.error('加载商品失败:', error);
                showMessage('加载商品失败，使用本地数据', 'error');
                
                // 使用备用数据
                products = [
                    { id: 'A', name: 'iPhone 15 Pro', price: 7999, category: '电子产品', brand: 'Apple', barcode: '001', isActive: true },
                    { id: 'B', name: 'MacBook Air', price: 8999, category: '电子产品', brand: 'Apple', barcode: '002', isActive: true },
                    { id: 'C', name: 'iPad Pro', price: 6799, category: '电子产品', brand: 'Apple', barcode: '003', isActive: true },
                    { id: 'D', name: 'AirPods Pro', price: 1999, category: '电子产品', brand: 'Apple', barcode: '004', isActive: true },
                    { id: 'E', name: '可口可乐', price: 3.5, category: '饮料', brand: 'Coca-Cola', barcode: '005', isActive: true },
                    { id: 'F', name: '百事可乐', price: 3, category: '饮料', brand: 'Pepsi', barcode: '006', isActive: true },
                    { id: 'G', name: '矿泉水', price: 2, category: '饮料', brand: '农夫山泉', barcode: '007', isActive: true },
                    { id: 'H', name: '笔记本', price: 15, category: '文具', brand: '晨光', barcode: '008', isActive: true }
                ];
                
                renderProducts();
            } finally {
                loading.style.display = 'none';
            }
        }

        // 渲染商品
        function renderProducts(filteredProducts = null) {
            const grid = document.getElementById('productsGrid');
            const productsToRender = filteredProducts || products;
            
            grid.innerHTML = productsToRender.map(product => `
                <div class="product-card">
                    <div class="product-image">
                        📦 ${product.category}
                    </div>
                    <div class="product-name">${product.name}</div>
                    <div class="product-brand">${product.brand}</div>
                    <div class="product-price">¥${product.price.toFixed(2)}</div>
                    <button class="btn" onclick="addToCart('${product.id}')">
                        🛒 加入购物车
                    </button>
                </div>
            `).join('');
        }        // 添加到购物车
        async function addToCart(productId) {
            const product = products.find(p => p.id === productId);
            if (!product) return;

            const existingItem = cart.items.find(item => item.product.id === productId);

            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.items.push({
                    product: product,
                    quantity: 1,
                    unitPrice: product.price,
                    actualUnitPrice: product.price,
                    isGift: false,
                    isConsumed: false,
                    appliedPromotionRuleIds: []
                });
            }

            // 如果是手动模式，需要重新应用选择的促销；否则自动计算
            if (isManualPromotionMode && selectedPromotions.size > 0) {
                await applySelectedPromotions();
            } else {
                await calculatePromotionsAndRefresh();
            }
            showMessage(`${product.name} 已加入购物车`, 'success');
        }// 更新购物车显示
        function updateCart() {
            const emptyCart = document.getElementById('emptyCart');
            const cartItems = document.getElementById('cartItems');
            const priceSummary = document.getElementById('priceSummary');
            const actionButtons = document.getElementById('actionButtons');
            const cartBadge = document.getElementById('cartBadge');
            const resetPromotionBtn = document.getElementById('resetPromotionBtn');

            const totalQuantity = cart.items.reduce((sum, item) => sum + item.quantity, 0);
            cartBadge.textContent = totalQuantity;

            // 更新重置促销模式按钮的显示
            if (resetPromotionBtn) {
                resetPromotionBtn.style.display = isManualPromotionMode ? 'block' : 'none';
            }
            
            if (cart.items.length === 0) {
                emptyCart.style.display = 'block';
                cartItems.style.display = 'none';
                priceSummary.style.display = 'none';
                actionButtons.style.display = 'none';
            } else {
                emptyCart.style.display = 'none';
                cartItems.style.display = 'block';
                priceSummary.style.display = 'block';
                actionButtons.style.display = 'block';
                  // 渲染购物车项目
                cartItems.innerHTML = cart.items.map((item, index) => `
                    <div class="cart-item">
                        <div class="cart-item-name">
                            ${item.product.name}
                            ${item.isGift ? '<span style="color: #52c41a; font-size: 12px; margin-left: 8px;">🎁 赠品</span>' : ''}
                            ${item.isConsumed ? '<span style="color: #faad14; font-size: 12px; margin-left: 8px;">⚡ 已参与促销</span>' : ''}
                        </div>
                        <div class="cart-item-controls">
                            <div class="quantity-control">
                                <button class="quantity-btn" onclick="updateQuantity(${index}, -1)" ${item.isGift ? 'disabled' : ''}>-</button>
                                <span>${item.quantity}</span>
                                <button class="quantity-btn" onclick="updateQuantity(${index}, 1)" ${item.isGift ? 'disabled' : ''}>+</button>
                            </div>
                            <button class="remove-btn" onclick="removeFromCart(${index})" ${item.isGift ? 'disabled title="赠品不能删除"' : ''}>
                                ${item.isGift ? '🎁' : '删除'}
                            </button>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>
                                ${item.isGift ? '<span style="color: #52c41a;">免费</span>' : `¥${item.unitPrice.toFixed(2)}`} × ${item.quantity}
                            </span>
                            <span style="font-weight: 600; ${item.actualUnitPrice < item.unitPrice ? 'color: #52c41a;' : ''}">
                                ${item.isGift ? '<span style="color: #52c41a;">¥0.00</span>' : `¥${(item.quantity * item.actualUnitPrice).toFixed(2)}`}
                                ${item.actualUnitPrice < item.unitPrice && !item.isGift ? '<small style="margin-left: 4px;">💰</small>' : ''}
                            </span>
                        </div>

                        <!-- 促销选择器 -->
                        ${!item.isGift ? renderPromotionSelector(item.product.id, index) : ''}

                        <!-- 促销信息 -->
                        <div class="cart-item-promotions">
                            ${(item.appliedPromotionRuleIds && item.appliedPromotionRuleIds.length > 0) ?
                                item.appliedPromotionRuleIds.map(ruleId => `
                                    <span class="promotion-tag ${item.isGift ? 'gift' : 'discount'}" title="已应用的促销: ${getPromotionDescription(ruleId)}">
                                        ${getPromotionDescription(ruleId)}
                                    </span>
                                `).join('') :
                                '<div style="color: #999; font-size: 12px;">暂无促销信息</div>'
                            }
                        </div>
                    </div>
                `).join('');
                
                updatePriceSummary();
            }
        }        // 更新商品数量
        async function updateQuantity(index, change) {
            const item = cart.items[index];

            // 赠品不能手动修改数量
            if (item.isGift) {
                showMessage('赠品数量由促销规则自动确定，不能手动修改', 'error');
                return;
            }

            const originalQuantity = item.quantity;
            item.quantity += change;

            if (item.quantity <= 0) {
                cart.items.splice(index, 1);
            }

            // 如果是手动模式，需要重新应用选择的促销；否则自动计算
            if (isManualPromotionMode && selectedPromotions.size > 0) {
                await applySelectedPromotions();
            } else {
                await calculatePromotionsAndRefresh();
            }
        }        // 从购物车移除
        async function removeFromCart(index) {
            const item = cart.items[index];

            // 赠品不能手动删除
            if (item.isGift) {
                showMessage('赠品由促销规则自动管理，不能手动删除', 'error');
                return;
            }

            const productName = item.product.name;
            const productId = item.product.id;
            cart.items.splice(index, 1);

            // 如果删除的商品有手动选择的促销，也要清除
            if (selectedPromotions.has(productId)) {
                selectedPromotions.delete(productId);
                if (selectedPromotions.size === 0) {
                    isManualPromotionMode = false;
                }
            }

            // 如果是手动模式，需要重新应用选择的促销；否则自动计算
            if (isManualPromotionMode && selectedPromotions.size > 0) {
                await applySelectedPromotions();
            } else {
                await calculatePromotionsAndRefresh();
            }
            showMessage(`${productName} 已移除`, 'success');
        }// 清空购物车
        async function clearCart() {
            cart.items = [];
            promotionResult = null;
            selectedPromotions.clear();
            isManualPromotionMode = false;
            availablePromotions = [];

            // 清空后直接更新UI，无需调用API
            document.getElementById('promotionInfo').innerHTML = '';
            document.getElementById('usedPromotions').style.display = 'none';
            document.getElementById('promotionSuggestions').innerHTML = '';
            updateCart();
            showMessage('购物车已清空', 'success');
        }        // 更新价格摘要
        function updatePriceSummary() {
            // 优先使用 API 返回的总金额，如果没有则计算
            const totalAmount = cart.totalAmount || cart.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
            const actualAmount = cart.actualTotalAmount || cart.items.reduce((sum, item) => sum + (item.quantity * item.actualUnitPrice), 0);
            const discountAmount = totalAmount - actualAmount;
            
            document.getElementById('totalAmount').textContent = `¥${totalAmount.toFixed(2)}`;
            document.getElementById('actualAmount').textContent = `¥${actualAmount.toFixed(2)}`;
            
            const discountRow = document.getElementById('discountRow');
            if (discountAmount > 0) {
                discountRow.style.display = 'flex';
                document.getElementById('discountAmount').textContent = `-¥${discountAmount.toFixed(2)}`;
            } else {
                discountRow.style.display = 'none';
            }
            
            // 显示调试信息（开发模式）
            console.log('价格摘要更新:', {
                totalAmount: totalAmount,
                actualAmount: actualAmount,
                discountAmount: discountAmount,
                source: cart.totalAmount ? 'API' : 'calculated'
            });
        }// 计算促销（手动触发按钮）
        async function calculatePromotions() {
            if (cart.items.length === 0) {
                showMessage('购物车为空，无法计算促销', 'error');
                return;
            }
            
            const calculateBtn = document.getElementById('calculateBtn');
            calculateBtn.disabled = true;
            calculateBtn.textContent = '🧮 计算中...';
            
            try {
                await calculatePromotionsAndRefresh();
                
                if (promotionResult && promotionResult.appliedPromotions && promotionResult.appliedPromotions.length > 0) {
                    showMessage(`找到了 ${promotionResult.appliedPromotions.length} 个优惠活动！`, 'success');
                } else {
                    showMessage('未找到适用的优惠活动', 'info');
                }
            } catch (error) {
                console.error('计算促销失败:', error);
                showMessage('计算促销失败，请检查网络连接', 'error');
            } finally {
                calculateBtn.disabled = false;
                calculateBtn.textContent = '🧮 计算优惠';
            }
        }

        // 重置促销模式（从手动模式切换回自动模式）
        async function resetPromotionMode() {
            if (!isManualPromotionMode) {
                showMessage('当前已是自动促销模式', 'info');
                return;
            }

            // 清除所有手动选择的促销
            selectedPromotions.clear();
            isManualPromotionMode = false;

            // 重新计算促销
            await calculatePromotionsAndRefresh();

            showMessage('已切换到自动促销模式', 'success');
        }

        // 渲染促销选择器
        function renderPromotionSelector(productId, itemIndex) {
            const productPromotions = availablePromotions.find(p => p.productId === productId);
            if (!productPromotions || !productPromotions.availablePromotions.length) {
                return `
                    <div class="promotion-selector">
                        <div class="promotion-selector-header">
                            <span>🎯 可用优惠</span>
                            <span style="color: #999;">暂无可用</span>
                        </div>
                    </div>
                `;
            }

            const selectedRuleId = selectedPromotions.get(productId) || '';
            const hasMultiplePromotions = productPromotions.availablePromotions.length > 1;

            return `
                <div class="promotion-selector">
                    <div class="promotion-selector-header">
                        <span>🎯 可用优惠 (${productPromotions.availablePromotions.length})</span>
                        ${hasMultiplePromotions ? '<span style="color: #1890ff;">可选择</span>' : '<span style="color: #52c41a;">自动应用</span>'}
                    </div>
                    <select class="promotion-dropdown" onchange="onPromotionSelected('${productId}', this.value)">
                        <option value="">请选择优惠活动</option>
                        ${productPromotions.availablePromotions.map(promo => `
                            <option value="${promo.ruleId}" ${selectedRuleId === promo.ruleId ? 'selected' : ''}>
                                ${promo.ruleName} - 预计省¥${promo.estimatedDiscount.toFixed(2)}
                                ${getPromotionStatusBadge(promo.conditionStatus)}
                            </option>
                        `).join('')}
                    </select>
                    ${selectedRuleId ? renderPromotionBenefits(productPromotions.availablePromotions.find(p => p.ruleId === selectedRuleId)) : ''}
                </div>
            `;
        }

        // 渲染促销状态标识
        function getPromotionStatusBadge(status) {
            switch (status) {
                case 0: return '✅'; // Met
                case 1: return '❌'; // NotMet
                case 2: return '⚠️'; // NearlyMet
                case 3: return '🚫'; // Conflicted
                default: return '';
            }
        }

        // 渲染促销收益信息
        function renderPromotionBenefits(promotion) {
            if (!promotion) return '';

            return `
                <div class="promotion-benefits">
                    <div class="promotion-benefits-title">💰 优惠详情</div>
                    <div class="promotion-benefits-list">
                        • ${promotion.description}<br>
                        • 预计节省: ¥${promotion.estimatedDiscount.toFixed(2)}<br>
                        • 最大应用次数: ${promotion.maxApplications}<br>
                        ${promotion.canStack ? '• 可与其他优惠叠加' : '• 不可与其他优惠叠加'}
                    </div>
                </div>
            `;
        }

        // 促销选择事件处理
        async function onPromotionSelected(productId, ruleId) {
            if (ruleId) {
                selectedPromotions.set(productId, ruleId);
                isManualPromotionMode = true; // 进入手动促销模式
                showMessage(`已选择促销活动`, 'success');
            } else {
                selectedPromotions.delete(productId);
                // 如果没有任何手动选择的促销，退出手动模式
                if (selectedPromotions.size === 0) {
                    isManualPromotionMode = false;
                }
                showMessage(`已取消促销选择`, 'info');
            }

            // 应用选择的促销
            await applySelectedPromotions();
        }

        // 应用选择的促销
        async function applySelectedPromotions() {
            if (selectedPromotions.size === 0) {
                // 如果没有选择任何促销，退出手动模式，使用自动计算
                isManualPromotionMode = false;
                await calculatePromotionsAndRefresh();
                return;
            }

            try {
                // 使用原始购物车（重置所有促销效果）
                const originalCart = resetCartToOriginal();

                const selectedPromotionsList = Array.from(selectedPromotions.entries()).map(([productId, ruleId]) => ({
                    ruleId: ruleId,
                    applicationCount: 1,
                    isSelected: true
                }));

                const response = await fetch('/api/promotion/apply-selected', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        cart: originalCart,
                        selectedPromotions: selectedPromotionsList
                    })
                });

                if (response.ok) {
                    promotionResult = await response.json();

                    // 更新购物车
                    if (promotionResult.processedCart && promotionResult.processedCart.items) {
                        cart.items = promotionResult.processedCart.items.map(item => ({
                            product: item.product,
                            quantity: item.quantity,
                            unitPrice: item.unitPrice,
                            actualUnitPrice: item.actualUnitPrice || item.unitPrice,
                            subTotal: item.quantity * item.unitPrice,
                            actualSubTotal: item.quantity * (item.actualUnitPrice || item.unitPrice),
                            isGift: item.isGift || false,
                            isConsumed: item.isConsumed || false,
                            appliedPromotionRuleIds: item.appliedPromotionRuleIds || []
                        }));

                        cart.totalAmount = promotionResult.processedCart.totalAmount || cart.items.reduce((sum, item) => sum + item.subTotal, 0);
                        cart.actualTotalAmount = promotionResult.processedCart.actualTotalAmount || cart.items.reduce((sum, item) => sum + item.actualSubTotal, 0);
                    }

                    updateCart();
                    renderPromotions();
                    renderUsedPromotions();
                    renderPromotionSuggestions();
                } else {
                    console.error('应用选择的促销失败:', response.status);
                    showMessage('应用促销失败', 'error');
                }
            } catch (error) {
                console.error('应用选择的促销时发生错误:', error);
                showMessage('应用促销时发生错误', 'error');
            }
        }

        // 重置购物车到原始状态（移除所有促销效果）
        function resetCartToOriginal() {
            const originalCart = {
                id: cart.id,
                customerId: cart.customerId,
                memberId: cart.memberId,
                items: cart.items.filter(item => !item.isGift).map(item => ({
                    product: item.product,
                    quantity: item.quantity,
                    unitPrice: item.unitPrice,
                    actualUnitPrice: item.unitPrice, // 重置为原价
                    subTotal: item.quantity * item.unitPrice,
                    actualSubTotal: item.quantity * item.unitPrice,
                    isGift: false,
                    isConsumed: false,
                    appliedPromotionRuleIds: []
                })),
                createdAt: cart.createdAt,
                updatedAt: new Date().toISOString()
            };

            originalCart.totalAmount = originalCart.items.reduce((sum, item) => sum + item.subTotal, 0);
            originalCart.actualTotalAmount = originalCart.totalAmount;

            return originalCart;
        }

        // 获取可用促销信息
        async function loadAvailablePromotions() {
            if (cart.items.length === 0) {
                availablePromotions = [];
                return;
            }

            try {
                const response = await fetch('/api/promotion/available-promotions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(cart)
                });

                if (response.ok) {
                    availablePromotions = await response.json();
                    console.log('可用促销信息已加载:', availablePromotions);
                } else {
                    console.error('获取可用促销信息失败:', response.status);
                    availablePromotions = [];
                }
            } catch (error) {
                console.error('获取可用促销信息时发生错误:', error);
                availablePromotions = [];
            }
        }

        // 计算促销并刷新购物车（统一入口）
        async function calculatePromotionsAndRefresh() {
            if (cart.items.length === 0) {
                promotionResult = null;
                availablePromotions = [];
                selectedPromotions.clear();
                isManualPromotionMode = false;
                document.getElementById('promotionInfo').innerHTML = '';
                document.getElementById('usedPromotions').style.display = 'none';
                updateCart();
                return;
            }

            // 如果是手动促销模式，不进行自动计算
            if (isManualPromotionMode) {
                console.log('当前为手动促销模式，跳过自动计算');
                await loadAvailablePromotions();
                updateCart();
                renderPromotionSuggestions();
                return;
            }

            // 先加载可用促销信息
            await loadAvailablePromotions();

            try {
                // 构建购物车数据
                const cartData = {
                    ...cart,
                    items: cart.items.map(item => ({
                        product: item.product,
                        quantity: item.quantity,
                        unitPrice: item.unitPrice,
                        actualUnitPrice: item.actualUnitPrice,
                        subTotal: item.quantity * item.unitPrice,
                        actualSubTotal: item.quantity * item.actualUnitPrice,
                        isGift: item.isGift,
                        isConsumed: item.isConsumed,
                        appliedPromotionRuleIds: item.appliedPromotionRuleIds || []
                    }))
                };

                const response = await fetch('/api/promotion/calculate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(cartData)
                });

                if (response.ok) {
                    promotionResult = await response.json();                    // 关键：使用 processedCart 完全重构购物车
                    if (promotionResult.processedCart && promotionResult.processedCart.items) {
                        // 完全替换购物车项目，而不是合并
                        cart.items = promotionResult.processedCart.items.map(item => ({
                            product: item.product,
                            quantity: item.quantity,
                            unitPrice: item.unitPrice,
                            actualUnitPrice: item.actualUnitPrice,
                            subTotal: item.quantity * item.unitPrice,
                            actualSubTotal: item.quantity * item.actualUnitPrice,
                            isGift: item.isGift || false,
                            isConsumed: item.isConsumed || false,
                            appliedPromotionRuleIds: item.appliedPromotionRuleIds || []
                        }));

                        // 更新购物车总金额（优先使用API返回值）
                        cart.totalAmount = promotionResult.processedCart.totalAmount || 
                                         promotionResult.originalAmount || 
                                         cart.items.reduce((sum, item) => sum + item.subTotal, 0);
                        
                        cart.actualTotalAmount = promotionResult.processedCart.actualTotalAmount || 
                                               promotionResult.finalAmount || 
                                               cart.items.reduce((sum, item) => sum + item.actualSubTotal, 0);
                        
                        // 更新购物车的基本信息
                        cart.updatedAt = new Date().toISOString();
                        
                        console.log('购物车已根据 processedCart 重构:', {
                            itemCount: cart.items.length,
                            totalAmount: cart.totalAmount,
                            actualAmount: cart.actualTotalAmount,
                            appliedPromotions: promotionResult.appliedPromotions?.length || 0
                        });
                    }

                    // 刷新所有UI组件
                    updateCart();
                    renderPromotions();
                    renderUsedPromotions();
                    renderPromotionSuggestions();

                } else {
                    console.error('促销计算API返回错误:', response.status);
                    // 如果API失败，仍然更新基础购物车显示
                    updateCart();
                }
            } catch (error) {
                console.error('计算促销失败:', error);
                // 网络错误时，仍然更新基础购物车显示
                updateCart();
            }
        }

        // 渲染促销信息
        function renderPromotions() {
            const promotionInfo = document.getElementById('promotionInfo');
            
            if (!promotionResult || !promotionResult.appliedPromotions || promotionResult.appliedPromotions.length === 0) {
                promotionInfo.innerHTML = '';
                return;
            }
            
            promotionInfo.innerHTML = promotionResult.appliedPromotions.map(promotion => `
                <div class="promotion-card">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div style="font-weight: 600; margin-bottom: 4px;">
                                ${promotion.description || '优惠活动'}
                            </div>
                            <div style="opacity: 0.9; font-size: 14px;">
                                应用次数: ${promotion.applicationCount || 1}
                            </div>
                        </div>
                        <div style="text-align: right;">
                            <div style="font-size: 16px; font-weight: 600;">
                                -¥${(promotion.discountAmount || 0).toFixed(2)}
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 获取促销描述
        function getPromotionDescription(ruleId) {
            if (!promotionResult || !promotionResult.appliedPromotions) {
                return '促销活动';
            }
            
            const promotion = promotionResult.appliedPromotions.find(p => p.ruleId === ruleId);
            if (promotion) {
                if (promotion.promotionType === 'PercentageDiscount') {
                    return `${promotion.ruleName || '百分比折扣'}`;
                } else if (promotion.promotionType === 'FixedAmountDiscount') {
                    return `${promotion.ruleName || '固定金额优惠'}`;
                } else if (promotion.promotionType === 'BuyXGetY') {
                    return `${promotion.ruleName || '买X送Y'}`;
                } else if (promotion.promotionType === 'BundleOffer') {
                    return `${promotion.ruleName || '套餐优惠'}`;
                } else {
                    return promotion.ruleName || '促销活动';
                }
            }
            return '促销活动';
        }

        // 渲染已使用的促销信息
        function renderUsedPromotions() {
            const usedPromotionsDiv = document.getElementById('usedPromotions');

            if (!promotionResult || !promotionResult.appliedPromotions || promotionResult.appliedPromotions.length === 0) {
                usedPromotionsDiv.style.display = 'none';
                return;
            }

            usedPromotionsDiv.style.display = 'block';
            const promotionsContainer = document.getElementById('promotionsContainer');

            promotionsContainer.innerHTML = promotionResult.appliedPromotions.map(promotion => `
                <div class="promotion-detail">
                    <div class="promotion-title">
                        ${promotion.ruleName || promotion.promotionType || '促销活动'}
                        <span style="background: #52c41a; color: white; padding: 1px 4px; border-radius: 3px; font-size: 10px; margin-left: 8px;">
                            ${promotion.promotionType || 'DISCOUNT'}
                        </span>
                    </div>
                    <div class="promotion-description">
                        ${promotion.description || '暂无详细描述'}
                        ${promotion.applicationCount > 1 ? ` (应用 ${promotion.applicationCount} 次)` : ''}
                    </div>
                    <div class="promotion-savings">
                        节省: ¥${(promotion.discountAmount || 0).toFixed(2)}
                    </div>
                    ${promotion.consumedItems && promotion.consumedItems.length > 0 ? `
                        <div style="margin-top: 6px; font-size: 11px; color: #666;">
                            涉及商品: ${promotion.consumedItems.map(item => `${item.productName} (${item.quantity})`).join(', ')}
                        </div>
                    ` : ''}
                    ${promotion.giftItems && promotion.giftItems.length > 0 ? `
                        <div style="margin-top: 6px; font-size: 11px; color: #faad14; font-weight: 600;">
                            🎁 赠品: ${promotion.giftItems.map(item => `${item.productName} (${item.quantity})`).join(', ')}
                        </div>
                    ` : ''}
                </div>
            `).join('');
        }

        // 渲染促销建议（凑单提示）
        function renderPromotionSuggestions() {
            const suggestionsDiv = document.getElementById('promotionSuggestions');

            if (!availablePromotions || availablePromotions.length === 0) {
                suggestionsDiv.innerHTML = '';
                return;
            }

            const suggestions = generatePromotionSuggestions();
            if (suggestions.length === 0) {
                suggestionsDiv.innerHTML = '';
                return;
            }

            suggestionsDiv.innerHTML = suggestions.map(suggestion => `
                <div class="promotion-suggestion">
                    <div class="promotion-suggestion-title">
                        💡 ${suggestion.title}
                    </div>
                    <div class="promotion-suggestion-content">
                        ${suggestion.content}
                    </div>
                    <div class="promotion-suggestion-action">
                        <button class="suggestion-btn" onclick="${suggestion.action}">
                            ${suggestion.actionText}
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 生成促销建议
        function generatePromotionSuggestions() {
            const suggestions = [];

            // 分析未满足条件的促销
            availablePromotions.forEach(productPromo => {
                productPromo.availablePromotions.forEach(promo => {
                    if (promo.conditionStatus === 1 || promo.conditionStatus === 2) { // NotMet or NearlyMet
                        // 这里可以根据具体的促销类型生成不同的建议
                        if (promo.ruleType.includes('Unified') && promo.conditionGap) {
                            suggestions.push({
                                title: `${promo.ruleName} - 还差一点就能享受优惠！`,
                                content: `${promo.conditionGap}，即可享受 ¥${promo.estimatedDiscount.toFixed(2)} 的优惠`,
                                action: `addMoreProducts('${productPromo.productId}', 1)`,
                                actionText: '立即凑单'
                            });
                        }
                    }
                });
            });

            // 限制建议数量
            return suggestions.slice(0, 3);
        }

        // 凑单操作
        async function addMoreProducts(productId, quantity) {
            const product = products.find(p => p.id === productId);
            if (product) {
                for (let i = 0; i < quantity; i++) {
                    await addToCart(productId);
                }
                showMessage(`已为您添加 ${quantity} 件 ${product.name}`, 'success');
            }
        }

        // 搜索功能
        function setupSearch() {
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('input', (e) => {
                const searchText = e.target.value.toLowerCase().trim();
                
                if (searchText === '') {
                    renderProducts();
                } else {
                    const filtered = products.filter(product =>
                        product.name.toLowerCase().includes(searchText) ||
                        product.brand.toLowerCase().includes(searchText) ||
                        product.category.toLowerCase().includes(searchText)
                    );
                    renderProducts(filtered);
                }
            });
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            loadProducts();
            setupSearch();
            showMessage('欢迎使用智能购物车！', 'success');
        });

        // 添加 CSS 动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
