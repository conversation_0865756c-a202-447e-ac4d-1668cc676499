[{"ContainingType": "PE2.Controllers.DemoController", "Method": "CreateComplexCart", "RelativePath": "api/Demo/complex-cart", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.DemoController", "Method": "FullDemo", "RelativePath": "api/Demo/full-demo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.DemoController", "Method": "CreateSampleCart", "RelativePath": "api/Demo/sample-cart", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.ExclusivityTestController", "Method": "CompareWithWithoutExclusivity", "RelativePath": "api/ExclusivityTest/compare-with-without-exclusivity", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "cart", "Type": "PE2.Models.ShoppingCart", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.ExclusivityTestController", "Method": "GetExclusivityConfiguration", "RelativePath": "api/ExclusivityTest/exclusivity-config", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.ExclusivityTestController", "Method": "TestPromotionExclusivity", "RelativePath": "api/ExclusivityTest/test-exclusivity", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PerformanceController", "Method": "RunBenchmark", "RelativePath": "api/Performance/benchmark", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "iterations", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PerformanceController", "Method": "AnalyzeComplexity", "RelativePath": "api/Performance/complexity-analysis", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PerformanceController", "Method": "AnalyzeMemoryUsage", "RelativePath": "api/Performance/memory-analysis", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PerformanceController", "Method": "RunStressTest", "RelativePath": "api/Performance/stress-test", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "concurrentRequests", "Type": "System.Int32", "IsRequired": false}, {"Name": "duration", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PromotionController", "Method": "ApplySpecificPromotion", "RelativePath": "api/Promotion/apply/{ruleId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ruleId", "Type": "System.String", "IsRequired": true}, {"Name": "cart", "Type": "PE2.Models.ShoppingCart", "IsRequired": true}, {"Name": "applicationCount", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PromotionController", "Method": "CalculateOptimalPromotions", "RelativePath": "api/Promotion/calculate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "cart", "Type": "PE2.Models.ShoppingCart", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PromotionController", "Method": "GetPromotionPreviews", "RelativePath": "api/Promotion/preview", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "cart", "Type": "PE2.Models.ShoppingCart", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PromotionController", "Method": "GetPromotionStatistics", "RelativePath": "api/Promotion/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PromotionController", "Method": "ValidatePromotionCombination", "RelativePath": "api/Promotion/validate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "PE2.Controllers.PromotionValidationRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PromotionAnalysisController", "Method": "ComparePromotionScenarios", "RelativePath": "api/PromotionAnalysis/compare-scenarios", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "PE2.Controllers.ComparisonRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PromotionAnalysisController", "Method": "DetailedPromotionAnalysis", "RelativePath": "api/PromotionAnalysis/detailed-analysis", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "cart", "Type": "PE2.Models.ShoppingCart", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PromotionRuleController", "Method": "GetAllRules", "RelativePath": "api/PromotionRule", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PromotionRuleController", "Method": "AddRule", "RelativePath": "api/PromotionRule", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "rule", "Type": "PE2.PromotionEngine.Rules.PromotionRuleBase", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PromotionRuleController", "Method": "GetRuleById", "RelativePath": "api/PromotionRule/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PromotionRuleController", "Method": "UpdateRule", "RelativePath": "api/PromotionRule/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "rule", "Type": "PE2.PromotionEngine.Rules.PromotionRuleBase", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PromotionRuleController", "Method": "DeleteRule", "RelativePath": "api/PromotionRule/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PromotionRuleController", "Method": "SetRuleEnabled", "RelativePath": "api/PromotionRule/{id}/enabled", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "enabled", "Type": "System.Boolean", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PromotionRuleController", "Method": "GetEnabledRules", "RelativePath": "api/PromotionRule/enabled", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PromotionRuleController", "Method": "ReloadRules", "RelativePath": "api/PromotionRule/reload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PromotionRuleController", "Method": "GetValidRules", "RelativePath": "api/PromotionRule/valid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "checkTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "PE2.Controllers.PromotionRuleController", "Method": "ValidateRulesFile", "RelativePath": "api/PromotionRule/validate-file", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_3", "RelativePath": "health", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "<>f__AnonymousType74`2[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["Health"], "EndpointName": "HealthCheck"}]