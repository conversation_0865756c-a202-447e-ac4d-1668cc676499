### 促销互斥性功能测试

### 1. 获取促销规则的互斥性配置
GET http://localhost:5213/api/exclusivitytest/exclusivity-config

### 2. 测试促销互斥性功能
POST http://localhost:5213/api/exclusivitytest/test-exclusivity

### 3. 基础互斥性测试 - A:2, B:4, C:6
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "EXCLUSIVITY_TEST_001",
  "customerId": "CUSTOMER_001",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 2,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 50.00,
        "category": "服装"
      },
      "quantity": 4,
      "unitPrice": 50.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 30.00,
        "category": "家居"
      },
      "quantity": 6,
      "unitPrice": 30.00
    }
  ]
}

### 4. 比较有无互斥性限制的差异
POST http://localhost:5213/api/exclusivitytest/compare-with-without-exclusivity
Content-Type: application/json

{
  "id": "EXCLUSIVITY_COMPARE_001",
  "customerId": "CUSTOMER_COMPARE",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 2,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 50.00,
        "category": "服装"
      },
      "quantity": 4,
      "unitPrice": 50.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 30.00,
        "category": "家居"
      },
      "quantity": 6,
      "unitPrice": 30.00
    }
  ]
}

### 5. VIP客户测试 - 测试客户类型限制
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "VIP_EXCLUSIVITY_TEST",
  "customerId": "VIP_CUSTOMER_001",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 3,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 50.00,
        "category": "服装"
      },
      "quantity": 2,
      "unitPrice": 50.00
    }
  ]
}

### 6. 高价值订单测试 - 测试总额折扣与其他促销的叠加
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "HIGH_VALUE_EXCLUSIVITY_TEST",
  "customerId": "CUSTOMER_HIGH_VALUE",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 200.00,
        "category": "电子产品"
      },
      "quantity": 3,
      "unitPrice": 200.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 100.00,
        "category": "服装"
      },
      "quantity": 5,
        "unitPrice": 100.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 50.00,
        "category": "家居"
      },
      "quantity": 4,
      "unitPrice": 50.00
    }
  ]
}

### 7. 组合套餐互斥性测试
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "BUNDLE_EXCLUSIVITY_TEST",
  "customerId": "CUSTOMER_BUNDLE",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 50.00,
        "category": "电子产品"
      },
      "quantity": 2,
      "unitPrice": 50.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 30.00,
        "category": "服装"
      },
      "quantity": 3,
      "unitPrice": 30.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 20.00,
        "category": "家居"
      },
      "quantity": 4,
      "unitPrice": 20.00
    }
  ]
}

### 8. 单商品多促销冲突测试
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "SINGLE_PRODUCT_CONFLICT_TEST",
  "customerId": "CUSTOMER_CONFLICT",
  "items": [
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 30.00,
        "category": "服装"
      },
      "quantity": 10,
      "unitPrice": 30.00
    }
  ]
}

### 9. 分类促销互斥性测试
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "CATEGORY_EXCLUSIVITY_TEST",
  "customerId": "CUSTOMER_CATEGORY",
  "items": [
    {
      "product": {
        "id": "B",
        "name": "时尚T恤",
        "price": 80.00,
        "category": "服装"
      },
      "quantity": 4,
      "unitPrice": 80.00
    },
    {
      "product": {
        "id": "D",
        "name": "运动鞋",
        "price": 200.00,
        "category": "服装"
      },
      "quantity": 2,
      "unitPrice": 200.00
    },
    {
      "product": {
        "id": "E",
        "name": "牛仔裤",
        "price": 150.00,
        "category": "服装"
      },
      "quantity": 3,
      "unitPrice": 150.00
    }
  ]
}

### 10. 边界条件测试 - 最小数量商品
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "BOUNDARY_EXCLUSIVITY_TEST",
  "customerId": "CUSTOMER_BOUNDARY",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 1,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 50.00,
        "category": "服装"
      },
      "quantity": 1,
      "unitPrice": 50.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 30.00,
        "category": "家居"
      },
      "quantity": 1,
      "unitPrice": 30.00
    }
  ]
}

### 11. 获取当前有效的促销规则
GET http://localhost:5213/api/promotionrule/valid

### 12. 获取促销统计信息
GET http://localhost:5213/api/promotion/statistics

### 13. 验证促销组合 - 测试互斥规则
POST http://localhost:5213/api/promotion/validate
Content-Type: application/json

{
  "cart": {
    "id": "VALIDATE_EXCLUSIVITY_TEST",
    "customerId": "CUSTOMER_VALIDATE",
    "items": [
      {
        "product": {
          "id": "A",
          "name": "商品A",
          "price": 100.00,
          "category": "电子产品"
        },
        "quantity": 2,
        "unitPrice": 100.00
      },
      {
        "product": {
          "id": "B",
          "name": "商品B",
          "price": 50.00,
          "category": "服装"
        },
        "quantity": 3,
        "unitPrice": 50.00
      }
    ]
  },
  "ruleIds": ["RULE002", "RULE004", "RULE008"]
}
