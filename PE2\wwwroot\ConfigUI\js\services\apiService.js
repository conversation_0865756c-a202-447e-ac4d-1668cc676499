/**
 * API服务模块 - 处理与.NET Core WebAPI的RESTful交互
 * 支持促销规则的CRUD操作、自动ID生成、数据映射等功能
 */

class ApiService {
    constructor() {
        // 检测当前环境，如果是file://协议，使用完整URL
        this.baseUrl = window.location.protocol === 'file:' 
            ? 'http://localhost:5213/api' 
            : '/api';
        this.endpoints = {
            promotionRules: '/PromotionRule',
            products: '/Product',
            // 新增：促销元数据端点
            promotionMetadata: '/promotion/metadata'
        };
    }    /**
     * 通用HTTP请求方法
     */
    async request(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        };

        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        try {
            const response = await fetch(url, finalOptions);
            
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }

            // 处理空响应（如DELETE请求）
            if (response.status === 204) {
                return null;
            }

            const contentType = response.headers.get('content-type');            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            }

            return await response.text();
        } catch (error) {
            // 处理网络错误
            if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
                throw new Error('无法连接到后端服务，请确保后端服务正在运行 (http://localhost:5213)');
            }
            throw error;
        }
    }

    /**
     * 生成唯一的规则ID
     */
    generateRuleId(ruleType = 'RULE') {
        const timestamp = Date.now();
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `${ruleType}_${timestamp}_${randomNum}`;
    }

    /**
     * 生成规则名称
     */
    generateRuleName(ruleType, promotionType) {
        const typeNames = {
            'UnifiedGift': '统一买赠',
            'TieredGift': '阶梯买赠',
            'CombinationGift': '组合买赠',
            'UnifiedSpecialPriceExchange': '统一特价换购',
            'UnifiedDiscountExchange': '统一折扣换购',
            'ProductBuyFree': '商品买免',
            'CombinationBuyFree': '组合买免',
            'UnifiedCashDiscount': '统一减现',
            'GradientCashDiscount': '阶梯减现',
            'UnifiedDiscount': '统一打折',
            'TieredDiscount': '阶梯打折',
            'UnifiedSpecialPrice': '统一特价'
        };
        
        const typeName = typeNames[promotionType] || promotionType;
        const date = new Date().toLocaleDateString('zh-CN');
        return `${typeName}规则_${date}`;
    }

    /**
     * 前端配置映射为后端格式
     */
    mapConfigToBackend(config) {
        const mapped = {
            // 基础字段直接映射
            id: config.id || this.generateRuleId(config.ruleType || 'RULE'),
            name: config.name || this.generateRuleName(config.ruleType, config.promotionType),
            description: config.description || '',
            ruleType: config.promotionType, // 前端的promotionType对应后端的ruleType
            priority: parseInt(config.priority) || 0,
            isEnabled: config.isEnabled !== false,
            startTime: config.startTime ? new Date(config.startTime).toISOString() : null,
            endTime: config.endTime ? new Date(config.endTime).toISOString() : null,
            isRepeatable: config.isRepeatable !== false,
            maxApplications: parseInt(config.maxApplications) || 0,
            canStackWithOthers: config.canStackWithOthers !== false,
            productExclusivity: config.productExclusivity || 'None'
        };

        // 业务字段映射 - 根据不同规则类型处理
        switch (config.promotionType) {
            case 'UnifiedGift':
                mapped.buyConditions = this.mapArrayField(config.buyConditions);
                mapped.giftConditions = this.mapArrayField(config.giftConditions);
                break;
            case 'UnifiedSpecialPriceExchange':
                mapped.buyConditions = this.mapArrayField(config.buyConditions);
                mapped.exchangeConditions = this.mapArrayField(config.exchangeConditions);
                break;
            case 'ProductBuyFree':
                mapped.buyConditions = this.mapArrayField(config.buyConditions);
                mapped.freeConditions = this.mapArrayField(config.freeConditions);
                break;
            case 'UnifiedCashDiscount':
                mapped.conditions = this.mapArrayField(config.conditions);
                mapped.discountAmount = parseFloat(config.discountAmount) || 0;
                break;
            case 'UnifiedDiscount':
                mapped.conditions = this.mapArrayField(config.conditions);
                mapped.discountRate = parseFloat(config.discountRate) || 0;
                break;
            case 'UnifiedSpecialPrice':
                mapped.conditions = this.mapArrayField(config.conditions);
                mapped.specialPrice = parseFloat(config.specialPrice) || 0;
                break;
            default:
                // 通用字段映射
                Object.keys(config).forEach(key => {
                    if (!mapped.hasOwnProperty(key) && key !== 'promotionType' && key !== 'ruleType') {
                        mapped[key] = config[key];
                    }
                });
        }

        return mapped;
    }

    /**
     * 后端格式映射为前端配置
     */
    mapBackendToConfig(backendRule) {
        const config = {
            // 基础字段映射
            id: backendRule.id,
            name: backendRule.name,
            description: backendRule.description,
            promotionType: backendRule.ruleType,
            priority: backendRule.priority,
            isEnabled: backendRule.isEnabled,
            startTime: backendRule.startTime ? new Date(backendRule.startTime).toISOString().slice(0, 16) : '',
            endTime: backendRule.endTime ? new Date(backendRule.endTime).toISOString().slice(0, 16) : '',
            isRepeatable: backendRule.isRepeatable,
            maxApplications: backendRule.maxApplications,
            canStackWithOthers: backendRule.canStackWithOthers,
            productExclusivity: backendRule.productExclusivity
        };

        // 业务字段映射
        Object.keys(backendRule).forEach(key => {
            if (!config.hasOwnProperty(key) && key !== 'ruleType') {
                config[key] = backendRule[key];
            }
        });

        return config;
    }

    /**
     * 数组字段映射处理
     */
    mapArrayField(field) {
        if (!Array.isArray(field)) return [];
        return field.filter(item => item && typeof item === 'object');
    }

    // ===== 促销规则API =====

    /**
     * 获取所有促销规则
     */
    async getAllPromotionRules() {
        const url = `${this.baseUrl}${this.endpoints.promotionRules}`;
        const rules = await this.request(url);
        return rules.map(rule => this.mapBackendToConfig(rule));
    }

    /**
     * 根据ID获取促销规则
     */
    async getPromotionRuleById(id) {
        const url = `${this.baseUrl}${this.endpoints.promotionRules}/${id}`;
        const rule = await this.request(url);
        return this.mapBackendToConfig(rule);
    }

    /**
     * 获取启用的促销规则
     */
    async getEnabledPromotionRules() {
        const url = `${this.baseUrl}${this.endpoints.promotionRules}/enabled`;
        const rules = await this.request(url);
        return rules.map(rule => this.mapBackendToConfig(rule));
    }

    /**
     * 获取有效的促销规则
     */
    async getValidPromotionRules(checkTime = null) {
        let url = `${this.baseUrl}${this.endpoints.promotionRules}/valid`;
        if (checkTime) {
            url += `?checkTime=${encodeURIComponent(checkTime)}`;
        }
        const rules = await this.request(url);
        return rules.map(rule => this.mapBackendToConfig(rule));
    }

    /**
     * 添加促销规则
     */
    async addPromotionRule(config) {
        const url = `${this.baseUrl}${this.endpoints.promotionRules}`;
        const backendRule = this.mapConfigToBackend(config);
        
        const options = {
            method: 'POST',
            body: JSON.stringify(backendRule)
        };
        
        const result = await this.request(url, options);
        return this.mapBackendToConfig(result);
    }

    /**
     * 更新促销规则
     */
    async updatePromotionRule(id, config) {
        const url = `${this.baseUrl}${this.endpoints.promotionRules}/${id}`;
        const backendRule = this.mapConfigToBackend(config);
        
        const options = {
            method: 'PUT',
            body: JSON.stringify(backendRule)
        };
        
        const result = await this.request(url, options);
        return this.mapBackendToConfig(result);
    }

    /**
     * 删除促销规则
     */
    async deletePromotionRule(id) {
        const url = `${this.baseUrl}${this.endpoints.promotionRules}/${id}`;
        const options = {
            method: 'DELETE'
        };
        
        await this.request(url, options);
        return true;
    }

    /**
     * 启用/禁用促销规则
     */
    async setPromotionRuleEnabled(id, enabled) {
        const url = `${this.baseUrl}${this.endpoints.promotionRules}/${id}/enabled`;
        const options = {
            method: 'PATCH',
            body: JSON.stringify(enabled)
        };
        
        return await this.request(url, options);
    }

    /**
     * 重新加载促销规则
     */
    async reloadPromotionRules() {
        const url = `${this.baseUrl}${this.endpoints.promotionRules}/reload`;
        const options = {
            method: 'POST'
        };
        
        return await this.request(url, options);
    }

    /**
     * 验证规则文件
     */
    async validateRulesFile() {
        const url = `${this.baseUrl}${this.endpoints.promotionRules}/validate-file`;
        return await this.request(url);
    }

    // ===== 商品API =====

    /**
     * 获取所有商品
     */
    async getAllProducts() {
        const url = `${this.baseUrl}${this.endpoints.products}`;
        return await this.request(url);
    }

    /**
     * 根据ID获取商品
     */
    async getProductById(id) {
        const url = `${this.baseUrl}${this.endpoints.products}/${id}`;
        return await this.request(url);
    }

    /**
     * 搜索商品
     */
    async searchProducts(keyword) {
        const url = `${this.baseUrl}${this.endpoints.products}/search?keyword=${encodeURIComponent(keyword)}`;
        return await this.request(url);
    }

    // ===== 实用方法 =====

    /**
     * 批量操作促销规则
     */
    async batchOperatePromotionRules(operations) {
        const results = [];
        
        for (const operation of operations) {
            try {
                let result;
                switch (operation.type) {
                    case 'add':
                        result = await this.addPromotionRule(operation.config);
                        break;
                    case 'update':
                        result = await this.updatePromotionRule(operation.id, operation.config);
                        break;
                    case 'delete':
                        result = await this.deletePromotionRule(operation.id);
                        break;
                    case 'enable':
                        result = await this.setPromotionRuleEnabled(operation.id, true);
                        break;
                    case 'disable':
                        result = await this.setPromotionRuleEnabled(operation.id, false);
                        break;
                }
                results.push({ success: true, operation, result });
            } catch (error) {
                results.push({ success: false, operation, error: error.message });
            }
        }
        
        return results;
    }

    /**
     * 导出促销规则配置
     */
    async exportPromotionRules(format = 'json') {
        const rules = await this.getAllPromotionRules();
        
        switch (format) {
            case 'json':
                return JSON.stringify(rules, null, 2);
            case 'csv':
                return this.convertToCSV(rules);
            default:
                return rules;
        }
    }

    /**
     * 导入促销规则配置
     */
    async importPromotionRules(data, format = 'json') {
        let rules;
        
        switch (format) {
            case 'json':
                rules = typeof data === 'string' ? JSON.parse(data) : data;
                break;
            case 'csv':
                rules = this.parseCSV(data);
                break;
            default:
                rules = data;
        }

        const operations = rules.map(rule => ({
            type: 'add',
            config: rule
        }));

        return await this.batchOperatePromotionRules(operations);
    }

    /**
     * 转换为CSV格式
     */
    convertToCSV(data) {
        if (!data.length) return '';
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => {
                const value = row[header];
                return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value;
            }).join(','))
        ].join('\n');
        
        return csvContent;
    }    /**
     * 解析CSV格式
     */
    parseCSV(csvText) {
        const lines = csvText.split('\n');
        const headers = lines[0].split(',');
        
        return lines.slice(1).map(line => {
            const values = line.split(',');
            const obj = {};
            headers.forEach((header, index) => {
                obj[header] = values[index];
            });
            return obj;
        });
    }

    // ========================================
    // 促销元数据相关API
    // ========================================

    /**
     * 获取所有促销类型的元数据
     * @returns {Promise<Object>} 促销类型元数据
     */
    async getPromotionMetadata() {
        const url = `${this.baseUrl}${this.endpoints.promotionMetadata}/types`;
        console.log(`正在获取促销元数据: ${url}`);
        
        try {
            const data = await this.request(url);
            console.log('促销元数据获取成功:', data);
            return data;
        } catch (error) {
            console.error('获取促销元数据失败:', error);
            throw new Error(`获取促销类型元数据失败: ${error.message}`);
        }
    }

    /**
     * 获取指定促销类型的详细信息
     * @param {string} ruleType 规则类型
     * @returns {Promise<Object>} 促销类型详细信息
     */
    async getPromotionTypeDetail(ruleType) {
        const url = `${this.baseUrl}${this.endpoints.promotionMetadata}/types/${encodeURIComponent(ruleType)}`;
        console.log(`正在获取促销类型详细信息: ${url}`);
        
        try {
            const data = await this.request(url);
            console.log(`促销类型 ${ruleType} 详细信息获取成功:`, data);
            return data;
        } catch (error) {
            console.error(`获取促销类型 ${ruleType} 详细信息失败:`, error);
            throw new Error(`获取促销类型详细信息失败: ${error.message}`);
        }
    }

    /**
     * 测试促销元数据API连接
     * @returns {Promise<boolean>} 连接状态
     */
    async testPromotionMetadataConnection() {
        try {
            const data = await this.getPromotionMetadata();
            return data !== null && typeof data === 'object';
        } catch (error) {
            console.error('促销元数据API连接测试失败:', error);
            return false;
        }
    }
}

// 创建全局API服务实例
window.apiService = new ApiService();

// ES模块导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ApiService;
}
