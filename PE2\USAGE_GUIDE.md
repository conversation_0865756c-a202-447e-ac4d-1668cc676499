# POSPE2 促销引擎使用指南

## 🚀 快速开始

### 1. 启动应用
```bash
cd PE2
dotnet run
```

应用启动后，访问 http://localhost:5213 查看 Swagger UI 界面。

### 2. 基础API测试

#### 健康检查
```bash
curl http://localhost:5213/health
```

#### 获取示例购物车
```bash
curl http://localhost:5213/api/demo/sample-cart
```

#### 完整功能演示
```bash
curl -X POST http://localhost:5213/api/demo/full-demo
```

## 📋 核心功能使用

### 1. 促销计算

#### 计算最优促销组合
```bash
curl -X POST http://localhost:5213/api/promotion/calculate \
  -H "Content-Type: application/json" \
  -d '{
    "id": "CART_001",
    "customerId": "CUSTOMER_001",
    "items": [
      {
        "product": {
          "id": "A",
          "name": "商品A",
          "price": 50.00,
          "category": "电子产品"
        },
        "quantity": 1,
        "unitPrice": 50.00
      },
      {
        "product": {
          "id": "B",
          "name": "商品B", 
          "price": 30.00,
          "category": "服装"
        },
        "quantity": 2,
        "unitPrice": 30.00
      },
      {
        "product": {
          "id": "C",
          "name": "商品C",
          "price": 20.00,
          "category": "家居"
        },
        "quantity": 5,
        "unitPrice": 20.00
      }
    ]
  }'
```

#### 获取促销预览
```bash
curl -X POST http://localhost:5213/api/promotion/preview \
  -H "Content-Type: application/json" \
  -d '{ /* 同上购物车数据 */ }'
```

### 2. 促销规则管理

#### 获取所有促销规则
```bash
curl http://localhost:5213/api/promotionrule
```

#### 获取有效的促销规则
```bash
curl http://localhost:5213/api/promotionrule/valid
```

#### 添加新的促销规则
```bash
curl -X POST http://localhost:5213/api/promotionrule \
  -H "Content-Type: application/json" \
  -d '{
    "$type": "PercentageDiscount",
    "id": "NEW_RULE_001",
    "name": "新促销规则",
    "description": "测试促销规则",
    "priority": 10,
    "isEnabled": true,
    "startTime": "2024-01-01T00:00:00",
    "endTime": "2024-12-31T23:59:59",
    "applicableProductIds": ["A"],
    "discountPercentage": 0.9,
    "minQuantity": 1
  }'
```

### 3. 性能测试

#### 运行基准测试
```bash
curl -X POST http://localhost:5213/api/performance/benchmark?iterations=50
```

#### 算法复杂度分析
```bash
curl -X POST http://localhost:5213/api/performance/complexity-analysis
```

## 🔧 配置管理

### 促销规则配置文件
规则配置文件位于 `Configuration/promotion-rules.json`，支持以下促销类型：

#### 1. 百分比折扣
```json
{
  "$type": "PercentageDiscount",
  "id": "RULE001",
  "name": "商品B满2件8折",
  "applicableProductIds": ["B"],
  "discountPercentage": 0.8,
  "minQuantity": 2
}
```

#### 2. 买赠促销
```json
{
  "$type": "BuyXGetY",
  "id": "RULE002", 
  "name": "买1件A+1件B半价",
  "buyConditions": [
    {"productIds": ["A"], "requiredQuantity": 1},
    {"productIds": ["B"], "requiredQuantity": 1}
  ],
  "giftConditions": [
    {"productIds": ["B"], "giftQuantity": 1}
  ],
  "giftSameProduct": true
}
```

#### 3. 组合套餐
```json
{
  "$type": "BundleOffer",
  "id": "RULE004",
  "name": "A+B+C组合套餐",
  "bundleItems": [
    {"productId": "A", "requiredQuantity": 1},
    {"productId": "B", "requiredQuantity": 1},
    {"productId": "C", "requiredQuantity": 1}
  ],
  "bundlePrice": 99.00
}
```

### 热重载规则
```bash
curl -X POST http://localhost:5213/api/promotionrule/reload
```

## 📊 结果分析

### 促销计算结果结构
```json
{
  "originalAmount": 210.00,
  "totalDiscount": 30.00,
  "finalAmount": 180.00,
  "discountRate": 0.14,
  "calculationTimeMs": 15,
  "isOptimal": true,
  "appliedPromotions": [
    {
      "ruleId": "RULE001",
      "ruleName": "商品B满2件8折",
      "discountAmount": 12.00,
      "applicationCount": 1,
      "consumedItems": [
        {
          "productId": "B",
          "productName": "商品B",
          "quantity": 2,
          "unitPrice": 30.00
        }
      ]
    }
  ],
  "ignoredPromotions": [
    {
      "ruleId": "RULE002",
      "ruleName": "买1件A+1件B半价",
      "reason": "非最优选择",
      "reasonType": "NotOptimal"
    }
  ],
  "calculationSteps": [
    {
      "stepNumber": 1,
      "type": "Start",
      "description": "开始计算最优促销组合"
    }
  ]
}
```

## 🧪 测试场景

### 经典测试场景
**购物车**: A:1件(50元), B:2件(30元/件), C:5件(20元/件)
**总价**: 210元

**预期结果**: 系统会分析所有可能的促销组合，找出最优解。

### 复杂测试场景
使用 `/api/demo/complex-cart` 获取包含多种商品和分类的复杂购物车进行测试。

## 🔍 故障排除

### 常见问题

#### 1. 应用启动失败
- 检查端口5213是否被占用
- 确认.NET 9.0 SDK已安装
- 检查项目文件完整性

#### 2. 促销规则不生效
- 检查规则文件格式是否正确
- 确认规则在有效期内
- 验证规则条件是否满足

#### 3. 计算结果异常
- 检查购物车数据格式
- 确认商品ID与规则配置匹配
- 查看计算步骤分析原因

### 日志查看
应用运行时会输出详细日志，包括：
- 促销计算开始/结束
- 规则加载状态
- 错误信息和异常

## 📈 性能优化建议

### 1. 规则配置优化
- 合理设置规则优先级
- 避免过多重复规则
- 定期清理过期规则

### 2. 购物车优化
- 控制购物车商品数量
- 避免过深的商品分类层级
- 合理设置促销规则数量

### 3. 系统配置优化
- 调整最大搜索深度
- 配置适当的缓存策略
- 监控内存使用情况

## 🔗 相关资源

- **API文档**: http://localhost:5213/swagger
- **项目源码**: 当前目录
- **测试脚本**: `test-api.ps1`
- **配置文件**: `Configuration/promotion-rules.json`
- **项目总结**: `PROJECT_SUMMARY.md`

## 💡 最佳实践

1. **规则设计**: 保持规则简单明确，避免过于复杂的条件
2. **性能考虑**: 对于大型购物车，考虑分批处理或异步计算
3. **测试验证**: 每次修改规则后进行充分测试
4. **监控告警**: 设置性能监控和异常告警
5. **文档维护**: 及时更新规则文档和使用说明

通过以上指南，您可以充分利用POSPE2促销引擎的强大功能，为您的POS系统提供智能的促销计算服务。
