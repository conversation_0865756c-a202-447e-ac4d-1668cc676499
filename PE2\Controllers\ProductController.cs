using Microsoft.AspNetCore.Mvc;
using PE2.Models;

namespace PE2.Controllers;

/// <summary>
/// 产品数据控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ProductController : ControllerBase
{
    private readonly ILogger<ProductController> _logger;

    public ProductController(ILogger<ProductController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 获取示例商品列表
    /// </summary>
    [HttpGet]
    public IActionResult GetProducts()
    {
        try
        {
            List<Product> products = GenerateProducts();

            return Ok(products);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取商品列表时发生错误");
            return StatusCode(500, "获取商品列表时发生内部错误");
        }
    }

    public static List<Product> GenerateProducts()
    {
        return new List<Product>
            {
                new() { Id = "A", Name = "商品A", Price = 50.00m, Category = "电子产品" } ,
                new() { Id = "B", Name = "商品B", Price = 30.00m, Category = "服装" } ,
                new() { Id = "C", Name = "商品C", Price = 20.00m, Category = "家居" } ,
                new() { Id = "E", Name = "商品E", Price = 200.00m, Category = "鞋子" }
            };
    }

    /// <summary>
    /// 根据ID获取商品
    /// </summary>
    [HttpGet("{id}")]
    public IActionResult GetProduct(string id)
    {
        try
        {
            var products = new List<Product>
            {
                new() { Id = "A", Name = "iPhone 15 Pro", Price = 7999, Category = "电子产品", Brand = "Apple", Barcode = "001", IsActive = true },
                new() { Id = "B", Name = "MacBook Air", Price = 8999, Category = "电子产品", Brand = "Apple", Barcode = "002", IsActive = true },
                new() { Id = "C", Name = "iPad Pro", Price = 6799, Category = "电子产品", Brand = "Apple", Barcode = "003", IsActive = true },
                new() { Id = "D", Name = "AirPods Pro", Price = 1999, Category = "电子产品", Brand = "Apple", Barcode = "004", IsActive = true },
                new() { Id = "E", Name = "可口可乐", Price = 3.5m, Category = "饮料", Brand = "Coca-Cola", Barcode = "005", IsActive = true },
                new() { Id = "F", Name = "百事可乐", Price = 3, Category = "饮料", Brand = "Pepsi", Barcode = "006", IsActive = true },
                new() { Id = "G", Name = "矿泉水", Price = 2, Category = "饮料", Brand = "农夫山泉", Barcode = "007", IsActive = true },
                new() { Id = "H", Name = "笔记本", Price = 15, Category = "文具", Brand = "晨光", Barcode = "008", IsActive = true },
                new() { Id = "I", Name = "原味薯片", Price = 8, Category = "零食", Brand = "乐事", Barcode = "009", IsActive = true },
                new() { Id = "J", Name = "巧克力", Price = 12, Category = "零食", Brand = "德芙", Barcode = "010", IsActive = true },
                new() { Id = "K", Name = "牛奶", Price = 25, Category = "饮料", Brand = "伊利", Barcode = "011", IsActive = true },
                new() { Id = "L", Name = "面包", Price = 6, Category = "食品", Brand = "桃李", Barcode = "012", IsActive = true }
            };

            var product = products.FirstOrDefault(p => p.Id == id);
            if (product == null)
            {
                return NotFound($"未找到ID为 {id} 的商品");
            }

            return Ok(product);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取商品 {ProductId} 时发生错误", id);
            return StatusCode(500, "获取商品时发生内部错误");
        }
    }

    /// <summary>
    /// 根据分类获取商品
    /// </summary>
    [HttpGet("category/{category}")]
    public IActionResult GetProductsByCategory(string category)
    {
        try
        {
            var allProducts = new List<Product>
            {
                new() { Id = "A", Name = "iPhone 15 Pro", Price = 7999, Category = "电子产品", Brand = "Apple", Barcode = "001", IsActive = true },
                new() { Id = "B", Name = "MacBook Air", Price = 8999, Category = "电子产品", Brand = "Apple", Barcode = "002", IsActive = true },
                new() { Id = "C", Name = "iPad Pro", Price = 6799, Category = "电子产品", Brand = "Apple", Barcode = "003", IsActive = true },
                new() { Id = "D", Name = "AirPods Pro", Price = 1999, Category = "电子产品", Brand = "Apple", Barcode = "004", IsActive = true },
                new() { Id = "E", Name = "可口可乐", Price = 3.5m, Category = "饮料", Brand = "Coca-Cola", Barcode = "005", IsActive = true },
                new() { Id = "F", Name = "百事可乐", Price = 3, Category = "饮料", Brand = "Pepsi", Barcode = "006", IsActive = true },
                new() { Id = "G", Name = "矿泉水", Price = 2, Category = "饮料", Brand = "农夫山泉", Barcode = "007", IsActive = true },
                new() { Id = "H", Name = "笔记本", Price = 15, Category = "文具", Brand = "晨光", Barcode = "008", IsActive = true },
                new() { Id = "I", Name = "原味薯片", Price = 8, Category = "零食", Brand = "乐事", Barcode = "009", IsActive = true },
                new() { Id = "J", Name = "巧克力", Price = 12, Category = "零食", Brand = "德芙", Barcode = "010", IsActive = true },
                new() { Id = "K", Name = "牛奶", Price = 25, Category = "饮料", Brand = "伊利", Barcode = "011", IsActive = true },
                new() { Id = "L", Name = "面包", Price = 6, Category = "食品", Brand = "桃李", Barcode = "012", IsActive = true }
            };

            var products = allProducts.Where(p =>
                string.Equals(p.Category, category, StringComparison.OrdinalIgnoreCase) && p.IsActive)
                .ToList();

            return Ok(products);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取分类 {Category} 的商品时发生错误", category);
            return StatusCode(500, "获取商品时发生内部错误");
        }
    }

    /// <summary>
    /// 获取所有商品分类
    /// </summary>
    [HttpGet("categories")]
    public IActionResult GetCategories()
    {
        try
        {
            var categories = new List<string>
            {
                "电子产品",
                "饮料",
                "文具",
                "零食",
                "食品"
            };

            return Ok(categories);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取商品分类时发生错误");
            return StatusCode(500, "获取商品分类时发生内部错误");
        }
    }
}
