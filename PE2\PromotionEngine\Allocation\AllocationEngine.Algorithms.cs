global using Microsoft.Extensions.Logging;
using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Allocation;

/// <summary>
/// 分配引擎算法实现部分
/// </summary>
public sealed partial class AllocationEngine
{
    #region 分配算法实现

    /// <summary>
    /// 按金额比例分配
    /// </summary>
    private async Task<AllocationResult> AllocateProportionalByAmountAsync(AllocationRequest request, List<AllocationItem> eligibleItems)
    {
        await Task.Delay(1).ConfigureAwait(false);
        
        var result = new AllocationResult
        {
            RequestId = request.Id,
            IsSuccessful = true,
            OriginalDiscountAmount = request.Promotion.DiscountAmount,
            AllocationDetails = new List<AllocationDetail>()
        };

        var totalAmount = eligibleItems.Sum(i => i.TotalPrice);
        if (totalAmount == 0)
        {
            return CreateFailedResult(request, "商品总金额为0，无法按金额比例分配");
        }

        foreach (var item in eligibleItems)
        {
            var ratio = item.TotalPrice / totalAmount;
            var allocatedAmount = request.Promotion.DiscountAmount * ratio;
            
            var detail = new AllocationDetail
            {
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                OriginalAmount = item.TotalPrice,
                AllocatedAmount = allocatedAmount,
                AllocationRatio = ratio,
                UnitAllocationAmount = allocatedAmount / item.Quantity,
                PreRoundingAmount = allocatedAmount,
                FinalUnitPrice = item.UnitPrice - (allocatedAmount / item.Quantity)
            };

            result.AllocationDetails.Add(detail);
        }

        result.TotalAllocatedAmount = result.AllocationDetails.Sum(d => d.AllocatedAmount);
        return result;
    }

    /// <summary>
    /// 按数量比例分配
    /// </summary>
    private async Task<AllocationResult> AllocateProportionalByQuantityAsync(AllocationRequest request, List<AllocationItem> eligibleItems)
    {
        await Task.Delay(1).ConfigureAwait(false);
        
        var result = new AllocationResult
        {
            RequestId = request.Id,
            IsSuccessful = true,
            OriginalDiscountAmount = request.Promotion.DiscountAmount,
            AllocationDetails = new List<AllocationDetail>()
        };

        var totalQuantity = eligibleItems.Sum(i => i.Quantity);
        if (totalQuantity == 0)
        {
            return CreateFailedResult(request, "商品总数量为0，无法按数量比例分配");
        }

        foreach (var item in eligibleItems)
        {
            var ratio = (decimal)item.Quantity / totalQuantity;
            var allocatedAmount = request.Promotion.DiscountAmount * ratio;
            
            var detail = new AllocationDetail
            {
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                OriginalAmount = item.TotalPrice,
                AllocatedAmount = allocatedAmount,
                AllocationRatio = ratio,
                UnitAllocationAmount = allocatedAmount / item.Quantity,
                PreRoundingAmount = allocatedAmount,
                FinalUnitPrice = item.UnitPrice - (allocatedAmount / item.Quantity)
            };

            result.AllocationDetails.Add(detail);
        }

        result.TotalAllocatedAmount = result.AllocationDetails.Sum(d => d.AllocatedAmount);
        return result;
    }

    /// <summary>
    /// 平均分配
    /// </summary>
    private async Task<AllocationResult> AllocateEqualDistributionAsync(AllocationRequest request, List<AllocationItem> eligibleItems)
    {
        await Task.Delay(1).ConfigureAwait(false);
        
        var result = new AllocationResult
        {
            RequestId = request.Id,
            IsSuccessful = true,
            OriginalDiscountAmount = request.Promotion.DiscountAmount,
            AllocationDetails = new List<AllocationDetail>()
        };

        var itemCount = eligibleItems.Count;
        var allocatedAmountPerItem = request.Promotion.DiscountAmount / itemCount;

        foreach (var item in eligibleItems)
        {
            var detail = new AllocationDetail
            {
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                OriginalAmount = item.TotalPrice,
                AllocatedAmount = allocatedAmountPerItem,
                AllocationRatio = 1m / itemCount,
                UnitAllocationAmount = allocatedAmountPerItem / item.Quantity,
                PreRoundingAmount = allocatedAmountPerItem,
                FinalUnitPrice = item.UnitPrice - (allocatedAmountPerItem / item.Quantity)
            };

            result.AllocationDetails.Add(detail);
        }

        result.TotalAllocatedAmount = result.AllocationDetails.Sum(d => d.AllocatedAmount);
        return result;
    }

    /// <summary>
    /// 自定义权重分配
    /// </summary>
    private async Task<AllocationResult> AllocateCustomWeightAsync(AllocationRequest request, List<AllocationItem> eligibleItems)
    {
        await Task.Delay(1).ConfigureAwait(false);
        
        var result = new AllocationResult
        {
            RequestId = request.Id,
            IsSuccessful = true,
            OriginalDiscountAmount = request.Promotion.DiscountAmount,
            AllocationDetails = new List<AllocationDetail>()
        };

        // 计算总权重
        var totalWeight = 0m;
        foreach (var item in eligibleItems)
        {
            var weight = request.CustomWeights.TryGetValue(item.ProductId, out var customWeight) ? customWeight : item.Weight;
            totalWeight += weight;
        }

        if (totalWeight == 0)
        {
            return CreateFailedResult(request, "总权重为0，无法按权重分配");
        }

        foreach (var item in eligibleItems)
        {
            var weight = request.CustomWeights.TryGetValue(item.ProductId, out var customWeight) ? customWeight : item.Weight;
            var ratio = weight / totalWeight;
            var allocatedAmount = request.Promotion.DiscountAmount * ratio;
            
            var detail = new AllocationDetail
            {
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                OriginalAmount = item.TotalPrice,
                AllocatedAmount = allocatedAmount,
                AllocationRatio = ratio,
                UnitAllocationAmount = allocatedAmount / item.Quantity,
                PreRoundingAmount = allocatedAmount,
                FinalUnitPrice = item.UnitPrice - (allocatedAmount / item.Quantity)
            };

            result.AllocationDetails.Add(detail);
        }

        result.TotalAllocatedAmount = result.AllocationDetails.Sum(d => d.AllocatedAmount);
        return result;
    }

    /// <summary>
    /// 高价商品优先分配
    /// </summary>
    private async Task<AllocationResult> AllocateHighValueFirstAsync(AllocationRequest request, List<AllocationItem> eligibleItems)
    {
        await Task.Delay(1).ConfigureAwait(false);
        
        var result = new AllocationResult
        {
            RequestId = request.Id,
            IsSuccessful = true,
            OriginalDiscountAmount = request.Promotion.DiscountAmount,
            AllocationDetails = new List<AllocationDetail>()
        };

        // 按单价降序排列
        var sortedItems = eligibleItems.OrderByDescending(i => i.UnitPrice).ToList();
        var remainingDiscount = request.Promotion.DiscountAmount;

        foreach (var item in sortedItems)
        {
            if (remainingDiscount <= 0) break;

            var maxAllowedDiscount = Math.Min(item.MaxAllowedDiscount > 0 ? item.MaxAllowedDiscount : item.TotalPrice, remainingDiscount);
            var allocatedAmount = Math.Max(maxAllowedDiscount, item.MinAllowedDiscount);
            
            var detail = new AllocationDetail
            {
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                OriginalAmount = item.TotalPrice,
                AllocatedAmount = allocatedAmount,
                AllocationRatio = allocatedAmount / request.Promotion.DiscountAmount,
                UnitAllocationAmount = allocatedAmount / item.Quantity,
                PreRoundingAmount = allocatedAmount,
                FinalUnitPrice = item.UnitPrice - (allocatedAmount / item.Quantity)
            };

            result.AllocationDetails.Add(detail);
            remainingDiscount -= allocatedAmount;
        }

        result.TotalAllocatedAmount = result.AllocationDetails.Sum(d => d.AllocatedAmount);
        return result;
    }

    /// <summary>
    /// 低价商品优先分配
    /// </summary>
    private async Task<AllocationResult> AllocateLowValueFirstAsync(AllocationRequest request, List<AllocationItem> eligibleItems)
    {
        await Task.Delay(1).ConfigureAwait(false);
        
        var result = new AllocationResult
        {
            RequestId = request.Id,
            IsSuccessful = true,
            OriginalDiscountAmount = request.Promotion.DiscountAmount,
            AllocationDetails = new List<AllocationDetail>()
        };

        // 按单价升序排列
        var sortedItems = eligibleItems.OrderBy(i => i.UnitPrice).ToList();
        var remainingDiscount = request.Promotion.DiscountAmount;

        foreach (var item in sortedItems)
        {
            if (remainingDiscount <= 0) break;

            var maxAllowedDiscount = Math.Min(item.MaxAllowedDiscount > 0 ? item.MaxAllowedDiscount : item.TotalPrice, remainingDiscount);
            var allocatedAmount = Math.Max(maxAllowedDiscount, item.MinAllowedDiscount);
            
            var detail = new AllocationDetail
            {
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                OriginalAmount = item.TotalPrice,
                AllocatedAmount = allocatedAmount,
                AllocationRatio = allocatedAmount / request.Promotion.DiscountAmount,
                UnitAllocationAmount = allocatedAmount / item.Quantity,
                PreRoundingAmount = allocatedAmount,
                FinalUnitPrice = item.UnitPrice - (allocatedAmount / item.Quantity)
            };

            result.AllocationDetails.Add(detail);
            remainingDiscount -= allocatedAmount;
        }

        result.TotalAllocatedAmount = result.AllocationDetails.Sum(d => d.AllocatedAmount);
        return result;
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 创建失败结果
    /// </summary>
    private static AllocationResult CreateFailedResult(AllocationRequest request, string errorMessage)
    {
        return new AllocationResult
        {
            RequestId = request.Id,
            IsSuccessful = false,
            OriginalDiscountAmount = request.Promotion.DiscountAmount,
            ErrorMessage = errorMessage,
            AllocationDetails = new List<AllocationDetail>(),
            Statistics = new AllocationStatistics()
        };
    }

    /// <summary>
    /// 应用舍入策略
    /// </summary>
    private static decimal ApplyRoundingStrategy(decimal amount, RoundingStrategy strategy)
    {
        return strategy switch
        {
            RoundingStrategy.RoundToNearestCent => Math.Round(amount, 2, MidpointRounding.AwayFromZero),
            RoundingStrategy.RoundDown => Math.Floor(amount * 100) / 100,
            RoundingStrategy.RoundUp => Math.Ceiling(amount * 100) / 100,
            RoundingStrategy.BankersRounding => Math.Round(amount, 2, MidpointRounding.ToEven),
            RoundingStrategy.NoRounding => amount,
            _ => Math.Round(amount, 2, MidpointRounding.AwayFromZero)
        };
    }

    /// <summary>
    /// 处理尾差
    /// </summary>
    private AllocationResult HandleTailDifference(AllocationResult result, TailDifferenceStrategy strategy)
    {
        var totalAllocated = result.AllocationDetails.Sum(d => d.AllocatedAmount);
        var difference = result.OriginalDiscountAmount - totalAllocated;
        
        if (Math.Abs(difference) < 0.01m) // 差异小于1分，忽略
        {
            return result;
        }

        var targetDetail = strategy switch
        {
            TailDifferenceStrategy.AllocateToLargestItem => result.AllocationDetails.OrderByDescending(d => d.OriginalAmount).FirstOrDefault(),
            TailDifferenceStrategy.AllocateToSmallestItem => result.AllocationDetails.OrderBy(d => d.OriginalAmount).FirstOrDefault(),
            TailDifferenceStrategy.AllocateToFirstItem => result.AllocationDetails.FirstOrDefault(),
            TailDifferenceStrategy.AllocateToLastItem => result.AllocationDetails.LastOrDefault(),
            TailDifferenceStrategy.RandomAllocation => result.AllocationDetails.OrderBy(_ => Guid.NewGuid()).FirstOrDefault(),
            TailDifferenceStrategy.IgnoreDifference => null,
            _ => result.AllocationDetails.OrderByDescending(d => d.OriginalAmount).FirstOrDefault()
        };

        if (targetDetail != null)
        {
            targetDetail.AllocatedAmount += difference;
            targetDetail.TailAdjustmentAmount = difference;
            targetDetail.ReceivedTailAdjustment = true;
            targetDetail.FinalUnitPrice = (targetDetail.OriginalAmount - targetDetail.AllocatedAmount) / 
                                         result.AllocationDetails.First(d => d.ProductId == targetDetail.ProductId).UnitAllocationAmount;
            
            result.TailDifferenceAdjustment = difference;
        }

        result.TotalAllocatedAmount = result.AllocationDetails.Sum(d => d.AllocatedAmount);
        return result;
    }

    /// <summary>
    /// 检查是否允许负分配
    /// </summary>
    private static bool IsNegativeAllocationAllowed(AllocationDetail detail)
    {
        // 简化实现，实际应该根据业务规则判断
        return false;
    }

    #endregion
}
