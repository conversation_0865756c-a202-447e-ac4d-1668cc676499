<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三栏布局验证</title>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        .debug-container { max-width: 1200px; margin: 0 auto; }
        .status { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        iframe { width: 100%; height: 600px; border: 1px solid #ccc; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>三栏布局验证</h1>
        <div id="status-container">
            <div class="status">正在检查组件状态...</div>
        </div>
        
        <iframe src="/ConfigUI/index_new.html" title="主页面"></iframe>
    </div>
    
    <script>
        async function checkStatus() {
            const container = document.getElementById('status-container');
            container.innerHTML = '';
            
            // 检查API服务是否运行
            try {
                const response = await fetch('/api/promotion/metadata/types');
                if (response.ok) {
                    container.innerHTML += '<div class="status success">✓ 后端API服务正常运行</div>';
                    
                    const data = await response.json();
                    if (data && typeof data === 'object') {
                        container.innerHTML += '<div class="status success">✓ 元数据API返回正常</div>';
                        container.innerHTML += `<div class="status success">✓ 发现 ${Object.keys(data).length} 个促销类型分类</div>`;
                    }
                } else {
                    container.innerHTML += '<div class="status error">✗ API服务响应异常</div>';
                }
            } catch (error) {
                container.innerHTML += '<div class="status error">✗ 无法连接到API服务</div>';
            }
            
            // 检查静态资源
            const resources = [
                '/ConfigUI/js/config/promotionTypes.js',
                '/ConfigUI/js/services/apiService.js',
                '/ConfigUI/js/services/metadataService.js',
                '/ConfigUI/js/components/PromotionConfigMainComponentHybrid.js'
            ];
            
            for (const resource of resources) {
                try {
                    const response = await fetch(resource);
                    if (response.ok) {
                        container.innerHTML += `<div class="status success">✓ ${resource} 加载正常</div>`;
                    } else {
                        container.innerHTML += `<div class="status error">✗ ${resource} 加载失败 (${response.status})</div>`;
                    }
                } catch (error) {
                    container.innerHTML += `<div class="status error">✗ ${resource} 加载异常</div>`;
                }
            }
        }
        
        window.addEventListener('load', checkStatus);
    </script>
</body>
</html>
