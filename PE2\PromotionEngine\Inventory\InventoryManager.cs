global using System.Collections.Concurrent;
global using Microsoft.Extensions.Logging;
using PE2.Models;
using PE2.PromotionEngine.Observability;

namespace PE2.PromotionEngine.Inventory;

/// <summary>
/// 库存管理器实现 - 商品数量预占用管理
/// </summary>
public sealed class InventoryManager : IInventoryManager, IDisposable
{
    private readonly ILogger<InventoryManager> _logger;
    private readonly IObservabilityEngine _observability;
    private readonly ConcurrentDictionary<string, QuantityReservation> _activeReservations;
    private readonly ConcurrentDictionary<string, InventorySnapshot> _snapshots;
    private readonly SemaphoreSlim _reservationSemaphore;
    private readonly Timer _cleanupTimer;
    private bool _disposed;

    public InventoryManager(
        ILogger<InventoryManager> logger,
        IObservabilityEngine observability)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _observability = observability ?? throw new ArgumentNullException(nameof(observability));
        _activeReservations = new ConcurrentDictionary<string, QuantityReservation>();
        _snapshots = new ConcurrentDictionary<string, InventorySnapshot>();
        _reservationSemaphore = new SemaphoreSlim(1, 1);
        
        // 每5分钟清理一次过期的预占用
        _cleanupTimer = new Timer(CleanupExpiredReservations, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        
        _logger.LogInformation("库存管理器已初始化");
    }

    public async Task<ReservationResult> ReserveQuantityAsync(ReservationRequest request)
    {
        var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString("N");
        
        try
        {
            await _reservationSemaphore.WaitAsync().ConfigureAwait(false);
            
            _observability.TrackInventoryReservation(traceId, "开始预占用", new { request.ProductId, request.Quantity, request.Priority });

            // 验证请求
            if (string.IsNullOrEmpty(request.ProductId) || request.Quantity <= 0)
            {
                return new ReservationResult
                {
                    IsSuccessful = false,
                    ErrorMessage = "无效的预占用请求",
                    ReservationId = string.Empty
                };
            }

            // 检查当前可用数量
            var availableQuantity = GetAvailableQuantity(request.ProductId, request.Cart);
            if (availableQuantity < request.Quantity)
            {
                _observability.TrackInventoryReservation(traceId, "预占用失败：数量不足", 
                    new { request.ProductId, 需要 = request.Quantity, 可用 = availableQuantity });

                return new ReservationResult
                {
                    IsSuccessful = false,
                    ErrorMessage = $"商品 {request.ProductId} 可用数量不足，需要 {request.Quantity}，可用 {availableQuantity}",
                    ReservationId = string.Empty,
                    AvailableQuantity = availableQuantity
                };
            }

            // 检查冲突
            var conflictingReservations = GetConflictingReservations(request);
            if (conflictingReservations.Any())
            {
                var resolved = await ResolveConflictsAsync(request, conflictingReservations).ConfigureAwait(false);
                if (!resolved)
                {
                    return new ReservationResult
                    {
                        IsSuccessful = false,
                        ErrorMessage = "无法解决预占用冲突",
                        ReservationId = string.Empty,
                        ConflictingReservations = conflictingReservations.Select(r => r.Id).ToList()
                    };
                }
            }

            // 创建预占用
            var reservation = new QuantityReservation
            {
                Id = Guid.NewGuid().ToString("N"),
                ProductId = request.ProductId,
                Quantity = request.Quantity,
                Priority = request.Priority,
                RuleId = request.RuleId,
                CreatedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.Add(request.ExpirationTime),
                Status = ReservationStatus.Active,
                TraceId = traceId
            };

            _activeReservations.TryAdd(reservation.Id, reservation);

            _observability.TrackInventoryReservation(traceId, "预占用成功", 
                new { reservation.Id, reservation.ProductId, reservation.Quantity });

            _logger.LogDebug("创建预占用 ReservationId: {ReservationId}, ProductId: {ProductId}, Quantity: {Quantity}", 
                reservation.Id, reservation.ProductId, reservation.Quantity);

            return new ReservationResult
            {
                IsSuccessful = true,
                ReservationId = reservation.Id,
                ReservedQuantity = request.Quantity,
                ExpiresAt = reservation.ExpiresAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预占用商品数量失败 ProductId: {ProductId}, Quantity: {Quantity}", 
                request.ProductId, request.Quantity);
            
            _observability.TrackInventoryReservation(traceId, $"预占用异常: {ex.Message}", ex);
            
            return new ReservationResult
            {
                IsSuccessful = false,
                ErrorMessage = $"预占用失败: {ex.Message}",
                ReservationId = string.Empty
            };
        }
        finally
        {
            _reservationSemaphore.Release();
        }
    }

    public async Task<bool> ConfirmReservationAsync(string reservationId)
    {
        var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString("N");
        
        try
        {
            if (!_activeReservations.TryGetValue(reservationId, out var reservation))
            {
                _logger.LogWarning("未找到预占用记录 ReservationId: {ReservationId}", reservationId);
                return false;
            }

            if (reservation.Status != ReservationStatus.Active)
            {
                _logger.LogWarning("预占用状态无效 ReservationId: {ReservationId}, Status: {Status}", 
                    reservationId, reservation.Status);
                return false;
            }

            if (DateTime.UtcNow > reservation.ExpiresAt)
            {
                _logger.LogWarning("预占用已过期 ReservationId: {ReservationId}, ExpiresAt: {ExpiresAt}", 
                    reservationId, reservation.ExpiresAt);
                
                reservation.Status = ReservationStatus.Expired;
                return false;
            }

            reservation.Status = ReservationStatus.Confirmed;
            reservation.ConfirmedAt = DateTime.UtcNow;

            _observability.TrackInventoryReservation(traceId, "确认预占用", 
                new { reservationId, reservation.ProductId, reservation.Quantity });

            _logger.LogDebug("确认预占用 ReservationId: {ReservationId}", reservationId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "确认预占用失败 ReservationId: {ReservationId}", reservationId);
            return false;
        }
    }

    public async Task<bool> ReleaseReservationAsync(string reservationId)
    {
        var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString("N");
        
        try
        {
            if (!_activeReservations.TryRemove(reservationId, out var reservation))
            {
                _logger.LogWarning("未找到预占用记录 ReservationId: {ReservationId}", reservationId);
                return false;
            }

            reservation.Status = ReservationStatus.Released;
            reservation.ReleasedAt = DateTime.UtcNow;

            _observability.TrackInventoryRelease(traceId, "释放预占用", 
                new { reservationId, reservation.ProductId, reservation.Quantity });

            _logger.LogDebug("释放预占用 ReservationId: {ReservationId}", reservationId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "释放预占用失败 ReservationId: {ReservationId}", reservationId);
            return false;
        }
    }

    public async Task<RollbackResult> RollbackReservationsAsync(List<string> reservationIds)
    {
        var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString("N");
        var successfulRollbacks = new List<string>();
        var failedRollbacks = new List<string>();

        try
        {
            _observability.TrackInventoryRelease(traceId, "开始批量回滚预占用", 
                new { reservationCount = reservationIds.Count, reservationIds });

            foreach (var reservationId in reservationIds)
            {
                var success = await ReleaseReservationAsync(reservationId).ConfigureAwait(false);
                if (success)
                {
                    successfulRollbacks.Add(reservationId);
                }
                else
                {
                    failedRollbacks.Add(reservationId);
                }
            }

            _observability.TrackInventoryRelease(traceId, "批量回滚完成", 
                new { 成功 = successfulRollbacks.Count, 失败 = failedRollbacks.Count });

            return new RollbackResult
            {
                IsSuccessful = failedRollbacks.Count == 0,
                SuccessfulRollbacks = successfulRollbacks,
                FailedRollbacks = failedRollbacks,
                TotalProcessed = reservationIds.Count
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量回滚预占用失败");
            
            return new RollbackResult
            {
                IsSuccessful = false,
                SuccessfulRollbacks = successfulRollbacks,
                FailedRollbacks = reservationIds.Except(successfulRollbacks).ToList(),
                TotalProcessed = reservationIds.Count,
                ErrorMessage = ex.Message
            };
        }
    }

    public InventorySnapshot CreateSnapshot(ShoppingCart cart)
    {
        var snapshotId = Guid.NewGuid().ToString("N");
        var snapshot = new InventorySnapshot
        {
            Id = snapshotId,
            CreatedAt = DateTime.UtcNow,
            CartItems = cart.Items.Select(item => new InventoryItem
            {
                ProductId = item.Product.Id,
                TotalQuantity = item.Quantity,
                AvailableQuantity = item.AvailableQuantity,
                ReservedQuantity = item.Quantity - item.AvailableQuantity,
                UnitPrice = item.UnitPrice
            }).ToList(),
            ActiveReservations = _activeReservations.Values
                .Where(r => r.Status == ReservationStatus.Active)
                .Select(r => new ReservationSummary
                {
                    Id = r.Id,
                    ProductId = r.ProductId,
                    Quantity = r.Quantity,
                    Priority = r.Priority,
                    CreatedAt = r.CreatedAt,
                    ExpiresAt = r.ExpiresAt
                }).ToList()
        };

        _snapshots.TryAdd(snapshotId, snapshot);
        
        _logger.LogDebug("创建库存快照 SnapshotId: {SnapshotId}, Items: {ItemCount}, Reservations: {ReservationCount}", 
            snapshotId, snapshot.CartItems.Count, snapshot.ActiveReservations.Count);

        return snapshot;
    }

    public bool RestoreSnapshot(string snapshotId, ShoppingCart cart)
    {
        try
        {
            if (!_snapshots.TryGetValue(snapshotId, out var snapshot))
            {
                _logger.LogWarning("未找到库存快照 SnapshotId: {SnapshotId}", snapshotId);
                return false;
            }

            // 恢复购物车商品的可用数量
            foreach (var snapshotItem in snapshot.CartItems)
            {
                var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == snapshotItem.ProductId);
                if (cartItem != null)
                {
                    cartItem.AvailableQuantity = snapshotItem.AvailableQuantity;
                }
            }

            _logger.LogDebug("恢复库存快照 SnapshotId: {SnapshotId}", snapshotId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "恢复库存快照失败 SnapshotId: {SnapshotId}", snapshotId);
            return false;
        }
    }

    public List<QuantityReservation> GetActiveReservations(string? productId = null)
    {
        var reservations = _activeReservations.Values
            .Where(r => r.Status == ReservationStatus.Active)
            .ToList();

        if (!string.IsNullOrEmpty(productId))
        {
            reservations = reservations.Where(r => r.ProductId == productId).ToList();
        }

        return reservations;
    }

    private int GetAvailableQuantity(string productId, ShoppingCart cart)
    {
        var cartQuantity = cart.GetAvailableProductQuantity(productId);
        var reservedQuantity = _activeReservations.Values
            .Where(r => r.ProductId == productId && r.Status == ReservationStatus.Active)
            .Sum(r => r.Quantity);

        return Math.Max(0, cartQuantity - reservedQuantity);
    }

    private List<QuantityReservation> GetConflictingReservations(ReservationRequest request)
    {
        return _activeReservations.Values
            .Where(r => r.ProductId == request.ProductId && 
                       r.Status == ReservationStatus.Active &&
                       r.Priority < request.Priority) // 只有低优先级的才算冲突
            .ToList();
    }

    private async Task<bool> ResolveConflictsAsync(ReservationRequest request, List<QuantityReservation> conflicts)
    {
        try
        {
            // 计算需要释放的数量
            var totalConflictQuantity = conflicts.Sum(c => c.Quantity);
            var availableAfterRelease = GetAvailableQuantity(request.ProductId, request.Cart) + totalConflictQuantity;

            if (availableAfterRelease < request.Quantity)
            {
                return false; // 即使释放所有冲突预占用也不够
            }

            // 按优先级排序，释放最低优先级的预占用
            var sortedConflicts = conflicts.OrderBy(c => c.Priority).ToList();
            var releasedQuantity = 0;

            foreach (var conflict in sortedConflicts)
            {
                await ReleaseReservationAsync(conflict.Id).ConfigureAwait(false);
                releasedQuantity += conflict.Quantity;

                if (releasedQuantity >= request.Quantity)
                {
                    break; // 已经释放足够的数量
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解决预占用冲突失败");
            return false;
        }
    }

    private void CleanupExpiredReservations(object? state)
    {
        try
        {
            var expiredReservations = _activeReservations.Values
                .Where(r => r.Status == ReservationStatus.Active && DateTime.UtcNow > r.ExpiresAt)
                .ToList();

            foreach (var expired in expiredReservations)
            {
                if (_activeReservations.TryRemove(expired.Id, out var reservation))
                {
                    reservation.Status = ReservationStatus.Expired;
                    _logger.LogDebug("清理过期预占用 ReservationId: {ReservationId}", expired.Id);
                }
            }

            // 清理过期快照
            var expiredSnapshots = _snapshots.Values
                .Where(s => DateTime.UtcNow.Subtract(s.CreatedAt) > TimeSpan.FromHours(1))
                .ToList();

            foreach (var snapshot in expiredSnapshots)
            {
                _snapshots.TryRemove(snapshot.Id, out _);
            }

            if (expiredReservations.Any() || expiredSnapshots.Any())
            {
                _logger.LogInformation("清理过期数据 - 预占用: {ReservationCount}, 快照: {SnapshotCount}", 
                    expiredReservations.Count, expiredSnapshots.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期预占用时发生错误");
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _cleanupTimer?.Dispose();
        _reservationSemaphore?.Dispose();
        _disposed = true;
        
        _logger.LogInformation("库存管理器已释放");
    }
}
