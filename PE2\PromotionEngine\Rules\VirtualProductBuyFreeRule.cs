using PE2.Models;
using PE2.PromotionEngine.Core;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.BuyFreeRules;

/// <summary>
/// 虚拟模式的商品买免规则 - 买2送1示例
/// </summary>
public class VirtualProductBuyFreeRule : VirtualPromotionRuleBase
{
    public override string RuleType => "VirtualProductBuyFree";

    public List<string> ApplicableProductIds { get; set; } = new();
    public int BuyQuantity { get; set; } = 2;
    public int FreeQuantity { get; set; } = 1;
    public BenefitSelectionStrategy ConditionStrategy { get; set; } =
        BenefitSelectionStrategy.MerchantBenefit;
    public BenefitSelectionStrategy FreeStrategy { get; set; } =
        BenefitSelectionStrategy.CustomerBenefit;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        var instances = CartExpander.ExpandCart(cart);
        var availableCount = CartExpander.GetAvailableCount(
            instances,
            ApplicableProductIds.FirstOrDefault() ?? ""
        );
        return availableCount >= (BuyQuantity + FreeQuantity);
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        var instances = CartExpander.ExpandCart(cart);
        var totalAvailable = ApplicableProductIds.Sum(id =>
            CartExpander.GetAvailableCount(instances, id)
        );

        return totalAvailable / (BuyQuantity + FreeQuantity);
    }

    protected override VirtualPromotionResult ApplyVirtualPromotion(
        List<VirtualCartExpander.VirtualCartInstance> instances,
        int applicationCount
    )
    {
        var result = new VirtualPromotionResult { Success = true };
        var totalDiscount = 0m;

        var maxApplications = CalculateMaxApplicationsFromInstances(instances);
        var actualApplications = Math.Min(applicationCount, maxApplications);

        for (int app = 0; app < actualApplications; app++)
        {
            // 1. 预留条件商品（买2）
            var conditionReserved = CartExpander.TryReserveInstances(
                instances,
                Id,
                ApplicableProductIds,
                BuyQuantity,
                ReservationType.Condition,
                ConditionStrategy
            );

            if (!conditionReserved)
            {
                if (app == 0)
                {
                    result.Success = false;
                    result.ErrorMessage = "条件商品数量不足";
                    return result;
                }
                break;
            }

            // 2. 预留执行商品（送1）
            var executionReserved = CartExpander.TryReserveInstances(
                instances,
                Id,
                ApplicableProductIds,
                FreeQuantity,
                ReservationType.Execution,
                FreeStrategy
            );

            if (!executionReserved)
            {
                // 释放条件预留
                CartExpander.ReleaseReservations(instances, Id);
                if (app == 0)
                {
                    result.Success = false;
                    result.ErrorMessage = "免费商品数量不足";
                    return result;
                }
                break;
            }

            // 3. 应用免费效果
            var freeInstances = CartExpander.GetReservedInstances(
                instances,
                Id,
                ReservationType.Execution
            );

            CartExpander.ApplyPromotionEffect(instances, Id, 0m, freeInstances);

            // 4. 计算折扣
            var appDiscount = freeInstances.Sum(x => x.UnitPrice);
            totalDiscount += appDiscount;

            // 5. 记录消耗和赠品信息
            RecordPromotionDetails(result, instances, app + 1);

            if (!IsRepeatable)
                break;
        }

        result.DiscountAmount = totalDiscount;
        return result;
    }

    private int CalculateMaxApplicationsFromInstances(
        List<VirtualCartExpander.VirtualCartInstance> instances
    )
    {
        var availableCount = ApplicableProductIds.Sum(id =>
            CartExpander.GetAvailableCount(instances, id)
        );

        return availableCount / (BuyQuantity + FreeQuantity);
    }

    private void RecordPromotionDetails(
        VirtualPromotionResult result,
        List<VirtualCartExpander.VirtualCartInstance> instances,
        int applicationNumber
    )
    {
        // 记录条件商品
        var conditionInstances = CartExpander.GetReservedInstances(
            instances,
            Id,
            ReservationType.Condition
        );

        foreach (var group in conditionInstances.GroupBy(x => x.ProductId))
        {
            result.ConsumedItems.Add(
                new ConsumedItem
                {
                    ProductId = group.Key,
                    ProductName = group.First().ProductName,
                    Quantity = group.Count(),
                    UnitPrice = group.First().UnitPrice
                }
            );
        }

        // 记录赠品
        var freeInstances = CartExpander.GetReservedInstances(
            instances,
            Id,
            ReservationType.Execution
        );

        foreach (var group in freeInstances.GroupBy(x => x.ProductId))
        {
            result.GiftItems.Add(
                new GiftItem
                {
                    ProductId = group.Key,
                    ProductName = group.First().ProductName,
                    Quantity = group.Count(),
                    //UnitPrice = group.First().UnitPrice,
                    Description = $"买{BuyQuantity}送{FreeQuantity}促销 - 第{applicationNumber}次应用"
                }
            );
        }
    }
}
