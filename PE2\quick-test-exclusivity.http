### 快速验证改进的互斥性分析功能

### 测试场景：多个促销竞争同一商品，验证详细的冲突分析
POST http://localhost:5213/api/promotionanalysis/detailed-analysis
Content-Type: application/json

{
  "id": "QUICK_EXCLUSIVITY_TEST",
  "customerId": "CUSTOMER_QUICK_TEST",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 100.00,
        "category": "电子产品"
      },
      "quantity": 2,
      "unitPrice": 100.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 50.00,
        "category": "服装"
      },
      "quantity": 4,
      "unitPrice": 50.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 30.00,
        "category": "家居"
      },
      "quantity": 6,
      "unitPrice": 30.00
    }
  ]
}

### 预期结果验证点：
### 1. ignoredPromotions 中应该有明确的 reasonType 区分：
###    - "Conflict" 表示互斥冲突
###    - "NotOptimal" 表示非最优选择
### 2. isExclusivityConflict 字段应该正确标识冲突类型
### 3. conflictCategory 应该显示具体的冲突分类：
###    - "规则级互斥"
###    - "商品级互斥" 
###    - "叠加限制"
###    - "非最优选择"
### 4. exclusivityAnalysis 应该包含详细的冲突分析
### 5. calculationSteps 中应该包含新的步骤类型：
###    - "ExclusivityCheck"
###    - "ExclusivityConflict"
###    - "PromotionExcluded"
