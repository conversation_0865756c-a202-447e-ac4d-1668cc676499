using PE2.Models;
using PE2.PromotionEngine.Models;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Rules.ExchangeRules;

/// <summary>
/// 组合特价换购规则
/// 某A类商品满X件或X元并且某B类商品满Y件或Y元，加Z元，换购一件C类商品
/// 场景：购买A、B商品各大于等于1件时，可以加100元换C商品
/// </summary>
public class CombinationSpecialPriceExchangeRule : BaseExchangeRule
{
    public override string RuleType => "CombinationSpecialPriceExchange";
    /// <summary>
    /// 组合购买条件列表
    /// </summary>
    public List<CombinationBuyCondition> CombinationConditions { get; set; } = new();

    /// <summary>
    /// 换购条件列表
    /// </summary>
    public List<SpecialPriceExchangeCondition> ExchangeConditions { get; set; } = new();

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!CombinationConditions.Any() || !ExchangeConditions.Any())
            return false;

        // 检查组合购买条件
        if (!CheckCombinationConditions(cart))
            return false;

        // 检查换购商品是否在购物车中（POS系统核心要求）
        var allExchangeProductIds = ExchangeConditions
            .SelectMany(c => c.ExchangeProductIds)
            .Distinct()
            .ToList();

        return ValidateExchangeProductsInCart(cart, allExchangeProductIds);
    }

    /// <summary>
    /// 检查组合购买条件
    /// </summary>
    private bool CheckCombinationConditions(ShoppingCart cart)
    {
        foreach (var condition in CombinationConditions)
        {
            var availableQuantity = cart.GetAvailableProductQuantity(condition.ProductId);
            var totalAmount = cart.Items
                .Where(x => x.Product.Id == condition.ProductId)
                .Sum(x => x.SubTotal);

            if (condition.RequiredQuantity > 0 && availableQuantity < condition.RequiredQuantity)
                return false;

            if (condition.RequiredAmount > 0 && totalAmount < condition.RequiredAmount)
                return false;
        }

        return true;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = int.MaxValue;

        // 计算每个组合条件的最大应用次数
        foreach (var condition in CombinationConditions)
        {
            var availableQuantity = cart.GetAvailableProductQuantity(condition.ProductId);
            
            if (condition.RequiredQuantity > 0)
            {
                var maxByQuantity = IsRepeatable 
                    ? availableQuantity / condition.RequiredQuantity
                    : (availableQuantity >= condition.RequiredQuantity ? 1 : 0);
                
                maxApplications = Math.Min(maxApplications, maxByQuantity);
            }
        }

        if (maxApplications == int.MaxValue)
            maxApplications = 1;

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyCombinationSpecialPriceExchange(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用组合特价换购促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用组合特价换购促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyCombinationSpecialPriceExchange(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var exchangeItems = new List<GiftItem>(); // 换购商品记录为GiftItem，但实际是换购

        for (int app = 0; app < applicationCount; app++)
        {
            // 消耗组合条件商品
            if (!ConsumeCombinationConditionProducts(cart, consumedItems))
                continue;

            // 根据策略选择换购商品
            var applicableExchanges = GetApplicableExchangeConditions();
            
            foreach (var exchange in applicableExchanges)
            {
                var selectedExchangeProducts = SelectOptimalExchangeProducts(
                    exchange.ExchangeProductIds, cart, exchange.ExchangeQuantity);

                foreach (var productId in selectedExchangeProducts)
                {
                    var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
                    if (cartItem == null || cartItem.Quantity <= 0) continue;

                    // 计算换购折扣（原价 - 加价金额）
                    var originalPrice = cartItem.UnitPrice;
                    var exchangePrice = exchange.AddAmount;
                    var discountAmount = originalPrice - exchangePrice;

                    if (discountAmount > 0)
                    {
                        totalDiscountAmount += discountAmount;

                        var strategyDescription = ExchangeSelectionStrategy == ExchangeSelectionStrategy.CustomerBenefit
                            ? "客户利益最大化"
                            : "商家利益最大化";

                        exchangeItems.Add(new GiftItem
                        {
                            ProductId = productId,
                            ProductName = cartItem.Product.Name,
                            Quantity = 1,
                            Value = discountAmount,
                            Description = $"组合特价换购：加{exchange.AddAmount:C}换购，节省{discountAmount:C}（{strategyDescription}）"
                        });

                        // 标记商品为换购商品（修改实际支付价格）
                        cartItem.ActualUnitPrice = exchangePrice;
                    }
                }
            }
        }

        return (totalDiscountAmount, consumedItems, exchangeItems);
    }

    /// <summary>
    /// 消耗组合条件商品
    /// </summary>
    private bool ConsumeCombinationConditionProducts(ShoppingCart cart, List<ConsumedItem> consumedItems)
    {
        foreach (var condition in CombinationConditions)
        {
            var remainingQuantity = condition.RequiredQuantity;

            var availableItems = cart.Items
                .Where(x => x.Product.Id == condition.ProductId && x.AvailableQuantity > 0)
                .ToList();

            foreach (var item in availableItems)
            {
                if (remainingQuantity <= 0) break;

                var consumeQuantity = Math.Min(item.AvailableQuantity, remainingQuantity);

                var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == condition.ProductId);
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += consumeQuantity;
                }
                else
                {
                    consumedItems.Add(new ConsumedItem
                    {
                        ProductId = condition.ProductId,
                        ProductName = item.Product.Name,
                        Quantity = consumeQuantity,
                        UnitPrice = item.UnitPrice
                    });
                }

                item.Quantity -= consumeQuantity;
                remainingQuantity -= consumeQuantity;
            }

            if (remainingQuantity > 0)
                return false; // 无法满足某个组合条件
        }

        return true;
    }

    /// <summary>
    /// 获取适用的换购条件
    /// </summary>
    private List<SpecialPriceExchangeCondition> GetApplicableExchangeConditions()
    {
        return ExchangeStrategy switch
        {
            ExchangeStrategy.ByGradient => ExchangeConditions.Take(1).ToList(), // 梯度换购：只取第一个
            ExchangeStrategy.AllExchange => ExchangeConditions.ToList(), // 全部换购：取所有
            _ => ExchangeConditions.Take(1).ToList()
        };
    }
}

/// <summary>
/// 组合购买条件
/// </summary>
public class CombinationBuyCondition
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 所需数量
    /// </summary>
    public int RequiredQuantity { get; set; }

    /// <summary>
    /// 所需金额
    /// </summary>
    public decimal RequiredAmount { get; set; }
}
