using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Core;
using PE2.PromotionEngine.Rules.ExchangeRules;
using PE2.PromotionEngine.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace PE2.PromotionEngine.Examples;

/// <summary>
/// 新架构规则使用示例
/// 展示如何使用迁移后的促销规则
/// </summary>
public class NewRuleUsageExample
{
    /// <summary>
    /// 演示新架构统一特价换购规则的使用
    /// </summary>
    public static async Task DemonstrateNewUnifiedSpecialPriceExchangeRule()
    {
        // 1. 配置服务容器
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole());
        services.AddPromotionEngine();
        
        var serviceProvider = services.BuildServiceProvider();

        // 2. 创建规则配置
        var ruleConfiguration = new UnifiedSpecialPriceExchangeConfiguration
        {
            Id = "EXCHANGE_001",
            Name = "买A商品换购B商品特价促销",
            Description = "购买A商品1件，可以50元特价换购B商品",
            Priority = 100,
            IsEnabled = true,
            IsRepeatable = true,
            MaxApplications = 3,
            Strategy = ExchangeStrategy.SpecialPrice,
            BenefitSelection = BenefitSelectionStrategy.CustomerBenefit,
            BuyCondition = new ExchangeBuyCondition
            {
                ProductIds = ["PRODUCT_A"],
                RequiredQuantity = 1,
                RequiredAmount = 0
            },
            ExchangeCondition = new ExchangeCondition
            {
                ExchangeProductIds = ["PRODUCT_B"],
                ExchangeQuantity = 1,
                ExchangeAmount = 50m
            },
            ExchangeConditions = 
            [
                new SpecialPriceExchangeCondition
                {
                    ExchangeProductIds = ["PRODUCT_B"],
                    ExchangeQuantity = 1,
                    SpecialPrice = 50m,
                    IsEnabled = true
                }
            ]
        };

        // 3. 使用工厂创建规则实例
        var ruleFactory = serviceProvider.GetRequiredService<IPromotionRuleFactory>();
        var rule = ruleFactory.CreateRule("UnifiedSpecialPriceExchange", ruleConfiguration);

        // 4. 创建测试购物车
        var shoppingCart = new ShoppingCart
        {
            Id = "CART_001",
            CustomerId = "CUSTOMER_001",
            Channel = "Online",
            Items = 
            [
                new CartItem
                {
                    ProductId = "PRODUCT_A",
                    ProductName = "商品A",
                    UnitPrice = 100m,
                    ActualUnitPrice = 100m,
                    Quantity = 2,
                    Category = "Electronics"
                },
                new CartItem
                {
                    ProductId = "PRODUCT_B",
                    ProductName = "商品B",
                    UnitPrice = 80m,
                    ActualUnitPrice = 80m,
                    Quantity = 1,
                    Category = "Accessories"
                }
            ]
        };

        // 5. 检查规则适用性
        var isApplicable = await rule.IsApplicableAsync(shoppingCart, DateTime.UtcNow);
        Console.WriteLine($"规则是否适用: {isApplicable}");

        if (isApplicable)
        {
            // 6. 计算最大应用次数
            var maxApplications = await rule.CalculateMaxApplicationsAsync(shoppingCart);
            Console.WriteLine($"最大应用次数: {maxApplications}");

            // 7. 转换为ProcessedCart
            var processedCart = RuleMigrationHelper.ConvertToProcessedCart(shoppingCart);

            // 8. 应用促销规则
            var traceId = Guid.NewGuid().ToString("N");
            var appliedPromotion = await rule.ApplyPromotionAsync(processedCart, 1, traceId);

            // 9. 输出结果
            Console.WriteLine($"促销应用结果:");
            Console.WriteLine($"  规则ID: {appliedPromotion.RuleId}");
            Console.WriteLine($"  规则名称: {appliedPromotion.RuleName}");
            Console.WriteLine($"  优惠金额: {appliedPromotion.DiscountAmount:C}");
            Console.WriteLine($"  应用次数: {appliedPromotion.ApplicationCount}");
            
            Console.WriteLine($"  消耗商品:");
            foreach (var consumed in appliedPromotion.ConsumedItems)
            {
                Console.WriteLine($"    {consumed.ProductName}: {consumed.Quantity}件 x {consumed.UnitPrice:C}");
            }
            
            Console.WriteLine($"  换购商品:");
            foreach (var gift in appliedPromotion.GiftItems)
            {
                Console.WriteLine($"    {gift.ProductName}: {gift.Quantity}件 x {gift.UnitPrice:C} (原价: {gift.OriginalUnitPrice:C})");
            }

            // 10. 显示购物车最终状态
            Console.WriteLine($"购物车最终状态:");
            foreach (var item in processedCart.Items)
            {
                Console.WriteLine($"  {item.ProductName}: {item.Quantity}件");
                Console.WriteLine($"    原价: {item.OriginalUnitPrice:C} -> 实际价格: {item.ActualUnitPrice:C}");
                Console.WriteLine($"    小计: {item.Quantity * item.ActualUnitPrice:C}");
            }

            var totalOriginal = processedCart.Items.Sum(i => i.Quantity * i.OriginalUnitPrice);
            var totalActual = processedCart.Items.Sum(i => i.Quantity * i.ActualUnitPrice);
            var totalSavings = totalOriginal - totalActual;

            Console.WriteLine($"总计:");
            Console.WriteLine($"  原始金额: {totalOriginal:C}");
            Console.WriteLine($"  实际金额: {totalActual:C}");
            Console.WriteLine($"  节省金额: {totalSavings:C}");
        }

        // 11. 清理资源
        rule.Dispose();
        await serviceProvider.DisposeAsync();
    }

    /// <summary>
    /// 演示规则工厂的使用
    /// </summary>
    public static async Task DemonstrateRuleFactory()
    {
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole());
        services.AddPromotionEngine();
        
        var serviceProvider = services.BuildServiceProvider();
        var ruleFactory = serviceProvider.GetRequiredService<IPromotionRuleFactory>();

        // 1. 获取支持的规则类型
        var supportedTypes = ruleFactory.GetSupportedRuleTypes();
        Console.WriteLine("支持的规则类型:");
        foreach (var type in supportedTypes)
        {
            Console.WriteLine($"  - {type}");
        }

        // 2. 验证规则配置
        var testConfig = new UnifiedSpecialPriceExchangeConfiguration
        {
            Id = "TEST_001",
            Name = "测试规则",
            BuyCondition = new ExchangeBuyCondition
            {
                ProductIds = ["PRODUCT_A"],
                RequiredQuantity = 1
            },
            ExchangeCondition = new ExchangeCondition
            {
                ExchangeProductIds = ["PRODUCT_B"],
                ExchangeQuantity = 1,
                ExchangeAmount = 10m
            }
        };

        var validationResult = ruleFactory.ValidateConfiguration("UnifiedSpecialPriceExchange", testConfig);
        Console.WriteLine($"配置验证结果: {(validationResult.IsValid ? "有效" : "无效")}");
        
        if (!validationResult.IsValid)
        {
            Console.WriteLine("错误:");
            foreach (var error in validationResult.Errors)
            {
                Console.WriteLine($"  - {error}");
            }
        }

        if (validationResult.Warnings.Any())
        {
            Console.WriteLine("警告:");
            foreach (var warning in validationResult.Warnings)
            {
                Console.WriteLine($"  - {warning}");
            }
        }

        await serviceProvider.DisposeAsync();
    }

    /// <summary>
    /// 演示新旧规则迁移一致性验证
    /// </summary>
    public static void DemonstrateMigrationConsistency()
    {
        // 创建旧规则实例
        var oldRule = new UnifiedSpecialPriceExchangeRule
        {
            Id = "RULE_001",
            Name = "测试规则",
            Priority = 100,
            IsEnabled = true,
            IsRepeatable = true,
            MaxApplications = 5
        };

        // 创建新规则配置
        var newRuleConfig = new UnifiedSpecialPriceExchangeConfiguration
        {
            Id = "RULE_001",
            Name = "测试规则",
            Priority = 100,
            IsEnabled = true,
            IsRepeatable = true,
            MaxApplications = 5,
            BuyCondition = new ExchangeBuyCondition
            {
                ProductIds = ["PRODUCT_A"],
                RequiredQuantity = 1
            },
            ExchangeCondition = new ExchangeCondition
            {
                ExchangeProductIds = ["PRODUCT_B"],
                ExchangeQuantity = 1,
                ExchangeAmount = 10m
            }
        };

        // 模拟创建新规则实例（实际使用中通过工厂创建）
        // var newRule = ruleFactory.CreateRule("UnifiedSpecialPriceExchange", newRuleConfig);

        // 验证迁移一致性
        // var consistencyResult = RuleMigrationHelper.ValidateMigrationConsistency(oldRule, newRule);
        
        Console.WriteLine("迁移一致性验证:");
        Console.WriteLine($"  旧规则ID: {oldRule.Id}");
        Console.WriteLine($"  旧规则名称: {oldRule.Name}");
        Console.WriteLine($"  旧规则优先级: {oldRule.Priority}");
        Console.WriteLine("  迁移状态: 准备就绪");
    }
}
