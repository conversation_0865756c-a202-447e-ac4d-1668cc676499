<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ComplexArrayRenderer 调试测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #f5f5f5;
        }
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 16px;
            color: #303133;
        }
        .debug-info {
            background: #f0f9ff;
            border: 1px solid #7dd3fc;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="debug-container">
            <h1>ComplexArrayRenderer 调试测试</h1>
            
            <!-- 组件状态检查 -->
            <div class="test-section">
                <div class="test-title">组件状态检查</div>
                <div class="debug-info">
                    <div>ComplexArrayRenderer 组件是否定义: {{ typeof ComplexArrayRenderer !== 'undefined' ? '✅ 已定义' : '❌ 未定义' }}</div>
                    <div>ProductSelector 组件是否定义: {{ typeof ProductSelector !== 'undefined' ? '✅ 已定义' : '❌ 未定义' }}</div>
                    <div>Vue 版本: {{ Vue?.version || '未知' }}</div>
                    <div>ElementPlus 是否可用: {{ typeof ElementPlus !== 'undefined' ? '✅ 可用' : '❌ 不可用' }}</div>
                </div>
            </div>
            
            <!-- 模拟 combinationConditions 字段测试 -->
            <div class="test-section">
                <div class="test-title">模拟 combinationConditions 字段测试</div>
                <div class="debug-info">
                    <div>当前数组长度: {{ testConditions.length }}</div>
                    <div>字段配置: {{ JSON.stringify(mockFieldConfig, null, 2) }}</div>
                </div>
                
                <el-button @click="loadMockMetadata" type="primary" style="margin-bottom: 16px;">
                    加载模拟元数据
                </el-button>
                
                <el-button @click="clearConditions" type="info" style="margin-bottom: 16px;">
                    清空条件
                </el-button>
                
                <div style="border: 2px solid #409eff; padding: 16px; border-radius: 8px;">
                    <h4>ComplexArrayRenderer 渲染测试:</h4>
                    <complex-array-renderer
                        v-if="mockFieldConfig.metadata"
                        :field-name="'combinationConditions'"
                        :field-config="mockFieldConfig"
                        :model-value="testConditions"
                        :disabled="false"
                        @update:model-value="testConditions = $event"
                    />
                    <div v-else style="color: #e6a23c;">
                        等待加载元数据...
                    </div>
                </div>
                
                <div class="debug-info" style="margin-top: 16px;">
                    <div>测试条件数据:</div>
                    <pre>{{ JSON.stringify(testConditions, null, 2) }}</pre>
                </div>
            </div>
            
            <!-- API 元数据获取测试 -->
            <div class="test-section">
                <div class="test-title">API 元数据获取测试</div>
                <el-button @click="fetchRealMetadata" type="success">
                    获取真实 CombinationBuyFreeRule 元数据
                </el-button>
                
                <div v-if="realMetadata" class="debug-info" style="margin-top: 16px;">
                    <div>combinationConditions 字段配置:</div>
                    <pre>{{ JSON.stringify(realMetadata.fields?.find(f => f.name === 'combinationConditions'), null, 2) }}</pre>
                </div>
                
                <div v-if="realMetadata" style="border: 2px solid #67c23a; padding: 16px; border-radius: 8px; margin-top: 16px;">
                    <h4>真实元数据 ComplexArrayRenderer 测试:</h4>
                    <complex-array-renderer
                        :field-name="'combinationConditions'"
                        :field-config="realCombinationConditionsField"
                        :model-value="realTestConditions"
                        :disabled="false"
                        @update:model-value="realTestConditions = $event"
                    />
                </div>
                
                <div v-if="realTestConditions.length > 0" class="debug-info" style="margin-top: 16px;">
                    <div>真实测试条件数据:</div>
                    <pre>{{ JSON.stringify(realTestConditions, null, 2) }}</pre>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="js/components/ProductSelector.js"></script>
    <script src="js/components/ComplexArrayRenderer.js"></script>

    <script>
        const { createApp, ref, computed } = Vue;

        createApp({
            components: {
                ComplexArrayRenderer,
                ProductSelector
            },
            setup() {
                const testConditions = ref([]);
                const realTestConditions = ref([]);
                const realMetadata = ref(null);

                // 模拟字段配置
                const mockFieldConfig = ref({
                    name: 'combinationConditions',
                    type: 'array-complex',
                    metadata: null
                });

                // 真实的 combinationConditions 字段配置
                const realCombinationConditionsField = computed(() => {
                    if (!realMetadata.value) return null;
                    return realMetadata.value.fields?.find(f => f.name === 'combinationConditions') || null;
                });

                // 加载模拟元数据
                const loadMockMetadata = () => {
                    mockFieldConfig.value = {
                        name: 'combinationConditions',
                        type: 'array-complex',
                        metadata: {
                            elementSchema: {
                                typeName: 'CombinationBuyCondition',
                                displayName: 'CombinationBuyCondition',
                                fields: [
                                    {
                                        name: 'productId',
                                        label: 'Product Id',
                                        type: {
                                            type: 'product-selector-multiple',
                                            isNullable: false,
                                            elementType: 'input'
                                        },
                                        required: false,
                                        group: 'condition'
                                    },
                                    {
                                        name: 'requiredQuantity',
                                        label: 'Required Quantity', 
                                        type: {
                                            type: 'number',
                                            isNullable: false
                                        },
                                        required: false,
                                        group: 'condition'
                                    },
                                    {
                                        name: 'requiredAmount',
                                        label: 'Required Amount',
                                        type: {
                                            type: 'number',
                                            isNullable: false
                                        },
                                        required: false,
                                        group: 'condition'
                                    }
                                ]
                            },
                            defaultItem: {
                                productId: [],
                                requiredQuantity: 0,
                                requiredAmount: 0
                            },
                            operations: ['add', 'remove', 'edit', 'reorder']
                        }
                    };
                    
                    ElMessage.success('模拟元数据已加载');
                };

                // 清空条件
                const clearConditions = () => {
                    testConditions.value = [];
                    realTestConditions.value = [];
                    ElMessage.info('条件已清空');
                };

                // 获取真实元数据
                const fetchRealMetadata = async () => {
                    try {
                        const response = await fetch('/api/promotion/metadata/types/CombinationBuyFreeRule');
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        realMetadata.value = await response.json();
                        realTestConditions.value = [];
                        ElMessage.success('真实元数据获取成功');
                    } catch (error) {
                        console.error('获取元数据失败:', error);
                        ElMessage.error(`获取元数据失败: ${error.message}`);
                    }
                };

                return {
                    testConditions,
                    realTestConditions,
                    realMetadata,
                    mockFieldConfig,
                    realCombinationConditionsField,
                    loadMockMetadata,
                    clearConditions,
                    fetchRealMetadata,
                    ComplexArrayRenderer,
                    ProductSelector
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
