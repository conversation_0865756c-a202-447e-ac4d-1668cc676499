<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复杂数组字段渲染测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    <script src="https://unpkg.com/vue@3.3.8/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.iife.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-title {
            margin-bottom: 24px;
            color: #333;
            font-size: 24px;
            font-weight: 600;
        }
        .test-description {
            margin-bottom: 24px;
            color: #666;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1 class="test-title">复杂数组字段渲染测试</h1>
            <div class="test-description">
                测试EnhancedConditionRenderer组件对复杂数组类型字段的渲染能力
            </div>

            <el-divider content-position="left">
                <el-text type="primary" size="large">CombinationSpecialPriceRule - 组合条件字段</el-text>
            </el-divider>

            <enhanced-condition-renderer
                v-if="testField"
                :field="testField"
                :value="testValue"
                @update="handleUpdate">
            </enhanced-condition-renderer>

            <el-divider content-position="left">
                <el-text type="info" size="large">当前值</el-text>
            </el-divider>

            <el-card>
                <pre>{{ JSON.stringify(testValue, null, 2) }}</pre>
            </el-card>
        </div>
    </div>

    <script src="./js/components/EnhancedConditionRenderer.js"></script>

    <script>
        const { createApp, ref, onMounted } = Vue;
        const { ElMessage, ElLoading } = ElementPlus;

        createApp({
            components: {
                'enhanced-condition-renderer': EnhancedConditionRenderer
            },
            setup() {
                const testField = ref(null);
                const testValue = ref([]);

                // 模拟API返回的combinationConditions字段元数据
                const initTestField = () => {                    testField.value = {
                        "name": "combinationConditions",
                        "label": "组合条件",
                        "type": "array-complex",
                        "required": false,
                        "group": "condition",
                        "default": null,
                        "options": null,
                        "validation": null,
                        "metadata": {
                            "elementSchema": {
                                "typeName": "CombinationSpecialPriceCondition",
                                "displayName": "组合特价条件",
                                "description": null,
                                "fields": [
                                    {
                                        "name": "productId",
                                        "label": "商品ID",
                                        "type": "input",
                                        "required": false,
                                        "group": "condition",
                                        "description": null,
                                        "validation": null,
                                        "default": null
                                    },
                                    {
                                        "name": "requiredQuantity",
                                        "label": "所需数量",
                                        "type": "number",
                                        "required": false,
                                        "group": "condition",
                                        "description": null,
                                        "validation": null,
                                        "default": null
                                    },
                                    {
                                        "name": "requiredAmount",
                                        "label": "所需金额",
                                        "type": "number",
                                        "required": false,
                                        "group": "condition",
                                        "description": null,
                                        "validation": null,
                                        "default": null
                                    }
                                ],
                                "category": "condition"
                            },
                            "defaultItem": {
                                "productId": "",
                                "requiredQuantity": 0,
                                "requiredAmount": 0
                            },
                            "operations": ["add", "remove", "edit", "reorder"]
                        },
                        "isNullable": false,
                        "elementType": "CombinationSpecialPriceCondition",
                        "typeName": null
                    };

                    // 初始化一些测试数据
                    testValue.value = [
                        {
                            productId: "PROD001",
                            requiredQuantity: 2,
                            requiredAmount: 100.0
                        },
                        {
                            productId: "PROD002", 
                            requiredQuantity: 1,
                            requiredAmount: 50.0
                        }
                    ];
                };

                const handleUpdate = (newValue) => {
                    console.log('字段值更新：', newValue);
                    testValue.value = newValue;
                    ElMessage.success('字段值已更新');
                };

                onMounted(() => {
                    initTestField();
                });

                return {
                    testField,
                    testValue,
                    handleUpdate
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
