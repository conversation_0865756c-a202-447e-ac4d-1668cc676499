//using PE2.Models;
//using PE2.PromotionEngine.Models;
//using PE2.PromotionEngine.Calculators;
//using PE2.Services;
//using PE2.Tests.Infrastructure;
//using Xunit;
//using Xunit.Abstractions;

//namespace PE2.Tests.Unit;

///// <summary>
///// 促销计算器单元测试类
///// 测试 PromotionCalculator 的核心算法逻辑，包括回溯算法、规则筛选、折扣分摊等
///// </summary>
//public class PromotionCalculatorTests : TestBase
//{
//    private readonly PromotionCalculator _calculator;

//    public PromotionCalculatorTests(ITestOutputHelper output) : base(output)
//    {
//        _calculator = new PromotionCalculator();
//    }

//    #region 核心算法测试

//    [Fact]
//    [Trait("Category", "Unit")]
//    [Trait("Priority", "High")]
//    public void FindBestCombination_SingleRule_ShouldReturnOptimalResult()
//    {
//        // Arrange
//        var rule = TestDataGenerator.CreateUnifiedDiscountRule_Buy3Get20PercentOff();
//        var cart = TestDataGenerator.CreateCustomCart("TEST_CART", "CUSTOMER_001",
//            (TestDataGenerator.CreateProductA(), 3) // 恰好满足条件
//        );
//        var rules = new List<PromotionRuleBase> { rule };

//        ValidateTestData(cart, rules);
//        LogCartDetails(cart, "单规则最优组合测试");

//        // Act
//        var result = _calculator.FindBestCombination(cart, rules);

//        // Assert
//        AssertPromotionResult(result, "单规则最优组合");
//        LogPromotionResultDetails(result);

//        Assert.Single(result.AppliedRules);
//        AssertRuleApplication(result.AppliedRules[0], rule.Id);

//        var expectedDiscount = TestDataGenerator.CreateProductA().Price * 3 * 0.2m; // 20%折扣
//        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "单规则应计算正确的优惠金额");
//    }

//    [Fact]
//    [Trait("Category", "Unit")]
//    [Trait("Priority", "High")]
//    public void FindBestCombination_MultipleRules_ShouldChooseOptimal()
//    {
//        // Arrange - 两个规则，应选择优惠更大的
//        var rule1 = TestDataGenerator.CreateUnifiedDiscountRule_Buy3Get20PercentOff(); // 20%折扣
//        var rule2 = new UnifiedDiscountRule
//        {
//            Id = "BETTER_DISCOUNT_RULE",
//            Name = "更好的折扣规则",
//            Priority = 80,
//            IsEnabled = true,
//            DiscountConditions = new List<DiscountCondition>
//            {
//                new()
//                {
//                    ProductIds = new List<string> { "A" },
//                    RequiredQuantity = 3,
//                    DiscountRate = 0.3m // 30%折扣，更优惠
//                }
//            }
//        };

//        var cart = TestDataGenerator.CreateCustomCart("TEST_CART", "CUSTOMER_002",
//            (TestDataGenerator.CreateProductA(), 3)
//        );
//        var rules = new List<PromotionRuleBase> { rule1, rule2 };

//        LogCartDetails(cart, "多规则最优选择测试");

//        // Act
//        var result = _calculator.FindBestCombination(cart, rules);

//        // Assert
//        AssertPromotionResult(result, "多规则最优选择");
//        LogPromotionResultDetails(result);

//        // 应选择优惠更大的规则2
//        Assert.Single(result.AppliedRules);
//        AssertRuleApplication(result.AppliedRules[0], rule2.Id);

//        var expectedDiscount = TestDataGenerator.CreateProductA().Price * 3 * 0.3m; // 30%折扣
//        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "应选择优惠更大的规则");

//        // 验证另一个规则被忽略
//        Assert.Single(result.IgnoredRules);
//        AssertIgnoredRule(result.IgnoredRules[0], "更优组合");
//    }

//    [Fact]
//    [Trait("Category", "Unit")]
//    [Trait("Priority", "High")]
//    public void FindBestCombination_StackableRules_ShouldCombineOptimally()
//    {
//        // Arrange - 可叠加规则组合
//        var specialPriceRule = TestDataGenerator.CreateSpecialPriceRule_ProductASpecialPrice();
//        specialPriceRule.CanStack = true;

//        var cashOffRule = TestDataGenerator.CreateUnifiedCashOffRule_100Minus20();
//        cashOffRule.CanStack = true;

//        var cart = TestDataGenerator.CreateCustomCart("TEST_CART", "CUSTOMER_STACKABLE",
//            (TestDataGenerator.CreateProductA(), 6) // 满足特价和减现条件
//        );
//        var rules = new List<PromotionRuleBase> { specialPriceRule, cashOffRule };

//        LogCartDetails(cart, "可叠加规则组合测试");

//        // Act
//        var result = _calculator.FindBestCombination(cart, rules);

//        // Assert
//        AssertPromotionResult(result, "可叠加规则组合");
//        LogPromotionResultDetails(result);

//        // 两个规则都应被应用
//        Assert.Equal(2, result.AppliedRules.Count);
//        Assert.Contains(result.AppliedRules, r => r.RuleId == specialPriceRule.Id);
//        Assert.Contains(result.AppliedRules, r => r.RuleId == cashOffRule.Id);

//        // 验证叠加效果
//        var totalDiscount = result.AppliedRules.Sum(r => r.DiscountAmount);
//        AssertAmountEqual(totalDiscount, result.TotalDiscount, "叠加规则总优惠应等于各规则优惠之和");
//    }

//    [Fact]
//    [Trait("Category", "Unit")]
//    [Trait("Priority", "High")]
//    public void FindBestCombination_MutuallyExclusiveRules_ShouldChooseByPriority()
//    {
//        // Arrange - 互斥规则，应按优先级选择
//        var rule1 = TestDataGenerator.CreateUnifiedDiscountRule_Buy3Get20PercentOff();
//        rule1.Priority = 60;
//        rule1.MutuallyExclusiveRules = new List<string> { "HIGH_PRIORITY_RULE" };

//        var rule2 = new UnifiedDiscountRule
//        {
//            Id = "HIGH_PRIORITY_RULE",
//            Name = "高优先级规则",
//            Priority = 90, // 更高优先级
//            IsEnabled = true,
//            MutuallyExclusiveRules = new List<string> { rule1.Id },
//            DiscountConditions = new List<DiscountCondition>
//            {
//                new()
//                {
//                    ProductIds = new List<string> { "A" },
//                    RequiredQuantity = 3,
//                    DiscountRate = 0.15m // 较小的折扣，但优先级高
//                }
//            }
//        };

//        var cart = TestDataGenerator.CreateCustomCart("TEST_CART", "CUSTOMER_EXCLUSIVE",
//            (TestDataGenerator.CreateProductA(), 3)
//        );
//        var rules = new List<PromotionRuleBase> { rule1, rule2 };

//        LogCartDetails(cart, "互斥规则优先级测试");

//        // Act
//        var result = _calculator.FindBestCombination(cart, rules);

//        // Assert
//        AssertPromotionResult(result, "互斥规则优先级选择");
//        LogPromotionResultDetails(result);

//        // 应选择优先级更高的规则2
//        Assert.Single(result.AppliedRules);
//        AssertRuleApplication(result.AppliedRules[0], rule2.Id);

//        // 验证低优先级规则被忽略
//        Assert.Single(result.IgnoredRules);
//        var ignoredRule = result.IgnoredRules.First();
//        Assert.Equal(rule1.Id, ignoredRule.RuleId);
//        Assert.Contains("互斥", ignoredRule.Reason);
//    }

//    #endregion

//    #region 规则筛选测试

//    [Fact]
//    [Trait("Category", "Unit")]
//    [Trait("Priority", "Medium")]
//    public void FilterApplicableRules_EnabledRules_ShouldReturnOnlyEnabled()
//    {
//        // Arrange
//        var enabledRule = TestDataGenerator.CreateUnifiedDiscountRule_Buy3Get20PercentOff();
//        enabledRule.IsEnabled = true;

//        var disabledRule = TestDataGenerator.CreateUnifiedGiftRule_Buy2Get1();
//        disabledRule.IsEnabled = false;

//        var cart = TestDataGenerator.CreateCustomCart("TEST_CART", "CUSTOMER_FILTER",
//            (TestDataGenerator.CreateProductA(), 3),
//            (TestDataGenerator.CreateProductB(), 1)
//        );
//        var rules = new List<PromotionRuleBase> { enabledRule, disabledRule };

//        // Act
//        var result = _calculator.FindBestCombination(cart, rules);

//        // Assert
//        AssertPromotionResult(result, "规则启用状态筛选");

//        // 只有启用的规则被应用
//        Assert.Single(result.AppliedRules);
//        AssertRuleApplication(result.AppliedRules[0], enabledRule.Id);

//        // 禁用的规则被忽略
//        Assert.Single(result.IgnoredRules);
//        var ignoredRule = result.IgnoredRules.First();
//        Assert.Equal(disabledRule.Id, ignoredRule.RuleId);
//        AssertIgnoredRule(ignoredRule, "规则未启用");
//    }

//    [Fact]
//    [Trait("Category", "Unit")]
//    [Trait("Priority", "Medium")]
//    public void FilterApplicableRules_CustomerTypeRestriction_ShouldFilterByCustomerType()
//    {
//        // Arrange
//        var generalRule = TestDataGenerator.CreateUnifiedDiscountRule_Buy3Get20PercentOff();
//        generalRule.ApplicableCustomerTypes = null; // 适用于所有客户

//        var vipRule = new UnifiedDiscountRule
//        {
//            Id = "VIP_RULE",
//            Name = "VIP专享规则",
//            Priority = 80,
//            IsEnabled = true,
//            ApplicableCustomerTypes = new List<string> { "VIP", "PREMIUM" },
//            DiscountConditions = new List<DiscountCondition>
//            {
//                new()
//                {
//                    ProductIds = new List<string> { "A" },
//                    RequiredQuantity = 2,
//                    DiscountRate = 0.3m
//                }
//            }
//        };

//        var regularCart = TestDataGenerator.CreateCustomCart("REGULAR_CART", "REGULAR_CUSTOMER",
//            (TestDataGenerator.CreateProductA(), 3)
//        );
//        regularCart.CustomerType = "REGULAR";

//        var vipCart = TestDataGenerator.CreateCustomCart("VIP_CART", "VIP_CUSTOMER",
//            (TestDataGenerator.CreateProductA(), 3)
//        );
//        vipCart.CustomerType = "VIP";

//        var rules = new List<PromotionRuleBase> { generalRule, vipRule };

//        // Act
//        var regularResult = _calculator.FindBestCombination(regularCart, rules);
//        var vipResult = _calculator.FindBestCombination(vipCart, rules);

//        // Assert
//        // 普通客户只能使用通用规则
//        AssertPromotionResult(regularResult, "普通客户规则筛选");
//        Assert.Single(regularResult.AppliedRules);
//        AssertRuleApplication(regularResult.AppliedRules[0], generalRule.Id);

//        Assert.Single(regularResult.IgnoredRules);
//        AssertIgnoredRule(regularResult.IgnoredRules[0], "客户类型不符");

//        // VIP客户应选择更优惠的VIP规则
//        AssertPromotionResult(vipResult, "VIP客户规则筛选");
//        Assert.Single(vipResult.AppliedRules);
//        AssertRuleApplication(vipResult.AppliedRules[0], vipRule.Id);

//        // VIP规则优惠更大
//        Assert.True(vipResult.TotalDiscount > regularResult.TotalDiscount, "VIP规则应提供更大优惠");
//    }

//    [Fact]
//    [Trait("Category", "Unit")]
//    [Trait("Priority", "Medium")]
//    public void FilterApplicableRules_DateTimeRestriction_ShouldFilterByDateTime()
//    {
//        // Arrange
//        var currentTime = DateTime.Now;

//        var activeRule = TestDataGenerator.CreateUnifiedDiscountRule_Buy3Get20PercentOff();
//        activeRule.StartDate = currentTime.AddDays(-1); // 昨天开始
//        activeRule.EndDate = currentTime.AddDays(1);    // 明天结束

//        var expiredRule = new UnifiedDiscountRule
//        {
//            Id = "EXPIRED_RULE",
//            Name = "过期规则",
//            Priority = 80,
//            IsEnabled = true,
//            StartDate = currentTime.AddDays(-10),
//            EndDate = currentTime.AddDays(-1), // 昨天已过期
//            DiscountConditions = new List<DiscountCondition>
//            {
//                new()
//                {
//                    ProductIds = new List<string> { "A" },
//                    RequiredQuantity = 2,
//                    DiscountRate = 0.5m
//                }
//            }
//        };

//        var futureRule = new UnifiedDiscountRule
//        {
//            Id = "FUTURE_RULE",
//            Name = "未来规则",
//            Priority = 90,
//            IsEnabled = true,
//            StartDate = currentTime.AddDays(1),  // 明天开始
//            EndDate = currentTime.AddDays(10),
//            DiscountConditions = new List<DiscountCondition>
//            {
//                new()
//                {
//                    ProductIds = new List<string> { "A" },
//                    RequiredQuantity = 2,
//                    DiscountRate = 0.4m
//                }
//            }
//        };

//        var cart = TestDataGenerator.CreateCustomCart("TEST_CART", "CUSTOMER_DATETIME",
//            (TestDataGenerator.CreateProductA(), 3)
//        );
//        var rules = new List<PromotionRuleBase> { activeRule, expiredRule, futureRule };

//        // Act
//        var result = _calculator.FindBestCombination(cart, rules);

//        // Assert
//        AssertPromotionResult(result, "日期时间筛选");

//        // 只有当前有效的规则被应用
//        Assert.Single(result.AppliedRules);
//        AssertRuleApplication(result.AppliedRules[0], activeRule.Id);

//        // 过期和未来的规则被忽略
//        Assert.Equal(2, result.IgnoredRules.Count);

//        var expiredIgnored = result.IgnoredRules.FirstOrDefault(r => r.RuleId == expiredRule.Id);
//        Assert.NotNull(expiredIgnored);
//        AssertIgnoredRule(expiredIgnored, "规则已过期");

//        var futureIgnored = result.IgnoredRules.FirstOrDefault(r => r.RuleId == futureRule.Id);
//        Assert.NotNull(futureIgnored);
//        AssertIgnoredRule(futureIgnored, "规则未生效");
//    }

//    #endregion

//    #region 折扣分摊测试

//    [Fact]
//    [Trait("Category", "Unit")]
//    [Trait("Priority", "Medium")]
//    public void ApplyDiscountDistribution_MultipleItems_ShouldDistributeProportionally()
//    {
//        // Arrange - 多商品按比例分摊折扣
//        var rule = new UnifiedCashOffRule
//        {
//            Id = "CASH_OFF_DISTRIBUTION_TEST",
//            Name = "减现分摊测试",
//            Priority = 70,
//            IsEnabled = true,
//            CashOffConditions = new List<CashOffCondition>
//            {
//                new()
//                {
//                    ProductIds = new List<string> { "A", "B" },
//                    RequiredAmount = 100m,
//                    CashOffAmount = 20m
//                }
//            }
//        };

//        var cart = TestDataGenerator.CreateCustomCart("TEST_CART", "CUSTOMER_DISTRIBUTION",
//            (TestDataGenerator.CreateProductA(), 2), // 价值 60元 (30*2)
//            (TestDataGenerator.CreateProductB(), 2)  // 价值 40元 (20*2)
//        ); // 总计100元，满足减现条件

//        LogCartDetails(cart, "折扣分摊测试购物车");

//        // Act
//        var result = _calculator.FindBestCombination(cart, new List<PromotionRuleBase> { rule });

//        // Assert
//        AssertPromotionResult(result, "折扣分摊");
//        LogPromotionResultDetails(result);

//        Assert.Single(result.AppliedRules);
//        AssertAmountEqual(20m, result.TotalDiscount, "总折扣应为20元");

//        // 验证折扣按比例分摊
//        var cartAfter = result.CartAfterPromotion;
//        var itemA = cartAfter.Items.First(i => i.Product.Id == "A");
//        var itemB = cartAfter.Items.First(i => i.Product.Id == "B");

//        // A商品应分摊 20 * (60/100) = 12元折扣
//        var expectedDiscountA = 20m * (60m / 100m);
//        var actualDiscountA = (itemA.UnitPrice - itemA.ActualUnitPrice) * itemA.Quantity;
//        AssertAmountEqual(expectedDiscountA, actualDiscountA, "商品A折扣分摊");

//        // B商品应分摊 20 * (40/100) = 8元折扣
//        var expectedDiscountB = 20m * (40m / 100m);
//        var actualDiscountB = (itemB.UnitPrice - itemB.ActualUnitPrice) * itemB.Quantity;
//        AssertAmountEqual(expectedDiscountB, actualDiscountB, "商品B折扣分摊");
//    }

//    [Fact]
//    [Trait("Category", "Unit")]
//    [Trait("Priority", "Medium")]
//    public void ApplyDiscountDistribution_SpecificProducts_ShouldOnlyAffectTargetProducts()
//    {
//        // Arrange - 指定商品折扣，不影响其他商品
//        var rule = new UnifiedDiscountRule
//        {
//            Id = "SPECIFIC_PRODUCT_DISCOUNT",
//            Name = "指定商品折扣",
//            Priority = 70,
//            IsEnabled = true,
//            DiscountConditions = new List<DiscountCondition>
//            {
//                new()
//                {
//                    ProductIds = new List<string> { "A" }, // 只对商品A打折
//                    RequiredQuantity = 2,
//                    DiscountRate = 0.2m
//                }
//            }
//        };

//        var cart = TestDataGenerator.CreateCustomCart("TEST_CART", "CUSTOMER_SPECIFIC",
//            (TestDataGenerator.CreateProductA(), 3), // 商品A，应受折扣影响
//            (TestDataGenerator.CreateProductB(), 2)  // 商品B，不应受影响
//        );

//        var originalPriceB = TestDataGenerator.CreateProductB().Price;

//        // Act
//        var result = _calculator.FindBestCombination(cart, new List<PromotionRuleBase> { rule });

//        // Assert
//        AssertPromotionResult(result, "指定商品折扣");

//        var cartAfter = result.CartAfterPromotion;
//        var itemA = cartAfter.Items.First(i => i.Product.Id == "A");
//        var itemB = cartAfter.Items.First(i => i.Product.Id == "B");

//        // 商品A应有折扣
//        Assert.True(itemA.ActualUnitPrice < itemA.UnitPrice, "商品A应有折扣");

//        // 商品B不应受影响
//        AssertAmountEqual(originalPriceB, itemB.ActualUnitPrice, "商品B价格不应变化");
//        AssertAmountEqual(itemB.UnitPrice, itemB.ActualUnitPrice, "商品B原价与实际价格应相等");
//    }

//    #endregion

//    #region 回溯算法测试

//    [Fact]
//    [Trait("Category", "Unit")]
//    [Trait("Priority", "High")]
//    public void BacktrackingAlgorithm_ComplexCombination_ShouldFindOptimal()
//    {
//        // Arrange - 复杂规则组合，测试回溯算法
//        var rules = new List<PromotionRuleBase>
//        {
//            // 规则1: 买3件A打8折
//            new UnifiedDiscountRule
//            {
//                Id = "RULE_1",
//                Name = "A商品3件8折",
//                Priority = 60,
//                IsEnabled = true,
//                DiscountConditions = new List<DiscountCondition>
//                {
//                    new() { ProductIds = new List<string> { "A" }, RequiredQuantity = 3, DiscountRate = 0.2m }
//                }
//            },
//            // 规则2: 买2件A送1件B
//            TestDataGenerator.CreateUnifiedGiftRule_Buy2Get1(),
//            // 规则3: 满100减20
//            TestDataGenerator.CreateUnifiedCashOffRule_100Minus20()
//        };

//        var cart = TestDataGenerator.CreateCustomCart("BACKTRACK_TEST", "CUSTOMER_BACKTRACK",
//            (TestDataGenerator.CreateProductA(), 4), // 4件A
//            (TestDataGenerator.CreateProductB(), 2)  // 2件B
//        );

//        LogCartDetails(cart, "回溯算法测试购物车");
//        Output.WriteLine($"测试规则数量: {rules.Count}");

//        // Act
//        var result = _calculator.FindBestCombination(cart, rules);

//        // Assert
//        AssertPromotionResult(result, "回溯算法复杂组合");
//        LogPromotionResultDetails(result);

//        // 验证找到了最优组合
//        Assert.NotEmpty(result.AppliedRules);
//        Assert.True(result.TotalDiscount > 0, "应找到最优的促销组合");

//        // 验证所有规则都被考虑
//        var totalRulesConsidered = result.AppliedRules.Count + result.IgnoredRules.Count;
//        Assert.Equal(rules.Count, totalRulesConsidered);

//        // 验证结果的合理性
//        foreach (var appliedRule in result.AppliedRules)
//        {
//            Assert.True(appliedRule.DiscountAmount > 0, "应用的规则应产生正向优惠");
//        }
//    }

//    [Fact]
//    [Trait("Category", "Unit")]
//    [Trait("Priority", "Medium")]
//    public void BacktrackingAlgorithm_NoValidCombination_ShouldReturnEmpty()
//    {
//        // Arrange - 没有满足条件的规则
//        var rule = new UnifiedDiscountRule
//        {
//            Id = "IMPOSSIBLE_RULE",
//            Name = "不可能满足的规则",
//            Priority = 70,
//            IsEnabled = true,
//            DiscountConditions = new List<DiscountCondition>
//            {
//                new()
//                {
//                    ProductIds = new List<string> { "A" },
//                    RequiredQuantity = 100, // 需要100件A，不可能满足
//                    DiscountRate = 0.5m
//                }
//            }
//        };

//        var cart = TestDataGenerator.CreateCustomCart("NO_VALID_TEST", "CUSTOMER_NO_VALID",
//            (TestDataGenerator.CreateProductA(), 2) // 只有2件A
//        );

//        // Act
//        var result = _calculator.FindBestCombination(cart, new List<PromotionRuleBase> { rule });

//        // Assert
//        AssertPromotionResult(result, "无有效组合");

//        Assert.Empty(result.AppliedRules);
//        Assert.Single(result.IgnoredRules);
//        AssertAmountEqual(0m, result.TotalDiscount, "无有效规则时优惠应为0");

//        var ignoredRule = result.IgnoredRules.First();
//        AssertIgnoredRule(ignoredRule, "条件不满足");
//    }

//    #endregion

//    #region 边界条件测试

//    [Fact]
//    [Trait("Category", "Unit")]
//    [Trait("Priority", "Low")]
//    public void FindBestCombination_EmptyCart_ShouldReturnEmptyResult()
//    {
//        // Arrange
//        var rules = new List<PromotionRuleBase>
//        {
//            TestDataGenerator.CreateUnifiedDiscountRule_Buy3Get20PercentOff(),
//            TestDataGenerator.CreateUnifiedGiftRule_Buy2Get1()
//        };
//        var emptyCart = TestDataGenerator.CreateEmptyCart();

//        // Act
//        var result = _calculator.FindBestCombination(emptyCart, rules);

//        // Assert
//        AssertPromotionResult(result, "空购物车");
//        Assert.Empty(result.AppliedRules);
//        Assert.Equal(rules.Count, result.IgnoredRules.Count);
//        AssertAmountEqual(0m, result.TotalDiscount, "空购物车优惠应为0");
//    }

//    [Fact]
//    [Trait("Category", "Unit")]
//    [Trait("Priority", "Low")]
//    public void FindBestCombination_NoRules_ShouldReturnOriginalCart()
//    {
//        // Arrange
//        var cart = TestDataGenerator.CreateCustomCart("TEST_CART", "CUSTOMER_NO_RULES",
//            (TestDataGenerator.CreateProductA(), 3)
//        );
//        var emptyRules = new List<PromotionRuleBase>();

//        // Act
//        var result = _calculator.FindBestCombination(cart, emptyRules);

//        // Assert
//        AssertPromotionResult(result, "无规则");
//        Assert.Empty(result.AppliedRules);
//        Assert.Empty(result.IgnoredRules);
//        AssertAmountEqual(0m, result.TotalDiscount, "无规则优惠应为0");

//        // 验证购物车未被修改
//        Assert.Equal(cart.Items.Count, result.CartAfterPromotion.Items.Count);
//        for (int i = 0; i < cart.Items.Count; i++)
//        {
//            var original = cart.Items[i];
//            var result_item = result.CartAfterPromotion.Items[i];
//            AssertAmountEqual(original.UnitPrice, result_item.ActualUnitPrice, "无规则时价格不应变化");
//        }
//    }

//    [Fact]
//    [Trait("Category", "Unit")]
//    [Trait("Priority", "Low")]
//    public void FindBestCombination_SingleItemCart_ShouldHandleCorrectly()
//    {
//        // Arrange
//        var rule = TestDataGenerator.CreateUnifiedDiscountRule_Buy3Get20PercentOff();
//        var singleItemCart = TestDataGenerator.CreateCustomCart("SINGLE_ITEM", "CUSTOMER_SINGLE",
//            (TestDataGenerator.CreateProductA(), 1) // 只有1件，不满足买3件的条件
//        );

//        // Act
//        var result = _calculator.FindBestCombination(singleItemCart, new List<PromotionRuleBase> { rule });

//        // Assert
//        AssertPromotionResult(result, "单商品购物车");
//        Assert.Empty(result.AppliedRules);
//        Assert.Single(result.IgnoredRules);
//        AssertIgnoredRule(result.IgnoredRules[0], "条件不满足");
//        AssertAmountEqual(0m, result.TotalDiscount, "不满足条件时优惠应为0");
//    }

//    #endregion

//    #region 性能测试

//    [Fact]
//    [Trait("Category", "Unit")]
//    [Trait("Priority", "Low")]
//    public void FindBestCombination_ManyRules_ShouldPerformWell()
//    {
//        // Arrange - 大量规则性能测试
//        var rules = new List<PromotionRuleBase>();

//        // 创建20个不同的折扣规则
//        for (int i = 1; i <= 20; i++)
//        {
//            rules.Add(new UnifiedDiscountRule
//            {
//                Id = $"PERF_RULE_{i}",
//                Name = $"性能测试规则{i}",
//                Priority = 50 + i,
//                IsEnabled = true,
//                DiscountConditions = new List<DiscountCondition>
//                {
//                    new()
//                    {
//                        ProductIds = new List<string> { "A", "B", "C" },
//                        RequiredQuantity = i,
//                        DiscountRate = 0.05m * i
//                    }
//                }
//            });
//        }

//        var cart = TestDataGenerator.CreateCustomCart("PERF_TEST", "CUSTOMER_PERF",
//            (TestDataGenerator.CreateProductA(), 25),
//            (TestDataGenerator.CreateProductB(), 20),
//            (TestDataGenerator.CreateProductC(), 15)
//        );

//        Output.WriteLine($"性能测试: {rules.Count} 个规则, {cart.Items.Sum(i => i.Quantity)} 件商品");

//        // Act & Assert
//        var executionTime = MeasureExecutionTime(() =>
//        {
//            var result = _calculator.FindBestCombination(cart, rules);
//            AssertPromotionResult(result, "大量规则性能测试");

//            // 验证结果合理性
//            Assert.True(result.AppliedRules.Count + result.IgnoredRules.Count == rules.Count,
//                "所有规则都应被处理");
//        });

//        // 性能断言：大量规则计算应在合理时间内完成
//        AssertPerformance(executionTime, TimeSpan.FromSeconds(1), "大量规则计算");
//    }

//    [Fact]
//    [Trait("Category", "Unit")]
//    [Trait("Priority", "Low")]
//    public void FindBestCombination_ComplexCart_ShouldPerformWell()
//    {
//        // Arrange - 复杂购物车性能测试
//        var rules = new List<PromotionRuleBase>
//        {
//            TestDataGenerator.CreateUnifiedGiftRule_Buy2Get1(),
//            TestDataGenerator.CreateTieredGiftRule_MultiTier(),
//            TestDataGenerator.CreateUnifiedDiscountRule_Buy3Get20PercentOff(),
//            TestDataGenerator.CreateTieredDiscountRule_MultiTier(),
//            TestDataGenerator.CreateUnifiedCashOffRule_100Minus20()
//        };

//        var complexCart = TestDataGenerator.PerformanceTestData.CreateLargeCart(100);

//        // 添加更多商品以触发规则
//        complexCart.Items.AddRange(new[]
//        {
//            new CartItem
//            {
//                Product = TestDataGenerator.CreateProductA(),
//                Quantity = 20,
//                UnitPrice = TestDataGenerator.CreateProductA().Price
//            },
//            new CartItem
//            {
//                Product = TestDataGenerator.CreateProductB(),
//                Quantity = 15,
//                UnitPrice = TestDataGenerator.CreateProductB().Price
//            }
//        });
//        complexCart.InitializeActualPrices();

//        Output.WriteLine($"复杂购物车性能测试: {complexCart.Items.Count} 个商品项, {rules.Count} 个规则");

//        // Act & Assert
//        var executionTime = MeasureExecutionTime(() =>
//        {
//            var result = _calculator.FindBestCombination(complexCart, rules);
//            AssertPromotionResult(result, "复杂购物车性能测试");
//        });

//        // 性能断言：复杂购物车计算应在合理时间内完成
//        AssertPerformance(executionTime, TimeSpan.FromMilliseconds(500), "复杂购物车计算");
//    }

//    #endregion

//    #region 数据一致性验证

//    [Fact]
//    [Trait("Category", "Unit")]
//    [Trait("Priority", "Medium")]
//    public void FindBestCombination_ResultConsistency_ShouldMaintainDataIntegrity()
//    {
//        // Arrange
//        var rules = new List<PromotionRuleBase>
//        {
//            TestDataGenerator.CreateUnifiedDiscountRule_Buy3Get20PercentOff(),
//            TestDataGenerator.CreateUnifiedCashOffRule_100Minus20()
//        };

//        var cart = TestDataGenerator.CreateCustomCart("CONSISTENCY_TEST", "CUSTOMER_CONSISTENCY",
//            (TestDataGenerator.CreateProductA(), 5),
//            (TestDataGenerator.CreateProductB(), 3)
//        );

//        var originalTotal = cart.Items.Sum(i => i.UnitPrice * i.Quantity);
//        var originalItemCount = cart.Items.Count;
//        var originalQuantity = cart.Items.Sum(i => i.Quantity);

//        // Act
//        var result = _calculator.FindBestCombination(cart, rules);

//        // Assert
//        AssertPromotionResult(result, "数据一致性验证");

//        // 验证购物车基本结构一致性
//        Assert.Equal(originalItemCount, result.CartAfterPromotion.Items.Count);
//        Assert.Equal(originalQuantity, result.CartAfterPromotion.Items.Sum(i => i.Quantity));

//        // 验证金额计算一致性
//        var finalTotal = result.CartAfterPromotion.Items.Sum(i => i.ActualUnitPrice * i.Quantity);
//        var expectedTotal = originalTotal - result.TotalDiscount;
//        AssertAmountEqual(expectedTotal, finalTotal, "最终总金额应等于原始总金额减去优惠金额");

//        // 验证优惠金额非负
//        Assert.True(result.TotalDiscount >= 0, "总优惠金额不应为负数");

//        // 验证每个商品的价格合理性
//        foreach (var item in result.CartAfterPromotion.Items)
//        {
//            Assert.True(item.ActualUnitPrice >= 0, "商品实际价格不应为负数");
//            Assert.True(item.ActualUnitPrice <= item.UnitPrice, "实际价格不应超过原价");
//        }

//        // 验证应用规则的优惠金额总和
//        var appliedRulesDiscount = result.AppliedRules.Sum(r => r.DiscountAmount);
//        AssertAmountEqual(appliedRulesDiscount, result.TotalDiscount, "应用规则优惠总和应等于总优惠");

//        Output.WriteLine("数据一致性验证通过 ✓");
//    }

//    #endregion
//}