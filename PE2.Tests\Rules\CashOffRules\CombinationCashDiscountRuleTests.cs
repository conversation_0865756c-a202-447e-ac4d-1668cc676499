using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.CashOffRules;

/// <summary>
/// 组合减现规则单元测试
/// 测试组合减现规则在各种场景下的正确性
/// </summary>
public class CombinationCashDiscountRuleTests : TestBase
{
    public CombinationCashDiscountRuleTests(ITestOutputHelper output) : base(output)
    {
    }

    #region 基础功能测试

    /// <summary>
    /// 测试基础组合减现 - 满足所有条件
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Basic")]
    public async Task Apply_BasicCombination_ValidConditions_ShouldApplyCorrectly()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateCombinationCashDiscountRule_AB();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_001", "CUSTOMER_001",
            (TestDataGenerator.CreateProductA(), 2), // A商品2件，满足条件
            (TestDataGenerator.CreateProductB(), 1)  // B商品1件，满足条件
        );

        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "基础组合减现");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(rule.Id, result.AppliedPromotions[0].RuleId);
        Assert.Equal(15.00m, result.TotalDiscount); // A商品2件+B商品1件，减15元
        Assert.Equal(115.00m, result.FinalAmount); // 原价130元，减15元
    }

    /// <summary>
    /// 测试基于金额的组合减现
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Basic")]
    public async Task Apply_AmountBasedCombination_ShouldApplyCorrectly()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateCombinationCashDiscountRule_AmountBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_002", "CUSTOMER_002",
            (TestDataGenerator.CreateProductA(), 3), // A商品3件，总价150元，满足满100元条件
            (TestDataGenerator.CreateProductB(), 2)  // B商品2件，总价60元，满足满50元条件
        );

        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "基于金额的组合减现");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(rule.Id, result.AppliedPromotions[0].RuleId);
        Assert.Equal(25.00m, result.TotalDiscount); // A商品满100元+B商品满50元，减25元
        Assert.Equal(185.00m, result.FinalAmount); // 原价210元，减25元
    }

    /// <summary>
    /// 测试复杂组合条件
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Basic")]
    public async Task Apply_ComplexCombination_ShouldApplyCorrectly()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateCombinationCashDiscountRule_Complex();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_003", "CUSTOMER_003",
            (TestDataGenerator.CreateProductA(), 3), // A商品3件，满足条件
            (TestDataGenerator.CreateProductB(), 2), // B商品2件，满足条件
            (TestDataGenerator.CreateProductC(), 1)  // C商品1件，满足条件
        );

        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "复杂组合条件");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(rule.Id, result.AppliedPromotions[0].RuleId);
        Assert.Equal(30.00m, result.TotalDiscount); // A+B+C组合，减30元
        Assert.Equal(200.00m, result.FinalAmount); // 原价230元，减30元
    }

    #endregion

    #region 条件不满足测试

    /// <summary>
    /// 测试组合条件不足 - A商品数量不够
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Insufficient")]
    public async Task Apply_InsufficientProductA_ShouldNotApply()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateCombinationCashDiscountRule_AB();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_004", "CUSTOMER_004",
            (TestDataGenerator.CreateProductA(), 1), // A商品1件，不满足2件条件
            (TestDataGenerator.CreateProductB(), 1)  // B商品1件，满足条件
        );

        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "A商品数量不足场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0m, result.TotalDiscount);
        Assert.Equal(80.00m, result.FinalAmount); // 原价80元，无折扣
    }

    /// <summary>
    /// 测试组合条件不足 - B商品缺失
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Insufficient")]
    public async Task Apply_MissingProductB_ShouldNotApply()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateCombinationCashDiscountRule_AB();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_005", "CUSTOMER_005",
            (TestDataGenerator.CreateProductA(), 2), // A商品2件，满足条件
            (TestDataGenerator.CreateProductC(), 1)  // C商品1件，不是B商品
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "B商品缺失场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0m, result.TotalDiscount);
        Assert.Equal(120.00m, result.FinalAmount); // 原价120元，无折扣
    }

    /// <summary>
    /// 测试金额条件不足
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Insufficient")]
    public async Task Apply_InsufficientAmount_ShouldNotApply()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateCombinationCashDiscountRule_AmountBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_006", "CUSTOMER_006",
            (TestDataGenerator.CreateProductA(), 1), // A商品1件，总价50元，不满足满100元条件
            (TestDataGenerator.CreateProductB(), 2)  // B商品2件，总价60元，满足满50元条件
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "金额条件不足场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0m, result.TotalDiscount);
        Assert.Equal(110.00m, result.FinalAmount); // 原价110元，无折扣
    }

    #endregion

    #region 重复应用测试

    /// <summary>
    /// 测试可重复应用的组合规则
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Multiple")]
    public async Task Apply_RepeatableCombination_ShouldApplyMultipleTimes()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateCombinationCashDiscountRule_Repeatable();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_007", "CUSTOMER_007",
            (TestDataGenerator.CreateProductA(), 4), // A商品4件，可应用2次
            (TestDataGenerator.CreateProductB(), 2)  // B商品2件，可应用2次
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "可重复应用的组合规则");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(rule.Id, result.AppliedPromotions[0].RuleId);
        Assert.Equal(30.00m, result.TotalDiscount); // 应用2次，每次减15元，共减30元
        Assert.Equal(230.00m, result.FinalAmount); // 原价260元，减30元
    }

    /// <summary>
    /// 测试不可重复应用的组合规则
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Multiple")]
    public async Task Apply_NonRepeatableCombination_ShouldApplyOnce()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateCombinationCashDiscountRule_NonRepeatable();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_008", "CUSTOMER_008",
            (TestDataGenerator.CreateProductA(), 4), // A商品4件，但只能应用一次
            (TestDataGenerator.CreateProductB(), 2)  // B商品2件，但只能应用一次
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "不可重复应用的组合规则");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(rule.Id, result.AppliedPromotions[0].RuleId);
        Assert.Equal(15.00m, result.TotalDiscount); // 只应用一次，减15元
        Assert.Equal(245.00m, result.FinalAmount); // 原价260元，减15元
    }

    #endregion

    #region 部分满足测试

    /// <summary>
    /// 测试部分组合条件满足 - 按最小限制应用
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Partial")]
    public async Task Apply_PartialCombinationSatisfied_ShouldApplyByMinimumLimit()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateCombinationCashDiscountRule_Repeatable();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_009", "CUSTOMER_009",
            (TestDataGenerator.CreateProductA(), 6), // A商品6件，可应用3次
            (TestDataGenerator.CreateProductB(), 2)  // B商品2件，只能应用2次
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "部分组合条件满足");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(rule.Id, result.AppliedPromotions[0].RuleId);
        Assert.Equal(30.00m, result.TotalDiscount); // 受B商品限制，只能应用2次，减30元
        Assert.Equal(330.00m, result.FinalAmount); // 原价360元，减30元
    }

    #endregion

    #region 边界条件测试

    /// <summary>
    /// 测试空购物车 - 不应用规则
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Boundary")]
    public async Task Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateCombinationCashDiscountRule_AB();
        var cart = TestDataGenerator.CreateEmptyCart();

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "空购物车场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0m, result.TotalDiscount);
        Assert.Equal(0m, result.FinalAmount);
    }

    /// <summary>
    /// 测试禁用规则 - 不应用规则
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Boundary")]
    public async Task Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateCombinationCashDiscountRule_Disabled();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_010", "CUSTOMER_010",
            (TestDataGenerator.CreateProductA(), 2),
            (TestDataGenerator.CreateProductB(), 1)
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "禁用规则场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0m, result.TotalDiscount);
        Assert.Equal(130.00m, result.FinalAmount);
    }

    #endregion

    #region 复杂场景测试

    /// <summary>
    /// 测试多种组合条件混合 
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Complex")]
    public async Task Apply_MixedCombinationConditions_ShouldApplyCorrectly()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateCombinationCashDiscountRule_Mixed();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_011", "CUSTOMER_011",
            (TestDataGenerator.CreateProductA(), 3), // A商品3件，满足数量条件
            (TestDataGenerator.CreateProductB(), 4), // B商品4件，总价120元，满足金额条件
            (TestDataGenerator.CreateProductC(), 2)  // C商品2件，不在组合条件内
        );

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "混合组合条件");
        Assert.Single(result.AppliedPromotions);
        Assert.Equal(rule.Id, result.AppliedPromotions[0].RuleId);
        Assert.Equal(20.00m, result.TotalDiscount); // 混合条件满足，减20元
        Assert.Equal(250.00m, result.FinalAmount); // 原价270元，减20元
    }

    /// <summary>
    /// 测试与其他规则的交互
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Complex")]
    public async Task Apply_WithOtherRules_ShouldInteractCorrectly()
    {
        // Arrange - 准备测试数据
        var combinationRule = TestDataGenerator.CreateCombinationCashDiscountRule_AB();
        var unifiedRule = TestDataGenerator.CreateUnifiedCashDiscountRule_QuantityBased();
        var cart = TestDataGenerator.CreateCustomCart(
            "CART_012", "CUSTOMER_012",
            (TestDataGenerator.CreateProductA(), 3), // A商品3件
            (TestDataGenerator.CreateProductB(), 1)  // B商品1件
        );


        TestPromotionRuleService.Rules = [combinationRule, unifiedRule];
        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "与其他规则交互");
        Assert.True(result.AppliedPromotions.Count >= 1, "应至少应用一个规则");
        Assert.True(result.TotalDiscount > 0, "应有总折扣");
    }

    #endregion

    #region 性能测试

    /// <summary>
    /// 测试大购物车性能
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Performance")]
    public async Task Apply_LargeCart_ShouldPerformWell()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateCombinationCashDiscountRule_AB();
        var cart = TestDataGenerator.CreateLargeTestCart(100); // 大量商品的购物车

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        stopwatch.Stop();

        // Assert - 验证结果
        AssertPromotionResult(result, "大购物车性能测试");
        Assert.True(stopwatch.ElapsedMilliseconds < 1000, "处理时间应少于1秒");
        Output.WriteLine($"处理时间: {stopwatch.ElapsedMilliseconds}ms");
    }

    #endregion

    #region 配置验证测试

    /// <summary>
    /// 测试空组合条件 - 优雅处理
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Configuration")]
    public async Task Apply_EmptyCombinationConditions_ShouldHandleGracefully()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateCombinationCashDiscountRule_EmptyConditions();
        var cart = TestDataGenerator.CreateClassicTestCart();

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "空组合条件场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0m, result.TotalDiscount);
    }

    /// <summary>
    /// 测试无效组合配置 - 优雅处理
    /// </summary>
    [Fact]
    [Trait("Category", "CashDiscount")]
    [Trait("Type", "Configuration")]
    public async Task Apply_InvalidCombinationConfiguration_ShouldHandleGracefully()
    {
        // Arrange - 准备测试数据
        var rule = TestDataGenerator.CreateCombinationCashDiscountRule_InvalidConfig();
        var cart = TestDataGenerator.CreateClassicTestCart();

        var testService = new TestPromotionRuleService();
        TestPromotionRuleService.Rules = [rule];

        // Act - 执行测试
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert - 验证结果
        AssertPromotionResult(result, "无效组合配置场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Equal(0m, result.TotalDiscount);
    }

    #endregion
}