using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.DiscountRules;

/// <summary>
/// 统一折扣规则 [OK]
/// 针对某一类商品，满X件或X元，打Y折
/// 场景案例：购买A商品（吊牌，零售，应收价格均为1000元），当满足数量大于等于1件时，A商品享受应收金额的0.8折。
/// 购买1件A商品时，执行结果为1000*0.8=800元。
///
/// </summary>
public class UnifiedDiscountRule : BaseDiscountRule
{
    public override string RuleType => "UnifiedDiscount";

    /// <summary>
    /// 适用的商品ID列表
    /// </summary>
    public List<string> ApplicableProductIds { get; set; } = [];

    /// <summary>
    /// 最小数量要求
    /// </summary>
    public int MinQuantity { get; set; }

    /// <summary>
    /// 最小金额要求
    /// </summary>
    public decimal MinAmount { get; set; }

    /// <summary>
    /// 折扣率（0.8表示8折）
    /// </summary>
    public decimal DiscountRate { get; set; }

    /// <summary>
    /// 是否按金额计算（如果为true，则使用MinAmount；否则使用MinQuantity）
    /// </summary>
    public bool IsByAmount => MinAmount > 0;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!ApplicableProductIds.Any())
            return false;

        // 验证商品是否在购物车中
        if (!ValidateDiscountProductsInCart(cart, ApplicableProductIds))
            return false;

        // 检查购买条件
        return CheckBuyConditions(cart);
    }

    /// <summary>
    /// 检查购买条件
    /// </summary>
    private bool CheckBuyConditions(ShoppingCart cart)
    {
        if (IsByAmount)
        {
            var totalAmount = CalculateTotalAmount(cart, ApplicableProductIds);
            return totalAmount >= MinAmount;
        }
        else
        {
            var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);
            return totalQuantity >= MinQuantity;
        }
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        // 统一折扣通常只应用一次，因为所有符合条件的商品都会打折
        return 1;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyUnifiedDiscount(cart);
            application.DiscountAmount = result.totalDiscountAmount;
            application.ConsumedItems = result.consumedItems;
            application.GiftItems = result.discountRecords;
            application.IsSuccessful = result.totalDiscountAmount > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用统一折扣促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用统一折扣促销
    /// </summary>
    private (decimal totalDiscountAmount, List<ConsumedItem> consumedItems, List<GiftItem> discountRecords) ApplyUnifiedDiscount(ShoppingCart cart)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var discountRecords = new List<GiftItem>(); // 折扣记录

        // 获取所有适用的商品项
        var applicableItems = cart
            .Items.Where(x => ApplicableProductIds.Contains(x.Product.Id) && x.Quantity > 0)
            .ToList();

        if (!applicableItems.Any())
            return (0m, consumedItems, discountRecords);

        // 根据策略排序商品
        var sortedItems = SortItemsByStrategy(applicableItems);

        var promotion = new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType
        };

        // 对所有适用商品应用折扣
        foreach (var item in sortedItems)
        {
            var originalPrice = item.UnitPrice;
            var discountedPrice = originalPrice * DiscountRate;
            var discountAmount = (originalPrice - discountedPrice) * item.Quantity;

            if (discountAmount > 0)
            {
                totalDiscountAmount += discountAmount;

                var strategyDescription =
                    DiscountSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                        ? "客户利益最大化"
                        : "商家利益最大化";

                var description =
                    $"统一折扣：{DiscountRate:P1}折，节省{discountAmount:C}（{strategyDescription}）";

                // 应用折扣到商品项
                ApplyDiscountToCartItem(item, DiscountRate, promotion, description);

                // 记录折扣
                discountRecords.Add(
                    CreateDiscountRecord(
                        item.Product.Id,
                        item.Product.Name,
                        item.Quantity,
                        discountAmount,
                        description
                    )
                );

                // 记录消耗的商品
                var existingConsumed = consumedItems.FirstOrDefault(x =>
                    x.ProductId == item.Product.Id
                );
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += item.Quantity;
                }
                else
                {
                    consumedItems.Add(
                        new ConsumedItem
                        {
                            ProductId = item.Product.Id,
                            ProductName = item.Product.Name,
                            Quantity = item.Quantity,
                            UnitPrice = originalPrice
                        }
                    );
                }
            }
        }

        return (totalDiscountAmount, consumedItems, discountRecords);
    }
}
