using PE2.Models;
using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.ExchangeRules;

/// <summary>
/// 统一优惠换购规则测试
/// 测试场景：购买A商品大于等于1件时，可以优惠100元购买B商品
/// </summary>
public class UnifiedDiscountAmountExchangeRuleTests(ITestOutputHelper output) : TestBase(output)
{

    #region 基本功能测试

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "BasicFunctionality")]
    public async Task Apply_BasicDiscountAmountExchange_ShouldApplyCorrectly()
    {
        // Arrange - 创建基本优惠换购规则：买A享B商品优惠100元
        var rule = TestDataGenerator.CreateUnifiedDiscountAmountExchangeRule_BuyA_Minus100_ExchangeB();
        var cart = TestDataGenerator.CreateCustomCart("CART_006", "CUSTOMER_006", new[]
        {
            (TestDataGenerator.CreateProductA(), 1), // A商品50元
            (TestDataGenerator.CreateProductD(), 1)  // D商品100元
        });
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.True(result.TotalDiscount > 0);
        Assert.Single(result.AppliedPromotions);

        var appliedPromotion = result.AppliedPromotions.First();
        Assert.Equal("EXCHANGE_DISCOUNT_AMOUNT_001", appliedPromotion.RuleId);
        Assert.Equal(100.00m, appliedPromotion.DiscountAmount); // D商品优惠100元

        // 验证D商品实际支付价格为0元（100-100）
        var productDItem = cart.Items.First(x => x.Product.Id == "D");
        Assert.Equal(0.00m, productDItem.ActualUnitPrice);

        Output.WriteLine($"基本优惠换购测试 - 优惠金额: {appliedPromotion.DiscountAmount}元");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "PartialDiscount")]
    public async Task Apply_PartialDiscountAmount_ShouldApplyCorrectly()
    {
        // Arrange - 部分优惠金额
        var rule = TestDataGenerator.CreateUnifiedDiscountAmountExchangeRule_BuyA_Minus20_ExchangeB();
        var cart = TestDataGenerator.CreateCustomCart("CART_006", "CUSTOMER_006", new[]
        {
            (TestDataGenerator.CreateProductA(), 1), // A商品50元
            (TestDataGenerator.CreateProductB(), 1)  // B商品30元
        });
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.True(result.TotalDiscount > 0);
        Assert.Single(result.AppliedPromotions);

        var appliedPromotion = result.AppliedPromotions.First();
        Assert.Equal(20.00m, appliedPromotion.DiscountAmount); // B商品优惠20元

        // 验证B商品实际支付价格为10元（30-20）
        var productBItem = cart.Items.First(x => x.Product.Id == "B");
        Assert.Equal(10.00m, productBItem.ActualUnitPrice);

        Output.WriteLine($"部分优惠金额测试 - 优惠金额: {appliedPromotion.DiscountAmount}元");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "QuantityBased")]
    public async Task Apply_QuantityBasedCondition_ShouldApplyCorrectly()
    {
        // Arrange - 创建基于数量的优惠换购规则
        var rule = TestDataGenerator.CreateUnifiedDiscountAmountExchangeRule_QuantityBased();
        var cart = TestDataGenerator.CreateCustomCart("CART_006", "CUSTOMER_006", new[]
        {
            (TestDataGenerator.CreateProductA(), 2), // 满足数量条件
            (TestDataGenerator.CreateProductB(), 1)
        });
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.True(result.TotalDiscount > 0);
        Assert.Single(result.AppliedPromotions);

        var appliedPromotion = result.AppliedPromotions.First();
        Assert.True(appliedPromotion.DiscountAmount > 0);

        Output.WriteLine($"基于数量的优惠换购测试 - 优惠金额: {appliedPromotion.DiscountAmount}元");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "AmountBased")]
    public async Task Apply_AmountBasedCondition_ShouldApplyCorrectly()
    {
        // Arrange - 创建基于金额的优惠换购规则
        var rule = TestDataGenerator.CreateUnifiedDiscountAmountExchangeRule_AmountBased();
        var cart = TestDataGenerator.CreateCustomCart("CART_006", "CUSTOMER_006", new[]
        {
            (TestDataGenerator.CreateProductA(), 3), // 150元，满足金额条件
            (TestDataGenerator.CreateProductC(), 1)
        });
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.True(result.TotalDiscount > 0);
        Assert.Single(result.AppliedPromotions);

        var appliedPromotion = result.AppliedPromotions.First();
        Assert.True(appliedPromotion.DiscountAmount > 0);

        Output.WriteLine($"基于金额的优惠换购测试 - 优惠金额: {appliedPromotion.DiscountAmount}元");
        AssertCartConsistency(cart, "基于金额的优惠换购");
    }

    #endregion

    #region 条件不满足测试

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "ConditionNotMet")]
    public async Task Apply_InsufficientBuyQuantity_ShouldNotApply()
    {
        // Arrange - 购买数量不足
        var rule = TestDataGenerator.CreateUnifiedDiscountAmountExchangeRule_BuyA_Minus100_ExchangeB();
        var cart = TestDataGenerator.CreateCustomCart("CART_006", "CUSTOMER_006", new[]
        {
            (TestDataGenerator.CreateProductB(), 1) // 只有B商品，没有A商品
        });
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.Equal(0, result.TotalDiscount);
        Assert.Empty(result.AppliedPromotions);

        Output.WriteLine("购买数量不足测试 - 无优惠应用");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "NoExchangeProduct")]
    public async Task Apply_NoExchangeProductInCart_ShouldNotApply()
    {
        // Arrange - 购物车中没有换购商品
        var rule = TestDataGenerator.CreateUnifiedDiscountAmountExchangeRule_BuyA_Minus100_ExchangeB();
        var cart = TestDataGenerator.CreateCustomCart("CART_006", "CUSTOMER_006", new[]
        {
            (TestDataGenerator.CreateProductA(), 1) // 只有A商品，没有换购商品
        });
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.Equal(0, result.TotalDiscount);
        Assert.Empty(result.AppliedPromotions);

        Output.WriteLine("无换购商品测试 - 无优惠应用");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "InsufficientAmount")]
    public async Task Apply_InsufficientBuyAmount_ShouldNotApply()
    {
        // Arrange - 购买金额不足
        var rule = TestDataGenerator.CreateUnifiedDiscountAmountExchangeRule_AmountBased();
        var cart = TestDataGenerator.CreateCustomCart("CART_006", "CUSTOMER_006", new[]
        {
            (TestDataGenerator.CreateProductA(), 1), // 50元，不满足100元条件
            (TestDataGenerator.CreateProductC(), 1)
        });
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.Equal(0, result.TotalDiscount);
        Assert.Empty(result.AppliedPromotions);

        Output.WriteLine("购买金额不足测试 - 无优惠应用");
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "BoundaryCondition")]
    public async Task Apply_DiscountExceedsProductPrice_ShouldCapAtProductPrice()
    {
        // Arrange - 优惠金额超过商品价格
        var rule = TestDataGenerator.CreateUnifiedDiscountAmountExchangeRule_BuyA_Minus100_ExchangeB();
        var cart = TestDataGenerator.CreateCustomCart("CART_006", "CUSTOMER_006", new[]
        {
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1) // B商品30元，优惠100元
        });
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.True(result.TotalDiscount > 0);
        Assert.Single(result.AppliedPromotions);

        var appliedPromotion = result.AppliedPromotions.First();
        Assert.Equal(30.00m, appliedPromotion.DiscountAmount); // 优惠金额应限制为商品价格

        // 验证B商品实际支付价格为0元
        var productBItem = cart.Items.First(x => x.Product.Id == "B");
        Assert.Equal(0.00m, productBItem.ActualUnitPrice);

        Output.WriteLine($"优惠超过商品价格测试 - 优惠金额: {appliedPromotion.DiscountAmount}元");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "ZeroDiscount")]
    public async Task Apply_ZeroDiscountAmount_ShouldNotApply()
    {
        // Arrange - 0优惠金额
        var rule = TestDataGenerator.CreateUnifiedDiscountAmountExchangeRule_ZeroDiscount();
        var cart = TestDataGenerator.CreateCustomCart("CART_006", "CUSTOMER_006", new[]
        {
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1)
        });
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.Equal(0, result.TotalDiscount);
        Assert.Empty(result.AppliedPromotions);

        Output.WriteLine("零优惠金额测试 - 无优惠应用");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "EmptyCart")]
    public async Task Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateUnifiedDiscountAmountExchangeRule_BuyA_Minus100_ExchangeB();
        var cart = TestDataGenerator.CreateCustomCart("CART_006", "CUSTOMER_006", new (Product, int)[0]);
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.Equal(0, result.TotalDiscount);
        Assert.Empty(result.AppliedPromotions);

        Output.WriteLine("空购物车测试 - 无优惠应用");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "DisabledRule")]
    public async Task Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateUnifiedDiscountAmountExchangeRule_Disabled();
        var cart = TestDataGenerator.CreateCustomCart("CART_006", "CUSTOMER_006", new[]
        {
            (TestDataGenerator.CreateProductA(), 1),
            (TestDataGenerator.CreateProductB(), 1)
        });
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.Equal(0, result.TotalDiscount);
        Assert.Empty(result.AppliedPromotions);

        Output.WriteLine("禁用规则测试 - 无优惠应用");
    }

    #endregion

    #region 可重复性测试

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "RepeatableRule")]
    public async Task Apply_RepeatableRule_ShouldApplyMultipleTimes()
    {
        // Arrange - 可重复的优惠换购规则
        var rule = TestDataGenerator.CreateUnifiedDiscountAmountExchangeRule_Repeatable();
        var cart = TestDataGenerator.CreateCustomCart("CART_006", "CUSTOMER_006", new[]
        {
            (TestDataGenerator.CreateProductA(), 3), // 3件A商品
            (TestDataGenerator.CreateProductB(), 3)  // 3件B商品可换购
        });
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.True(result.TotalDiscount > 0);
        Assert.NotEmpty(result.AppliedPromotions);

        Output.WriteLine($"可重复规则测试 - 总优惠金额: {result.TotalDiscount}元");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "NonRepeatableRule")]
    public async Task Apply_NonRepeatableRule_ShouldApplyOnlyOnce()
    {
        // Arrange - 不可重复的优惠换购规则
        var rule = TestDataGenerator.CreateUnifiedDiscountAmountExchangeRule_NonRepeatable();
        var cart = TestDataGenerator.CreateCustomCart("CART_006", "CUSTOMER_006", new[]
        {
            (TestDataGenerator.CreateProductA(), 5), // 足够多的A商品
            (TestDataGenerator.CreateProductB(), 5)  // 足够多的B商品
        });
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.True(result.AppliedPromotions.Count <= 1);

        Output.WriteLine($"不可重复规则测试 - 应用次数: {result.AppliedPromotions.Count}");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "CustomerBenefit")]
    public async Task Apply_CustomerBenefitMaximization_ShouldSelectHighValueProducts()
    {
        // Arrange - 客户利益最大化：应选择高价商品进行换购
        var rule = TestDataGenerator.CreateUnifiedDiscountAmountExchangeRule_CustomerBenefit();
        var cart = TestDataGenerator.CreateCustomCart("CART_006", "CUSTOMER_006", new[]
        {
            (TestDataGenerator.CreateProductA(), 1), // 购买条件商品
            (TestDataGenerator.CreateProductB(), 1), // 低价换购商品 30元
            (TestDataGenerator.CreateProductD(), 1)  // 高价换购商品 100元
        });
        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.True(result.TotalDiscount > 0);
        Assert.Single(result.AppliedPromotions);

        // 验证选择了高价商品D进行换购
        var productDItem = cart.Items.First(x => x.Product.Id == "D");
        Assert.True(productDItem.ActualUnitPrice < productDItem.Product.Price);

        Output.WriteLine($"客户利益最大化测试 - 选择高价商品D进行换购，优惠金额: {result.TotalDiscount}元");
    }

    [Fact]
    [Trait("Category", "ExchangeRule")]
    [Trait("Type", "MerchantBenefit")]
    public async Task Apply_MerchantBenefitMaximization_ShouldSelectLowValueProducts()
    {
        // Arrange - 商户利益最大化：应选择低价商品进行换购
        var rule = TestDataGenerator.CreateUnifiedDiscountAmountExchangeRule_MerchantBenefit();
        var cart = TestDataGenerator.CreateCustomCart("CART_006", "CUSTOMER_006", new[]
        {
            (TestDataGenerator.CreateProductA(), 1), // 购买条件商品
            (TestDataGenerator.CreateProductB(), 1), // 低价换购商品 30元
            (TestDataGenerator.CreateProductD(), 1)  // 高价换购商品 100元
        });

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = await PromotionEngine.CalculateOptimalPromotionsAsync(cart);

        // Assert
        Assert.True(result.TotalDiscount > 0);
        Assert.Single(result.AppliedPromotions);

        // 验证选择了低价商品B进行换购
        var productBItem = cart.Items.First(x => x.Product.Id == "B");
        Assert.True(productBItem.ActualUnitPrice < productBItem.Product.Price);

        Output.WriteLine($"商户利益最大化测试 - 选择低价商品B进行换购，优惠金额: {result.TotalDiscount}元");
    }

    #endregion

    #region 辅助方法

    private async Task AssertCartConsistency(ShoppingCart cart, string testName)
    {
        // 验证购物车一致性
        Assert.True(cart.Items.All(item => item.ActualUnitPrice >= 0), $"{testName}: 所有商品实际价格应大于等于0");
        Assert.True(cart.Items.All(item => item.ActualUnitPrice <= item.Product.Price), $"{testName}: 实际价格不应超过原价");

        var totalOriginalPrice = cart.Items.Sum(item => item.Product.Price * item.Quantity);
        var totalActualPrice = cart.Items.Sum(item => item.ActualUnitPrice * item.Quantity);
        Assert.True(totalActualPrice <= totalOriginalPrice, $"{testName}: 总实际价格不应超过总原价");
    }

    #endregion
}