namespace PE2.Models;

/// <summary>
/// 购物车商品项
/// </summary>
public class CartItem
{
    /// <summary>
    /// 商品信息
    /// </summary>
    public Product Product { get; set; } = new();

    /// <summary>
    /// 商品数量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 单价（可能与商品原价不同，比如会员价）
    /// </summary>
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// 小计金额（数量 * 单价）
    /// </summary>
    public decimal SubTotal => Quantity * UnitPrice;

    /// <summary>
    /// 是否已被促销规则消耗
    /// </summary>
    public bool IsConsumed { get; set; }

    /// <summary>
    /// 剩余可用数量（用于促销计算）
    /// </summary>
    public int AvailableQuantity => IsConsumed ? 0 : Quantity;

    public CartItem Clone()
    {
        return new CartItem
        {
            Product = Product,
            Quantity = Quantity,
            UnitPrice = UnitPrice,
            IsConsumed = IsConsumed
        };
    }

    public override bool Equals(object? obj)
    {
        return obj is CartItem item && 
               Product.Id == item.Product.Id && 
               UnitPrice == item.UnitPrice;
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(Product.Id, UnitPrice);
    }
}
