<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>促销规则配置 - 商品选择器集成测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 40px;
            padding: 24px;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #303133;
            border-bottom: 2px solid #409eff;
            padding-bottom: 8px;
        }
        .form-container {
            background: white;
            padding: 20px;
            border-radius: 6px;
            margin-top: 16px;
        }
        .summary-box {
            background: #f0f9ff;
            border: 1px solid #7dd3fc;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 20px;
        }
        .summary-title {
            font-weight: bold;
            color: #0369a1;
            margin-bottom: 8px;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            margin: 4px 0;
            padding-left: 20px;
            position: relative;
        }
        .feature-list li:before {
            content: "✓";
            color: #16a34a;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>促销规则配置系统 - 商品选择器集成测试</h1>
            
            <!-- 功能总览 -->
            <div class="summary-box">
                <div class="summary-title">✨ 新功能特性</div>
                <ul class="feature-list">
                    <li>后端自动识别商品ID相关字段，返回 product-selector-single/multiple 类型</li>
                    <li>前端优先使用 type 字段判断，字段名识别作为兜底方案</li>
                    <li>支持嵌套 array-complex 字段中的商品选择器</li>
                    <li>统一的商品选择器组件，支持单选/多选、远程搜索</li>
                    <li>增强的元数据结构，完整支持复杂嵌套类型</li>
                </ul>
            </div>
            
            <!-- 测试区域 -->
            <div class="test-section">
                <div class="test-title">🧪 规则类型测试</div>
                <el-row :gutter="16">
                    <el-col :span="6">
                        <el-select v-model="selectedRuleType" placeholder="选择规则类型" style="width: 100%;">
                            <el-option-group label="买赠规则">
                                <el-option value="CombinationBuyFreeRule" label="组合买赠" />
                                <el-option value="ProductBuyFreeRule" label="商品买赠" />
                            </el-option-group>
                            <el-option-group label="买赠规则">
                                <el-option value="CombinationGiftRule" label="组合买赠" />
                                <el-option value="UnifiedGiftRule" label="统一买赠" />
                            </el-option-group>
                            <el-option-group label="换购规则">
                                <el-option value="UnifiedSpecialPriceExchangeRule" label="特价换购" />
                                <el-option value="CombinationSpecialPriceExchangeRule" label="组合特价换购" />
                            </el-option-group>
                        </el-select>
                    </el-col>
                    <el-col :span="6">
                        <el-button @click="loadRuleMetadata" type="primary" style="width: 100%;">
                            加载规则元数据
                        </el-button>
                    </el-col>
                    <el-col :span="6">
                        <el-button @click="createNewRule" type="success" style="width: 100%;">
                            新建规则
                        </el-button>
                    </el-col>
                    <el-col :span="6">
                        <el-button @click="clearAll" type="info" style="width: 100%;">
                            清空所有
                        </el-button>
                    </el-col>
                </el-row>
                
                <!-- 元数据显示 -->
                <div v-if="ruleMetadata" style="margin-top: 20px;">
                    <el-collapse v-model="activeCollapse">
                        <el-collapse-item title="📋 元数据结构" name="metadata">
                            <pre style="background: #f6f8fa; padding: 16px; border-radius: 6px; overflow-x: auto; font-size: 12px;">{{ JSON.stringify(ruleMetadata, null, 2) }}</pre>
                        </el-collapse-item>
                    </el-collapse>
                </div>
            </div>
            
            <!-- 表单渲染区域 -->
            <div v-if="ruleMetadata" class="test-section">
                <div class="test-title">📝 动态表单渲染</div>
                <div class="form-container">
                    <dynamic-form-renderer
                        :fields="ruleMetadata.fields"
                        :model-value="formData"
                        @update:model-value="formData = $event"
                        mode="edit"
                    />
                </div>
                
                <!-- 表单数据显示 -->
                <div style="margin-top: 20px;">
                    <el-collapse v-model="activeCollapse">
                        <el-collapse-item title="💾 表单数据" name="formData">
                            <pre style="background: #f6f8fa; padding: 16px; border-radius: 6px; overflow-x: auto; font-size: 12px;">{{ JSON.stringify(formData, null, 2) }}</pre>
                        </el-collapse-item>
                    </el-collapse>
                </div>
            </div>
            
            <!-- 商品选择器专项测试 -->
            <div class="test-section">
                <div class="test-title">🛍️ 商品选择器专项测试</div>
                <el-row :gutter="16">
                    <el-col :span="12">
                        <h4>单选商品选择器</h4>
                        <product-selector
                            v-model="singleProduct"
                            placeholder="请选择商品"
                            style="width: 100%;"
                        />
                        <p style="margin-top: 8px; color: #666;">选中商品: {{ singleProduct || '无' }}</p>
                    </el-col>
                    <el-col :span="12">
                        <h4>多选商品选择器</h4>
                        <product-selector
                            v-model="multipleProducts"
                            :multiple="true"
                            placeholder="请选择商品"
                            style="width: 100%;"
                        />
                        <p style="margin-top: 8px; color: #666;">选中商品: {{ multipleProducts?.length || 0 }} 个</p>
                    </el-col>
                </el-row>
            </div>
            
            <!-- 测试结果总结 -->
            <div class="test-section">
                <div class="test-title">📊 测试结果总结</div>
                <el-row :gutter="16">
                    <el-col :span="8">
                        <el-card>
                            <template #header>
                                <span>🎯 元数据识别</span>
                            </template>
                            <div v-if="ruleMetadata">
                                <p><strong>规则类型:</strong> {{ selectedRuleType }}</p>
                                <p><strong>字段总数:</strong> {{ ruleMetadata.fields?.length || 0 }}</p>
                                <p><strong>商品选择器字段:</strong> {{ productSelectorFieldsCount }}</p>
                                <p><strong>复杂数组字段:</strong> {{ complexArrayFieldsCount }}</p>
                            </div>
                            <div v-else>
                                <p style="color: #999;">请先加载规则元数据</p>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="8">
                        <el-card>
                            <template #header>
                                <span>📝 表单交互</span>
                            </template>
                            <div>
                                <p><strong>表单字段数:</strong> {{ Object.keys(formData).length }}</p>
                                <p><strong>已填写字段:</strong> {{ filledFieldsCount }}</p>
                                <p><strong>商品ID字段:</strong> {{ productIdFieldsInForm }}</p>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="8">
                        <el-card>
                            <template #header>
                                <span>🔧 组件状态</span>
                            </template>
                            <div>
                                <p><strong>ProductSelector 组件:</strong> ✅ 已加载</p>
                                <p><strong>DynamicFormRenderer:</strong> ✅ 已加载</p>
                                <p><strong>ComplexArrayRenderer:</strong> ✅ 已加载</p>
                                <p><strong>API 连接:</strong> {{ apiStatus }}</p>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="js/components/ProductSelector.js"></script>
    <script src="js/components/DynamicFormRenderer_template.js"></script>

    <script>
        const { createApp, ref, computed, onMounted } = Vue;

        createApp({
            components: {
                ProductSelector,
                DynamicFormRenderer: DynamicFormRenderer_template
            },
            setup() {
                const selectedRuleType = ref('CombinationBuyFreeRule');
                const ruleMetadata = ref(null);
                const formData = ref({});
                const activeCollapse = ref(['metadata']);
                const singleProduct = ref('');
                const multipleProducts = ref([]);
                const apiStatus = ref('未测试');

                // 计算属性
                const productSelectorFieldsCount = computed(() => {
                    if (!ruleMetadata.value?.fields) return 0;
                    return ruleMetadata.value.fields.filter(field => 
                        field.type === 'product-selector-single' || 
                        field.type === 'product-selector-multiple' ||
                        (field.type === 'array-complex' && hasProductSelectorInMetadata(field.metadata))
                    ).length;
                });

                const complexArrayFieldsCount = computed(() => {
                    if (!ruleMetadata.value?.fields) return 0;
                    return ruleMetadata.value.fields.filter(field => 
                        field.type === 'array-complex'
                    ).length;
                });

                const filledFieldsCount = computed(() => {
                    return Object.values(formData.value).filter(value => 
                        value !== null && value !== undefined && value !== '' && 
                        !(Array.isArray(value) && value.length === 0)
                    ).length;
                });

                const productIdFieldsInForm = computed(() => {
                    return Object.keys(formData.value).filter(key => 
                        key.toLowerCase().includes('productid') || 
                        key.toLowerCase().includes('productids')
                    ).length;
                });

                // 检查元数据中是否包含商品选择器
                const hasProductSelectorInMetadata = (metadata) => {
                    if (!metadata?.elementSchema?.fields) return false;
                    return metadata.elementSchema.fields.some(field => 
                        field.type?.type === 'product-selector-single' ||
                        field.type?.type === 'product-selector-multiple'
                    );
                };

                // 加载规则元数据
                const loadRuleMetadata = async () => {
                    if (!selectedRuleType.value) {
                        ElMessage.warning('请先选择规则类型');
                        return;
                    }

                    try {
                        const response = await fetch(`/api/promotion/metadata/types/${selectedRuleType.value}`);
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        ruleMetadata.value = await response.json();
                        apiStatus.value = '✅ 连接正常';
                        ElMessage.success('元数据加载成功');
                    } catch (error) {
                        console.error('加载元数据失败:', error);
                        apiStatus.value = '❌ 连接失败';
                        ElMessage.error(`加载元数据失败: ${error.message}`);
                    }
                };

                // 新建规则
                const createNewRule = () => {
                    if (!ruleMetadata.value) {
                        ElMessage.warning('请先加载规则元数据');
                        return;
                    }
                    formData.value = {};
                    ElMessage.success('已创建新规则，请填写表单');
                };

                // 清空所有
                const clearAll = () => {
                    ruleMetadata.value = null;
                    formData.value = {};
                    singleProduct.value = '';
                    multipleProducts.value = [];
                    apiStatus.value = '未测试';
                    ElMessage.info('已清空所有数据');
                };

                // 测试API连接
                const testApiConnection = async () => {
                    try {
                        const response = await fetch('/api/promotion/metadata/types');
                        if (response.ok) {
                            apiStatus.value = '✅ 连接正常';
                        } else {
                            apiStatus.value = '❌ 连接失败';
                        }
                    } catch (error) {
                        apiStatus.value = '❌ 连接失败';
                    }
                };

                onMounted(() => {
                    testApiConnection();
                });

                return {
                    selectedRuleType,
                    ruleMetadata,
                    formData,
                    activeCollapse,
                    singleProduct,
                    multipleProducts,
                    apiStatus,
                    productSelectorFieldsCount,
                    complexArrayFieldsCount,
                    filledFieldsCount,
                    productIdFieldsInForm,
                    loadRuleMetadata,
                    createNewRule,
                    clearAll
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
