using PE2.Models;
using PE2.PromotionEngine.Models;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Rules;

/// <summary>
/// 梯度送赠品促销规则
/// 针对某一类商品，满第一梯度送A商品，满第二梯度送B商品
/// </summary>
public class GradientGiftRule : PromotionRuleBase
{
    public override string RuleType => "GradientGift";

    /// <summary>
    /// 购买条件列表
    /// </summary>
    public List<BuyCondition> BuyConditions { get; set; } = new();

    /// <summary>
    /// 梯度赠品条件列表
    /// </summary>
    public List<GradientGiftCondition> GradientGiftConditions { get; set; } = new();

    /// <summary>
    /// 梯度赠送策略
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public GradientGiftStrategy GradientStrategy { get; set; } = GradientGiftStrategy.ByGradient;

    /// <summary>
    /// 赠品选择策略
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public GiftSelectionStrategy GiftSelectionStrategy { get; set; } = GiftSelectionStrategy.CustomerBenefit;

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!BuyConditions.Any() || !GradientGiftConditions.Any())
            return false;

        // 使用择优算法检查是否能满足梯度条件
        var optimalPlan = FindOptimalGradientPlan(cart);
        return optimalPlan != null && optimalPlan.MaxApplications > 0;
    }

    /// <summary>
    /// 梯度促销计划
    /// </summary>
    private class GradientPromotionPlan
    {
        public int MaxApplications { get; set; }
        public List<ProductAllocation> ConsumedAllocations { get; set; } = new();
        public List<GradientGiftAllocation> GiftAllocations { get; set; } = new();
        public decimal TotalGiftValue { get; set; }
    }

    /// <summary>
    /// 商品分配
    /// </summary>
    private class ProductAllocation
    {
        public string ProductId { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalValue => Quantity * UnitPrice;
    }

    /// <summary>
    /// 梯度赠品分配
    /// </summary>
    private class GradientGiftAllocation : ProductAllocation
    {
        public int GradientLevel { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 找到最优的梯度促销计划
    /// 考虑所有可能的梯度组合和商品分配
    /// </summary>
    private GradientPromotionPlan? FindOptimalGradientPlan(ShoppingCart cart)
    {
        var allProducts = GetAllRelevantProducts(cart);
        if (!allProducts.Any()) return null;

        var bestPlan = new GradientPromotionPlan();
        var maxApplications = IsRepeatable ? (MaxApplications > 0 ? MaxApplications : 10) : 1;

        // 尝试不同的应用次数，找到最优方案
        for (int appCount = 1; appCount <= maxApplications; appCount++)
        {
            var plan = TryCreateGradientPlan(cart, allProducts, appCount);
            if (plan != null && plan.MaxApplications > bestPlan.MaxApplications)
            {
                bestPlan = plan;
            }
        }

        return bestPlan.MaxApplications > 0 ? bestPlan : null;
    }

    /// <summary>
    /// 获取所有相关的商品
    /// </summary>
    private List<ProductAllocation> GetAllRelevantProducts(ShoppingCart cart)
    {
        var allProductIds = new HashSet<string>();

        // 添加购买条件中的商品
        foreach (var condition in BuyConditions)
        {
            foreach (var productId in condition.ProductIds)
            {
                allProductIds.Add(productId);
            }
        }

        // 添加梯度赠品中的商品
        foreach (var gradient in GradientGiftConditions)
        {
            foreach (var productId in gradient.GiftProductIds)
            {
                allProductIds.Add(productId);
            }
        }

        var products = new List<ProductAllocation>();
        foreach (var productId in allProductIds)
        {
            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
            if (cartItem != null && cartItem.AvailableQuantity > 0)
            {
                products.Add(new ProductAllocation
                {
                    ProductId = productId,
                    Quantity = cartItem.AvailableQuantity,
                    UnitPrice = cartItem.UnitPrice
                });
            }
        }

        return products;
    }

    /// <summary>
    /// 尝试创建指定应用次数的梯度促销计划
    /// </summary>
    private GradientPromotionPlan? TryCreateGradientPlan(ShoppingCart cart, List<ProductAllocation> allProducts, int targetApplications)
    {
        var plan = new GradientPromotionPlan();
        var remainingProducts = allProducts.Select(p => new ProductAllocation
        {
            ProductId = p.ProductId,
            Quantity = p.Quantity,
            UnitPrice = p.UnitPrice
        }).ToList();

        int actualApplications = 0;

        for (int app = 0; app < targetApplications; app++)
        {
            // 找到当前可以应用的最高梯度
            var applicableGradient = FindApplicableGradient(remainingProducts);
            if (applicableGradient == null) break;

            // 消耗购买条件中的商品
            if (!TryConsumeGradientBuyConditions(remainingProducts, plan, applicableGradient))
                break;

            // 分配梯度赠品
            if (!TryAllocateGradientGifts(remainingProducts, plan, applicableGradient))
                break;

            actualApplications++;
        }

        plan.MaxApplications = actualApplications;
        return plan.MaxApplications > 0 ? plan : null;
    }

    /// <summary>
    /// 找到当前可以应用的梯度（考虑购买条件和赠品库存）
    /// </summary>
    private GradientGiftCondition? FindApplicableGradient(List<ProductAllocation> remainingProducts)
    {
        foreach (var buyCondition in BuyConditions)
        {
            var availableQuantity = remainingProducts
                .Where(p => buyCondition.ProductIds.Contains(p.ProductId))
                .Sum(p => p.Quantity);

            // 找到所有满足购买条件的梯度
            var applicableGradients = GradientGiftConditions
                .Where(g => availableQuantity >= g.RequiredQuantity)
                .OrderByDescending(g => g.GradientLevel)
                .ToList();

            // 检查每个梯度是否有足够的赠品库存
            foreach (var gradient in applicableGradients)
            {
                var hasEnoughGifts = gradient.GiftProductIds.Any(giftId =>
                    remainingProducts.Where(p => p.ProductId == giftId).Sum(p => p.Quantity) >= gradient.GiftQuantity);

                if (hasEnoughGifts)
                    return gradient;
            }
        }

        return null;
    }

    /// <summary>
    /// 消耗梯度购买条件中的商品
    /// </summary>
    private bool TryConsumeGradientBuyConditions(List<ProductAllocation> remainingProducts, GradientPromotionPlan plan, GradientGiftCondition gradient)
    {
        foreach (var buyCondition in BuyConditions)
        {
            var requiredQuantity = gradient.RequiredQuantity;
            var consumedThisCondition = new List<ProductAllocation>();

            // 按价格排序：商家利益最大化时优先消耗高价商品作为购买条件
            var candidateProducts = remainingProducts
                .Where(p => buyCondition.ProductIds.Contains(p.ProductId) && p.Quantity > 0)
                .OrderBy(p => GiftSelectionStrategy == GiftSelectionStrategy.MerchantBenefit ? -p.UnitPrice : p.UnitPrice)
                .ToList();

            foreach (var product in candidateProducts)
            {
                if (requiredQuantity <= 0) break;

                var consumeQuantity = Math.Min(product.Quantity, requiredQuantity);

                consumedThisCondition.Add(new ProductAllocation
                {
                    ProductId = product.ProductId,
                    Quantity = consumeQuantity,
                    UnitPrice = product.UnitPrice
                });

                product.Quantity -= consumeQuantity;
                requiredQuantity -= consumeQuantity;
            }

            if (requiredQuantity > 0)
                return false;

            plan.ConsumedAllocations.AddRange(consumedThisCondition);
        }

        return true;
    }

    /// <summary>
    /// 分配梯度赠品
    /// </summary>
    private bool TryAllocateGradientGifts(List<ProductAllocation> remainingProducts, GradientPromotionPlan plan, GradientGiftCondition gradient)
    {
        var requiredGiftQuantity = gradient.GiftQuantity;
        var giftAllocations = new List<GradientGiftAllocation>();

        // 根据策略排序候选赠品
        var candidateGifts = remainingProducts
            .Where(p => gradient.GiftProductIds.Contains(p.ProductId) && p.Quantity > 0)
            .OrderBy(p => GiftSelectionStrategy == GiftSelectionStrategy.CustomerBenefit ? -p.UnitPrice : p.UnitPrice)
            .ToList();

        foreach (var product in candidateGifts)
        {
            if (requiredGiftQuantity <= 0) break;

            var giftQuantity = Math.Min(product.Quantity, requiredGiftQuantity);

            giftAllocations.Add(new GradientGiftAllocation
            {
                ProductId = product.ProductId,
                Quantity = giftQuantity,
                UnitPrice = product.UnitPrice,
                GradientLevel = gradient.GradientLevel,
                Description = gradient.Description
            });

            product.Quantity -= giftQuantity;
            requiredGiftQuantity -= giftQuantity;
            plan.TotalGiftValue += giftQuantity * product.UnitPrice;
        }

        if (requiredGiftQuantity > 0)
            return false;

        plan.GiftAllocations.AddRange(giftAllocations);
        return true;
    }

    /// <summary>
    /// 检查特定梯度的赠品库存是否充足（已废弃，由择优算法替代）
    /// </summary>
    private bool CheckGradientGiftStock(ShoppingCart cart, GradientGiftCondition gradient)
    {
        if (gradient.GiftProductIds.Count > 1)
        {
            // 多选一：只要有一个赠品有库存就可以
            return gradient.GiftProductIds.Any(id =>
                cart.Items.Where(x => x.Product.Id == id).Sum(x => x.Quantity) > 0);
        }
        else
        {
            // 单一赠品：必须有足够库存
            var giftProductId = gradient.GiftProductIds.FirstOrDefault();
            if (string.IsNullOrEmpty(giftProductId)) return false;

            var availableGiftQuantity = cart.Items
                .Where(x => x.Product.Id == giftProductId)
                .Sum(x => x.Quantity);

            return availableGiftQuantity >= gradient.GiftQuantity;
        }
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        var optimalPlan = FindOptimalGradientPlan(cart);
        return optimalPlan?.MaxApplications ?? 0;
    }

    /// <summary>
    /// 根据赠品库存计算最大应用次数
    /// </summary>
    private int CalculateMaxApplicationsByGiftStock(ShoppingCart cart, List<GradientGiftCondition> gradients)
    {
        var minApplications = int.MaxValue;

        foreach (var gradient in gradients)
        {
            var maxForThisGradient = CalculateMaxApplicationsForSingleGradient(cart, gradient);
            minApplications = Math.Min(minApplications, maxForThisGradient);
        }

        return minApplications == int.MaxValue ? int.MaxValue : minApplications;
    }

    /// <summary>
    /// 计算单个梯度的最大应用次数（基于赠品库存）
    /// </summary>
    private int CalculateMaxApplicationsForSingleGradient(ShoppingCart cart, GradientGiftCondition gradient)
    {
        if (gradient.GiftProductIds.Count > 1)
        {
            // 多选一的情况：只要有一个赠品有库存就可以应用
            var hasAnyStock = gradient.GiftProductIds.Any(id =>
                cart.Items.Where(x => x.Product.Id == id).Sum(x => x.Quantity) > 0);
            return hasAnyStock ? int.MaxValue : 0;
        }
        else
        {
            // 单一赠品：根据库存数量计算
            var giftProductId = gradient.GiftProductIds.FirstOrDefault();
            if (string.IsNullOrEmpty(giftProductId)) return 0;

            var availableStock = cart.Items
                .Where(x => x.Product.Id == giftProductId)
                .Sum(x => x.Quantity);

            return gradient.GiftQuantity > 0 ? availableStock / gradient.GiftQuantity : 0;
        }
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyGradientGiftPromotion(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用梯度送赠品促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = $"应用梯度送赠品促销时发生错误: {ex.Message}";
        }

        return application;
    }

    /// <summary>
    /// 应用梯度送赠品促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyGradientGiftPromotion(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var giftItems = new List<GiftItem>();

        for (int app = 0; app < applicationCount; app++)
        {
            foreach (var buyCondition in BuyConditions)
            {
                var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
                var totalAmount = buyCondition.ProductIds.Sum(id =>
                    cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));

                // 找到所有满足购买条件的梯度
                var applicableGradients = GradientGiftConditions
                    .Where(g => availableQuantity >= g.RequiredQuantity &&
                               (g.RequiredAmount <= 0 || totalAmount >= g.RequiredAmount))
                    .OrderByDescending(g => g.GradientLevel)
                    .ToList();

                if (!applicableGradients.Any()) continue;

                // 根据策略决定赠送哪些梯度的赠品
                var gradientsToApply = GradientStrategy == GradientGiftStrategy.ByGradient
                    ? new List<GradientGiftCondition> { applicableGradients.First() }
                    : applicableGradients;

                // 验证所有要应用的梯度是否都有足够的赠品库存
                if (!gradientsToApply.All(gradient => CheckGradientGiftStock(cart, gradient)))
                    continue;

                // 消耗购买商品
                var totalRequiredQuantity = gradientsToApply.Max(g => g.RequiredQuantity);
                var remainingQuantity = totalRequiredQuantity;

                foreach (var productId in buyCondition.ProductIds)
                {
                    if (remainingQuantity <= 0) break;

                    var availableItems = cart.Items
                        .Where(x => x.Product.Id == productId && x.AvailableQuantity > 0)
                        .ToList();

                    foreach (var item in availableItems)
                    {
                        if (remainingQuantity <= 0) break;

                        var consumeQuantity = Math.Min(item.AvailableQuantity, remainingQuantity);

                        var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == productId);
                        if (existingConsumed != null)
                        {
                            existingConsumed.Quantity += consumeQuantity;
                        }
                        else
                        {
                            consumedItems.Add(new ConsumedItem
                            {
                                ProductId = productId,
                                ProductName = item.Product.Name,
                                Quantity = consumeQuantity,
                                UnitPrice = item.UnitPrice
                            });
                        }

                        item.Quantity -= consumeQuantity;
                        remainingQuantity -= consumeQuantity;
                    }
                }

                // 处理梯度赠品
                foreach (var gradient in gradientsToApply)
                {
                    var selectedProductIds = gradient.GiftProductIds.Count > 1
                        ? SelectOptimalGiftProducts(gradient.GiftProductIds, cart, gradient.GiftQuantity)
                        : gradient.GiftProductIds;

                    var distributedQuantity = 0;
                    foreach (var giftProductId in selectedProductIds)
                    {
                        if (distributedQuantity >= gradient.GiftQuantity) break;

                        var productItem = cart.Items.FirstOrDefault(x => x.Product.Id == giftProductId);
                        if (productItem != null)
                        {
                            var giftQuantity = Math.Min(gradient.GiftQuantity - distributedQuantity, 1);
                            var giftValue = giftQuantity * productItem.UnitPrice;

                            totalDiscountAmount += giftValue;

                            var existingGift = giftItems.FirstOrDefault(x => x.ProductId == giftProductId);
                            if (existingGift != null)
                            {
                                existingGift.Quantity += giftQuantity;
                                existingGift.Value += giftValue;
                            }
                            else
                            {
                                var strategyDescription = GiftSelectionStrategy == GiftSelectionStrategy.CustomerBenefit
                                    ? "客户利益最大化选择"
                                    : "商家利益最大化选择";

                                giftItems.Add(new GiftItem
                                {
                                    ProductId = giftProductId,
                                    ProductName = productItem.Product.Name,
                                    Quantity = giftQuantity,
                                    Value = giftValue,
                                    Description = $"梯度{gradient.GradientLevel}: {gradient.Description} - {strategyDescription}"
                                });
                            }

                            distributedQuantity += giftQuantity;
                        }
                    }
                }
            }
        }

        cart.Items.RemoveAll(x => x.Quantity <= 0);
        return (totalDiscountAmount, consumedItems, giftItems);
    }

    /// <summary>
    /// 根据策略选择最优的赠品商品
    /// </summary>
    private List<string> SelectOptimalGiftProducts(List<string> candidateProductIds, ShoppingCart cart, int requiredQuantity)
    {
        if (!candidateProductIds.Any())
            return new List<string>();

        var productPrices = new List<(string ProductId, decimal Price)>();

        foreach (var productId in candidateProductIds)
        {
            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
            if (cartItem != null)
            {
                productPrices.Add((productId, cartItem.Product.Price));
            }
        }

        if (!productPrices.Any())
            return new List<string>();

        var sortedProducts = GiftSelectionStrategy switch
        {
            GiftSelectionStrategy.CustomerBenefit => productPrices.OrderByDescending(p => p.Price),
            GiftSelectionStrategy.MerchantBenefit => productPrices.OrderBy(p => p.Price),
            _ => productPrices.OrderByDescending(p => p.Price)
        };

        var selectedProducts = new List<string>();
        var remainingQuantity = requiredQuantity;

        foreach (var (productId, _) in sortedProducts)
        {
            if (remainingQuantity <= 0) break;

            var availableQuantity = cart.Items
                .Where(x => x.Product.Id == productId)
                .Sum(x => x.Quantity);

            if (availableQuantity > 0)
            {
                selectedProducts.Add(productId);
                remainingQuantity--;
            }
        }

        return selectedProducts;
    }
}
