using Microsoft.Extensions.DependencyInjection;
using PE2.PromotionEngine.Core;

namespace PE2.PromotionEngine.Extensions;

/// <summary>
/// 服务集合扩展 - 注册商品占用和条件筛选相关服务
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加商品占用管理服务
    /// </summary>
    public static IServiceCollection AddProductOccupationServices(this IServiceCollection services)
    {
        // 核心引擎服务
        services.AddSingleton<ProductOccupationEngine>();
        services.AddScoped<ConditionExecutionSeparator>();
        services.AddScoped<ExclusivityManager>();

        // 日志服务
        services.AddLogging();

        return services;
    }

    /// <summary>
    /// 添加促销引擎完整服务
    /// </summary>
    public static IServiceCollection AddPromotionEngineServices(this IServiceCollection services)
    {
        // 添加商品占用服务
        services.AddProductOccupationServices();

        // 添加其他促销引擎服务
        // services.AddScoped<IPromotionRuleFactory, PromotionRuleFactory>();
        // services.AddScoped<IPromotionCalculator, PromotionCalculator>();
        // services.AddScoped<IPromotionOptimizer, PromotionOptimizer>();

        return services;
    }
}
