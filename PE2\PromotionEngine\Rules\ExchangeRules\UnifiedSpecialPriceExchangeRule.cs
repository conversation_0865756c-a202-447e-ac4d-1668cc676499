using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.ExchangeRules;

/// <summary>
/// 统一特价换购规则 [OK]
/// 针对某类商品，满X件或X元，加Y元，换购一件C类商品
/// 场景案例：够买A商品1件时，若再增加1元可换购B商品，若增加2元可换购C商品。
/// 促销设置为不翻倍且梯度换：A商品吊牌价、零售价为1000元；购买A商品2件，且再加1元时，应收金额为2001元（2件A商品和1件B商品）
/// 促销设置为不翻倍且全部换：A商品吊牌价、零售价为1000元；购买A商品2件，且再加3元时，应收金额为2003元（2件A商品、1件B商品和1件C商品）
/// 促销设置为翻2倍且全部换：A商品吊牌价、零售价为1000元；购买A商品3件，且再加6元时，应收金额为3006元（3件A商品、2件B商品和2件C商品）
/// </summary>
public class UnifiedSpecialPriceExchangeRule : BaseExchangeRule
{
    public override string RuleType => "UnifiedSpecialPriceExchange";

    /// <summary>
    /// 购买条件列表
    /// </summary>
    public List<ExchangeBuyCondition> BuyConditions { get; set; } = new();

    /// <summary>
    /// 换购条件列表（支持梯度换购）
    /// </summary>
    public List<SpecialPriceExchangeCondition> ExchangeConditions { get; set; } = new();

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!BuyConditions.Any() || !ExchangeConditions.Any())
            return false;

        // 检查购买条件和换购商品是否在购物车中
        return CheckBuyConditions(cart) && ValidateExchangeProductsInCart(cart, GetAllExchangeProductIds());
    }

    /// <summary>
    /// 检查购买条件
    /// </summary>
    private bool CheckBuyConditions(ShoppingCart cart)
    {
        return BuyConditions.All(buyCondition =>
        {
            var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
            var totalAmount = buyCondition.ProductIds.Sum(id =>
                cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));

            return (buyCondition.RequiredQuantity <= 0 || availableQuantity >= buyCondition.RequiredQuantity) &&
                   (buyCondition.RequiredAmount <= 0 || totalAmount >= buyCondition.RequiredAmount);
        });
    }

    /// <summary>
    /// 获取所有换购商品ID
    /// </summary>
    private List<string> GetAllExchangeProductIds()
    {
        return ExchangeConditions.SelectMany(c => c.ExchangeProductIds).Distinct().ToList();
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        // 计算基于购买条件的最大应用次数
        var maxByBuyConditions = CalculateMaxApplicationsByBuyConditions(cart);

        // 计算基于换购商品的最大应用次数
        var maxByExchangeProducts = CalculateMaxApplicationsByExchangeProducts(cart);

        // 取两者最小值，确保换购逻辑正确
        var maxApplications = Math.Min(maxByBuyConditions, maxByExchangeProducts);

        return ApplyApplicationLimits(maxApplications);
    }

    /// <summary>
    /// 修复：根据购买条件计算最大应用次数 - 支持数量和金额双重条件
    /// </summary>
    private int CalculateMaxApplicationsByBuyConditions(ShoppingCart cart)
    {
        var maxApplications = 0;

        foreach (var buyCondition in BuyConditions)
        {
            var maxByThisCondition = CalculateMaxApplicationsBySingleBuyCondition(cart, buyCondition);
            maxApplications = Math.Max(maxApplications, maxByThisCondition);
        }

        return maxApplications > 0 ? maxApplications : 1;
    }

    /// <summary>
    /// 修复：根据单个购买条件计算最大应用次数
    /// </summary>
    private int CalculateMaxApplicationsBySingleBuyCondition(ShoppingCart cart, ExchangeBuyCondition buyCondition)
    {
        var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
        var totalAmount = buyCondition.ProductIds.Sum(id =>
            cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));

        var maxByQuantity = int.MaxValue;
        var maxByAmount = int.MaxValue;

        // 按数量计算最大应用次数
        if (buyCondition.RequiredQuantity > 0)
        {
            maxByQuantity = IsRepeatable
                ? availableQuantity / buyCondition.RequiredQuantity
                : (availableQuantity >= buyCondition.RequiredQuantity ? 1 : 0);
        }

        // 修复：按金额计算最大应用次数
        if (buyCondition.RequiredAmount > 0)
        {
            maxByAmount = IsRepeatable
                ? (int)(totalAmount / buyCondition.RequiredAmount)
                : (totalAmount >= buyCondition.RequiredAmount ? 1 : 0);
        }

        // 修复：当其中一个条件为0时，不应该限制应用次数
        if (buyCondition.RequiredQuantity <= 0 && buyCondition.RequiredAmount > 0)
        {
            // 只有金额条件
            return maxByAmount;
        }
        else if (buyCondition.RequiredQuantity > 0 && buyCondition.RequiredAmount <= 0)
        {
            // 只有数量条件
            return maxByQuantity;
        }
        else if (buyCondition.RequiredQuantity > 0 && buyCondition.RequiredAmount > 0)
        {
            // 两个条件都有，取最小值
            return Math.Min(maxByQuantity, maxByAmount);
        }

        // 都没有条件，返回1
        return 1;
    }
    /// <summary>
    /// 根据换购商品可用性计算最大应用次数
    /// </summary>
    private int CalculateMaxApplicationsByExchangeProducts(ShoppingCart cart)
    {
        var applicableExchanges = GetApplicableExchangeConditions();
        if (!applicableExchanges.Any())
            return 0;

        return applicableExchanges.Min(exchange =>
        {
            var totalAvailableQuantity = exchange.ExchangeProductIds
                .Sum(id => cart.Items.Where(x => x.Product.Id == id).Sum(x => x.Quantity));

            var requiredQuantity = Math.Max(exchange.ExchangeQuantity, 1);
            return totalAvailableQuantity / requiredQuantity;
        });
    }

    /// <summary>
    /// 修复：应用应用次数限制
    /// </summary>
    private int ApplyApplicationLimits(int maxApplications)
    {
        // 修复：当IsRepeatable为false时，最大应用次数应该是1，不管MaxApplications设置为多少
        if (!IsRepeatable)
        {
            maxApplications = Math.Min(maxApplications, 1);
        }
        else if (MaxApplications > 0)
        {
            // 只有当IsRepeatable为true时，才考虑MaxApplications的限制
            maxApplications = Math.Min(maxApplications, MaxApplications);
        }

        return maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var (discountAmount, consumedItems, exchangeItems) = ApplySpecialPriceExchange(cart, applicationCount);

            application.DiscountAmount = discountAmount;
            application.ConsumedItems = consumedItems;
            application.GiftItems = exchangeItems;
            application.IsSuccessful = discountAmount > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用特价换购促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用特价换购促销 - 核心逻辑优化
    /// </summary>
    private (decimal totalDiscount, List<ConsumedItem> consumed, List<GiftItem> exchanged) ApplySpecialPriceExchange(
        ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var exchangeItems = new List<GiftItem>();

        // 创建购物车副本进行计算，避免影响原始状态
        var workingCart = CreateCartSnapshot(cart);

        for (int application = 0; application < applicationCount; application++)
        {
            var (success, discount, exchanges) = TryApplySingleExchange(workingCart, consumedItems, application + 1);

            if (!success)
                break; // 无法继续应用，停止

            totalDiscountAmount += discount;
            exchangeItems.AddRange(exchanges);
        }

        // 将换购结果同步到原始购物车
        SyncExchangeResultsToCart(cart, exchangeItems);

        return (totalDiscountAmount, consumedItems, exchangeItems);
    }

    /// <summary>
    /// 尝试应用单次换购
    /// </summary>
    private (bool success, decimal discount, List<GiftItem> exchanges) TryApplySingleExchange(
        ShoppingCart workingCart, List<ConsumedItem> consumedItems, int applicationIndex)
    {
        foreach (var buyCondition in BuyConditions)
        {
            // 检查并消耗购买条件商品
            if (!CanAndConsumeBuyCondition(workingCart, buyCondition, consumedItems))
                continue;

            // 执行换购操作
            var (success, discount, exchanges) = ExecuteExchangeOperations(workingCart, applicationIndex);

            if (success)
                return (true, discount, exchanges);
        }

        return (false, 0m, new List<GiftItem>());
    }

    /// <summary>
    /// 修复：检查并消耗购买条件商品 - 支持数量和金额双重判断
    /// </summary>
    private bool CanAndConsumeBuyCondition(ShoppingCart cart, ExchangeBuyCondition buyCondition,
        List<ConsumedItem> consumedItems)
    {
        // 检查数量条件
        if (buyCondition.RequiredQuantity > 0)
        {
            var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
            if (availableQuantity < buyCondition.RequiredQuantity)
                return false;
        }

        // 修复：检查金额条件
        if (buyCondition.RequiredAmount > 0)
        {
            var availableAmount = CalculateAvailableAmount(cart, buyCondition.ProductIds);
            if (availableAmount < buyCondition.RequiredAmount)
                return false;
        }

        // 修复：对于纯金额条件，使用专门的消耗逻辑
        if (buyCondition.RequiredQuantity <= 0 && buyCondition.RequiredAmount > 0)
        {
            return ConsumeByAmountOnly(cart, buyCondition, consumedItems);
        }

        var consumeResult = ConsumeBuyConditionProducts(cart, buyCondition, consumedItems);

        return consumeResult;
    }

    /// <summary>
    /// 修复：计算可用商品的总金额
    /// </summary>
    private decimal CalculateAvailableAmount(ShoppingCart cart, List<string> productIds)
    {
        return productIds.Sum(id =>
            cart.Items.Where(x => x.Product.Id == id && x.AvailableQuantity > 0)
                     .Sum(x => x.AvailableQuantity * x.UnitPrice));
    }

    /// <summary>
    /// 执行换购操作
    /// </summary>
    private (bool success, decimal discount, List<GiftItem> exchanges) ExecuteExchangeOperations(
        ShoppingCart cart, int applicationIndex)
    {
        var totalDiscount = 0m;
        var exchangeItems = new List<GiftItem>();
        var applicableExchanges = GetApplicableExchangeConditions();

        foreach (var exchange in applicableExchanges)
        {
            var (exchangeSuccess, exchangeDiscount, exchangeItem) = ProcessSingleExchange(cart, exchange, applicationIndex);

            if (exchangeSuccess)
            {
                totalDiscount += exchangeDiscount;
                exchangeItems.Add(exchangeItem);

                // 梯度换购策略：只执行第一个成功的换购
                if (ExchangeStrategy == ExchangeStrategy.ByGradient)
                    break;
            }
        }

        return (exchangeItems.Any(), totalDiscount, exchangeItems);
    }

    /// <summary>
    /// 处理单个换购条件
    /// </summary>
    /// <summary>
    /// 处理单个换购条件 - 增加调试信息
    /// </summary>
    private (bool success, decimal discount, GiftItem exchangeItem) ProcessSingleExchange(
        ShoppingCart cart, SpecialPriceExchangeCondition exchange, int applicationIndex)
    {
        var selectedProducts = SelectOptimalExchangeProducts(
            exchange.ExchangeProductIds, cart, exchange.ExchangeQuantity);

        // 调试：检查选择的商品
        if (!selectedProducts.Any())
        {
            // 没有选择到换购商品，检查原因
            var availableProducts = exchange.ExchangeProductIds
                .Where(id => cart.Items.Any(x => x.Product.Id == id && x.Quantity > 0))
                .ToList();

            // 如果有可用商品但没选择到，可能是基类方法的问题
            if (availableProducts.Any())
            {
                // 直接选择第一个可用的换购商品
                selectedProducts = new List<string> { availableProducts.First() };
            }
        }

        foreach (var productId in selectedProducts)
        {
            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId && x.Quantity > 0);
            if (cartItem == null) continue;

            var discountAmount = cartItem.UnitPrice - exchange.AddAmount;

            // 调试：检查折扣计算
            if (discountAmount <= 0)
            {
                // 折扣为负数或0，跳过这个商品
                continue;
            }

            // 创建换购记录
            var exchangeItem = CreateExchangeItem(cartItem, exchange, discountAmount, applicationIndex);

            // 消耗换购商品
            cartItem.Quantity--;

            return (true, discountAmount, exchangeItem);
        }

        return (false, 0m, null);
    }

    /// <summary>
    /// 创建换购商品记录
    /// </summary>
    private GiftItem CreateExchangeItem(CartItem cartItem, SpecialPriceExchangeCondition exchange,
        decimal discountAmount, int applicationIndex)
    {
        var strategyDesc = ExchangeSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
            ? "客户利益最大化" : "商家利益最大化";

        return new GiftItem
        {
            ProductId = cartItem.Product.Id,
            ProductName = cartItem.Product.Name,
            Quantity = 1,
            Value = discountAmount,
            Description = $"特价换购：加{exchange.AddAmount:C}换购，节省{discountAmount:C}（{strategyDesc}）第{applicationIndex}次"
        };
    }

    /// <summary>
    /// 创建购物车快照
    /// </summary>
    private ShoppingCart CreateCartSnapshot(ShoppingCart original)
    {
        var snapshot = new ShoppingCart();
        foreach (var item in original.Items)
        {
            snapshot.Items.Add(new CartItem
            {
                Product = item.Product,
                Quantity = item.Quantity,
                UnitPrice = item.UnitPrice,
                ActualUnitPrice = item.ActualUnitPrice
            });
        }
        return snapshot;
    }

    /// <summary>
    /// 修复：同步换购结果到原始购物车
    /// </summary>
    private void SyncExchangeResultsToCart(ShoppingCart originalCart, List<GiftItem> exchangeItems)
    {
        foreach (var exchangeItem in exchangeItems)
        {
            var cartItem = originalCart.Items.FirstOrDefault(x => x.Product.Id == exchangeItem.ProductId);
            if (cartItem != null)
            {
                // 修复：更精确的换购价格获取
                var exchangePrice = GetExchangePriceForProduct(exchangeItem.ProductId);
                cartItem.ActualUnitPrice = exchangePrice;
            }
        }
    }

    /// <summary>
    /// 修复：获取指定商品的换购价格
    /// </summary>
    private decimal GetExchangePriceForProduct(string productId)
    {
        var applicableExchanges = GetApplicableExchangeConditions();
        var exchange = applicableExchanges.FirstOrDefault(e => e.ExchangeProductIds.Contains(productId));
        return exchange?.AddAmount ?? 0;
    }



    /// <summary>
    /// 修复：消耗购买条件商品 - 支持按数量和金额消耗
    /// </summary>
    private bool ConsumeBuyConditionProducts(ShoppingCart cart, ExchangeBuyCondition buyCondition,
        List<ConsumedItem> consumedItems)
    {
        var remainingQuantity = buyCondition.RequiredQuantity;
        var remainingAmount = buyCondition.RequiredAmount;

        // 修复：标记哪些条件需要满足
        var needQuantity = buyCondition.RequiredQuantity > 0;
        var needAmount = buyCondition.RequiredAmount > 0;

        // 修复：如果没有任何条件，直接返回true
        if (!needQuantity && !needAmount)
            return true;

        // 调试：记录初始状态
        var initialRemainingQuantity = remainingQuantity;
        var initialRemainingAmount = remainingAmount;

        foreach (var productId in buyCondition.ProductIds)
        {
            // 修复：正确的退出条件判断
            if ((!needQuantity || remainingQuantity <= 0) && (!needAmount || remainingAmount <= 0))
                break;

            var availableItems = cart.Items
                .Where(x => x.Product.Id == productId && x.AvailableQuantity > 0)
                .ToList();

            // 调试：检查可用商品
            if (!availableItems.Any())
                continue;

            foreach (var item in availableItems)
            {
                // 修复：正确的退出条件判断
                if ((!needQuantity || remainingQuantity <= 0) && (!needAmount || remainingAmount <= 0))
                    break;

                var consumeQuantity = CalculateConsumeQuantity(item, remainingQuantity, remainingAmount, needQuantity, needAmount);

                if (consumeQuantity <= 0)
                    continue;

                // 调试：记录消耗前的状态
                var beforeQuantity = remainingQuantity;
                var beforeAmount = remainingAmount;

                UpdateOrAddConsumedItem(consumedItems, productId, item, consumeQuantity);

                item.Quantity -= consumeQuantity;

                // 修复：只有在需要数量条件时才减少remainingQuantity
                if (needQuantity)
                    remainingQuantity = Math.Max(0, remainingQuantity - consumeQuantity);

                // 修复：只有在需要金额条件时才减少remainingAmount
                if (needAmount)
                    remainingAmount = Math.Max(0, remainingAmount - (consumeQuantity * item.UnitPrice));

                // 调试：记录消耗后的状态
                var afterQuantity = remainingQuantity;
                var afterAmount = remainingAmount;
            }
        }

        // 修复：检查是否满足所有条件
        var quantityMet = !needQuantity || remainingQuantity <= 0;
        var amountMet = !needAmount || remainingAmount <= 0;

        // 调试：详细的结果检查
        var result = quantityMet && amountMet;

        // 添加详细的调试信息
        if (!result)
        {
            // 调试信息：
            // needQuantity: {needQuantity}, quantityMet: {quantityMet}, remainingQuantity: {remainingQuantity}
            // needAmount: {needAmount}, amountMet: {amountMet}, remainingAmount: {remainingAmount}
            // initialRemainingAmount: {initialRemainingAmount}
        }

        return result;
    }

    /// <summary>
    /// 修复：计算需要消耗的商品数量 - 考虑数量和金额双重限制
    /// </summary>
    private int CalculateConsumeQuantity(CartItem item, int remainingQuantity, decimal remainingAmount,
        bool needQuantity, bool needAmount)
    {
        var maxQuantity = item.AvailableQuantity;

        // 修复：只有在需要数量条件时才限制数量
        if (needQuantity && remainingQuantity > 0)
        {
            maxQuantity = Math.Min(maxQuantity, remainingQuantity);
        }

        // 修复：只有在需要金额条件时才限制金额
        if (needAmount && remainingAmount > 0)
        {
            // 修复：确保计算正确的数量
            var maxByAmount = (int)Math.Ceiling(remainingAmount / item.UnitPrice);
            maxQuantity = Math.Min(maxQuantity, maxByAmount);
        }

        // 修复：如果既不需要数量也不需要金额（理论上不会发生，但作为保险）
        if (!needQuantity && !needAmount)
        {
            return 0;
        }

        // 修复：确保至少返回合理的数量
        return Math.Max(0, maxQuantity);
    }

    /// <summary>
    /// 新增：专门处理纯金额条件的消耗逻辑
    /// </summary>
    private bool ConsumeByAmountOnly(ShoppingCart cart, ExchangeBuyCondition buyCondition,
        List<ConsumedItem> consumedItems)
    {
        var remainingAmount = buyCondition.RequiredAmount;
        var totalConsumedAmount = 0m;

        foreach (var productId in buyCondition.ProductIds)
        {
            if (remainingAmount <= 0)
                break;

            var availableItems = cart.Items
                .Where(x => x.Product.Id == productId && x.AvailableQuantity > 0)
                .OrderBy(x => x.UnitPrice) // 优先消耗便宜的商品
                .ToList();

            foreach (var item in availableItems)
            {
                if (remainingAmount <= 0)
                    break;

                // 计算需要消耗多少件来满足金额要求
                var neededQuantity = Math.Min(item.AvailableQuantity,
                    (int)Math.Ceiling(remainingAmount / item.UnitPrice));

                if (neededQuantity <= 0)
                    continue;

                var consumeAmount = neededQuantity * item.UnitPrice;

                UpdateOrAddConsumedItem(consumedItems, productId, item, neededQuantity);

                item.Quantity -= neededQuantity;
                remainingAmount -= consumeAmount;
                totalConsumedAmount += consumeAmount;
            }
        }

        return totalConsumedAmount >= buyCondition.RequiredAmount;
    }

    /// <summary>
    /// 更新或添加消耗商品记录
    /// </summary>
    private void UpdateOrAddConsumedItem(List<ConsumedItem> consumedItems, string productId,
        CartItem item, int consumeQuantity)
    {
        var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == productId);

        if (existingConsumed != null)
        {
            existingConsumed.Quantity += consumeQuantity;
        }
        else
        {
            consumedItems.Add(new ConsumedItem
            {
                ProductId = productId,
                ProductName = item.Product.Name,
                Quantity = consumeQuantity,
                UnitPrice = item.UnitPrice
            });
        }
    }

    /// <summary>
    /// 获取适用的换购条件
    /// </summary>
    private List<SpecialPriceExchangeCondition> GetApplicableExchangeConditions()
    {
        return ExchangeStrategy switch
        {
            ExchangeStrategy.ByGradient => ExchangeConditions.Take(1).ToList(),
            ExchangeStrategy.AllExchange => ExchangeConditions.ToList(),
            _ => ExchangeConditions.Take(1).ToList()
        };
    }
}

/// <summary>
/// 换购购买条件
/// </summary>
public class ExchangeBuyCondition
{
    /// <summary>
    /// 商品ID列表
    /// </summary>
    public List<string> ProductIds { get; set; } = new();

    /// <summary>
    /// 所需数量（0表示不限制数量）
    /// </summary>
    public int RequiredQuantity { get; set; }

    /// <summary>
    /// 所需金额（0表示不限制金额）
    /// </summary>
    public decimal RequiredAmount { get; set; }
}

/// <summary>
/// 特价换购条件
/// </summary>
public class SpecialPriceExchangeCondition
{
    /// <summary>
    /// 换购商品ID列表
    /// </summary>
    public List<string> ExchangeProductIds { get; set; } = new();

    /// <summary>
    /// 换购数量
    /// </summary>
    public int ExchangeQuantity { get; set; } = 1;

    /// <summary>
    /// 加价金额
    /// </summary>
    public decimal AddAmount { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}