<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Buy Conditions 字段测试</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    
    <style>
        .test-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>Buy Conditions 字段渲染测试</h1>            <div class="test-section">
                <h2>直接测试 ComplexArrayRenderer</h2>
                <p>测试 buyConditions 字段是否能正确渲染</p>
                
                <div v-if="!buyConditionsField" class="loading">
                    加载中...
                </div>
                
                <div v-else>
                    <h4>字段类型检查:</h4>
                    <p>字段类型: {{ buyConditionsField.type }}</p>
                    <p>是否有 metadata: {{ !!buyConditionsField.metadata }}</p>
                    <p>是否有 elementSchema: {{ !!buyConditionsField.metadata?.elementSchema }}</p>
                    
                    <h4>elementSchema 字段数量:</h4>
                    <p>{{ buyConditionsField.metadata?.elementSchema?.fields?.length || 0 }} 个字段</p>
                    
                    <div style="margin: 10px 0;">
                        <el-button @click="showDebugInfo = !showDebugInfo">
                            {{ showDebugInfo ? '隐藏' : '显示' }}调试信息
                        </el-button>
                    </div>
                    
                    <div v-if="showDebugInfo">
                        <h4>完整字段配置:</h4>
                        <pre>{{ JSON.stringify(buyConditionsField, null, 2) }}</pre>
                    </div>
                    
                    <div style="border: 2px solid #409eff; padding: 16px; margin: 16px 0;">
                        <h4>ComplexArrayRenderer 输出:</h4>
                        <complex-array-renderer
                            field-name="buyConditions"
                            :field-config="buyConditionsField"
                            :model-value="buyConditionsValue"
                            @update:model-value="value => { 
                                console.log('值更新:', value); 
                                buyConditionsValue = value; 
                            }"
                        ></complex-array-renderer>
                    </div>
                </div>
                
                <div style="margin-top: 20px;">
                    <h4>当前值:</h4>
                    <pre>{{ JSON.stringify(buyConditionsValue, null, 2) }}</pre>
                </div>
            </div>
            
            <div class="test-section">
                <h2>字段配置信息</h2>
                <el-collapse>
                    <el-collapse-item title="查看 buyConditions 字段配置" name="1">
                        <pre>{{ JSON.stringify(buyConditionsField, null, 2) }}</pre>
                    </el-collapse-item>
                </el-collapse>
            </div>
        </div>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.min.js"></script>
      <!-- 组件 -->
    <script src="js/components/ProductSelector.js"></script>
    <script src="js/components/ComplexArrayRenderer.js"></script>

    <script>
        const { createApp, ref } = Vue;
        const { ElMessage } = ElementPlus;        createApp({
            components: {
                'complex-array-renderer': ComplexArrayRenderer,
                'product-selector': ProductSelector
            },
            setup() {
                console.log('Test app setup started');
                
                // buyConditions字段配置
                const buyConditionsField = ref({
                    "name": "buyConditions",
                    "label": "Buy Conditions",
                    "type": "array-complex",
                    "required": false,
                    "group": "condition",
                    "default": null,
                    "options": null,
                    "validation": null,
                    "metadata": {
                        "elementSchema": {
                            "typeName": "ExchangeBuyCondition",
                            "displayName": "ExchangeBuyCondition",
                            "description": null,
                            "fields": [
                                {
                                    "name": "productIds",
                                    "label": "Product Ids",
                                    "type": {
                                        "type": "array-simple",
                                        "isNullable": false,
                                        "elementType": "input"
                                    },
                                    "required": false,
                                    "group": "condition",
                                    "description": null,
                                    "validation": null,
                                    "default": null
                                },
                                {
                                    "name": "requiredQuantity",
                                    "label": "Required Quantity",
                                    "type": {
                                        "type": "number",
                                        "isNullable": false
                                    },
                                    "required": false,
                                    "group": "condition",
                                    "description": null,
                                    "validation": null,
                                    "default": null
                                },
                                {
                                    "name": "requiredAmount",
                                    "label": "Required Amount",
                                    "type": {
                                        "type": "number",
                                        "isNullable": false
                                    },
                                    "required": false,
                                    "group": "condition",
                                    "description": null,
                                    "validation": null,
                                    "default": null
                                }
                            ],
                            "category": "condition"
                        },
                        "defaultItem": {
                            "productIds": [],
                            "requiredQuantity": 0,
                            "requiredAmount": 0
                        },
                        "operations": [
                            "add",
                            "remove",
                            "edit",
                            "reorder"
                        ]
                    },
                    "isNullable": false,
                    "elementType": "ExchangeBuyCondition",
                    "typeName": null
                });
                  // buyConditions的值
                const buyConditionsValue = ref([]);
                  // 显示调试信息
                const showDebugInfo = ref(false);

                console.log('buyConditionsField created:', buyConditionsField.value);
                console.log('buyConditionsValue created:', buyConditionsValue.value);

                return {
                    buyConditionsField,
                    buyConditionsValue,
                    showDebugInfo
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
