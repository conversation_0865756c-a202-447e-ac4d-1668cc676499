using PE2.Models;
using PE2.PromotionEngine.Models;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Rules.DiscountRules;

/// <summary>
/// 组合折扣规则
/// 某A类商品满X件或X元并且某B类商品满T件或Y元，打Z折
/// 场景：购买A商品和B商品各大于等于1件，优惠C商品，C商品打9折
/// </summary>
public class CombinationDiscountRule : BaseDiscountRule
{
    public override string RuleType => "CombinationDiscount";

    /// <summary>
    /// 组合购买条件列表
    /// </summary>
    public List<CombinationCondition> CombinationConditions { get; set; } = new();

    /// <summary>
    /// 享受折扣的商品ID列表
    /// </summary>
    public List<string> DiscountProductIds { get; set; } = new();

    /// <summary>
    /// 折扣率（0.9表示9折）
    /// </summary>
    public decimal DiscountRate { get; set; }

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!CombinationConditions.Any() || !DiscountProductIds.Any())
            return false;

        // 验证组合商品是否在购物车中
        var allConditionProductIds = CombinationConditions.Select(c => c.ProductId).ToList();
        if (!ValidateDiscountProductsInCart(cart, allConditionProductIds))
            return false;

        // 验证折扣商品是否在购物车中
        if (!ValidateDiscountProductsInCart(cart, DiscountProductIds))
            return false;

        // 检查组合购买条件
        return CheckCombinationConditions(cart);
    }

    /// <summary>
    /// 检查组合购买条件
    /// </summary>
    private bool CheckCombinationConditions(ShoppingCart cart)
    {
        foreach (var condition in CombinationConditions)
        {
            var availableQuantity = cart.GetAvailableProductQuantity(condition.ProductId);
            var totalAmount = cart.Items
                .Where(x => x.Product.Id == condition.ProductId)
                .Sum(x => x.SubTotal);

            if (condition.RequiredQuantity > 0 && availableQuantity < condition.RequiredQuantity)
                return false;

            if (condition.RequiredAmount > 0 && totalAmount < condition.RequiredAmount)
                return false;
        }

        return true;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = int.MaxValue;

        // 计算每个组合条件的最大应用次数
        foreach (var condition in CombinationConditions)
        {
            var conditionMaxApplications = 0;

            if (condition.RequiredQuantity > 0)
            {
                var availableQuantity = cart.GetAvailableProductQuantity(condition.ProductId);
                conditionMaxApplications = IsRepeatable 
                    ? availableQuantity / condition.RequiredQuantity
                    : (availableQuantity >= condition.RequiredQuantity ? 1 : 0);
            }
            else if (condition.RequiredAmount > 0)
            {
                var totalAmount = cart.Items
                    .Where(x => x.Product.Id == condition.ProductId)
                    .Sum(x => x.SubTotal);
                conditionMaxApplications = IsRepeatable 
                    ? (int)(totalAmount / condition.RequiredAmount)
                    : (totalAmount >= condition.RequiredAmount ? 1 : 0);
            }
            else
            {
                conditionMaxApplications = 1;
            }

            maxApplications = Math.Min(maxApplications, conditionMaxApplications);
        }

        if (maxApplications == int.MaxValue)
            maxApplications = 1;

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyCombinationDiscount(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用组合折扣促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用组合折扣促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyCombinationDiscount(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var discountRecords = new List<GiftItem>(); // 折扣记录

        for (int app = 0; app < applicationCount; app++)
        {
            // 消耗组合条件商品（记录消耗但不修改购物车）
            var combinationConsumed = ConsumeCombinationProducts(cart);
            if (!combinationConsumed.Any()) break;

            // 获取享受折扣的商品项
            var discountItems = cart.Items
                .Where(x => DiscountProductIds.Contains(x.Product.Id) && x.Quantity > 0)
                .ToList();

            if (!discountItems.Any()) break;

            // 根据策略排序商品
            var sortedItems = SortItemsByStrategy(discountItems);

            var promotion = new AppliedPromotion
            {
                RuleId = Id,
                RuleName = Name,
                PromotionType = RuleType
            };

            // 对折扣商品应用折扣
            foreach (var item in sortedItems)
            {
                var originalPrice = item.UnitPrice;
                var discountedPrice = originalPrice * DiscountRate;
                var discountAmount = (originalPrice - discountedPrice) * item.Quantity;

                if (discountAmount > 0)
                {
                    totalDiscountAmount += discountAmount;

                    var strategyDescription = DiscountSelectionStrategy == DiscountSelectionStrategy.CustomerBenefit
                        ? "客户利益最大化"
                        : "商家利益最大化";

                    var conditionDescription = string.Join("、", CombinationConditions.Select(c => 
                        c.RequiredQuantity > 0 ? $"{c.ProductId}满{c.RequiredQuantity}件" : $"{c.ProductId}满{c.RequiredAmount:C}"));
                    
                    var description = $"组合折扣：{conditionDescription}享{DiscountRate:P1}折，节省{discountAmount:C}（{strategyDescription}）";

                    // 应用折扣到商品项
                    ApplyDiscountToCartItem(item, DiscountRate, promotion, description);

                    // 记录折扣
                    discountRecords.Add(CreateDiscountRecord(
                        item.Product.Id,
                        item.Product.Name,
                        item.Quantity,
                        discountAmount,
                        description
                    ));
                }
            }

            // 合并消耗记录
            foreach (var item in combinationConsumed)
            {
                var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == item.ProductId);
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += item.Quantity;
                }
                else
                {
                    consumedItems.Add(item);
                }
            }
        }

        return (totalDiscountAmount, consumedItems, discountRecords);
    }

    /// <summary>
    /// 消耗组合条件商品
    /// </summary>
    private List<ConsumedItem> ConsumeCombinationProducts(ShoppingCart cart)
    {
        var consumedItems = new List<ConsumedItem>();

        foreach (var condition in CombinationConditions)
        {
            var requiredQuantity = condition.RequiredQuantity;
            if (requiredQuantity <= 0) continue;

            var consumed = ConsumeConditionProducts(cart, new List<string> { condition.ProductId }, requiredQuantity);
            consumedItems.AddRange(consumed);
        }

        return consumedItems;
    }
}

/// <summary>
/// 组合条件
/// </summary>
public class CombinationCondition
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 所需数量
    /// </summary>
    public int RequiredQuantity { get; set; }

    /// <summary>
    /// 所需金额
    /// </summary>
    public decimal RequiredAmount { get; set; }
}
