using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Core;
using PE2.PromotionEngine.Observability;
using PE2.PromotionEngine.Conditions;
using PE2.PromotionEngine.Inventory;
using PE2.PromotionEngine.Performance;
using PE2.PromotionEngine.Allocation;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Rules.BuyFreeRules;

/// <summary>
/// 新架构商品买免规则
/// 针对某一类商品，满X件免Y件
/// 集成新架构的所有基础设施组件，支持.NET 9.0语法和异步模式
/// </summary>
public sealed class NewProductBuyFreeRule : NewBuyFreeRuleBase, INewPromotionRule
{
    /// <summary>
    /// 构造函数
    /// </summary>
    public NewProductBuyFreeRule(
        ILogger<NewProductBuyFreeRule> logger,
        IObservabilityEngine observability,
        IConditionEngine conditionEngine,
        IInventoryManager inventoryManager,
        IPerformanceOptimizer performanceOptimizer,
        IAllocationEngine allocationEngine,
        ProductBuyFreeConfiguration configuration)
        : base(logger, observability, conditionEngine, inventoryManager, performanceOptimizer, allocationEngine)
    {
        ArgumentNullException.ThrowIfNull(configuration);
        
        Id = configuration.Id;
        Name = configuration.Name;
        Description = configuration.Description;
        Priority = configuration.Priority;
        IsEnabled = configuration.IsEnabled;
        StartTime = configuration.StartTime;
        EndTime = configuration.EndTime;
        IsRepeatable = configuration.IsRepeatable;
        MaxApplications = configuration.MaxApplications;
        ApplicableCustomerTypes = configuration.ApplicableCustomerTypes;
        ExclusiveRuleIds = configuration.ExclusiveRuleIds;
        CanStackWithOthers = configuration.CanStackWithOthers;
        ProductExclusivity = configuration.ProductExclusivity;
        FreeItemSelectionStrategy = configuration.FreeItemSelectionStrategy;
        ApplicableProductIds = configuration.ApplicableProductIds;
        BuyQuantity = configuration.BuyQuantity;
        FreeQuantity = configuration.FreeQuantity;
    }

    /// <summary>
    /// 规则类型
    /// </summary>
    public override string RuleType => "ProductBuyFree";

    /// <summary>
    /// 适用的商品ID列表
    /// </summary>
    public List<string> ApplicableProductIds { get; init; } = [];

    /// <summary>
    /// 购买数量要求
    /// </summary>
    public int BuyQuantity { get; init; }

    /// <summary>
    /// 免费数量
    /// </summary>
    public int FreeQuantity { get; init; }

    /// <summary>
    /// 异步检查促销条件是否满足
    /// </summary>
    protected override async Task<bool> CheckConditionsAsync(ShoppingCart cart, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证适用商品是否在购物车中
            if (!await ValidateBuyFreeProductsInCartAsync(cart, ApplicableProductIds, cancellationToken).ConfigureAwait(false))
                return false;

            // 检查是否有足够的商品数量
            var applicableItems = cart.Items.Where(x => 
                ApplicableProductIds.Contains(x.ProductId) && x.Quantity > 0);
            
            if (!applicableItems.Any())
                return false;

            var totalQuantity = applicableItems.Sum(x => x.Quantity);
            return totalQuantity >= BuyQuantity + FreeQuantity;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "检查商品买免条件时发生异常: {RuleId}", Id);
            return false;
        }
    }

    /// <summary>
    /// 异步计算可应用的最大次数
    /// </summary>
    public override async Task<int> CalculateMaxApplicationsAsync(ShoppingCart cart, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!await CheckConditionsAsync(cart, cancellationToken).ConfigureAwait(false))
                return 0;

            // 使用性能优化器计算最大应用次数
            var optimizationContext = new OptimizationContext
            {
                Cart = cart,
                Rules = [this],
                Target = OptimizationTarget.MaximizeApplications
            };

            var result = await PerformanceOptimizer.OptimizeRuleCombinationAsync(optimizationContext, cancellationToken).ConfigureAwait(false);

            var applicableItems = cart.Items.Where(x => 
                ApplicableProductIds.Contains(x.ProductId) && x.Quantity > 0);
            
            if (!applicableItems.Any())
                return 0;

            var totalQuantity = applicableItems.Sum(x => x.Quantity);
            var maxApplications = totalQuantity / (BuyQuantity + FreeQuantity);

            return ApplyApplicationLimits(maxApplications);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "计算商品买免最大应用次数时发生异常: {RuleId}", Id);
            return 0;
        }
    }

    /// <summary>
    /// 异步应用促销规则
    /// </summary>
    public override async Task<AppliedPromotion> ApplyPromotionAsync(
        ProcessedCart cart, 
        int applicationCount = 1, 
        string? traceId = null,
        CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        
        try
        {
            // 记录开始追踪
            if (!string.IsNullOrEmpty(traceId))
            {
                Observability.TrackCalculationStep(traceId, new CalculationStep
                {
                    Id = Guid.NewGuid().ToString("N"),
                    TraceId = traceId,
                    StepType = StepType.PromotionApplication,
                    Timestamp = startTime,
                    Description = $"开始应用商品买免规则: {Name}",
                    RuleId = Id,
                    Data = new Dictionary<string, object>
                    {
                        ["applicationCount"] = applicationCount,
                        ["cartItemCount"] = cart.Items.Count,
                        ["buyQuantity"] = BuyQuantity,
                        ["freeQuantity"] = FreeQuantity
                    }
                });
            }

            // 预留库存
            var reservationId = await ReserveInventoryAsync(cart, applicationCount, cancellationToken).ConfigureAwait(false);

            try
            {
                // 应用商品买免逻辑
                var (discountAmount, consumedItems, giftItems) = await ApplyProductBuyFreeAsync(
                    cart, applicationCount, traceId, cancellationToken).ConfigureAwait(false);

                var appliedPromotion = CreateAppliedPromotion(discountAmount, applicationCount, consumedItems, giftItems);

                // 记录促销应用追踪
                TrackPromotionApplication(traceId, appliedPromotion);

                // 使用分配引擎进行精确的折扣分配
                if (discountAmount > 0)
                {
                    var allocationRequest = new AllocationRequest
                    {
                        TotalDiscount = discountAmount,
                        Items = cart.Items.Where(item => giftItems.Any(gi => gi.ProductId == item.ProductId)).ToList(),
                        Strategy = AllocationStrategy.ProportionalByValue,
                        RoundingStrategy = RoundingStrategy.RoundToNearest
                    };

                    var allocationResult = await AllocationEngine.AllocateDiscountAsync(allocationRequest, cancellationToken).ConfigureAwait(false);
                    
                    // 应用分配结果到购物车
                    ApplyAllocationToCart(cart, allocationResult);
                }

                return appliedPromotion;
            }
            finally
            {
                // 释放库存预留
                if (!string.IsNullOrEmpty(reservationId))
                {
                    await InventoryManager.ReleaseReservationAsync(reservationId, cancellationToken).ConfigureAwait(false);
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "应用商品买免规则时发生异常: {RuleId}", Id);
            
            return CreateAppliedPromotion(0, 0, [], []);
        }
        finally
        {
            // 记录性能指标
            var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
            Logger.LogDebug("商品买免规则执行完成: {RuleId}, 耗时: {ExecutionTime}ms", Id, executionTime);
        }
    }

    /// <summary>
    /// 应用应用次数限制
    /// </summary>
    private int ApplyApplicationLimits(int maxApplications)
    {
        if (!IsRepeatable)
        {
            maxApplications = Math.Min(maxApplications, 1);
        }
        else if (MaxApplications > 0)
        {
            maxApplications = Math.Min(maxApplications, MaxApplications);
        }

        return maxApplications;
    }

    /// <summary>
    /// 验证买免商品是否在购物车中
    /// </summary>
    private async Task<bool> ValidateBuyFreeProductsInCartAsync(
        ShoppingCart cart, 
        List<string> productIds, 
        CancellationToken cancellationToken)
    {
        try
        {
            foreach (var productId in productIds)
            {
                var hasProduct = cart.Items.Any(item => item.ProductId == productId && item.Quantity > 0);
                if (!hasProduct)
                    return false;

                // 验证库存可用性
                var available = await InventoryManager.GetAvailableQuantityAsync(productId, cancellationToken).ConfigureAwait(false);
                if (available <= 0)
                    return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "验证买免商品时发生异常: {RuleId}", Id);
            return false;
        }
    }

    /// <summary>
    /// 预留库存
    /// </summary>
    private async Task<string?> ReserveInventoryAsync(ProcessedCart cart, int applicationCount, CancellationToken cancellationToken)
    {
        try
        {
            var requiredProducts = new List<(string ProductId, int Quantity)>();

            // 计算需要预留的商品数量
            var totalRequiredQuantity = (BuyQuantity + FreeQuantity) * applicationCount;
            
            foreach (var productId in ApplicableProductIds)
            {
                requiredProducts.Add((productId, totalRequiredQuantity));
            }

            if (!requiredProducts.Any())
                return null;

            var reservationRequest = new ReservationRequest
            {
                ReservationId = Guid.NewGuid().ToString("N"),
                ProductQuantities = requiredProducts.ToDictionary(p => p.ProductId, p => p.Quantity),
                Priority = ReservationPriority.High,
                ExpirationTime = DateTime.UtcNow.AddMinutes(5),
                Source = $"Rule_{Id}"
            };

            var result = await InventoryManager.ReserveProductsAsync(reservationRequest, cancellationToken).ConfigureAwait(false);
            return result.IsSuccessful ? reservationRequest.ReservationId : null;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "预留库存时发生异常: {RuleId}", Id);
            return null;
        }
    }

    /// <summary>
    /// 应用商品买免逻辑
    /// </summary>
    private async Task<(decimal totalDiscount, List<ConsumedItem> consumed, List<GiftItem> gifts)> ApplyProductBuyFreeAsync(
        ProcessedCart cart,
        int applicationCount,
        string? traceId,
        CancellationToken cancellationToken)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var giftItems = new List<GiftItem>();

        try
        {
            var processedByProduct = new Dictionary<string, int>();

            for (int application = 0; application < applicationCount; application++)
            {
                var (success, discount, gifts) = await TryApplySingleProductBuyFreeAsync(
                    cart, processedByProduct, application + 1, traceId, cancellationToken).ConfigureAwait(false);

                if (!success)
                    break; // 无法继续应用，停止

                totalDiscountAmount += discount;
                giftItems.AddRange(gifts);

                if (!IsRepeatable)
                    break;
            }

            return (totalDiscountAmount, consumedItems, giftItems);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "应用商品买免逻辑时发生异常: {RuleId}", Id);
            return (0, [], []);
        }
    }

    /// <summary>
    /// 尝试应用单次商品买免
    /// </summary>
    private async Task<(bool success, decimal discount, List<GiftItem> gifts)> TryApplySingleProductBuyFreeAsync(
        ProcessedCart cart,
        Dictionary<string, int> processedByProduct,
        int applicationIndex,
        string? traceId,
        CancellationToken cancellationToken)
    {
        try
        {
            // 计算买免分配
            var (buyItems, freeItems) = await CalculateBuyFreeAllocationAsync(cart, processedByProduct, cancellationToken).ConfigureAwait(false);

            if (!buyItems.Any() || !freeItems.Any())
                return (false, 0, []);

            // 处理购买商品（消耗数量）
            ProcessBuyItems(buyItems, processedByProduct);

            // 处理免费商品
            var (freeDiscount, gifts) = await ProcessFreeItemsAsync(freeItems, processedByProduct, traceId, cancellationToken).ConfigureAwait(false);

            return (freeDiscount > 0, freeDiscount, gifts);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "尝试应用单次商品买免时发生异常: {RuleId}, ApplicationIndex: {ApplicationIndex}", Id, applicationIndex);
            return (false, 0, []);
        }
    }

    /// <summary>
    /// 计算买免分配
    /// </summary>
    private async Task<(List<(CartItem Item, int Quantity)> buyItems, List<(CartItem Item, int Quantity)> freeItems)> CalculateBuyFreeAllocationAsync(
        ProcessedCart cart,
        Dictionary<string, int> processedByProduct,
        CancellationToken cancellationToken)
    {
        try
        {
            // 获取可用商品列表
            var availableItems = GetAvailableItems(cart, processedByProduct);

            if (!HasSufficientItems(availableItems))
                return ([], []);

            // 根据策略排序商品
            var buyOrderItems = FreeItemSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                ? availableItems.OrderBy(x => x.Item.OriginalUnitPrice).ToList()
                : availableItems.OrderByDescending(x => x.Item.OriginalUnitPrice).ToList();

            var freeOrderItems = FreeItemSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                ? availableItems.OrderByDescending(x => x.Item.OriginalUnitPrice).ToList()
                : availableItems.OrderBy(x => x.Item.OriginalUnitPrice).ToList();

            // 分配购买商品
            var buyItems = AllocateItems(buyOrderItems, BuyQuantity);
            if (buyItems.Sum(x => x.Quantity) < BuyQuantity)
                return ([], []);

            // 更新可用数量后分配免费商品
            var updatedAvailable = UpdateAvailableAfterAllocation(availableItems, buyItems);
            var freeItems = AllocateItems(
                freeOrderItems
                    .Select(x => (x.Item, updatedAvailable.GetValueOrDefault(x.Item.ProductId, 0)))
                    .ToList(),
                FreeQuantity
            );

            return freeItems.Sum(x => x.Quantity) >= FreeQuantity
                ? (buyItems, freeItems)
                : ([], []);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "计算买免分配时发生异常: {RuleId}", Id);
            return ([], []);
        }
    }

    /// <summary>
    /// 获取可用商品列表
    /// </summary>
    private List<(CartItem Item, int Available)> GetAvailableItems(
        ProcessedCart cart,
        Dictionary<string, int> processedByProduct)
    {
        return cart.Items
            .Where(x => ApplicableProductIds.Contains(x.ProductId) && x.Quantity > 0)
            .Select(x => (x, Math.Max(0, x.Quantity - processedByProduct.GetValueOrDefault(x.ProductId, 0))))
            .Where(x => x.Item2 > 0)
            .ToList();
    }

    /// <summary>
    /// 检查是否有足够商品
    /// </summary>
    private bool HasSufficientItems(List<(CartItem Item, int Available)> items)
    {
        return items.Sum(x => x.Available) >= BuyQuantity + FreeQuantity;
    }

    /// <summary>
    /// 分配商品
    /// </summary>
    private static List<(CartItem Item, int Quantity)> AllocateItems(
        List<(CartItem Item, int Available)> items,
        int requiredQuantity)
    {
        var allocated = new List<(CartItem Item, int Quantity)>();
        var remaining = requiredQuantity;

        foreach (var (item, available) in items)
        {
            if (remaining <= 0) break;

            var allocateQuantity = Math.Min(available, remaining);
            if (allocateQuantity > 0)
            {
                allocated.Add((item, allocateQuantity));
                remaining -= allocateQuantity;
            }
        }

        return allocated;
    }

    /// <summary>
    /// 更新分配后的可用数量
    /// </summary>
    private static Dictionary<string, int> UpdateAvailableAfterAllocation(
        List<(CartItem Item, int Available)> availableItems,
        List<(CartItem Item, int Quantity)> allocatedItems)
    {
        var updated = availableItems.ToDictionary(x => x.Item.ProductId, x => x.Available);

        foreach (var (item, quantity) in allocatedItems)
        {
            if (updated.ContainsKey(item.ProductId))
            {
                updated[item.ProductId] = Math.Max(0, updated[item.ProductId] - quantity);
            }
        }

        return updated;
    }

    /// <summary>
    /// 处理购买商品
    /// </summary>
    private static void ProcessBuyItems(
        List<(CartItem Item, int Quantity)> buyItems,
        Dictionary<string, int> processedByProduct)
    {
        foreach (var (item, quantity) in buyItems)
        {
            processedByProduct[item.ProductId] = processedByProduct.GetValueOrDefault(item.ProductId, 0) + quantity;
        }
    }

    /// <summary>
    /// 处理免费商品
    /// </summary>
    private async Task<(decimal discount, List<GiftItem> gifts)> ProcessFreeItemsAsync(
        List<(CartItem Item, int Quantity)> freeItems,
        Dictionary<string, int> processedByProduct,
        string? traceId,
        CancellationToken cancellationToken)
    {
        var totalDiscount = 0m;
        var gifts = new List<GiftItem>();

        try
        {
            foreach (var (item, quantity) in freeItems)
            {
                var freeDiscount = item.OriginalUnitPrice * quantity;
                totalDiscount += freeDiscount;

                // 更新商品实际价格为0（免费）
                item.ActualUnitPrice = 0;

                gifts.Add(new GiftItem
                {
                    ProductId = item.ProductId,
                    ProductName = item.ProductName,
                    Quantity = quantity,
                    UnitPrice = 0,
                    OriginalUnitPrice = item.OriginalUnitPrice
                });

                // 更新已处理数量
                processedByProduct[item.ProductId] = processedByProduct.GetValueOrDefault(item.ProductId, 0) + quantity;
            }

            return (totalDiscount, gifts);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "处理免费商品时发生异常: {RuleId}", Id);
            return (0, []);
        }
    }

    /// <summary>
    /// 应用分配结果到购物车
    /// </summary>
    private static void ApplyAllocationToCart(ProcessedCart cart, AllocationResult allocationResult)
    {
        foreach (var allocation in allocationResult.Allocations)
        {
            var cartItem = cart.Items.FirstOrDefault(item => item.ProductId == allocation.ProductId);
            if (cartItem != null)
            {
                cartItem.ActualUnitPrice = Math.Max(0, cartItem.ActualUnitPrice - allocation.AllocatedDiscount / cartItem.Quantity);
            }
        }
    }
}

/// <summary>
/// 商品买免规则配置
/// </summary>
public sealed class ProductBuyFreeConfiguration
{
    public required string Id { get; init; }
    public required string Name { get; init; }
    public string Description { get; init; } = string.Empty;
    public int Priority { get; init; } = 0;
    public bool IsEnabled { get; init; } = true;
    public DateTime? StartTime { get; init; }
    public DateTime? EndTime { get; init; }
    public bool IsRepeatable { get; init; } = true;
    public int MaxApplications { get; init; } = 1;
    public List<string> ApplicableCustomerTypes { get; init; } = [];
    public List<string> ExclusiveRuleIds { get; init; } = [];
    public bool CanStackWithOthers { get; init; } = true;
    public ProductExclusivityLevel ProductExclusivity { get; init; } = ProductExclusivityLevel.None;
    public BenefitSelectionStrategy FreeItemSelectionStrategy { get; init; } = BenefitSelectionStrategy.CustomerBenefit;
    public List<string> ApplicableProductIds { get; init; } = [];
    public int BuyQuantity { get; init; }
    public int FreeQuantity { get; init; }
}
