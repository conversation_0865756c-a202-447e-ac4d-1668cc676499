using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Performance;

/// <summary>
/// 性能优化器接口 - 促销计算性能优化
/// </summary>
public interface IPerformanceOptimizer
{
    /// <summary>
    /// 预筛选促销规则
    /// </summary>
    Task<List<PromotionRuleBase>> PrefilterRulesAsync(List<PromotionRuleBase> allRules, ShoppingCart cart);

    /// <summary>
    /// 优化促销组合算法
    /// </summary>
    Task<OptimizationResult> OptimizePromotionCombinationAsync(List<PromotionRuleBase> rules, ShoppingCart cart);

    /// <summary>
    /// 缓存促销计算结果
    /// </summary>
    Task<bool> CacheCalculationResultAsync(string cacheKey, PromotionResult result, TimeSpan expiry);

    /// <summary>
    /// 获取缓存的计算结果
    /// </summary>
    Task<PromotionResult?> GetCachedResultAsync(string cacheKey);

    /// <summary>
    /// 生成缓存键
    /// </summary>
    string GenerateCacheKey(ShoppingCart cart, List<string> ruleIds);

    /// <summary>
    /// 分析计算复杂度
    /// </summary>
    ComplexityAnalysis AnalyzeComplexity(List<PromotionRuleBase> rules, ShoppingCart cart);

    /// <summary>
    /// 获取性能基准
    /// </summary>
    PerformanceBenchmark GetPerformanceBenchmark();

    /// <summary>
    /// 执行性能测试
    /// </summary>
    Task<PerformanceTestResult> RunPerformanceTestAsync(PerformanceTestConfig config);
}

/// <summary>
/// 优化结果
/// </summary>
public class OptimizationResult
{
    /// <summary>
    /// 优化后的规则列表
    /// </summary>
    public List<PromotionRuleBase> OptimizedRules { get; set; } = new();

    /// <summary>
    /// 预估计算时间（毫秒）
    /// </summary>
    public long EstimatedCalculationTimeMs { get; set; }

    /// <summary>
    /// 优化策略
    /// </summary>
    public List<string> OptimizationStrategies { get; set; } = new();

    /// <summary>
    /// 跳过的规则及原因
    /// </summary>
    public Dictionary<string, string> SkippedRules { get; set; } = new();

    /// <summary>
    /// 性能提升百分比
    /// </summary>
    public double PerformanceImprovement { get; set; }
}

/// <summary>
/// 复杂度分析
/// </summary>
public class ComplexityAnalysis
{
    /// <summary>
    /// 时间复杂度等级
    /// </summary>
    public ComplexityLevel TimeComplexity { get; set; }

    /// <summary>
    /// 空间复杂度等级
    /// </summary>
    public ComplexityLevel SpaceComplexity { get; set; }

    /// <summary>
    /// 预估计算时间（毫秒）
    /// </summary>
    public long EstimatedTimeMs { get; set; }

    /// <summary>
    /// 预估内存使用（MB）
    /// </summary>
    public double EstimatedMemoryMB { get; set; }

    /// <summary>
    /// 复杂度因子
    /// </summary>
    public Dictionary<string, int> ComplexityFactors { get; set; } = new();

    /// <summary>
    /// 优化建议
    /// </summary>
    public List<string> OptimizationRecommendations { get; set; } = new();
}

/// <summary>
/// 复杂度等级
/// </summary>
public enum ComplexityLevel
{
    /// <summary>常数时间 O(1)</summary>
    Constant,
    /// <summary>对数时间 O(log n)</summary>
    Logarithmic,
    /// <summary>线性时间 O(n)</summary>
    Linear,
    /// <summary>线性对数时间 O(n log n)</summary>
    LinearLogarithmic,
    /// <summary>二次时间 O(n²)</summary>
    Quadratic,
    /// <summary>立方时间 O(n³)</summary>
    Cubic,
    /// <summary>指数时间 O(2^n)</summary>
    Exponential
}

/// <summary>
/// 性能基准
/// </summary>
public class PerformanceBenchmark
{
    /// <summary>
    /// 基准测试时间
    /// </summary>
    public DateTime BenchmarkTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 不同场景的基准数据
    /// </summary>
    public Dictionary<string, BenchmarkData> Scenarios { get; set; } = new();

    /// <summary>
    /// 系统信息
    /// </summary>
    public SystemInfo SystemInfo { get; set; } = new();
}

/// <summary>
/// 基准数据
/// </summary>
public class BenchmarkData
{
    /// <summary>
    /// 场景名称
    /// </summary>
    public string ScenarioName { get; set; } = string.Empty;

    /// <summary>
    /// 商品数量
    /// </summary>
    public int ProductCount { get; set; }

    /// <summary>
    /// 规则数量
    /// </summary>
    public int RuleCount { get; set; }

    /// <summary>
    /// 平均计算时间（毫秒）
    /// </summary>
    public double AverageTimeMs { get; set; }

    /// <summary>
    /// 最大计算时间（毫秒）
    /// </summary>
    public long MaxTimeMs { get; set; }

    /// <summary>
    /// 最小计算时间（毫秒）
    /// </summary>
    public long MinTimeMs { get; set; }

    /// <summary>
    /// 标准差
    /// </summary>
    public double StandardDeviation { get; set; }

    /// <summary>
    /// 内存使用（MB）
    /// </summary>
    public double MemoryUsageMB { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate { get; set; }
}

/// <summary>
/// 系统信息
/// </summary>
public class SystemInfo
{
    /// <summary>
    /// CPU信息
    /// </summary>
    public string CpuInfo { get; set; } = string.Empty;

    /// <summary>
    /// 内存大小（GB）
    /// </summary>
    public double MemoryGB { get; set; }

    /// <summary>
    /// .NET版本
    /// </summary>
    public string DotNetVersion { get; set; } = string.Empty;

    /// <summary>
    /// 操作系统
    /// </summary>
    public string OperatingSystem { get; set; } = string.Empty;
}

/// <summary>
/// 性能测试配置
/// </summary>
public class PerformanceTestConfig
{
    /// <summary>
    /// 测试场景列表
    /// </summary>
    public List<TestScenario> Scenarios { get; set; } = new();

    /// <summary>
    /// 每个场景的测试次数
    /// </summary>
    public int TestIterations { get; set; } = 100;

    /// <summary>
    /// 预热次数
    /// </summary>
    public int WarmupIterations { get; set; } = 10;

    /// <summary>
    /// 是否收集内存使用数据
    /// </summary>
    public bool CollectMemoryMetrics { get; set; } = true;

    /// <summary>
    /// 是否收集GC数据
    /// </summary>
    public bool CollectGCMetrics { get; set; } = true;

    /// <summary>
    /// 超时时间（毫秒）
    /// </summary>
    public int TimeoutMs { get; set; } = 30000;
}

/// <summary>
/// 测试场景
/// </summary>
public class TestScenario
{
    /// <summary>
    /// 场景名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 场景描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 购物车配置
    /// </summary>
    public CartConfiguration CartConfig { get; set; } = new();

    /// <summary>
    /// 规则配置
    /// </summary>
    public RuleConfiguration RuleConfig { get; set; } = new();
}

/// <summary>
/// 购物车配置
/// </summary>
public class CartConfiguration
{
    /// <summary>
    /// 商品数量
    /// </summary>
    public int ProductCount { get; set; }

    /// <summary>
    /// 总数量范围
    /// </summary>
    public (int Min, int Max) QuantityRange { get; set; } = (1, 10);

    /// <summary>
    /// 价格范围
    /// </summary>
    public (decimal Min, decimal Max) PriceRange { get; set; } = (1m, 1000m);

    /// <summary>
    /// 分类数量
    /// </summary>
    public int CategoryCount { get; set; } = 5;
}

/// <summary>
/// 规则配置
/// </summary>
public class RuleConfiguration
{
    /// <summary>
    /// 规则数量
    /// </summary>
    public int RuleCount { get; set; }

    /// <summary>
    /// 规则类型分布
    /// </summary>
    public Dictionary<string, double> RuleTypeDistribution { get; set; } = new();

    /// <summary>
    /// 复杂度等级
    /// </summary>
    public ComplexityLevel ComplexityLevel { get; set; } = ComplexityLevel.Linear;
}

/// <summary>
/// 性能测试结果
/// </summary>
public class PerformanceTestResult
{
    /// <summary>
    /// 测试开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 测试结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 总测试时间
    /// </summary>
    public TimeSpan TotalTestTime { get; set; }

    /// <summary>
    /// 场景测试结果
    /// </summary>
    public Dictionary<string, ScenarioTestResult> ScenarioResults { get; set; } = new();

    /// <summary>
    /// 总体统计
    /// </summary>
    public OverallStatistics OverallStats { get; set; } = new();

    /// <summary>
    /// 性能回归分析
    /// </summary>
    public RegressionAnalysis? RegressionAnalysis { get; set; }
}

/// <summary>
/// 场景测试结果
/// </summary>
public class ScenarioTestResult
{
    /// <summary>
    /// 场景名称
    /// </summary>
    public string ScenarioName { get; set; } = string.Empty;

    /// <summary>
    /// 成功次数
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败次数
    /// </summary>
    public int FailureCount { get; set; }

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageTimeMs { get; set; }

    /// <summary>
    /// 中位数执行时间（毫秒）
    /// </summary>
    public double MedianTimeMs { get; set; }

    /// <summary>
    /// 95分位数执行时间（毫秒）
    /// </summary>
    public double P95TimeMs { get; set; }

    /// <summary>
    /// 99分位数执行时间（毫秒）
    /// </summary>
    public double P99TimeMs { get; set; }

    /// <summary>
    /// 最大执行时间（毫秒）
    /// </summary>
    public long MaxTimeMs { get; set; }

    /// <summary>
    /// 最小执行时间（毫秒）
    /// </summary>
    public long MinTimeMs { get; set; }

    /// <summary>
    /// 标准差
    /// </summary>
    public double StandardDeviation { get; set; }

    /// <summary>
    /// 平均内存使用（MB）
    /// </summary>
    public double AverageMemoryMB { get; set; }

    /// <summary>
    /// GC统计
    /// </summary>
    public GCStatistics GCStats { get; set; } = new();
}

/// <summary>
/// 总体统计
/// </summary>
public class OverallStatistics
{
    /// <summary>
    /// 总测试次数
    /// </summary>
    public int TotalTests { get; set; }

    /// <summary>
    /// 总成功次数
    /// </summary>
    public int TotalSuccesses { get; set; }

    /// <summary>
    /// 总失败次数
    /// </summary>
    public int TotalFailures { get; set; }

    /// <summary>
    /// 整体成功率
    /// </summary>
    public double OverallSuccessRate { get; set; }

    /// <summary>
    /// 整体平均时间（毫秒）
    /// </summary>
    public double OverallAverageTimeMs { get; set; }

    /// <summary>
    /// 最佳场景
    /// </summary>
    public string BestScenario { get; set; } = string.Empty;

    /// <summary>
    /// 最差场景
    /// </summary>
    public string WorstScenario { get; set; } = string.Empty;
}

/// <summary>
/// GC统计
/// </summary>
public class GCStatistics
{
    /// <summary>
    /// Gen0 GC次数
    /// </summary>
    public int Gen0Collections { get; set; }

    /// <summary>
    /// Gen1 GC次数
    /// </summary>
    public int Gen1Collections { get; set; }

    /// <summary>
    /// Gen2 GC次数
    /// </summary>
    public int Gen2Collections { get; set; }

    /// <summary>
    /// 总分配内存（字节）
    /// </summary>
    public long TotalAllocatedBytes { get; set; }
}

/// <summary>
/// 回归分析
/// </summary>
public class RegressionAnalysis
{
    /// <summary>
    /// 是否存在性能回归
    /// </summary>
    public bool HasRegression { get; set; }

    /// <summary>
    /// 回归百分比
    /// </summary>
    public double RegressionPercentage { get; set; }

    /// <summary>
    /// 回归场景
    /// </summary>
    public List<string> RegressedScenarios { get; set; } = new();

    /// <summary>
    /// 基准数据
    /// </summary>
    public PerformanceBenchmark? BaselineBenchmark { get; set; }
}
