using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.DiscountRules;

/// <summary>
/// 打折促销规则基类
/// 核心原则：通过修改商品的ActualUnitPrice来实现打折效果
/// </summary>
public abstract class BaseDiscountRule : PromotionRuleBase
{
    /// <summary>
    /// 折扣商品选择策略：客户利益最大化 vs 商家利益最大化
    /// </summary>
    public DiscountSelectionStrategy DiscountSelectionStrategy { get; set; } = DiscountSelectionStrategy.CustomerBenefit;

    /// <summary>
    /// 规则类型（由子类实现）
    /// </summary>
    public abstract override string RuleType { get; }

    /// <summary>
    /// 检查促销条件是否满足（由子类实现）
    /// </summary>
    protected abstract override bool CheckConditions(ShoppingCart cart);

    /// <summary>
    /// 计算可应用的最大次数（由子类实现）
    /// </summary>
    public abstract override int CalculateMaxApplications(ShoppingCart cart);

    /// <summary>
    /// 应用促销规则（由子类实现）
    /// </summary>
    public abstract override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1);

    /// <summary>
    /// 验证打折商品是否在购物车中
    /// </summary>
    protected bool ValidateDiscountProductsInCart(ShoppingCart cart, List<string> productIds)
    {
        foreach (var productId in productIds)
        {
            var hasProduct = cart.Items.Any(x => x.Product.Id == productId && x.Quantity > 0);
            if (!hasProduct)
            {
                return false; // 打折商品不在购物车中，条件不成立
            }
        }
        return true;
    }

    /// <summary>
    /// 计算商品的总数量
    /// </summary>
    protected int CalculateTotalQuantity(ShoppingCart cart, List<string> productIds)
    {
        return productIds.Sum(id => cart.GetAvailableProductQuantity(id));
    }

    /// <summary>
    /// 计算商品的总金额
    /// </summary>
    protected decimal CalculateTotalAmount(ShoppingCart cart, List<string> productIds)
    {
        return productIds.Sum(id =>
            cart.Items.Where(x => x.Product.Id == id).Sum(x => x.SubTotal));
    }

    /// <summary>
    /// 应用折扣到指定商品项
    /// </summary>
    protected void ApplyDiscountToCartItem(CartItem cartItem, decimal discountRate, AppliedPromotion promotion, string description)
    {
        var originalPrice = cartItem.UnitPrice;
        var discountedPrice = originalPrice * discountRate;
        var discountAmount = originalPrice - discountedPrice;

        // 应用折扣
        cartItem.ActualUnitPrice = discountedPrice;

        // 记录促销详情
        var promotionDetail = new ItemPromotionDetail
        {
            RuleId = promotion.RuleId,
            RuleName = promotion.RuleName,
            PromotionType = promotion.PromotionType,
            DiscountAmount = discountAmount * cartItem.Quantity,
            Description = description,
            IsGiftRelated = false
        };

        cartItem.AddPromotionDetail(promotionDetail);
    }

    /// <summary>
    /// 根据策略排序商品（用于优化折扣分配）
    /// </summary>
    protected List<CartItem> SortItemsByStrategy(List<CartItem> items)
    {
        return DiscountSelectionStrategy switch
        {
            DiscountSelectionStrategy.CustomerBenefit => items.OrderByDescending(x => x.UnitPrice).ToList(), // 客户利益：优先给高价商品打折
            DiscountSelectionStrategy.MerchantBenefit => items.OrderBy(x => x.UnitPrice).ToList(), // 商家利益：优先给低价商品打折
            _ => items.OrderByDescending(x => x.UnitPrice).ToList()
        };
    }

    /// <summary>
    /// 创建折扣记录（作为GiftItem记录，但实际是折扣）
    /// </summary>
    protected GiftItem CreateDiscountRecord(string productId, string productName, int quantity, decimal discountAmount, string description)
    {
        return new GiftItem
        {
            ProductId = productId,
            ProductName = productName,
            Quantity = quantity,
            Value = discountAmount,
            Description = description
        };
    }

    /// <summary>
    /// 消耗购买条件商品（记录消耗但不修改购物车）
    /// </summary>
    protected List<ConsumedItem> ConsumeConditionProducts(ShoppingCart cart, List<string> productIds, int requiredQuantity)
    {
        var consumedItems = new List<ConsumedItem>();
        var remainingQuantity = requiredQuantity;

        foreach (var productId in productIds)
        {
            if (remainingQuantity <= 0) break;

            var cartItems = cart.Items
                .Where(x => x.Product.Id == productId && x.Quantity > 0)
                .ToList();

            foreach (var cartItem in cartItems)
            {
                if (remainingQuantity <= 0) break;

                var consumeQuantity = Math.Min(cartItem.Quantity, remainingQuantity);

                var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == productId);
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += consumeQuantity;
                }
                else
                {
                    consumedItems.Add(new ConsumedItem
                    {
                        ProductId = productId,
                        ProductName = cartItem.Product.Name,
                        Quantity = consumeQuantity,
                        UnitPrice = cartItem.UnitPrice
                    });
                }

                remainingQuantity -= consumeQuantity;
            }
        }

        return consumedItems;
    }
}

/// <summary>
/// 折扣商品选择策略枚举
/// </summary>
public enum DiscountSelectionStrategy
{
    /// <summary>
    /// 客户利益最大化：优先给高价值商品打折
    /// </summary>
    CustomerBenefit = 0,

    /// <summary>
    /// 商家利益最大化：优先给低价值商品打折
    /// </summary>
    MerchantBenefit = 1
}
