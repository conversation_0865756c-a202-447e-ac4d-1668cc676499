global using Microsoft.Extensions.Logging;
global using Microsoft.Extensions.Caching.Memory;
global using Microsoft.Extensions.ObjectPool;
global using System.Collections.Concurrent;
global using System.Diagnostics;
using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Observability;

namespace PE2.PromotionEngine.Allocation;

/// <summary>
/// 分配引擎实现 - 精确折扣分配算法
/// 支持比例分配、优先级分配、最优分配等多种算法
/// 处理赠品成本分配和舍入调整，确保分配结果的财务准确性
/// </summary>
/// <remarks>
/// 建议单元测试：
/// 1. TestAllocateDiscountAsync - 测试基本折扣分配功能
/// 2. TestAllocateBatchAsync - 测试批量分配功能
/// 3. TestValidateAllocation - 测试分配结果验证
/// 4. TestHandleRounding - 测试舍入处理
/// 5. TestCalculateAllocationPreview - 测试分配预览
/// 6. TestComplexityAnalysis - 测试复杂度分析
/// 7. TestConcurrentOperations - 测试并发操作安全性
/// 8. TestFinancialAccuracy - 测试财务准确性验证
/// </remarks>
public sealed class AllocationEngine : IAllocationEngine, IDisposable
{
    private readonly ILogger<AllocationEngine> _logger;
    private readonly IObservabilityEngine _observability;
    private readonly IMemoryCache _memoryCache;
    private readonly ObjectPool<StringBuilder> _stringBuilderPool;
    
    // 分配缓存
    private readonly ConcurrentDictionary<string, AllocationResult> _allocationCache;
    private readonly ConcurrentDictionary<string, AllocationPreview> _previewCache;
    
    // 性能统计
    private readonly ConcurrentDictionary<string, AllocationMetrics> _performanceMetrics;
    private readonly Timer _cacheCleanupTimer;
    private readonly SemaphoreSlim _allocationSemaphore;
    
    // 配置参数
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(15);
    private readonly int _maxConcurrentAllocations = Environment.ProcessorCount * 2;
    private readonly decimal _precisionTolerance = 0.01m; // 1分的精度容差
    
    private bool _disposed;

    /// <summary>
    /// 初始化分配引擎
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="observability">可观测性引擎</param>
    /// <param name="memoryCache">内存缓存</param>
    /// <param name="stringBuilderPool">字符串构建器对象池</param>
    public AllocationEngine(
        ILogger<AllocationEngine> logger,
        IObservabilityEngine observability,
        IMemoryCache memoryCache,
        ObjectPool<StringBuilder> stringBuilderPool)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _observability = observability ?? throw new ArgumentNullException(nameof(observability));
        _memoryCache = memoryCache ?? throw new ArgumentNullException(nameof(memoryCache));
        _stringBuilderPool = stringBuilderPool ?? throw new ArgumentNullException(nameof(stringBuilderPool));
        
        _allocationCache = new ConcurrentDictionary<string, AllocationResult>();
        _previewCache = new ConcurrentDictionary<string, AllocationPreview>();
        _performanceMetrics = new ConcurrentDictionary<string, AllocationMetrics>();
        
        _allocationSemaphore = new SemaphoreSlim(_maxConcurrentAllocations, _maxConcurrentAllocations);
        
        // 启动定时清理任务
        _cacheCleanupTimer = new Timer(CleanupExpiredCache, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        
        _logger.LogInformation("分配引擎已初始化，最大并发分配数: {MaxConcurrent}", _maxConcurrentAllocations);
    }

    /// <summary>
    /// 执行优惠分摊
    /// </summary>
    /// <param name="request">分摊请求</param>
    /// <returns>分摊结果</returns>
    public async Task<AllocationResult> AllocateDiscountAsync(AllocationRequest request)
    {
        if (request == null)
        {
            throw new ArgumentNullException(nameof(request));
        }

        var cacheKey = GenerateAllocationCacheKey(request);
        
        // 尝试从缓存获取
        if (_allocationCache.TryGetValue(cacheKey, out var cachedResult))
        {
            _logger.LogDebug("从缓存获取分配结果: {RequestId}", request.Id);
            return cachedResult;
        }

        var traceId = _observability.StartCalculationTrace($"AllocateDiscount_{request.Id}");
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            await _allocationSemaphore.WaitAsync().ConfigureAwait(false);
            
            _observability.TrackCalculationStep(traceId, "开始折扣分配", new {
                RequestId = request.Id,
                Strategy = request.Strategy.ToString(),
                ItemCount = request.Items.Count,
                DiscountAmount = request.Promotion.DiscountAmount
            });

            // 验证请求
            var validationResult = ValidateAllocationRequest(request);
            if (!validationResult.IsValid)
            {
                return CreateFailedResult(request, validationResult.Errors.First().Message);
            }

            // 执行分配算法
            var result = await ExecuteAllocationAlgorithmAsync(request, traceId).ConfigureAwait(false);
            
            // 处理舍入
            var roundingResult = HandleRounding(result, request.RoundingStrategy);
            if (roundingResult.IsSuccessful)
            {
                result = roundingResult.AdjustedResult;
            }

            // 处理尾差
            result = HandleTailDifference(result, request.TailStrategy);

            // 验证结果
            var finalValidation = ValidateAllocation(result);
            if (!finalValidation.IsValid)
            {
                result.Warnings.AddRange(finalValidation.Warnings.Select(w => w.Message));
            }

            stopwatch.Stop();
            result.AllocationTimeMs = stopwatch.ElapsedMilliseconds;

            // 缓存结果
            _allocationCache.TryAdd(cacheKey, result);
            
            // 记录性能指标
            RecordAllocationMetric(request.Strategy.ToString(), stopwatch.ElapsedMilliseconds, request.Items.Count);
            
            _observability.TrackCalculationStep(traceId, "折扣分配完成", new {
                IsSuccessful = result.IsSuccessful,
                TotalAllocated = result.TotalAllocatedAmount,
                AllocationDifference = result.AllocationDifference,
                ElapsedMs = stopwatch.ElapsedMilliseconds
            });

            _logger.LogInformation("折扣分配完成: 请求{RequestId}, 策略{Strategy}, 分配{Allocated:C}, 耗时{ElapsedMs}ms",
                request.Id, request.Strategy, result.TotalAllocatedAmount, stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "折扣分配过程发生异常: {RequestId}", request.Id);
            return CreateFailedResult(request, $"分配异常: {ex.Message}");
        }
        finally
        {
            _allocationSemaphore.Release();
            _observability.EndCalculationTrace(traceId);
        }
    }

    /// <summary>
    /// 批量执行优惠分摊
    /// </summary>
    /// <param name="requests">分摊请求列表</param>
    /// <returns>分摊结果字典</returns>
    public async Task<Dictionary<string, AllocationResult>> AllocateBatchAsync(List<AllocationRequest> requests)
    {
        if (requests?.Any() != true)
        {
            return new Dictionary<string, AllocationResult>();
        }

        var traceId = _observability.StartCalculationTrace("AllocateBatch");
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _observability.TrackCalculationStep(traceId, "开始批量分配", new {
                RequestCount = requests.Count
            });

            var results = new ConcurrentDictionary<string, AllocationResult>();
            
            // 并行处理分配请求
            var allocationTasks = requests.Select(async request =>
            {
                try
                {
                    var result = await AllocateDiscountAsync(request).ConfigureAwait(false);
                    results.TryAdd(request.Id, result);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "批量分配中单个请求失败: {RequestId}", request.Id);
                    results.TryAdd(request.Id, CreateFailedResult(request, $"批量分配异常: {ex.Message}"));
                }
            });

            await Task.WhenAll(allocationTasks).ConfigureAwait(false);

            stopwatch.Stop();
            
            var successCount = results.Values.Count(r => r.IsSuccessful);
            var failureCount = results.Count - successCount;
            
            _observability.TrackCalculationStep(traceId, "批量分配完成", new {
                TotalRequests = requests.Count,
                SuccessCount = successCount,
                FailureCount = failureCount,
                ElapsedMs = stopwatch.ElapsedMilliseconds
            });

            _logger.LogInformation("批量分配完成: 总计{Total}个, 成功{Success}个, 失败{Failure}个, 耗时{ElapsedMs}ms",
                requests.Count, successCount, failureCount, stopwatch.ElapsedMilliseconds);

            return results.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量分配过程发生异常");
            throw;
        }
        finally
        {
            _observability.EndCalculationTrace(traceId);
        }
    }

    /// <summary>
    /// 验证分摊结果
    /// </summary>
    /// <param name="result">分摊结果</param>
    /// <returns>验证结果</returns>
    public AllocationValidationResult ValidateAllocation(AllocationResult result)
    {
        var validation = new AllocationValidationResult
        {
            IsValid = true,
            AmountConsistencyCheck = true,
            PrecisionCheck = true,
            BusinessRuleCheck = true
        };

        try
        {
            // 金额一致性检查
            var totalAllocated = result.AllocationDetails.Sum(d => d.AllocatedAmount);
            var amountDifference = Math.Abs(result.OriginalDiscountAmount - totalAllocated);
            
            if (amountDifference > _precisionTolerance)
            {
                validation.AmountConsistencyCheck = false;
                validation.Errors.Add(new ValidationError
                {
                    Code = "AMOUNT_INCONSISTENCY",
                    Message = $"分配金额不一致，差异: {amountDifference:C}",
                    Severity = ValidationSeverity.Error
                });
            }

            // 精度检查
            foreach (var detail in result.AllocationDetails)
            {
                if (detail.AllocatedAmount < 0 && !IsNegativeAllocationAllowed(detail))
                {
                    validation.PrecisionCheck = false;
                    validation.Errors.Add(new ValidationError
                    {
                        Code = "NEGATIVE_ALLOCATION",
                        Message = $"商品 {detail.ProductName} 出现负分配: {detail.AllocatedAmount:C}",
                        ProductId = detail.ProductId,
                        Severity = ValidationSeverity.Warning
                    });
                }
            }

            // 业务规则检查
            if (result.AllocationDetails.Any(d => d.FinalUnitPrice < 0))
            {
                validation.BusinessRuleCheck = false;
                validation.Errors.Add(new ValidationError
                {
                    Code = "NEGATIVE_FINAL_PRICE",
                    Message = "存在最终单价为负的商品",
                    Severity = ValidationSeverity.Critical
                });
            }

            validation.IsValid = validation.AmountConsistencyCheck && 
                               validation.PrecisionCheck && 
                               validation.BusinessRuleCheck &&
                               !validation.Errors.Any(e => e.Severity == ValidationSeverity.Critical);

            return validation;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证分配结果时发生异常");
            validation.IsValid = false;
            validation.Errors.Add(new ValidationError
            {
                Code = "VALIDATION_EXCEPTION",
                Message = $"验证异常: {ex.Message}",
                Severity = ValidationSeverity.Critical
            });
            return validation;
        }
    }

    /// <summary>
    /// 处理舍入问题
    /// </summary>
    /// <param name="result">分配结果</param>
    /// <param name="strategy">舍入策略</param>
    /// <returns>舍入结果</returns>
    public RoundingResult HandleRounding(AllocationResult result, RoundingStrategy strategy)
    {
        var roundingResult = new RoundingResult
        {
            IsSuccessful = true,
            AdjustedResult = new AllocationResult
            {
                RequestId = result.RequestId,
                IsSuccessful = result.IsSuccessful,
                OriginalDiscountAmount = result.OriginalDiscountAmount,
                ErrorMessage = result.ErrorMessage,
                Warnings = new List<string>(result.Warnings),
                Statistics = result.Statistics,
                AllocationDetails = new List<AllocationDetail>()
            }
        };

        try
        {
            decimal totalRoundingAdjustment = 0;

            foreach (var detail in result.AllocationDetails)
            {
                var roundedAmount = ApplyRoundingStrategy(detail.AllocatedAmount, strategy);
                var roundingAdjustment = roundedAmount - detail.AllocatedAmount;

                var adjustedDetail = new AllocationDetail
                {
                    ProductId = detail.ProductId,
                    ProductName = detail.ProductName,
                    OriginalAmount = detail.OriginalAmount,
                    AllocatedAmount = roundedAmount,
                    AllocationRatio = detail.AllocationRatio,
                    UnitAllocationAmount = detail.UnitAllocationAmount,
                    PreRoundingAmount = detail.AllocatedAmount,
                    RoundingAdjustment = roundingAdjustment,
                    ReceivedTailAdjustment = detail.ReceivedTailAdjustment,
                    TailAdjustmentAmount = detail.TailAdjustmentAmount,
                    FinalUnitPrice = detail.FinalUnitPrice
                };

                roundingResult.AdjustedResult.AllocationDetails.Add(adjustedDetail);

                roundingResult.RoundingOperations.Add(new RoundingOperation
                {
                    ProductId = detail.ProductId,
                    BeforeRounding = detail.AllocatedAmount,
                    AfterRounding = roundedAmount,
                    Direction = roundingAdjustment > 0 ? RoundingDirection.Up :
                               roundingAdjustment < 0 ? RoundingDirection.Down : RoundingDirection.None
                });

                totalRoundingAdjustment += roundingAdjustment;
            }

            roundingResult.TotalRoundingAdjustment = totalRoundingAdjustment;
            roundingResult.AdjustedResult.TotalAllocatedAmount =
                roundingResult.AdjustedResult.AllocationDetails.Sum(d => d.AllocatedAmount);
            roundingResult.AdjustedResult.RoundingAdjustment = totalRoundingAdjustment;

            return roundingResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理舍入时发生异常");
            roundingResult.IsSuccessful = false;
            return roundingResult;
        }
    }

    /// <summary>
    /// 计算分摊预览
    /// </summary>
    /// <param name="request">分摊请求</param>
    /// <returns>分摊预览</returns>
    public AllocationPreview CalculateAllocationPreview(AllocationRequest request)
    {
        var cacheKey = GeneratePreviewCacheKey(request);

        if (_previewCache.TryGetValue(cacheKey, out var cachedPreview))
        {
            return cachedPreview;
        }

        try
        {
            var preview = new AllocationPreview
            {
                RecommendedStrategy = AnalyzeOptimalStrategy(request)
            };

            // 计算预览分配
            var previewDetails = CalculatePreviewAllocation(request);
            preview.PreviewDetails = previewDetails;
            preview.EstimatedTotalAmount = previewDetails.Sum(d => d.AllocatedAmount);

            // 分析潜在问题
            preview.PotentialIssues = AnalyzePotentialIssues(request, previewDetails);

            // 估算精度损失
            preview.EstimatedPrecisionLoss = EstimatePrecisionLoss(request, previewDetails);

            _previewCache.TryAdd(cacheKey, preview);

            return preview;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "计算分配预览时发生异常: {RequestId}", request.Id);
            return new AllocationPreview
            {
                PotentialIssues = new List<string> { $"预览计算异常: {ex.Message}" },
                RecommendedStrategy = AllocationStrategy.ProportionalByAmount
            };
        }
    }

    /// <summary>
    /// 获取支持的分摊策略
    /// </summary>
    /// <returns>支持的策略列表</returns>
    public List<AllocationStrategy> GetSupportedStrategies()
    {
        return Enum.GetValues<AllocationStrategy>().ToList();
    }

    /// <summary>
    /// 分析分摊复杂度
    /// </summary>
    /// <param name="request">分摊请求</param>
    /// <returns>复杂度分析</returns>
    public AllocationComplexityAnalysis AnalyzeComplexity(AllocationRequest request)
    {
        var analysis = new AllocationComplexityAnalysis();

        try
        {
            var itemCount = request.Items.Count;
            var hasCustomWeights = request.Strategy == AllocationStrategy.CustomWeight;
            var hasComplexRounding = request.RoundingStrategy != RoundingStrategy.NoRounding;

            // 计算复杂度因子
            analysis.ComplexityFactors["ItemCount"] = itemCount;
            analysis.ComplexityFactors["HasCustomWeights"] = hasCustomWeights ? 1 : 0;
            analysis.ComplexityFactors["HasComplexRounding"] = hasComplexRounding ? 1 : 0;
            analysis.ComplexityFactors["StrategyComplexity"] = GetStrategyComplexity(request.Strategy);

            // 确定复杂度等级
            analysis.ComplexityLevel = DetermineComplexityLevel(analysis.ComplexityFactors);

            // 估算处理时间
            analysis.EstimatedProcessingTimeMs = EstimateProcessingTime(analysis.ComplexityLevel, itemCount);

            // 生成优化建议
            analysis.OptimizationSuggestions = GenerateOptimizationSuggestions(analysis);

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分析分配复杂度时发生异常: {RequestId}", request.Id);
            return new AllocationComplexityAnalysis
            {
                ComplexityLevel = AllocationComplexity.Simple,
                EstimatedProcessingTimeMs = 10,
                OptimizationSuggestions = new List<string> { $"复杂度分析异常: {ex.Message}" }
            };
        }
    }
