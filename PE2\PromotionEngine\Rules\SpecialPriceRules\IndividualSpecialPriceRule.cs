using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.SpecialPriceRules;

/// <summary>
/// 单品补充特价规则 [OK]
/// 支持excel导入商品特价X元
/// 场景案例：A商品特价600元，B商品特价500元。
/// A、B商品吊牌、零售价为1000元；购买A商品买2件、B商品买1件时，应收金额为600 * 2 + 500 =1700元
/// </summary>
public class IndividualSpecialPriceRule : BaseSpecialPriceRule
{
    public override string RuleType => "IndividualSpecialPrice";

    /// <summary>
    /// 商品特价配置列表
    /// </summary>
    public List<ProductSpecialPriceConfig> ProductConfigs { get; set; } = new();

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!ProductConfigs.Any())
            return false;

        // 验证商品是否在购物车中
        var allProductIds = ProductConfigs.Select(c => c.ProductId).ToList();
        return ValidateSpecialPriceProductsInCart(cart, allProductIds);
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        // 单品特价通常只应用一次，因为每个商品都有独立的特价
        return 1;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyIndividualSpecialPrice(cart);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用单品特价促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用单品特价促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyIndividualSpecialPrice(ShoppingCart cart)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var specialPriceRecords = new List<GiftItem>(); // 特价记录

        var promotion = new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType
        };

        // 为每个商品配置应用对应的特价
        foreach (var config in ProductConfigs)
        {
            // 获取该商品的购物车项
            var productItems = cart.Items
                .Where(x => x.Product.Id == config.ProductId && x.Quantity > 0)
                .ToList();

            if (!productItems.Any()) continue;

            // 根据策略排序商品
            var sortedItems = SortItemsByStrategy(productItems);

            // 对该商品的所有项应用特价
            foreach (var item in sortedItems)
            {
                var originalPrice = item.UnitPrice;
                var specialPrice = config.SpecialPrice;
                var discountAmount = (originalPrice - specialPrice) * item.Quantity;

                if (discountAmount > 0)
                {
                    totalDiscountAmount += discountAmount;

                    var strategyDescription = SpecialPriceSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                        ? "客户利益最大化"
                        : "商家利益最大化";

                    var description = $"单品特价：{config.Description}，特价{specialPrice:C}（{strategyDescription}）";

                    // 应用特价到商品项
                    ApplySpecialPriceToCartItem(item, specialPrice, promotion, description);

                    // 记录特价
                    specialPriceRecords.Add(CreateSpecialPriceRecord(
                        item.Product.Id,
                        item.Product.Name,
                        item.Quantity,
                        discountAmount,
                        description
                    ));

                    // 记录消耗的商品
                    var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == item.Product.Id);
                    if (existingConsumed != null)
                    {
                        existingConsumed.Quantity += item.Quantity;
                    }
                    else
                    {
                        consumedItems.Add(new ConsumedItem
                        {
                            ProductId = item.Product.Id,
                            ProductName = item.Product.Name,
                            Quantity = item.Quantity,
                            UnitPrice = originalPrice
                        });
                    }
                }
            }
        }

        return (totalDiscountAmount, consumedItems, specialPriceRecords);
    }

    /// <summary>
    /// 从Excel数据导入商品特价配置
    /// </summary>
    public void ImportFromExcelData(List<ExcelSpecialPriceData> excelData)
    {
        ProductConfigs.Clear();

        foreach (var data in excelData)
        {
            if (string.IsNullOrWhiteSpace(data.ProductId) || data.SpecialPrice <= 0)
                continue;

            ProductConfigs.Add(new ProductSpecialPriceConfig
            {
                ProductId = data.ProductId.Trim(),
                SpecialPrice = data.SpecialPrice,
                Description = string.IsNullOrWhiteSpace(data.Description)
                    ? $"{data.ProductId}特价{data.SpecialPrice:C}"
                    : data.Description.Trim()
            });
        }
    }
}

/// <summary>
/// 商品特价配置
/// </summary>
public class ProductSpecialPriceConfig
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 特价金额
    /// </summary>
    public decimal SpecialPrice { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Excel特价数据
/// </summary>
public class ExcelSpecialPriceData
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 特价金额
    /// </summary>
    public decimal SpecialPrice { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}
