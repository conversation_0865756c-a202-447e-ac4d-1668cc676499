using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.DiscountRules;

/// <summary>
/// 递增折扣规则测试类
/// 测试 ProgressiveDiscountRule 的递增折扣计算和应用逻辑
/// </summary>
public class ProgressiveDiscountRuleTests(ITestOutputHelper output) : TestBase(output)
{
    #region 基础递增折扣功能测试

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_ValidProgressiveDiscountScenario_ShouldApplyCorrectly()
    {
        // Arrange - 递增折扣：第1件9折，第2件8折，第3件及以上7折
        var rule = TestDataGenerator.CreateProgressiveDiscountRule_A();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            (TestDataGenerator.CreateProductA(), 3) // 3件A，应分别享受9折、8折、7折
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "递增折扣测试购物车");

        var originalPrice = TestDataGenerator.CreateProductA().Price; // 50元
        // 第1件：50 * 0.9 = 45元，优惠5元
        // 第2件：50 * 0.8 = 40元，优惠10元
        // 第3件：50 * 0.7 = 35元，优惠15元
        // 总优惠：5 + 10 + 15 = 30元
        var expectedTotalDiscount = 5m + 10m + 15m;

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "递增折扣测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "总优惠应为递增折扣的累计金额");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_SingleItem_ShouldApplyFirstTierDiscount()
    {
        // Arrange - 只有1件A，应享受第一梯度9折
        var rule = TestDataGenerator.CreateProgressiveDiscountRule_A();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_002",
            (TestDataGenerator.CreateProductA(), 1) // 1件A，应享受9折
        );

        LogCartDetails(cart, "单件递增折扣测试购物车");

        var originalPrice = TestDataGenerator.CreateProductA().Price; // 50元
        var expectedDiscountedPrice = originalPrice * 0.9m; // 45元
        var expectedTotalDiscount = originalPrice - expectedDiscountedPrice; // 5元

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "单件递增折扣测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "单件应享受第一梯度折扣");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "High")]
    public void Apply_TwoItems_ShouldApplyFirstTwoTierDiscounts()
    {
        // Arrange - 2件A，应分别享受9折和8折
        var rule = TestDataGenerator.CreateProgressiveDiscountRule_A();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_003",
            (TestDataGenerator.CreateProductA(), 2) // 2件A
        );

        LogCartDetails(cart, "两件递增折扣测试购物车");

        var originalPrice = TestDataGenerator.CreateProductA().Price; // 50元
        // 第1件：50 * 0.9 = 45元，优惠5元
        // 第2件：50 * 0.8 = 40元，优惠10元
        // 总优惠：5 + 10 = 15元
        var expectedTotalDiscount = 5m + 10m;

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "两件递增折扣测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "两件应享受前两个梯度的递增折扣");
    }

    #endregion

    #region 多数量递增折扣测试

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Medium")]
    public void Apply_ExcessQuantity_ShouldApplyLastTierToRemaining()
    {
        // Arrange - 5件A，前3件按梯度折扣，后2件按最后梯度折扣
        var rule = TestDataGenerator.CreateProgressiveDiscountRule_A();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_004",
            (TestDataGenerator.CreateProductA(), 5) // 5件A
        );

        LogCartDetails(cart, "超量递增折扣测试购物车");

        var originalPrice = TestDataGenerator.CreateProductA().Price; // 50元
        // 第1件：50 * 0.9 = 45元，优惠5元
        // 第2件：50 * 0.8 = 40元，优惠10元
        // 第3-5件：50 * 0.7 = 35元，每件优惠15元，共3件，优惠45元
        // 总优惠：5 + 10 + 15*3 = 60元
        var expectedTotalDiscount = 5m + 10m + 15m * 3;

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "超量递增折扣测试");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证总优惠金额
        AssertAmountEqual(expectedTotalDiscount, result.TotalDiscount, "超量商品应按最后梯度计算折扣");
    }

    [Theory]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Medium")]
    [InlineData(1, 5.0, "1件享受9折")]
    [InlineData(2, 15.0, "2件享受9折+8折")]
    [InlineData(3, 30.0, "3件享受9折+8折+7折")]
    [InlineData(4, 45.0, "4件享受9折+8折+7折+7折")]
    public void Apply_DifferentQuantities_ShouldCalculateCorrectDiscount(
        int quantity,
        decimal expectedDiscount,
        string description
    )
    {
        // Arrange
        var rule = TestDataGenerator.CreateProgressiveDiscountRule_A();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            $"CUSTOMER_PROGRESSIVE_{quantity}",
            (TestDataGenerator.CreateProductA(), quantity)
        );

        Output.WriteLine($"测试场景: {description}");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, description);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "递增折扣金额应正确计算");

        Output.WriteLine($"{description} 测试通过 ✓");
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Low")]
    public void Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateProgressiveDiscountRule_A();
        var cart = TestDataGenerator.CreateEmptyCart();

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "空购物车");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertAmountEqual(0m, result.TotalDiscount, "空购物车应无优惠");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Low")]
    public void Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange
        var rule = TestDataGenerator.CreateProgressiveDiscountRule_A();
        rule.IsEnabled = false; // 禁用规则

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_DISABLED",
            (TestDataGenerator.CreateProductA(), 3)
        );

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "禁用规则场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");
        AssertAmountEqual(0m, result.TotalDiscount, "禁用规则应无优惠");
    }

    [Fact]
    [Trait("Category", "Discount")]
    [Trait("Priority", "Low")]
    public void Apply_NoTargetProducts_ShouldNotApply()
    {
        // Arrange - 购物车中没有目标商品A
        var rule = TestDataGenerator.CreateProgressiveDiscountRule_A();
        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_NO_TARGET",
            (TestDataGenerator.CreateProductB(), 3) // 只有B，没有A
        );

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "无目标商品场景");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");
        AssertAmountEqual(0m, result.TotalDiscount, "无目标商品应无优惠");
    }

    #endregion

    #region 性能测试

    [Fact]
    [Trait("Category", "Performance")]
    [Trait("Priority", "Low")]
    public void Apply_LargeCart_ShouldPerformWell()
    {
        // Arrange
        var rule = TestDataGenerator.CreateProgressiveDiscountRule_A();
        var cart = TestDataGenerator.CreateLargeTestCart(100); // 100个商品项

        Output.WriteLine($"大型购物车测试: {cart.Items.Count} 个商品项");

        TestPromotionRuleService.Rules = [rule];
        // Act & Assert
        var executionTime = MeasureExecutionTime(() =>
        {
            var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;
            AssertPromotionResult(result, "大型购物车性能测试");
        });

        // 性能断言：大型购物车处理应在合理时间内完成
        AssertPerformance(executionTime, TimeSpan.FromMilliseconds(500), "大型购物车递增折扣规则计算");
    }

    #endregion
}