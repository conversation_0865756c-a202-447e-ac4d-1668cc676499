### POSPE2 促销引擎 API 测试用例

### 1. 健康检查
GET http://localhost:5213/health

### 2. 获取示例购物车
GET http://localhost:5213/api/demo/sample-cart

### 3. 完整功能演示
POST http://localhost:5213/api/demo/full-demo

### 4. 获取所有促销规则
GET http://localhost:5213/api/promotionrule

### 5. 获取有效的促销规则
GET http://localhost:5213/api/promotionrule/valid

### 6. 获取促销统计信息
GET http://localhost:5213/api/promotion/statistics

### 7. 计算最优促销 - 基础场景 (A:1, B:2, C:5)
POST http://localhost:5213/api/promotion/calculate
Content-Type: application/json

{
  "id": "TEST_CART_001",
  "customerId": "CUSTOMER_001",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 50.00,
        "category": "电子产品",
        "barcode": "1234567890001",
        "brand": "品牌A"
      },
      "quantity": 1,
      "unitPrice": 50.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 30.00,
        "category": "服装",
        "barcode": "1234567890002",
        "brand": "品牌B"
      },
      "quantity": 2,
      "unitPrice": 30.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 20.00,
        "category": "家居",
        "barcode": "1234567890003",
        "brand": "品牌C"
      },
      "quantity": 5,
      "unitPrice": 20.00
    }
  ]
}

### 8. 获取促销预览 - 基础场景
POST http://localhost:5213/api/promotion/preview
Content-Type: application/json

{
  "id": "TEST_CART_002",
  "customerId": "CUSTOMER_002",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 50.00,
        "category": "电子产品"
      },
      "quantity": 1,
      "unitPrice": 50.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 30.00,
        "category": "服装"
      },
      "quantity": 2,
      "unitPrice": 30.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 20.00,
        "category": "家居"
      },
      "quantity": 5,
      "unitPrice": 20.00
    }
  ]
}

### 9. 应用指定促销规则 - B商品8折
POST http://localhost:5213/api/promotion/apply/RULE001?applicationCount=1
Content-Type: application/json

{
  "id": "TEST_CART_003",
  "customerId": "CUSTOMER_003",
  "items": [
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 30.00,
        "category": "服装"
      },
      "quantity": 3,
      "unitPrice": 30.00
    }
  ]
}

### 10. 验证促销组合
POST http://localhost:5213/api/promotion/validate
Content-Type: application/json

{
  "cart": {
    "id": "TEST_CART_004",
    "customerId": "CUSTOMER_004",
    "items": [
      {
        "product": {
          "id": "A",
          "name": "商品A",
          "price": 50.00,
          "category": "电子产品"
        },
        "quantity": 2,
        "unitPrice": 50.00
      },
      {
        "product": {
          "id": "B",
          "name": "商品B",
          "price": 30.00,
          "category": "服装"
        },
        "quantity": 3,
        "unitPrice": 30.00
      }
    ]
  },
  "ruleIds": ["RULE001", "RULE002"]
}

### 11. 复杂场景测试 - 高价值购物车
POST http://localhost:5213/api/promotion/calculate
Content-Type: application/json

{
  "id": "COMPLEX_CART_001",
  "customerId": "VIP_CUSTOMER_001",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "高端手机",
        "price": 3000.00,
        "category": "电子产品"
      },
      "quantity": 2,
      "unitPrice": 3000.00
    },
    {
      "product": {
        "id": "B",
        "name": "时尚T恤",
        "price": 150.00,
        "category": "服装"
      },
      "quantity": 4,
      "unitPrice": 150.00
    },
    {
      "product": {
        "id": "C",
        "name": "家用电器",
        "price": 800.00,
        "category": "家居"
      },
      "quantity": 3,
      "unitPrice": 800.00
    }
  ]
}

### 12. 添加新的促销规则
POST http://localhost:5213/api/promotionrule
Content-Type: application/json

{
  "$type": "PercentageDiscount",
  "id": "TEST_RULE_001",
  "name": "测试规则 - 电子产品9折",
  "description": "电子产品类商品享受9折优惠",
  "priority": 15,
  "isEnabled": true,
  "startTime": "2024-01-01T00:00:00",
  "endTime": "2024-12-31T23:59:59",
  "isRepeatable": true,
  "maxApplications": 0,
  "applicableCustomerTypes": [],
  "exclusiveRuleIds": [],
  "applicableProductIds": ["A"],
  "discountPercentage": 0.9,
  "minQuantity": 1,
  "maxDiscountQuantity": 0
}

### 13. 获取指定促销规则
GET http://localhost:5213/api/promotionrule/TEST_RULE_001

### 14. 更新促销规则
PUT http://localhost:5213/api/promotionrule/TEST_RULE_001
Content-Type: application/json

{
  "$type": "PercentageDiscount",
  "id": "TEST_RULE_001",
  "name": "测试规则 - 电子产品85折",
  "description": "电子产品类商品享受85折优惠",
  "priority": 15,
  "isEnabled": true,
  "startTime": "2024-01-01T00:00:00",
  "endTime": "2024-12-31T23:59:59",
  "isRepeatable": true,
  "maxApplications": 0,
  "applicableCustomerTypes": [],
  "exclusiveRuleIds": [],
  "applicableProductIds": ["A"],
  "discountPercentage": 0.85,
  "minQuantity": 1,
  "maxDiscountQuantity": 0
}

### 15. 禁用促销规则
PATCH http://localhost:5213/api/promotionrule/TEST_RULE_001/enabled
Content-Type: application/json

false

### 16. 删除促销规则
DELETE http://localhost:5213/api/promotionrule/TEST_RULE_001

### 17. 重新加载促销规则
POST http://localhost:5213/api/promotionrule/reload

### 18. 验证规则文件
GET http://localhost:5213/api/promotionrule/validate-file

### 19. 边界测试 - 空购物车
POST http://localhost:5213/api/promotion/calculate
Content-Type: application/json

{
  "id": "EMPTY_CART",
  "customerId": "CUSTOMER_EMPTY",
  "items": []
}

### 20. 边界测试 - 单商品购物车
POST http://localhost:5213/api/promotion/calculate
Content-Type: application/json

{
  "id": "SINGLE_ITEM_CART",
  "customerId": "CUSTOMER_SINGLE",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 50.00,
        "category": "电子产品"
      },
      "quantity": 1,
      "unitPrice": 50.00
    }
  ]
}
