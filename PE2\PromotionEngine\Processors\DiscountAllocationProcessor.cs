using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Processors;

/// <summary>
/// 折扣分摊处理器
/// 专为POS系统设计：所有商品都通过扫码进入购物车，不添加新商品项
/// 使用按价值比例分摊法，将顾客实际支付的总金额按照促销组合中所有商品（包括赠品）的原始单独售价的比例分摊到每个商品上
/// 形成每个商品的"有效售价"或"折后单价"
/// </summary>
public class DiscountAllocationProcessor
{
    /// <summary>
    /// 处理促销结果，进行按价值比例分摊
    /// </summary>
    public void ProcessPromotionResult(PromotionResult result)
    {
        // 初始化处理后的购物车
        result.ProcessedCart = result.OriginalCart.Clone();
        result.ProcessedCart.InitializeActualPrices();

        // 按顺序处理每个应用的促销
        foreach (var appliedPromotion in result.AppliedPromotions)
        {
            ProcessSinglePromotion(result.ProcessedCart, appliedPromotion);
        }

        // 处理四舍五入问题
        HandleRoundingIssues(result.ProcessedCart, result.AppliedPromotions);
    }

    /// <summary>
    /// 处理单个促销的分摊
    /// </summary>
    private void ProcessSinglePromotion(ShoppingCart cart, AppliedPromotion promotion)
    {
        switch (promotion.PromotionType)
        {
            case "PercentageDiscount":
            case "FixedAmountDiscount":
            case "CategoryDiscount":
            case "TotalAmountDiscount":
                ProcessDiscountPromotion(cart, promotion);
                break;
            case "BuyXGetY":
            case "UnifiedGift":
            case "GradientGift":
            case "CombinationGift":
                ProcessBuyGetGiftPromotion(cart, promotion);
                break;
            case "BundleOffer":
                ProcessBundlePromotion(cart, promotion);
                break;
        }
    }

    /// <summary>
    /// 处理折扣类促销
    /// </summary>
    private void ProcessDiscountPromotion(ShoppingCart cart, AppliedPromotion promotion)
    {
        var consumedItems = promotion.ConsumedItems;
        var totalDiscountAmount = promotion.DiscountAmount;

        if (!consumedItems.Any() || totalDiscountAmount <= 0)
            return;

        // 计算参与促销的商品总价值
        var totalOriginalAmount = consumedItems.Sum(c => c.TotalPrice);

        if (totalOriginalAmount <= 0)
            return;

        var remainingDiscount = totalDiscountAmount;

        // 按比例分摊折扣
        for (int i = 0; i < consumedItems.Count; i++)
        {
            var consumedItem = consumedItems[i];
            var cartItems = cart.Items.Where(x =>
                x.Product.Id == consumedItem.ProductId &&
                !x.IsGift &&
                x.UnitPrice == consumedItem.UnitPrice).ToList();

            if (!cartItems.Any()) continue;

            // 计算该商品应分摊的折扣金额
            decimal itemDiscountAmount;
            if (i == consumedItems.Count - 1)
            {
                // 最后一个商品承担剩余的折扣金额（处理四舍五入）
                itemDiscountAmount = remainingDiscount;
            }
            else
            {
                var discountRatio = consumedItem.TotalPrice / totalOriginalAmount;
                itemDiscountAmount = Math.Round(totalDiscountAmount * discountRatio, 2);
                remainingDiscount -= itemDiscountAmount;
            }

            // 分摊到具体的购物车项
            AllocateDiscountToCartItems(cartItems, consumedItem.Quantity, itemDiscountAmount, promotion);
        }
    }

    /// <summary>
    /// 处理买赠促销
    /// 使用按价值比例分摊法：
    /// 1. 计算促销组合中所有商品（包括赠品）的原始总价值
    /// 2. 将顾客实际支付金额按比例分摊到每个商品上
    /// 3. 正确处理同商品买赠，避免重复显示赠品
    /// </summary>
    private void ProcessBuyGetGiftPromotion(ShoppingCart cart, AppliedPromotion promotion)
    {
        if (!promotion.ConsumedItems.Any() && !promotion.GiftItems.Any())
            return;

        // 使用按价值比例分摊法处理买赠促销
        ApplyValueProportionAllocation(cart, promotion);
    }

    /// <summary>
    /// 按价值比例分摊法处理买赠促销
    /// POS系统核心算法：所有商品已通过扫码进入购物车，只需调整现有商品的价格
    /// </summary>
    private void ApplyValueProportionAllocation(ShoppingCart cart, AppliedPromotion promotion)
    {
        // 第一步：构建促销商品映射表
        var promotionMapping = BuildPromotionMapping(cart, promotion);
        if (!promotionMapping.Any()) return;

        // 第二步：计算总原价和分摊比例
        var totalOriginalValue = promotionMapping.Values.Sum(item => item.TotalOriginalValue);
        var customerPayment = totalOriginalValue - promotion.DiscountAmount;
        var allocationRatio = totalOriginalValue > 0 ? customerPayment / totalOriginalValue : 0;

        // 第三步：直接调整购物车中现有商品的价格
        ApplyPriceAdjustmentToExistingItems(cart, promotionMapping, allocationRatio, promotion);
    }

    /// <summary>
    /// 促销商品映射信息
    /// 记录购物车中每个商品在促销中的角色和数量
    /// </summary>
    private class PromotionItemMapping
    {
        public string ProductId { get; set; } = string.Empty;
        public decimal OriginalUnitPrice { get; set; }
        public int ConsumedQuantity { get; set; } = 0; // 作为购买条件消耗的数量
        public int GiftQuantity { get; set; } = 0; // 作为赠品的数量
        public int TotalPromotionQuantity => ConsumedQuantity + GiftQuantity; // 参与促销的总数量
        public decimal TotalOriginalValue => OriginalUnitPrice * TotalPromotionQuantity;
        public bool IsSameProductGift => ConsumedQuantity > 0 && GiftQuantity > 0; // 是否为同商品买赠
    }

    /// <summary>
    /// 构建促销商品映射表
    /// 分析购物车中每个商品在促销中的角色和数量
    /// </summary>
    private Dictionary<string, PromotionItemMapping> BuildPromotionMapping(ShoppingCart cart, AppliedPromotion promotion)
    {
        var mapping = new Dictionary<string, PromotionItemMapping>();

        // 分析消耗的商品（购买条件）
        foreach (var consumedItem in promotion.ConsumedItems)
        {
            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == consumedItem.ProductId);
            if (cartItem == null) continue;

            if (!mapping.ContainsKey(consumedItem.ProductId))
            {
                mapping[consumedItem.ProductId] = new PromotionItemMapping
                {
                    ProductId = consumedItem.ProductId,
                    OriginalUnitPrice = cartItem.UnitPrice
                };
            }

            mapping[consumedItem.ProductId].ConsumedQuantity += consumedItem.Quantity;
        }

        // 分析赠品
        foreach (var giftItem in promotion.GiftItems)
        {
            var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == giftItem.ProductId);
            if (cartItem == null) continue;

            if (!mapping.ContainsKey(giftItem.ProductId))
            {
                mapping[giftItem.ProductId] = new PromotionItemMapping
                {
                    ProductId = giftItem.ProductId,
                    OriginalUnitPrice = cartItem.UnitPrice
                };
            }

            mapping[giftItem.ProductId].GiftQuantity += giftItem.Quantity;
        }

        return mapping;
    }

    /// <summary>
    /// 直接调整购物车中现有商品的价格
    /// POS系统核心：不添加新商品项，只调整现有商品的有效售价
    /// </summary>
    private void ApplyPriceAdjustmentToExistingItems(ShoppingCart cart, Dictionary<string, PromotionItemMapping> promotionMapping, decimal allocationRatio, AppliedPromotion promotion)
    {
        // 清空临时商品项列表
        _tempPromotionItems.Clear();

        foreach (var mapping in promotionMapping)
        {
            var productId = mapping.Key;
            var promotionInfo = mapping.Value;

            // 计算该商品的有效售价
            var effectiveUnitPrice = Math.Round(promotionInfo.OriginalUnitPrice * allocationRatio, 2);

            // 查找购物车中对应的商品项
            var cartItems = cart.Items.Where(x =>
                x.Product.Id == productId &&
                Math.Abs(x.UnitPrice - promotionInfo.OriginalUnitPrice) < 0.01m).ToList();

            // 调整参与促销的商品价格
            AdjustCartItemPrices(cartItems, promotionInfo, effectiveUnitPrice, promotion);
        }

        // 将拆分出来的促销商品项添加到购物车
        cart.Items.AddRange(_tempPromotionItems);
        _tempPromotionItems.Clear();
    }

    /// <summary>
    /// 调整购物车项的价格
    /// 根据促销参与情况，智能调整商品的有效售价
    /// </summary>
    private void AdjustCartItemPrices(List<CartItem> cartItems, PromotionItemMapping promotionInfo, decimal effectiveUnitPrice, AppliedPromotion promotion)
    {
        if (!cartItems.Any()) return;

        var totalPromotionQuantity = promotionInfo.TotalPromotionQuantity;
        var processedQuantity = 0;

        foreach (var cartItem in cartItems)
        {
            var remainingPromotionQuantity = totalPromotionQuantity - processedQuantity;
            if (remainingPromotionQuantity <= 0) break;

            var quantityToProcess = Math.Min(cartItem.Quantity, remainingPromotionQuantity);

            if (quantityToProcess == cartItem.Quantity)
            {
                // 整个购物车项都参与促销，直接调整价格
                cartItem.ActualUnitPrice = effectiveUnitPrice;
                AddPromotionDetailToCartItem(cartItem, promotion, effectiveUnitPrice, promotionInfo.OriginalUnitPrice, promotionInfo);
            }
            else
            {
                // 部分数量参与促销，需要拆分购物车项
                SplitCartItemForPromotion(cartItem, quantityToProcess, effectiveUnitPrice, promotion, promotionInfo);
            }

            processedQuantity += quantityToProcess;
        }
    }

    /// <summary>
    /// 拆分购物车项以处理部分参与促销的情况
    /// </summary>
    private void SplitCartItemForPromotion(CartItem originalItem, int promotionQuantity, decimal effectiveUnitPrice, AppliedPromotion promotion, PromotionItemMapping promotionInfo)
    {
        // 减少原购物车项的数量（保持原价）
        originalItem.Quantity -= promotionQuantity;

        // 创建新的促销商品项（调整后的价格）
        var promotionCartItem = new CartItem
        {
            Product = originalItem.Product,
            Quantity = promotionQuantity,
            UnitPrice = promotionInfo.OriginalUnitPrice,
            ActualUnitPrice = effectiveUnitPrice,
            IsGift = false
        };

        AddPromotionDetailToCartItem(promotionCartItem, promotion, effectiveUnitPrice, promotionInfo.OriginalUnitPrice, promotionInfo);

        // 注意：这里需要在调用方法中将新项添加到购物车
        // 暂时存储在临时列表中，由调用方处理
        _tempPromotionItems.Add(promotionCartItem);
    }

    // 临时存储拆分出来的促销商品项
    private readonly List<CartItem> _tempPromotionItems = new();

    // 废弃的方法已删除，使用新的按价值比例分摊算法

    /// <summary>
    /// 为购物车项添加促销详情
    /// </summary>
    private void AddPromotionDetailToCartItem(CartItem cartItem, AppliedPromotion promotion, decimal effectiveUnitPrice, decimal originalUnitPrice, PromotionItemMapping promotionInfo)
    {
        var discountPerUnit = originalUnitPrice - effectiveUnitPrice;
        var totalDiscount = discountPerUnit * cartItem.Quantity;

        // 根据商品在促销中的角色生成描述
        var roleDescription = GeneratePromotionRoleDescription(promotionInfo);
        var description = $"{promotion.RuleName} - {roleDescription}（按价值比例分摊）";

        var promotionDetail = new ItemPromotionDetail
        {
            RuleId = promotion.RuleId,
            RuleName = promotion.RuleName,
            PromotionType = promotion.PromotionType,
            DiscountAmount = totalDiscount,
            Description = description,
            IsGiftRelated = promotionInfo.GiftQuantity > 0
        };

        cartItem.AddPromotionDetail(promotionDetail);
    }

    /// <summary>
    /// 生成促销角色描述
    /// </summary>
    private string GeneratePromotionRoleDescription(PromotionItemMapping promotionInfo)
    {
        if (promotionInfo.IsSameProductGift)
        {
            return $"购买{promotionInfo.ConsumedQuantity}件+赠品{promotionInfo.GiftQuantity}件";
        }
        else if (promotionInfo.ConsumedQuantity > 0)
        {
            return $"购买{promotionInfo.ConsumedQuantity}件";
        }
        else if (promotionInfo.GiftQuantity > 0)
        {
            return $"赠品{promotionInfo.GiftQuantity}件";
        }
        else
        {
            return "参与促销";
        }
    }

    /// <summary>
    /// 处理组合套餐促销
    /// </summary>
    private void ProcessBundlePromotion(ShoppingCart cart, AppliedPromotion promotion)
    {
        var consumedItems = promotion.ConsumedItems;
        var totalDiscountAmount = promotion.DiscountAmount;

        if (!consumedItems.Any() || totalDiscountAmount <= 0)
            return;

        // 套餐促销按商品价值比例分摊折扣
        var totalOriginalAmount = consumedItems.Sum(c => c.TotalPrice);
        var remainingDiscount = totalDiscountAmount;

        for (int i = 0; i < consumedItems.Count; i++)
        {
            var consumedItem = consumedItems[i];
            var cartItems = cart.Items.Where(x =>
                x.Product.Id == consumedItem.ProductId &&
                !x.IsGift &&
                x.UnitPrice == consumedItem.UnitPrice).ToList();

            if (!cartItems.Any()) continue;

            decimal itemDiscountAmount;
            if (i == consumedItems.Count - 1)
            {
                itemDiscountAmount = remainingDiscount;
            }
            else
            {
                var discountRatio = consumedItem.TotalPrice / totalOriginalAmount;
                itemDiscountAmount = Math.Round(totalDiscountAmount * discountRatio, 2);
                remainingDiscount -= itemDiscountAmount;
            }

            AllocateDiscountToCartItems(cartItems, consumedItem.Quantity, itemDiscountAmount, promotion);
        }
    }

    /// <summary>
    /// 将折扣分摊到具体的购物车项
    /// </summary>
    private void AllocateDiscountToCartItems(List<CartItem> cartItems, int consumedQuantity,
        decimal totalDiscountAmount, AppliedPromotion promotion)
    {
        var remainingQuantity = consumedQuantity;
        var remainingDiscount = totalDiscountAmount;

        foreach (var cartItem in cartItems)
        {
            if (remainingQuantity <= 0) break;

            var allocatedQuantity = Math.Min(cartItem.Quantity, remainingQuantity);
            var itemDiscountAmount = (totalDiscountAmount * allocatedQuantity) / consumedQuantity;

            // 四舍五入到分
            itemDiscountAmount = Math.Round(itemDiscountAmount, 2);

            // 确保不超过商品原价
            var maxDiscount = cartItem.UnitPrice * allocatedQuantity;
            itemDiscountAmount = Math.Min(itemDiscountAmount, maxDiscount);

            // 更新实际支付单价
            var discountPerUnit = itemDiscountAmount / allocatedQuantity;
            cartItem.ActualUnitPrice = Math.Max(0, cartItem.UnitPrice - discountPerUnit);

            // 添加促销详情
            cartItem.AddPromotionDetail(new ItemPromotionDetail
            {
                RuleId = promotion.RuleId,
                RuleName = promotion.RuleName,
                PromotionType = promotion.PromotionType,
                DiscountAmount = itemDiscountAmount,
                Description = $"{promotion.RuleName} - 优惠 {itemDiscountAmount:C}",
                IsGiftRelated = false
            });

            remainingQuantity -= allocatedQuantity;
            remainingDiscount -= itemDiscountAmount;
        }
    }

    /// <summary>
    /// 处理四舍五入导致的金额差异
    /// </summary>
    private void HandleRoundingIssues(ShoppingCart cart, List<AppliedPromotion> appliedPromotions)
    {
        var expectedTotalDiscount = appliedPromotions.Sum(p => p.DiscountAmount);
        var actualTotalDiscount = cart.TotalDiscountAmount;
        var difference = expectedTotalDiscount - actualTotalDiscount;

        if (Math.Abs(difference) < 0.01m) return; // 差异小于1分，忽略

        // 找到最大金额的非赠品商品进行调整
        var adjustableItem = cart.Items
            .Where(x => !x.IsGift && x.ActualUnitPrice > 0)
            .OrderByDescending(x => x.ActualSubTotal)
            .FirstOrDefault();

        if (adjustableItem != null)
        {
            // 调整实际支付单价
            var adjustment = difference / adjustableItem.Quantity;
            adjustableItem.ActualUnitPrice = Math.Max(0, adjustableItem.ActualUnitPrice - adjustment);

            // 更新促销详情
            var roundingDetail = new ItemPromotionDetail
            {
                RuleId = "ROUNDING_ADJUSTMENT",
                RuleName = "四舍五入调整",
                PromotionType = "RoundingAdjustment",
                DiscountAmount = difference,
                Description = $"四舍五入调整: {difference:C}",
                IsGiftRelated = false
            };

            adjustableItem.AddPromotionDetail(roundingDetail);
        }
    }

    /// <summary>
    /// 验证分摊结果的正确性
    /// </summary>
    public ValidationResult ValidateAllocation(PromotionResult result)
    {
        var validationResult = new ValidationResult();

        // 验证总金额是否正确
        var expectedFinalAmount = result.OriginalAmount - result.TotalDiscount;
        var actualFinalAmount = result.ProcessedCart.ActualTotalAmount;
        var amountDifference = Math.Abs(expectedFinalAmount - actualFinalAmount);

        validationResult.IsValid = amountDifference < 0.01m;
        validationResult.AmountDifference = amountDifference;

        if (!validationResult.IsValid)
        {
            validationResult.ErrorMessage = $"金额验证失败：期望 {expectedFinalAmount:C}，实际 {actualFinalAmount:C}，差异 {amountDifference:C}";
        }

        // 验证按价值比例分摊的赠品价格
        // 注意：在按价值比例分摊法中，赠品不是0元，而是按比例分摊后的有效售价
        var giftItems = result.ProcessedCart.Items.Where(x => x.IsGift).ToList();
        foreach (var giftItem in giftItems)
        {
            // 赠品的有效售价应该大于0（按价值比例分摊）
            if (giftItem.ActualUnitPrice < 0)
            {
                validationResult.IsValid = false;
                validationResult.ErrorMessage += $" 赠品 {giftItem.Product.Name} 价格不能为负数，实际为 {giftItem.ActualUnitPrice:C}";
            }
        }

        return validationResult;
    }
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; set; } = true;
    public decimal AmountDifference { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
}
