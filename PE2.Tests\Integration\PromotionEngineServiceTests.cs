//using PE2.Models;
//using PE2.PromotionEngine.Models;
//using PE2.Services;
//using PE2.Tests.Infrastructure;
//using Xunit;
//using Xunit.Abstractions;

//namespace PE2.Tests.Integration;

///// <summary>
///// 促销引擎服务集成测试类
///// 测试 PromotionEngineService 的完整促销计算流程和多规则组合场景
///// </summary>
//public class PromotionEngineServiceTests : TestBase
//{
//    private readonly PromotionEngineService _promotionService;

//    public PromotionEngineServiceTests(ITestOutputHelper output) : base(output)
//    {
//        _promotionService = new PromotionEngineService();
//    }

//    #region 基础服务功能测试

//    [Fact]
//    [Trait("Category", "Integration")]
//    [Trait("Priority", "High")]
//    public void CalculatePromotion_SingleRule_ShouldCalculateCorrectly()
//    {
//        // Arrange
//        var rule = TestDataGenerator.CreateUnifiedGiftRule_Buy2Get1();
//        var cart = TestDataGenerator.CreateCustomCart("TEST_CART", "CUSTOMER_001",
//            (TestDataGenerator.CreateProductA(), 2), // 买2件A
//            (TestDataGenerator.CreateProductB(), 1)  // 送1件B
//        );

//        ValidateTestData(cart, new List<PromotionRuleBase> { rule });
//        LogCartDetails(cart, "单规则促销测试购物车");

//        // Act
//        var result = _promotionService.CalculatePromotion(cart, new List<PromotionRuleBase> { rule });

//        // Assert
//        AssertPromotionResult(result, "单规则促销计算");
//        LogPromotionResultDetails(result);

//        Assert.Single(result.AppliedRules);
//        AssertRuleApplication(result.AppliedRules[0], rule.Id);

//        var expectedDiscount = TestDataGenerator.CreateProductB().Price; // 赠品B的价值
//        AssertAmountEqual(expectedDiscount, result.TotalDiscount, "单规则应正确计算优惠金额");
//    }

//    [Fact]
//    [Trait("Category", "Integration")]
//    [Trait("Priority", "High")]
//    public void CalculatePromotion_MultipleRules_ShouldOptimize()
//    {
//        // Arrange - 多规则组合：买赠 + 打折
//        var giftRule = TestDataGenerator.CreateUnifiedGiftRule_Buy2Get1();
//        var discountRule = TestDataGenerator.CreateUnifiedDiscountRule_Buy3Get20PercentOff();
//        var rules = new List<PromotionRuleBase> { giftRule, discountRule };

//        var cart = TestDataGenerator.CreateCustomCart("TEST_CART", "CUSTOMER_002",
//            (TestDataGenerator.CreateProductA(), 4), // 4件A，可以触发多个规则
//            (TestDataGenerator.CreateProductB(), 2)  // 2件B，可作为赠品
//        );

//        LogCartDetails(cart, "多规则组合测试购物车");

//        // Act
//        var result = _promotionService.CalculatePromotion(cart, rules);

//        // Assert
//        AssertPromotionResult(result, "多规则组合优化");
//        LogPromotionResultDetails(result);

//        // 验证选择了最优的规则组合
//        Assert.NotEmpty(result.AppliedRules);
//        Assert.True(result.TotalDiscount > 0, "多规则组合应产生优惠");

//        // 验证规则优化逻辑
//        var totalRulesConsidered = result.AppliedRules.Count + result.IgnoredRules.Count;
//        Assert.Equal(rules.Count, totalRulesConsidered);
//    }

//    [Fact]
//    [Trait("Category", "Integration")]
//    [Trait("Priority", "High")]
//    public void PreviewPromotion_ShouldReturnPreviewWithoutModifyingCart()
//    {
//        // Arrange
//        var rule = TestDataGenerator.CreateUnifiedDiscountRule_Buy3Get20PercentOff();
//        var originalCart = TestDataGenerator.CreateCustomCart("TEST_CART", "CUSTOMER_003",
//            (TestDataGenerator.CreateProductA(), 3) // 3件A，满足打折条件
//        );

//        // 保存原始购物车状态
//        var originalItems = originalCart.Items.Select(i => new
//        {
//            ProductId = i.Product.Id,
//            Quantity = i.Quantity,
//            UnitPrice = i.UnitPrice,
//            ActualUnitPrice = i.ActualUnitPrice
//        }).ToList();

//        LogCartDetails(originalCart, "预览测试原始购物车");

//        // Act
//        var previewResult = _promotionService.PreviewPromotion(originalCart, new List<PromotionRuleBase> { rule });

//        // Assert
//        AssertPromotionResult(previewResult, "促销预览");
//        LogPromotionResultDetails(previewResult);

//        // 验证预览结果
//        Assert.Single(previewResult.AppliedRules);
//        Assert.True(previewResult.TotalDiscount > 0, "预览应显示优惠金额");

//        // 验证原始购物车未被修改
//        Assert.Equal(originalItems.Count, originalCart.Items.Count);
//        for (int i = 0; i < originalItems.Count; i++)
//        {
//            var original = originalItems[i];
//            var current = originalCart.Items[i];

//            Assert.Equal(original.ProductId, current.Product.Id);
//            Assert.Equal(original.Quantity, current.Quantity);
//            Assert.Equal(original.UnitPrice, current.UnitPrice);
//            Assert.Equal(original.ActualUnitPrice, current.ActualUnitPrice);
//        }

//        Output.WriteLine("原始购物车状态未被修改 ✓");
//    }

//    [Fact]
//    [Trait("Category", "Integration")]
//    [Trait("Priority", "Medium")]
//    public void ApplySpecificPromotion_ShouldApplyOnlySpecifiedRule()
//    {
//        // Arrange
//        var giftRule = TestDataGenerator.CreateUnifiedGiftRule_Buy2Get1();
//        var discountRule = TestDataGenerator.CreateUnifiedDiscountRule_Buy3Get20PercentOff();
//        var rules = new List<PromotionRuleBase> { giftRule, discountRule };

//        var cart = TestDataGenerator.CreateCustomCart("TEST_CART", "CUSTOMER_004",
//            (TestDataGenerator.CreateProductA(), 4), // 4件A，满足两个规则
//            (TestDataGenerator.CreateProductB(), 2)  // 2件B
//        );

//        LogCartDetails(cart, "指定规则应用测试购物车");

//        // Act - 只应用买赠规则
//        var result = _promotionService.ApplySpecificPromotion(cart, giftRule.Id, rules);

//        // Assert
//        AssertPromotionResult(result, "指定规则应用");
//        LogPromotionResultDetails(result);

//        // 验证只应用了指定的买赠规则
//        Assert.Single(result.AppliedRules);
//        AssertRuleApplication(result.AppliedRules[0], giftRule.Id);

//        // 验证其他规则被忽略
//        var ignoredRule = result.IgnoredRules.FirstOrDefault(r => r.RuleId == discountRule.Id);
//        Assert.NotNull(ignoredRule);
//        Assert.Contains("未选择", ignoredRule.Reason);
//    }

//    #endregion

//    #region 复杂场景集成测试

//    [Fact]
//    [Trait("Category", "Integration")]
//    [Trait("Priority", "High")]
//    public void CalculatePromotion_AllRuleTypes_ShouldOptimizeCorrectly()
//    {
//        // Arrange - 所有类型规则的组合测试
//        var rules = new List<PromotionRuleBase>
//        {
//            TestDataGenerator.CreateUnifiedGiftRule_Buy2Get1(),           // 买赠
//            TestDataGenerator.CreateTieredGiftRule_MultiTier(),           // 阶梯买赠
//            TestDataGenerator.CreateUnifiedDiscountRule_Buy3Get20PercentOff(), // 打折
//            TestDataGenerator.CreateTieredDiscountRule_MultiTier(),       // 阶梯打折
//            TestDataGenerator.CreateSpecialPriceRule_ProductASpecialPrice(), // 特价
//            TestDataGenerator.CreateUnifiedCashOffRule_100Minus20(),      // 减现
//            TestDataGenerator.CreateTieredCashOffRule_MultiTier(),        // 阶梯减现
//            TestDataGenerator.CreateBuyFreeRule_Buy2Free1()               // 买免
//        };

//        var cart = TestDataGenerator.CreateCustomCart("TEST_CART", "CUSTOMER_ALL_RULES",
//            (TestDataGenerator.CreateProductA(), 6), // 6件A
//            (TestDataGenerator.CreateProductB(), 4), // 4件B
//            (TestDataGenerator.CreateProductC(), 3), // 3件C
//            (TestDataGenerator.CreateProductD(), 2)  // 2件D
//        );

//        LogCartDetails(cart, "全规则类型测试购物车");
//        Output.WriteLine($"测试规则数量: {rules.Count}");

//        // Act
//        var result = _promotionService.CalculatePromotion(cart, rules);

//        // Assert
//        AssertPromotionResult(result, "全规则类型优化");
//        LogPromotionResultDetails(result);

//        // 验证优化结果
//        Assert.NotEmpty(result.AppliedRules);
//        Assert.True(result.TotalDiscount > 0, "全规则类型组合应产生优惠");

//        // 验证所有规则都被考虑
//        var totalRulesConsidered = result.AppliedRules.Count + result.IgnoredRules.Count;
//        Assert.Equal(rules.Count, totalRulesConsidered);

//        // 验证购物车状态一致性
//        AssertCartConsistency(result.CartAfterPromotion);

//        Output.WriteLine($"应用规则数: {result.AppliedRules.Count}, 忽略规则数: {result.IgnoredRules.Count}");
//        Output.WriteLine($"总优惠金额: {result.TotalDiscount:C}");
//    }

//    [Fact]
//    [Trait("Category", "Integration")]
//    [Trait("Priority", "Medium")]
//    public void CalculatePromotion_ConflictingRules_ShouldResolveConflicts()
//    {
//        // Arrange - 冲突规则：互斥的促销规则
//        var rule1 = TestDataGenerator.CreateUnifiedDiscountRule_Buy3Get20PercentOff();
//        rule1.MutuallyExclusiveRules = new List<string> { "CONFLICTING_RULE_2" };

//        var rule2 = new UnifiedDiscountRule
//        {
//            Id = "CONFLICTING_RULE_2",
//            Name = "冲突规则2",
//            Description = "与规则1互斥的打折规则",
//            Priority = 75, // 更高优先级
//            IsEnabled = true,
//            MutuallyExclusiveRules = new List<string> { rule1.Id },
//            DiscountConditions = new List<DiscountCondition>
//            {
//                new()
//                {
//                    ProductIds = new List<string> { "A" },
//                    RequiredQuantity = 2,
//                    DiscountRate = 0.3m // 30%折扣
//                }
//            }
//        };

//        var rules = new List<PromotionRuleBase> { rule1, rule2 };

//        var cart = TestDataGenerator.CreateCustomCart("TEST_CART", "CUSTOMER_CONFLICT",
//            (TestDataGenerator.CreateProductA(), 3) // 3件A，满足两个规则
//        );

//        LogCartDetails(cart, "冲突规则测试购物车");

//        // Act
//        var result = _promotionService.CalculatePromotion(cart, rules);

//        // Assert
//        AssertPromotionResult(result, "冲突规则解决");
//        LogPromotionResultDetails(result);

//        // 验证只应用了一个规则（优先级更高的）
//        Assert.Single(result.AppliedRules);
//        AssertRuleApplication(result.AppliedRules[0], rule2.Id); // 优先级更高的规则2

//        // 验证冲突规则被忽略
//        Assert.Single(result.IgnoredRules);
//        var ignoredRule = result.IgnoredRules.First();
//        Assert.Equal(rule1.Id, ignoredRule.RuleId);
//        Assert.Contains("互斥", ignoredRule.Reason);
//    }

//    [Fact]
//    [Trait("Category", "Integration")]
//    [Trait("Priority", "Medium")]
//    public void CalculatePromotion_StackableRules_ShouldStackCorrectly()
//    {
//        // Arrange - 可叠加规则：特价 + 减现
//        var specialPriceRule = TestDataGenerator.CreateSpecialPriceRule_ProductASpecialPrice();
//        specialPriceRule.CanStack = true;

//        var cashOffRule = TestDataGenerator.CreateUnifiedCashOffRule_100Minus20();
//        cashOffRule.CanStack = true;

//        var rules = new List<PromotionRuleBase> { specialPriceRule, cashOffRule };

//        var cart = TestDataGenerator.CreateCustomCart("TEST_CART", "CUSTOMER_STACKABLE",
//            (TestDataGenerator.CreateProductA(), 6) // 6件A，满足特价和减现条件
//        );

//        LogCartDetails(cart, "可叠加规则测试购物车");

//        // Act
//        var result = _promotionService.CalculatePromotion(cart, rules);

//        // Assert
//        AssertPromotionResult(result, "可叠加规则");
//        LogPromotionResultDetails(result);

//        // 验证两个规则都被应用
//        Assert.Equal(2, result.AppliedRules.Count);
//        Assert.Contains(result.AppliedRules, r => r.RuleId == specialPriceRule.Id);
//        Assert.Contains(result.AppliedRules, r => r.RuleId == cashOffRule.Id);

//        // 验证叠加效果
//        Assert.True(result.TotalDiscount > 0, "可叠加规则应产生累积优惠");

//        // 验证购物车状态
//        AssertCartConsistency(result.CartAfterPromotion);
//    }

//    [Fact]
//    [Trait("Category", "Integration")]
//    [Trait("Priority", "Medium")]
//    public void CalculatePromotion_CustomerTypeRestriction_ShouldRespectRestrictions()
//    {
//        // Arrange - 客户类型限制
//        var vipRule = new UnifiedDiscountRule
//        {
//            Id = "VIP_ONLY_RULE",
//            Name = "VIP专享规则",
//            Description = "仅限VIP客户的打折规则",
//            Priority = 80,
//            IsEnabled = true,
//            ApplicableCustomerTypes = new List<string> { "VIP" },
//            DiscountConditions = new List<DiscountCondition>
//            {
//                new()
//                {
//                    ProductIds = new List<string> { "A" },
//                    RequiredQuantity = 1,
//                    DiscountRate = 0.5m // 50%折扣
//                }
//            }
//        };

//        // 普通客户购物车
//        var regularCart = TestDataGenerator.CreateCustomCart("REGULAR_CART", "REGULAR_CUSTOMER",
//            (TestDataGenerator.CreateProductA(), 2)
//        );
//        regularCart.CustomerType = "REGULAR";

//        // VIP客户购物车
//        var vipCart = TestDataGenerator.CreateCustomCart("VIP_CART", "VIP_CUSTOMER",
//            (TestDataGenerator.CreateProductA(), 2)
//        );
//        vipCart.CustomerType = "VIP";

//        var rules = new List<PromotionRuleBase> { vipRule };

//        // Act
//        var regularResult = _promotionService.CalculatePromotion(regularCart, rules);
//        var vipResult = _promotionService.CalculatePromotion(vipCart, rules);

//        // Assert
//        // 普通客户不能享受VIP规则
//        AssertPromotionResult(regularResult, "普通客户VIP规则");
//        Assert.Empty(regularResult.AppliedRules);
//        Assert.Single(regularResult.IgnoredRules);
//        AssertIgnoredRule(regularResult.IgnoredRules[0], "客户类型不符");

//        // VIP客户可以享受VIP规则
//        AssertPromotionResult(vipResult, "VIP客户VIP规则");
//        Assert.Single(vipResult.AppliedRules);
//        AssertRuleApplication(vipResult.AppliedRules[0], vipRule.Id);
//        Assert.True(vipResult.TotalDiscount > 0, "VIP客户应享受VIP优惠");
//    }

//    #endregion

//    #region 边界条件和异常处理测试

//    [Fact]
//    [Trait("Category", "Integration")]
//    [Trait("Priority", "Low")]
//    public void CalculatePromotion_EmptyCart_ShouldReturnEmptyResult()
//    {
//        // Arrange
//        var rules = new List<PromotionRuleBase>
//        {
//            TestDataGenerator.CreateUnifiedGiftRule_Buy2Get1(),
//            TestDataGenerator.CreateUnifiedDiscountRule_Buy3Get20PercentOff()
//        };
//        var emptyCart = TestDataGenerator.CreateEmptyCart();

//        // Act
//        var result = _promotionService.CalculatePromotion(emptyCart, rules);

//        // Assert
//        AssertPromotionResult(result, "空购物车");
//        Assert.Empty(result.AppliedRules);
//        Assert.Equal(rules.Count, result.IgnoredRules.Count);
//        AssertAmountEqual(0m, result.TotalDiscount, "空购物车应无优惠");
//    }

//    [Fact]
//    [Trait("Category", "Integration")]
//    [Trait("Priority", "Low")]
//    public void CalculatePromotion_NoRules_ShouldReturnOriginalCart()
//    {
//        // Arrange
//        var cart = TestDataGenerator.CreateCustomCart("TEST_CART", "CUSTOMER_NO_RULES",
//            (TestDataGenerator.CreateProductA(), 3)
//        );
//        var emptyRules = new List<PromotionRuleBase>();

//        // Act
//        var result = _promotionService.CalculatePromotion(cart, emptyRules);

//        // Assert
//        AssertPromotionResult(result, "无规则");
//        Assert.Empty(result.AppliedRules);
//        Assert.Empty(result.IgnoredRules);
//        AssertAmountEqual(0m, result.TotalDiscount, "无规则应无优惠");

//        // 验证购物车未被修改
//        Assert.Equal(cart.Items.Count, result.CartAfterPromotion.Items.Count);
//    }

//    [Fact]
//    [Trait("Category", "Integration")]
//    [Trait("Priority", "Low")]
//    public void CalculatePromotion_AllRulesDisabled_ShouldIgnoreAllRules()
//    {
//        // Arrange
//        var rule1 = TestDataGenerator.CreateUnifiedGiftRule_Buy2Get1();
//        rule1.IsEnabled = false;

//        var rule2 = TestDataGenerator.CreateUnifiedDiscountRule_Buy3Get20PercentOff();
//        rule2.IsEnabled = false;

//        var rules = new List<PromotionRuleBase> { rule1, rule2 };

//        var cart = TestDataGenerator.CreateCustomCart("TEST_CART", "CUSTOMER_DISABLED_RULES",
//            (TestDataGenerator.CreateProductA(), 4),
//            (TestDataGenerator.CreateProductB(), 2)
//        );

//        // Act
//        var result = _promotionService.CalculatePromotion(cart, rules);

//        // Assert
//        AssertPromotionResult(result, "所有规则禁用");
//        Assert.Empty(result.AppliedRules);
//        Assert.Equal(rules.Count, result.IgnoredRules.Count);

//        foreach (var ignoredRule in result.IgnoredRules)
//        {
//            AssertIgnoredRule(ignoredRule, "规则未启用");
//        }

//        AssertAmountEqual(0m, result.TotalDiscount, "禁用规则应无优惠");
//    }

//    [Fact]
//    [Trait("Category", "Integration")]
//    [Trait("Priority", "Low")]
//    public void ApplySpecificPromotion_NonExistentRule_ShouldThrowException()
//    {
//        // Arrange
//        var rules = new List<PromotionRuleBase>
//        {
//            TestDataGenerator.CreateUnifiedGiftRule_Buy2Get1()
//        };
//        var cart = TestDataGenerator.CreateCustomCart("TEST_CART", "CUSTOMER_NONEXISTENT",
//            (TestDataGenerator.CreateProductA(), 2)
//        );

//        // Act & Assert
//        Assert.Throws<ArgumentException>(() =>
//            _promotionService.ApplySpecificPromotion(cart, "NON_EXISTENT_RULE", rules)
//        );
//    }

//    #endregion

//    #region 性能和压力测试

//    [Fact]
//    [Trait("Category", "Integration")]
//    [Trait("Priority", "Low")]
//    public void CalculatePromotion_LargeCartManyRules_ShouldPerformWell()
//    {
//        // Arrange - 大型购物车 + 多规则
//        var rules = new List<PromotionRuleBase>();

//        // 创建多个不同类型的规则
//        for (int i = 1; i <= 10; i++)
//        {
//            rules.Add(new UnifiedDiscountRule
//            {
//                Id = $"DISCOUNT_RULE_{i}",
//                Name = $"打折规则{i}",
//                Priority = 50 + i,
//                IsEnabled = true,
//                DiscountConditions = new List<DiscountCondition>
//                {
//                    new()
//                    {
//                        ProductIds = new List<string> { "A", "B", "C" },
//                        RequiredQuantity = i,
//                        DiscountRate = 0.1m * i
//                    }
//                }
//            });
//        }

//        var cart = TestDataGenerator.PerformanceTestData.CreateLargeCart(200);

//        // 添加更多商品以触发规则
//        cart.Items.AddRange(new[]
//        {
//            new CartItem
//            {
//                Product = TestDataGenerator.CreateProductA(),
//                Quantity = 50,
//                UnitPrice = TestDataGenerator.CreateProductA().Price
//            },
//            new CartItem
//            {
//                Product = TestDataGenerator.CreateProductB(),
//                Quantity = 30,
//                UnitPrice = TestDataGenerator.CreateProductB().Price
//            },
//            new CartItem
//            {
//                Product = TestDataGenerator.CreateProductC(),
//                Quantity = 20,
//                UnitPrice = TestDataGenerator.CreateProductC().Price
//            }
//        });
//        cart.InitializeActualPrices();

//        Output.WriteLine($"性能测试: {cart.Items.Count} 个商品项, {rules.Count} 个规则");

//        // Act & Assert
//        var executionTime = MeasureExecutionTime(() =>
//        {
//            var result = _promotionService.CalculatePromotion(cart, rules);
//            AssertPromotionResult(result, "大型购物车多规则性能测试");

//            // 验证结果合理性
//            Assert.True(result.AppliedRules.Count + result.IgnoredRules.Count == rules.Count,
//                "所有规则都应被处理");
//        });

//        // 性能断言：大型购物车多规则计算应在合理时间内完成
//        AssertPerformance(executionTime, TimeSpan.FromSeconds(2), "大型购物车多规则计算");
//    }

//    [Fact]
//    [Trait("Category", "Integration")]
//    [Trait("Priority", "Low")]
//    public void CalculatePromotion_ConcurrentRequests_ShouldBeThreadSafe()
//    {
//        // Arrange
//        var rule = TestDataGenerator.CreateUnifiedDiscountRule_Buy3Get20PercentOff();
//        var rules = new List<PromotionRuleBase> { rule };

//        var tasks = new List<Task<PromotionResult>>();
//        const int concurrentRequests = 10;

//        // Act - 并发执行促销计算
//        for (int i = 0; i < concurrentRequests; i++)
//        {
//            var taskId = i;
//            var task = Task.Run(() =>
//            {
//                var cart = TestDataGenerator.CreateCustomCart($"CONCURRENT_CART_{taskId}", $"CUSTOMER_{taskId}",
//                    (TestDataGenerator.CreateProductA(), 3)
//                );

//                return _promotionService.CalculatePromotion(cart, rules);
//            });

//            tasks.Add(task);
//        }

//        // Wait for all tasks to complete
//        Task.WaitAll(tasks.ToArray(), TimeSpan.FromSeconds(10));

//        // Assert
//        foreach (var task in tasks)
//        {
//            Assert.True(task.IsCompletedSuccessfully, "所有并发任务都应成功完成");

//            var result = task.Result;
//            AssertPromotionResult(result, "并发请求");

//            // 验证每个结果都是正确的
//            Assert.Single(result.AppliedRules);
//            AssertRuleApplication(result.AppliedRules[0], rule.Id);
//            Assert.True(result.TotalDiscount > 0, "每个并发请求都应产生相同的优惠");
//        }

//        // 验证所有结果一致
//        var firstResult = tasks[0].Result;
//        foreach (var task in tasks.Skip(1))
//        {
//            var result = task.Result;
//            AssertAmountEqual(firstResult.TotalDiscount, result.TotalDiscount, "所有并发请求结果应一致");
//        }

//        Output.WriteLine($"并发测试完成: {concurrentRequests} 个并发请求全部成功 ✓");
//    }

//    #endregion

//    #region 数据一致性验证测试

//    [Fact]
//    [Trait("Category", "Integration")]
//    [Trait("Priority", "Medium")]
//    public void CalculatePromotion_ComplexScenario_ShouldMaintainDataConsistency()
//    {
//        // Arrange - 复杂场景：多种规则类型组合
//        var rules = new List<PromotionRuleBase>
//        {
//            TestDataGenerator.CreateUnifiedGiftRule_Buy2Get1(),
//            TestDataGenerator.CreateUnifiedDiscountRule_Buy3Get20PercentOff(),
//            TestDataGenerator.CreateSpecialPriceRule_ProductASpecialPrice(),
//            TestDataGenerator.CreateUnifiedCashOffRule_100Minus20()
//        };

//        var cart = TestDataGenerator.CreateCustomCart("CONSISTENCY_TEST", "CUSTOMER_CONSISTENCY",
//            (TestDataGenerator.CreateProductA(), 5),
//            (TestDataGenerator.CreateProductB(), 3),
//            (TestDataGenerator.CreateProductC(), 2)
//        );

//        var originalTotal = cart.Items.Sum(i => i.UnitPrice * i.Quantity);
//        LogCartDetails(cart, "数据一致性测试购物车");
//        Output.WriteLine($"原始总金额: {originalTotal:C}");

//        // Act
//        var result = _promotionService.CalculatePromotion(cart, rules);

//        // Assert
//        AssertPromotionResult(result, "数据一致性验证");
//        LogPromotionResultDetails(result);

//        // 验证数据一致性
//        AssertCartConsistency(result.CartAfterPromotion);

//        // 验证金额计算一致性
//        var finalTotal = result.CartAfterPromotion.Items.Sum(i => i.ActualUnitPrice * i.Quantity);
//        var calculatedTotal = originalTotal - result.TotalDiscount;

//        AssertAmountEqual(calculatedTotal, finalTotal, "计算总金额应与购物车最终总金额一致");

//        // 验证商品数量一致性
//        var originalQuantity = cart.Items.Sum(i => i.Quantity);
//        var finalQuantity = result.CartAfterPromotion.Items.Sum(i => i.Quantity);

//        // 商品数量可能因为赠品而增加，但不应减少
//        Assert.True(finalQuantity >= originalQuantity, "最终商品数量不应少于原始数量");

//        // 验证规则应用的逻辑一致性
//        foreach (var appliedRule in result.AppliedRules)
//        {
//            Assert.True(appliedRule.DiscountAmount >= 0, "优惠金额不应为负数");
//            Assert.False(string.IsNullOrEmpty(appliedRule.RuleId), "应用规则ID不应为空");
//        }

//        Output.WriteLine("数据一致性验证通过 ✓");
//    }

//    #endregion

//    #region 辅助方法

//    /// <summary>
//    /// 验证购物车数据一致性
//    /// </summary>
//    private void AssertCartConsistency(ShoppingCart cart)
//    {
//        Assert.NotNull(cart);
//        Assert.NotNull(cart.Items);

//        foreach (var item in cart.Items)
//        {
//            Assert.NotNull(item.Product);
//            Assert.True(item.Quantity > 0, "商品数量必须大于0");
//            Assert.True(item.UnitPrice >= 0, "商品单价不能为负数");
//            Assert.True(item.ActualUnitPrice >= 0, "商品实际单价不能为负数");
//            Assert.True(item.ActualUnitPrice <= item.UnitPrice, "实际单价不应超过原价");
//        }

//        // 验证购物车总金额
//        var totalAmount = cart.Items.Sum(i => i.ActualUnitPrice * i.Quantity);
//        Assert.True(totalAmount >= 0, "购物车总金额不能为负数");
//    }

//    #endregion
//}