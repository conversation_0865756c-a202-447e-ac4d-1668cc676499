{"Version": 1, "Hash": "8L8kq3v35dHbFKtwhgXHdplNQ0CZcjRDUUBzYJbrt80=", "Source": "PE2", "BasePath": "_content/PE2", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "PE2\\wwwroot", "Source": "PE2", "ContentRoot": "F:\\ac\\POSPE2\\PE2\\wwwroot\\", "BasePath": "_content/PE2", "Pattern": "**"}], "Assets": [{"Identity": "F:\\ac\\POSPE2\\PE2\\obj\\Debug\\net9.0\\compressed\\6aazo58n67-5ipweew5fc.gz", "SourceId": "PE2", "SourceType": "Discovered", "ContentRoot": "F:\\ac\\POSPE2\\PE2\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PE2", "RelativePath": "standalone#[.{fingerprint=5ipweew5fc}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "F:\\ac\\POSPE2\\PE2\\wwwroot\\standalone.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\ac\\POSPE2\\PE2\\wwwroot\\standalone.html"}, {"Identity": "F:\\ac\\POSPE2\\PE2\\obj\\Debug\\net9.0\\compressed\\w83e256bdn-a18q5nlcb0.gz", "SourceId": "PE2", "SourceType": "Discovered", "ContentRoot": "F:\\ac\\POSPE2\\PE2\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PE2", "RelativePath": "simple#[.{fingerprint=a18q5nlcb0}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "F:\\ac\\POSPE2\\PE2\\wwwroot\\simple.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td2pv9ru9f", "Integrity": "r7SMTb2SoN/s2+NsxbMPbF8f1JC/pYaOMF9gsXk0c28=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\ac\\POSPE2\\PE2\\wwwroot\\simple.html"}, {"Identity": "F:\\ac\\POSPE2\\PE2\\wwwroot\\simple.html", "SourceId": "PE2", "SourceType": "Discovered", "ContentRoot": "F:\\ac\\POSPE2\\PE2\\wwwroot\\", "BasePath": "_content/PE2", "RelativePath": "simple#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "a18q5nlcb0", "Integrity": "0iOWCDzoJynmCoepavmurixyw3WoyPu95/YsxLQ4BwI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\simple.html"}, {"Identity": "F:\\ac\\POSPE2\\PE2\\wwwroot\\standalone.html", "SourceId": "PE2", "SourceType": "Discovered", "ContentRoot": "F:\\ac\\POSPE2\\PE2\\wwwroot\\", "BasePath": "_content/PE2", "RelativePath": "standalone#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\standalone.html"}], "Endpoints": [{"Route": "simple.a18q5nlcb0.html", "AssetFile": "F:\\ac\\POSPE2\\PE2\\obj\\Debug\\net9.0\\compressed\\w83e256bdn-a18q5nlcb0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000131943528"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7578"}, {"Name": "ETag", "Value": "\"r7SMTb2SoN/s2+NsxbMPbF8f1JC/pYaOMF9gsXk0c28=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:43:57 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"0iOWCDzoJynmCoepavmurixyw3WoyPu95/YsxLQ4BwI=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a18q5nlcb0"}, {"Name": "label", "Value": "simple.html"}, {"Name": "integrity", "Value": "sha256-0iOWCDzoJynmCoepavmurixyw3WoyPu95/YsxLQ4BwI="}]}, {"Route": "simple.a18q5nlcb0.html", "AssetFile": "F:\\ac\\POSPE2\\PE2\\wwwroot\\simple.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "39459"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"0iOWCDzoJynmCoepavmurixyw3WoyPu95/YsxLQ4BwI=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:43:57 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a18q5nlcb0"}, {"Name": "label", "Value": "simple.html"}, {"Name": "integrity", "Value": "sha256-0iOWCDzoJynmCoepavmurixyw3WoyPu95/YsxLQ4BwI="}]}, {"Route": "simple.a18q5nlcb0.html.gz", "AssetFile": "F:\\ac\\POSPE2\\PE2\\obj\\Debug\\net9.0\\compressed\\w83e256bdn-a18q5nlcb0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7578"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"r7SMTb2SoN/s2+NsxbMPbF8f1JC/pYaOMF9gsXk0c28=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:43:57 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a18q5nlcb0"}, {"Name": "label", "Value": "simple.html.gz"}, {"Name": "integrity", "Value": "sha256-r7SMTb2SoN/s2+NsxbMPbF8f1JC/pYaOMF9gsXk0c28="}]}, {"Route": "simple.html", "AssetFile": "F:\\ac\\POSPE2\\PE2\\obj\\Debug\\net9.0\\compressed\\w83e256bdn-a18q5nlcb0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000131943528"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7578"}, {"Name": "ETag", "Value": "\"r7SMTb2SoN/s2+NsxbMPbF8f1JC/pYaOMF9gsXk0c28=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:43:57 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"0iOWCDzoJynmCoepavmurixyw3WoyPu95/YsxLQ4BwI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0iOWCDzoJynmCoepavmurixyw3WoyPu95/YsxLQ4BwI="}]}, {"Route": "simple.html", "AssetFile": "F:\\ac\\POSPE2\\PE2\\wwwroot\\simple.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "39459"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"0iOWCDzoJynmCoepavmurixyw3WoyPu95/YsxLQ4BwI=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:43:57 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0iOWCDzoJynmCoepavmurixyw3WoyPu95/YsxLQ4BwI="}]}, {"Route": "simple.html.gz", "AssetFile": "F:\\ac\\POSPE2\\PE2\\obj\\Debug\\net9.0\\compressed\\w83e256bdn-a18q5nlcb0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7578"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"r7SMTb2SoN/s2+NsxbMPbF8f1JC/pYaOMF9gsXk0c28=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:43:57 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r7SMTb2SoN/s2+NsxbMPbF8f1JC/pYaOMF9gsXk0c28="}]}, {"Route": "standalone.5ipweew5fc.html", "AssetFile": "F:\\ac\\POSPE2\\PE2\\obj\\Debug\\net9.0\\compressed\\6aazo58n67-5ipweew5fc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:09:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "standalone.html"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "standalone.5ipweew5fc.html", "AssetFile": "F:\\ac\\POSPE2\\PE2\\wwwroot\\standalone.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:09:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "standalone.html"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "standalone.5ipweew5fc.html.gz", "AssetFile": "F:\\ac\\POSPE2\\PE2\\obj\\Debug\\net9.0\\compressed\\6aazo58n67-5ipweew5fc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:09:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "standalone.html.gz"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "standalone.html", "AssetFile": "F:\\ac\\POSPE2\\PE2\\obj\\Debug\\net9.0\\compressed\\6aazo58n67-5ipweew5fc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:09:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "standalone.html", "AssetFile": "F:\\ac\\POSPE2\\PE2\\wwwroot\\standalone.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:09:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "standalone.html.gz", "AssetFile": "F:\\ac\\POSPE2\\PE2\\obj\\Debug\\net9.0\\compressed\\6aazo58n67-5ipweew5fc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:09:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}]}