using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.ExchangeRules;

/// <summary>
/// 统一优惠换购规则 [OK]
/// 针对某类商品，满X件或X元，优惠Y元，换购一件C类商品
/// 场景案例：购买A商品大于等于1件时，可以优惠100元购买B商品。
/// A、B商品吊牌价、零售价都为1000元；购买A、B商品各1件时，应收金额为1000 +（1000-100）=1900元（B商品优惠100元）
/// 备注：1.如果优惠商品和条件商品重复，那么需要考虑客户利益最大化或商家利益最大化的选择策略。
///       2.优惠商品和条件商品可以是同一商品，但不能是同一件商品（即不能同时满足购买和换购条件）。
/// </summary>
public class UnifiedDiscountAmountExchangeRule : BaseExchangeRule
{
    public override string RuleType => "UnifiedDiscountAmountExchange";
    /// <summary>
    /// 购买条件列表
    /// </summary>
    public List<ExchangeBuyCondition> BuyConditions { get; set; } = new();

    /// <summary>
    /// 优惠换购条件列表
    /// </summary>
    public List<DiscountAmountExchangeCondition> ExchangeConditions { get; set; } = new();

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!BuyConditions.Any() || !ExchangeConditions.Any())
            return false;

        // 检查购买条件
        if (!CheckBuyConditions(cart))
            return false;

        // 检查换购商品是否在购物车中且有足够数量（POS系统核心要求）
        return CheckExchangeProductAvailability(cart);
    }

    /// <summary>
    /// 检查购买条件
    /// </summary>
    private bool CheckBuyConditions(ShoppingCart cart)
    {
        foreach (var buyCondition in BuyConditions)
        {
            var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));
            var totalAmount = buyCondition.ProductIds.Sum(id =>
                cart.Items.Where(x => x.Product.Id == id && x.AvailableQuantity > 0).Sum(x => x.UnitPrice * x.AvailableQuantity));

            if (buyCondition.RequiredQuantity > 0 && availableQuantity < buyCondition.RequiredQuantity)
                return false;

            if (buyCondition.RequiredAmount > 0 && totalAmount < buyCondition.RequiredAmount)
                return false;
        }

        return true;
    }

    /// <summary>
    /// 检查换购商品可用性
    /// 确保在消耗购买条件商品后，仍有足够的换购商品可用
    /// </summary>
    private bool CheckExchangeProductAvailability(ShoppingCart cart)
    {
        // 模拟消耗购买条件商品后的商品可用性
        var simulatedCart = SimulateCartAfterBuyConditionConsumption(cart);

        var allExchangeProductIds = ExchangeConditions
            .SelectMany(c => c.ExchangeProductIds)
            .Distinct()
            .ToList();

        // 检查是否还有可用的换购商品
        foreach (var exchangeProductId in allExchangeProductIds)
        {
            var availableQuantity = simulatedCart.ContainsKey(exchangeProductId) ? simulatedCart[exchangeProductId] : 0;
            if (availableQuantity > 0)
                return true; // 至少有一个换购商品可用
        }

        return false;
    }

    /// <summary>
    /// 模拟消耗购买条件商品后的购物车状态
    /// </summary>
    private Dictionary<string, int> SimulateCartAfterBuyConditionConsumption(ShoppingCart cart)
    {
        var simulatedAvailability = new Dictionary<string, int>();

        // 初始化所有商品的可用数量
        foreach (var item in cart.Items)
        {
            simulatedAvailability[item.Product.Id] = item.AvailableQuantity;
        }

        // 模拟消耗购买条件商品
        foreach (var buyCondition in BuyConditions)
        {
            var remainingQuantity = buyCondition.RequiredQuantity;
            var remainingAmount = buyCondition.RequiredAmount;

            // 按数量消耗
            if (buyCondition.RequiredQuantity > 0)
            {
                foreach (var productId in buyCondition.ProductIds)
                {
                    if (remainingQuantity <= 0) break;

                    if (simulatedAvailability.ContainsKey(productId))
                    {
                        var canConsume = Math.Min(simulatedAvailability[productId], remainingQuantity);
                        simulatedAvailability[productId] -= canConsume;
                        remainingQuantity -= canConsume;
                    }
                }
            }
            // 按金额消耗（需要更精确的计算）
            else if (buyCondition.RequiredAmount > 0)
            {
                foreach (var productId in buyCondition.ProductIds)
                {
                    if (remainingAmount <= 0) break;

                    var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId);
                    if (cartItem != null && simulatedAvailability.ContainsKey(productId))
                    {
                        var availableQuantity = simulatedAvailability[productId];
                        var canConsumeByAmount = Math.Min(availableQuantity, (int)Math.Ceiling(remainingAmount / cartItem.UnitPrice));
                        var actualConsume = Math.Min(availableQuantity, canConsumeByAmount);

                        simulatedAvailability[productId] -= actualConsume;
                        remainingAmount -= actualConsume * cartItem.UnitPrice;
                    }
                }
            }
        }

        return simulatedAvailability;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        var maxApplications = 0;

        // 计算基于购买条件的最大应用次数
        foreach (var buyCondition in BuyConditions)
        {
            var availableQuantity = buyCondition.ProductIds.Sum(id => cart.GetAvailableProductQuantity(id));

            if (buyCondition.RequiredQuantity > 0)
            {
                var maxByQuantity = IsRepeatable
                    ? availableQuantity / buyCondition.RequiredQuantity
                    : (availableQuantity >= buyCondition.RequiredQuantity ? 1 : 0);

                maxApplications = Math.Max(maxApplications, maxByQuantity);
            }
            else
            {
                maxApplications = Math.Max(maxApplications, 1);
            }
        }

        // 检查换购商品的限制
        var simulatedCart = SimulateCartAfterBuyConditionConsumption(cart);
        var availableExchangeProducts = ExchangeConditions
            .SelectMany(c => c.ExchangeProductIds)
            .Distinct()
            .Sum(id => simulatedCart.ContainsKey(id) ? simulatedCart[id] : 0);

        // 换购商品数量也会限制应用次数
        if (availableExchangeProducts == 0)
            return 0;

        var requiredExchangeProducts = ExchangeConditions.Sum(c => c.ExchangeQuantity);
        if (requiredExchangeProducts > 0)
        {
            var maxByExchangeProducts = availableExchangeProducts / requiredExchangeProducts;
            maxApplications = Math.Min(maxApplications, maxByExchangeProducts);
        }

        if (!IsRepeatable)
            maxApplications = Math.Min(maxApplications, 1);

        if (MaxApplications > 0)
            maxApplications = Math.Min(maxApplications, MaxApplications);

        return maxApplications;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyDiscountAmountExchange(cart, applicationCount);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用优惠换购促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用优惠换购促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyDiscountAmountExchange(ShoppingCart cart, int applicationCount)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var exchangeItems = new List<GiftItem>(); // 换购商品记录为GiftItem，但实际是换购

        for (int app = 0; app < applicationCount; app++)
        {
            // 检查是否还有足够的商品用于此次应用
            if (!CheckConditions(cart))
                break;

            foreach (var buyCondition in BuyConditions)
            {
                // 消耗购买条件商品
                if (!ConsumeBuyConditionProducts(cart, buyCondition, consumedItems))
                    continue;

                // 根据策略选择换购商品
                var applicableExchanges = GetApplicableExchangeConditions();

                foreach (var exchange in applicableExchanges)
                {
                    var selectedExchangeProducts = SelectOptimalExchangeProducts(
                        exchange.ExchangeProductIds, cart, exchange.ExchangeQuantity);

                    foreach (var productId in selectedExchangeProducts)
                    {
                        var cartItem = cart.Items.FirstOrDefault(x => x.Product.Id == productId && x.AvailableQuantity > 0);
                        if (cartItem == null) continue;

                        // 计算优惠换购折扣
                        var originalPrice = cartItem.UnitPrice;
                        var discountedPrice = CalculateDiscountAmountExchange(originalPrice, exchange.DiscountAmount);
                        var actualDiscountAmount = originalPrice - discountedPrice;

                        if (actualDiscountAmount > 0)
                        {
                            totalDiscountAmount += actualDiscountAmount;

                            var strategyDescription = ExchangeSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                                ? "客户利益最大化"
                                : "商家利益最大化";

                            exchangeItems.Add(new GiftItem
                            {
                                ProductId = productId,
                                ProductName = cartItem.Product.Name,
                                Quantity = 1,
                                Value = actualDiscountAmount,
                                Description = $"优惠换购：优惠{exchange.DiscountAmount:C}换购，节省{actualDiscountAmount:C}（{strategyDescription}）"
                            });

                            // 标记商品为换购商品（修改实际支付价格）
                            cartItem.ActualUnitPrice = discountedPrice;
                            // 消耗一个换购商品
                            cartItem.Quantity -= 1;
                        }
                    }
                }
            }
        }

        return (totalDiscountAmount, consumedItems, exchangeItems);
    }

    /// <summary>
    /// 消耗购买条件商品
    /// </summary>
    private bool ConsumeBuyConditionProducts(ShoppingCart cart, ExchangeBuyCondition buyCondition, List<ConsumedItem> consumedItems)
    {
        var remainingQuantity = buyCondition.RequiredQuantity;
        var remainingAmount = buyCondition.RequiredAmount;

        // 按数量要求消耗
        if (buyCondition.RequiredQuantity > 0)
        {
            foreach (var productId in buyCondition.ProductIds)
            {
                if (remainingQuantity <= 0) break;

                var availableItems = cart.Items
                    .Where(x => x.Product.Id == productId && x.AvailableQuantity > 0)
                    .ToList();

                foreach (var item in availableItems)
                {
                    if (remainingQuantity <= 0) break;

                    var consumeQuantity = Math.Min(item.AvailableQuantity, remainingQuantity);

                    var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == productId);
                    if (existingConsumed != null)
                    {
                        existingConsumed.Quantity += consumeQuantity;
                    }
                    else
                    {
                        consumedItems.Add(new ConsumedItem
                        {
                            ProductId = productId,
                            ProductName = item.Product.Name,
                            Quantity = consumeQuantity,
                            UnitPrice = item.UnitPrice
                        });
                    }

                    item.Quantity -= consumeQuantity;
                    remainingQuantity -= consumeQuantity;
                }
            }

            return remainingQuantity <= 0;
        }
        // 按金额要求消耗
        else if (buyCondition.RequiredAmount > 0)
        {
            foreach (var productId in buyCondition.ProductIds)
            {
                if (remainingAmount <= 0) break;

                var availableItems = cart.Items
                    .Where(x => x.Product.Id == productId && x.AvailableQuantity > 0)
                    .ToList();

                foreach (var item in availableItems)
                {
                    if (remainingAmount <= 0) break;

                    var itemValue = item.UnitPrice * item.AvailableQuantity;
                    var consumeValue = Math.Min(itemValue, remainingAmount);
                    var consumeQuantity = (int)Math.Ceiling(consumeValue / item.UnitPrice);
                    consumeQuantity = Math.Min(consumeQuantity, item.AvailableQuantity);

                    var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == productId);
                    if (existingConsumed != null)
                    {
                        existingConsumed.Quantity += consumeQuantity;
                    }
                    else
                    {
                        consumedItems.Add(new ConsumedItem
                        {
                            ProductId = productId,
                            ProductName = item.Product.Name,
                            Quantity = consumeQuantity,
                            UnitPrice = item.UnitPrice
                        });
                    }

                    item.Quantity -= consumeQuantity;
                    remainingAmount -= consumeQuantity * item.UnitPrice;
                }
            }

            return remainingAmount <= 0;
        }

        return false;
    }

    /// <summary>
    /// 获取适用的换购条件
    /// </summary>
    private List<DiscountAmountExchangeCondition> GetApplicableExchangeConditions()
    {
        return ExchangeStrategy switch
        {
            ExchangeStrategy.ByGradient => ExchangeConditions.Take(1).ToList(), // 梯度换购：只取第一个
            ExchangeStrategy.AllExchange => ExchangeConditions.ToList(), // 全部换购：取所有
            _ => ExchangeConditions.Take(1).ToList()
        };
    }
}

/// <summary>
/// 优惠换购条件
/// </summary>
public class DiscountAmountExchangeCondition
{
    /// <summary>
    /// 换购商品ID列表
    /// </summary>
    public List<string> ExchangeProductIds { get; set; } = new();

    /// <summary>
    /// 换购数量
    /// </summary>
    public int ExchangeQuantity { get; set; } = 1;

    /// <summary>
    /// 优惠金额
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}