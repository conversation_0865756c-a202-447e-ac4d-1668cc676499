### 测试组合折扣规则修复
### 场景：购买A商品50元 + B商品30元 + C商品20元
### 规则：购买A(1件) + B(1件)，C商品打9折
### 预期结果：A和B作为条件被消耗，只有C商品享受9折，总价应该是50+30+20*0.9=98元

POST {{host}}/api/promotion/test-combination-discount
Content-Type: application/json

{
  "cart": {
    "items": [
      {
        "product": {
          "id": "A",
          "name": "商品A",
          "price": 50.00,
          "category": "电子产品"
        },
        "quantity": 1,
        "unitPrice": 50.00
      },
      {
        "product": {
          "id": "B", 
          "name": "商品B",
          "price": 30.00,
          "category": "服装"
        },
        "quantity": 1,
        "unitPrice": 30.00
      },
      {
        "product": {
          "id": "C",
          "name": "商品C", 
          "price": 20.00,
          "category": "家居"
        },
        "quantity": 1,
        "unitPrice": 20.00
      }
    ]
  },
  "rules": [
    {
      "$type": "CombinationDiscount",
      "id": "COMBINATION_DISCOUNT_001",
      "name": "组合折扣测试 - 买A+B享C商品9折",
      "description": "购买A商品和B商品各大于等于1件，优惠C商品，C商品打9折",
      "priority": 85,
      "isEnabled": true,
      "startTime": "2024-01-01T00:00:00",
      "endTime": "2025-12-31T23:59:59",
      "isRepeatable": true,
      "maxApplications": 2,
      "applicableCustomerTypes": [],
      "exclusiveRuleIds": [],
      "canStackWithOthers": true,
      "productExclusivity": "None",
      "discountSelectionStrategy": "CustomerBenefit",
      "discountRate": 0.9,
      "discountProductIds": [ "C" ],
      "combinationConditions": [
        {
          "productIds": [ "A" ],
          "requiredQuantity": 1,
          "requiredAmount": 0
        },
        {
          "productIds": [ "B" ],
          "requiredQuantity": 1,
          "requiredAmount": 0
        }
      ]
    }
  ]
}

###

### 测试错误配置 - B商品既是条件又是折扣对象
POST {{host}}/api/promotion/test-combination-discount
Content-Type: application/json

{
  "cart": {
    "items": [
      {
        "product": {
          "id": "A",
          "name": "商品A",
          "price": 50.00,
          "category": "电子产品"
        },
        "quantity": 1,
        "unitPrice": 50.00
      },
      {
        "product": {
          "id": "B", 
          "name": "商品B",
          "price": 30.00,
          "category": "服装"
        },
        "quantity": 1,
        "unitPrice": 30.00
      },
      {
        "product": {
          "id": "C",
          "name": "商品C", 
          "price": 20.00,
          "category": "家居"
        },
        "quantity": 1,
        "unitPrice": 20.00
      }
    ]
  },
  "rules": [
    {
      "$type": "CombinationDiscount",
      "id": "COMBINATION_DISCOUNT_002",
      "name": "错误配置测试 - B既是条件又是折扣对象",
      "description": "购买A商品和B商品各大于等于1件，B和C商品打9折",
      "priority": 85,
      "isEnabled": true,
      "startTime": "2024-01-01T00:00:00",
      "endTime": "2025-12-31T23:59:59",
      "isRepeatable": true,
      "maxApplications": 2,
      "applicableCustomerTypes": [],
      "exclusiveRuleIds": [],
      "canStackWithOthers": true,
      "productExclusivity": "None",
      "discountSelectionStrategy": "CustomerBenefit",
      "discountRate": 0.9,
      "discountProductIds": [ "B", "C" ],
      "combinationConditions": [
        {
          "productIds": [ "A" ],
          "requiredQuantity": 1,
          "requiredAmount": 0
        },
        {
          "productIds": [ "B" ],
          "requiredQuantity": 1,
          "requiredAmount": 0
        }
      ]
    }
  ]
}

###

### 测试多次应用 - 购买A2件+B2件+C2件
POST {{host}}/api/promotion/test-combination-discount
Content-Type: application/json

{
  "cart": {
    "items": [
      {
        "product": {
          "id": "A",
          "name": "商品A",
          "price": 50.00,
          "category": "电子产品"
        },
        "quantity": 2,
        "unitPrice": 50.00
      },
      {
        "product": {
          "id": "B", 
          "name": "商品B",
          "price": 30.00,
          "category": "服装"
        },
        "quantity": 2,
        "unitPrice": 30.00
      },
      {
        "product": {
          "id": "C",
          "name": "商品C", 
          "price": 20.00,
          "category": "家居"
        },
        "quantity": 2,
        "unitPrice": 20.00
      }
    ]
  },
  "rules": [
    {
      "$type": "CombinationDiscount",
      "id": "COMBINATION_DISCOUNT_003",
      "name": "多次应用测试 - 买A+B享C商品9折",
      "description": "购买A商品和B商品各大于等于1件，优惠C商品，C商品打9折",
      "priority": 85,
      "isEnabled": true,
      "startTime": "2024-01-01T00:00:00",
      "endTime": "2025-12-31T23:59:59",
      "isRepeatable": true,
      "maxApplications": 2,
      "applicableCustomerTypes": [],
      "exclusiveRuleIds": [],
      "canStackWithOthers": true,
      "productExclusivity": "None",
      "discountSelectionStrategy": "CustomerBenefit",
      "discountRate": 0.9,
      "discountProductIds": [ "C" ],
      "combinationConditions": [
        {
          "productIds": [ "A" ],
          "requiredQuantity": 1,
          "requiredAmount": 0
        },
        {
          "productIds": [ "B" ],
          "requiredQuantity": 1,
          "requiredAmount": 0
        }
      ]
    }
  ]
}
