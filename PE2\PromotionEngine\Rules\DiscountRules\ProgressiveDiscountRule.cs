using PE2.Models;
using PE2.PromotionEngine.Models;
using System.Text.Json.Serialization;

namespace PE2.PromotionEngine.Rules.DiscountRules;

/// <summary>
/// 递增折扣规则
/// 针对某一类商品，第一件X折，第二件Y折，第三件及以上Z折
/// 场景：购买A商品第一件0.8折，第二件0.7折，第三件及以上0.5折
/// </summary>
public class ProgressiveDiscountRule : BaseDiscountRule
{
    public override string RuleType => "ProgressiveDiscount";

    /// <summary>
    /// 适用的商品ID列表
    /// </summary>
    public List<string> ApplicableProductIds { get; set; } = new();

    /// <summary>
    /// 递增折扣列表（按顺序应用）
    /// </summary>
    public List<ProgressiveDiscountTier> ProgressiveTiers { get; set; } = new();

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!ApplicableProductIds.Any() || !ProgressiveTiers.Any())
            return false;

        // 验证商品是否在购物车中
        if (!ValidateDiscountProductsInCart(cart, ApplicableProductIds))
            return false;

        // 检查是否有足够的商品数量
        var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);
        return totalQuantity >= 1;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        // 递增折扣通常只应用一次，因为会对所有商品按顺序应用不同折扣
        return 1;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyProgressiveDiscount(cart);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用递增折扣促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用递增折扣促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyProgressiveDiscount(ShoppingCart cart)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var discountRecords = new List<GiftItem>(); // 折扣记录

        // 获取所有适用的商品项
        var applicableItems = cart.Items
            .Where(x => ApplicableProductIds.Contains(x.Product.Id) && x.Quantity > 0)
            .ToList();

        if (!applicableItems.Any())
            return (0m, consumedItems, discountRecords);

        // 根据策略排序商品（客户利益最大化时，高价商品优先享受高折扣）
        var sortedItems = SortItemsByStrategy(applicableItems);

        var promotion = new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType
        };

        // 展开所有商品为单个商品项进行递增折扣计算
        var expandedItems = new List<(CartItem Item, decimal UnitPrice)>();
        foreach (var item in sortedItems)
        {
            for (int i = 0; i < item.Quantity; i++)
            {
                expandedItems.Add((item, item.UnitPrice));
            }
        }

        // 按递增折扣规则应用折扣
        var itemIndex = 0;
        var processedItems = new Dictionary<CartItem, (int ProcessedQuantity, decimal TotalDiscount, List<decimal> DiscountRates)>();

        foreach (var (item, unitPrice) in expandedItems)
        {
            // 确定当前商品应用的折扣梯度
            var tierIndex = Math.Min(itemIndex, ProgressiveTiers.Count - 1);
            var tier = ProgressiveTiers[tierIndex];

            var discountedPrice = unitPrice * tier.DiscountRate;
            var discountAmount = unitPrice - discountedPrice;

            if (discountAmount > 0)
            {
                totalDiscountAmount += discountAmount;

                // 记录处理的商品信息
                if (!processedItems.ContainsKey(item))
                {
                    processedItems[item] = (0, 0m, new List<decimal>());
                }

                var processed = processedItems[item];
                processed.ProcessedQuantity++;
                processed.TotalDiscount += discountAmount;
                processed.DiscountRates.Add(tier.DiscountRate);
                processedItems[item] = processed;
            }

            itemIndex++;
        }

        // 应用折扣到购物车项并记录详情
        foreach (var kvp in processedItems)
        {
            var item = kvp.Key;
            var (processedQuantity, totalDiscount, discountRates) = kvp.Value;

            if (processedQuantity > 0 && totalDiscount > 0)
            {
                // 计算平均折扣率（用于显示）
                var avgDiscountRate = discountRates.Average();
                
                // 计算新的实际单价
                var newActualUnitPrice = item.UnitPrice - (totalDiscount / processedQuantity);
                item.ActualUnitPrice = Math.Max(0, newActualUnitPrice);

                var strategyDescription = DiscountSelectionStrategy == DiscountSelectionStrategy.CustomerBenefit
                    ? "客户利益最大化"
                    : "商家利益最大化";

                var description = $"递增折扣：平均{avgDiscountRate:P1}折，节省{totalDiscount:C}（{strategyDescription}）";

                // 记录促销详情
                var promotionDetail = new ItemPromotionDetail
                {
                    RuleId = promotion.RuleId,
                    RuleName = promotion.RuleName,
                    PromotionType = promotion.PromotionType,
                    DiscountAmount = totalDiscount,
                    Description = description,
                    IsGiftRelated = false
                };

                item.AddPromotionDetail(promotionDetail);

                // 记录折扣
                discountRecords.Add(CreateDiscountRecord(
                    item.Product.Id,
                    item.Product.Name,
                    processedQuantity,
                    totalDiscount,
                    description
                ));

                // 记录消耗的商品
                var existingConsumed = consumedItems.FirstOrDefault(x => x.ProductId == item.Product.Id);
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += processedQuantity;
                }
                else
                {
                    consumedItems.Add(new ConsumedItem
                    {
                        ProductId = item.Product.Id,
                        ProductName = item.Product.Name,
                        Quantity = processedQuantity,
                        UnitPrice = item.UnitPrice
                    });
                }
            }
        }

        return (totalDiscountAmount, consumedItems, discountRecords);
    }
}

/// <summary>
/// 递增折扣梯度
/// </summary>
public class ProgressiveDiscountTier
{
    /// <summary>
    /// 折扣率（0.8表示8折）
    /// </summary>
    public decimal DiscountRate { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}
