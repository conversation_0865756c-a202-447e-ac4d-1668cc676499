// 主入口文件
const { createApp } = Vue;

// 创建Vue应用
const app = createApp({});

// 注册全局组件
app.component('PromotionConfigApp', PromotionConfigApp);
app.component('PromotionTypeSelector', PromotionTypeSelector);
app.component('DynamicForm', DynamicForm);
app.component('FormField', FormField);
app.component('ArrayField', ArrayField);
app.component('TagsInput', TagsInput);
app.component('ProductSelector', ProductSelector);
app.component('JsonPreview', JsonPreview);
app.component('TemplateSelector', TemplateSelector);
app.component('RuleValidationPanel', RuleValidationPanel);

// 挂载应用
app.mount('#app');

// 添加一些额外的样式
const additionalStyles = `
  .tags-input {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 4px;
    background: white;
    min-height: 32px;
    display: flex;
    align-items: center;
  }

  .tags-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 4px;
    width: 100%;
  }

  .tag {
    display: inline-flex;
    align-items: center;
    background: #f0f0f0;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 2px 8px;
    font-size: 12px;
    color: #595959;
  }

  .tag-remove {
    background: none;
    border: none;
    color: #999;
    margin-left: 4px;
    cursor: pointer;
    font-size: 14px;
    line-height: 1;
  }

  .tag-remove:hover {
    color: #ff4d4f;
  }

  .tag-input {
    border: none;
    outline: none;
    flex: 1;
    min-width: 100px;
    padding: 4px;
    font-size: 14px;
  }

  .switch-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 0 !important;
  }

  .switch-label input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
  }

  .switch-text {
    font-size: 14px;
    color: #595959;
  }

  .validation-message.success {
    background: #f6ffed;
    border: 1px solid #b7eb8f;
    color: #389e0d;
  }

  .validation-message.error {
    background: #fff2f0;
    border: 1px solid #ffccc7;
    color: #cf1322;
  }

  /* 响应式表单布局 */
  @media (max-width: 768px) {
    .form-row {
      flex-direction: column;
    }
    
    .form-col {
      margin-bottom: 12px;
    }
  }

  /* 改进的滚动条样式 */
  .sidebar::-webkit-scrollbar,
  .form-container::-webkit-scrollbar,
  .preview-container::-webkit-scrollbar {
    width: 6px;
  }

  .sidebar::-webkit-scrollbar-track,
  .form-container::-webkit-scrollbar-track,
  .preview-container::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  .sidebar::-webkit-scrollbar-thumb,
  .form-container::-webkit-scrollbar-thumb,
  .preview-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .sidebar::-webkit-scrollbar-thumb:hover,
  .form-container::-webkit-scrollbar-thumb:hover,
  .preview-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
`;

// 添加样式到头部
const styleElement = document.createElement('style');
styleElement.textContent = additionalStyles;
document.head.appendChild(styleElement);
