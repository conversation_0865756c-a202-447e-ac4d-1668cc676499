/**
 * 主要的促销配置系统组件
 * 集成动态表单渲染器和元数据服务
 * 支持基于反射API的完全动态化配置
 */

const PromotionConfigMainComponent = {
    name: 'PromotionConfigSystem',    components: {
        DynamicFormRenderer: window.DynamicFormRenderer,
        RuleManager: window.RuleManager
    },
    setup() {
        const { ref, reactive, computed, onMounted, watch } = Vue;
        const { ElMessage, ElMessageBox, ElLoading } = ElementPlus;

        // ===== 响应式数据 =====
        const loading = ref(false);
        const saving = ref(false);
        const metadataLoading = ref(true);
        
        // 促销类型元数据（从API动态加载）
        const promotionTypes = ref({});
        const selectedCategory = ref(null);
        const selectedType = ref(null);
        const currentStep = ref(1);
        
        // 表单数据
        const formData = ref({});
        
        // UI状态
        const showPreviewModal = ref(false);
        const showRuleManager = ref(false);
        
        // 服务实例
        const metadataService = ref(null);
        
        // ===== 计算属性 =====
        const selectedCategoryData = computed(() => {
            if (!selectedCategory.value || !promotionTypes.value[selectedCategory.value]) {
                return null;
            }
            return promotionTypes.value[selectedCategory.value];
        });

        const selectedTypeData = computed(() => {
            if (!selectedCategoryData.value || !selectedType.value) {
                return null;
            }
            return selectedCategoryData.value.types?.[selectedType.value] || null;
        });

        const isValidConfig = computed(() => {
            return !!(formData.value.ruleId && formData.value.ruleName && selectedTypeData.value);
        });

        const stepperItems = computed(() => [
            { title: '选择类型', description: '选择促销活动类型' },
            { title: '配置参数', description: '设置促销规则参数' },
            { title: '预览确认', description: '预览并确认配置' }
        ]);

        // ===== 初始化方法 =====
        const initializeSystem = async () => {
            try {
                metadataLoading.value = true;
                
                // 初始化元数据服务
                if (!window.apiService) {
                    throw new Error('API服务未初始化');
                }
                
                metadataService.value = window.getMetadataService(window.apiService);
                
                // 测试API连接
                const isConnected = await metadataService.value.checkApiConnection();
                if (!isConnected) {
                    ElMessage.warning('无法连接到后端API，使用本地配置数据');
                    // 如果API不可用，回退到静态配置
                    promotionTypes.value = window.PROMOTION_TYPES || {};
                } else {
                    // 从API加载元数据
                    const metadata = await metadataService.value.getPromotionTypes();
                    promotionTypes.value = metadata;
                    ElMessage.success('动态元数据加载成功');
                }
                
            } catch (error) {
                console.error('系统初始化失败:', error);
                ElMessage.error(`系统初始化失败: ${error.message}`);
                
                // 回退到静态配置
                promotionTypes.value = window.PROMOTION_TYPES || {};
                
            } finally {
                metadataLoading.value = false;
            }
        };

        // ===== 事件处理方法 =====
        const selectCategory = (categoryKey, categoryData) => {
            selectedCategory.value = categoryKey;
            selectedType.value = null;
            formData.value = {};
            currentStep.value = 1;
            
            console.log('选择分类:', categoryKey, categoryData);
        };

        const selectType = (typeKey, typeData) => {
            selectedType.value = typeKey;
            formData.value = {
                ruleId: generateRuleId(),
                ruleName: generateRuleName(typeData),
                ruleType: typeData.ruleType,
                description: '',
                priority: 0,
                isEnabled: true,
                startTime: null,
                endTime: null
            };
            currentStep.value = 2;
            
            console.log('选择类型:', typeKey, typeData);
        };

        const generateRuleId = () => {
            const timestamp = Date.now();
            const random = Math.floor(Math.random() * 1000);
            return `rule_${timestamp}_${random}`;
        };

        const generateRuleName = (typeData) => {
            if (!typeData) return '新促销规则';
            const typeName = typeData.name;
            const timestamp = new Date().toLocaleString('zh-CN', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            }).replace(/[\/\s:]/g, '');
            return `${typeName}_${timestamp}`;
        };

        const autoGenerateIds = () => {
            if (!selectedTypeData.value) {
                ElMessage.warning('请先选择促销类型');
                return;
            }
            
            formData.value.ruleId = generateRuleId();
            formData.value.ruleName = generateRuleName(selectedTypeData.value);
            
            ElMessage.success('ID和名称已自动生成');
        };

        const onFormSubmit = async (data) => {
            try {
                saving.value = true;
                
                // 保存到后端
                const result = await window.apiService.createPromotionRule(data);
                
                ElMessage.success('促销规则创建成功');
                console.log('创建结果:', result);
                
                // 重置表单
                resetForm();
                
            } catch (error) {
                console.error('保存失败:', error);
                ElMessage.error(`保存失败: ${error.message}`);
            } finally {
                saving.value = false;
            }
        };

        const onFormCancel = () => {
            ElMessageBox.confirm('确定要取消配置吗？未保存的数据将丢失。', '确认取消', {
                confirmButtonText: '确定',
                cancelButtonText: '继续编辑',
                type: 'warning'
            }).then(() => {
                resetForm();
            }).catch(() => {
                // 用户取消了取消操作，继续编辑
            });
        };

        const resetForm = () => {
            selectedCategory.value = null;
            selectedType.value = null;
            formData.value = {};
            currentStep.value = 1;
        };

        const previewConfig = () => {
            if (!isValidConfig.value) {
                ElMessage.warning('请先完成配置');
                return;
            }
            showPreviewModal.value = true;
        };

        const exportConfig = () => {
            if (!isValidConfig.value) {
                ElMessage.warning('没有可导出的配置');
                return;
            }

            try {
                const exportData = {
                    ...formData.value,
                    metadata: {
                        category: selectedCategory.value,
                        type: selectedType.value,
                        exportTime: new Date().toISOString()
                    }
                };

                const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                    type: 'application/json'
                });
                
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `promotion_rule_${formData.value.ruleId || 'config'}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                ElMessage.success('配置导出成功');
                
            } catch (error) {
                console.error('导出失败:', error);
                ElMessage.error('导出失败');
            }
        };

        const importConfig = () => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = (e) => {
                const file = e.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const importData = JSON.parse(e.target.result);
                        
                        // 验证导入数据
                        if (!importData.ruleId || !importData.ruleType) {
                            throw new Error('导入的配置文件格式无效');
                        }

                        // 查找对应的类型
                        let foundCategory = null;
                        let foundType = null;
                        
                        Object.keys(promotionTypes.value).forEach(categoryKey => {
                            const category = promotionTypes.value[categoryKey];
                            if (category.types) {
                                Object.keys(category.types).forEach(typeKey => {
                                    const type = category.types[typeKey];
                                    if (type.ruleType === importData.ruleType) {
                                        foundCategory = categoryKey;
                                        foundType = typeKey;
                                    }
                                });
                            }
                        });

                        if (!foundCategory || !foundType) {
                            throw new Error(`未找到匹配的促销类型: ${importData.ruleType}`);
                        }

                        // 应用导入的配置
                        selectedCategory.value = foundCategory;
                        selectedType.value = foundType;
                        formData.value = { ...importData };
                        currentStep.value = 2;
                        
                        ElMessage.success('配置导入成功');
                        
                    } catch (error) {
                        console.error('导入失败:', error);
                        ElMessage.error(`导入失败: ${error.message}`);
                    }
                };
                reader.readAsText(file);
            };
            input.click();
        };

        const saveToBackend = async () => {
            if (!isValidConfig.value) {
                ElMessage.warning('请先完成配置');
                return;
            }

            try {
                saving.value = true;
                const result = await window.apiService.createPromotionRule(formData.value);
                ElMessage.success('配置已保存到后端');
                console.log('保存结果:', result);
            } catch (error) {
                console.error('保存到后端失败:', error);
                ElMessage.error(`保存失败: ${error.message}`);
            } finally {
                saving.value = false;
            }
        };

        const gotoStep = (step) => {
            if (step === 1 || (step === 2 && selectedType.value) || (step === 3 && isValidConfig.value)) {
                currentStep.value = step;
            }
        };

        const nextStep = () => {
            if (currentStep.value < 3) {
                if (currentStep.value === 1 && !selectedType.value) {
                    ElMessage.warning('请先选择促销类型');
                    return;
                }
                if (currentStep.value === 2 && !isValidConfig.value) {
                    ElMessage.warning('请完成必填字段');
                    return;
                }
                currentStep.value++;
            }
        };        const prevStep = () => {
            if (currentStep.value > 1) {
                currentStep.value--;
            }
        };

        // ===== 生命周期 =====
        onMounted(() => {
            initializeSystem();
        });

        // ===== 返回供模板使用 =====
        return {
            // 响应式数据
            loading,
            saving,
            metadataLoading,
            promotionTypes,
            selectedCategory,
            selectedType,
            currentStep,
            formData,
            showPreviewModal,
            showRuleManager,
            
            // 计算属性
            selectedCategoryData,
            selectedTypeData,
            isValidConfig,
            stepperItems,
            
            // 方法
            selectCategory,
            selectType,
            autoGenerateIds,
            onFormSubmit,
            onFormCancel,
            resetForm,
            previewConfig,
            exportConfig,
            importConfig,
            saveToBackend,
            gotoStep,
            nextStep,
            prevStep
        };
    },
    template: `
        <div class="app-container">
            <div class="main-content">
                <!-- 顶部标题栏 -->
                <div class="header">
                    <div class="header-title">
                        <div style="width: 40px; height: 40px; background: var(--ant-primary-color); border-radius: var(--ant-border-radius-base); display: flex; align-items: center; justify-content: center; color: white;">
                            <el-icon size="20"><Setting /></el-icon>
                        </div>
                        <div>
                            <h1>促销规则配置管理系统</h1>
                            <div class="header-subtitle">基于反射API的动态配置平台</div>
                        </div>
                    </div>
                    
                    <div class="header-actions">
                        <el-button @click="showRuleManager = true">
                            <el-icon><List /></el-icon>
                            规则管理
                        </el-button>
                        <el-button @click="autoGenerateIds" type="info" :disabled="!selectedType">
                            <el-icon><Magic /></el-icon>
                            自动生成ID
                        </el-button>
                        <el-button @click="importConfig">
                            <el-icon><Upload /></el-icon>
                            导入配置
                        </el-button>
                        <el-button @click="saveToBackend" type="success" :loading="saving" :disabled="!isValidConfig">
                            <el-icon><CloudUpload /></el-icon>
                            保存到后端
                        </el-button>
                        <el-button type="primary" @click="exportConfig" :disabled="!isValidConfig">
                            <el-icon><Download /></el-icon>
                            导出配置
                        </el-button>
                    </div>
                </div>

                <!-- 主内容 -->
                <div class="layout" v-loading="metadataLoading" element-loading-text="正在加载促销类型...">
                    <!-- 步骤指示器 -->
                    <div class="steps-container">
                        <el-steps :active="currentStep - 1" finish-status="success" align-center>
                            <el-step 
                                v-for="(item, index) in stepperItems" 
                                :key="index"
                                :title="item.title"
                                :description="item.description"
                                @click="gotoStep(index + 1)"
                                style="cursor: pointer;"
                            />
                        </el-steps>
                    </div>

                    <!-- 步骤内容 -->
                    <div class="step-content">
                        <!-- 步骤1: 选择促销类型 -->
                        <div v-if="currentStep === 1" class="step-panel">
                            <div class="step-title">
                                <h2>选择促销类型</h2>
                                <p>从下面的分类中选择您需要的促销活动类型</p>
                            </div>
                            
                            <!-- 分类选择 -->
                            <div class="category-grid">
                                <div 
                                    v-for="(category, key) in promotionTypes" 
                                    :key="key"
                                    class="category-card"
                                    :class="{ active: selectedCategory === key }"
                                    @click="selectCategory(key, category)"
                                >                                    <div class="category-icon">
                                        <span style="font-size: 24px;">{{ category.icon || '⚙️' }}</span>
                                    </div>
                                    <div class="category-info">
                                        <h3>{{ category.name }}</h3>
                                        <p>{{ category.description }}</p>
                                        <span class="type-count">{{ Object.keys(category.types || {}).length }} 种类型</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 类型选择 -->
                            <div v-if="selectedCategoryData" class="type-selection">
                                <h3>选择具体类型</h3>
                                <div class="type-grid">
                                    <div 
                                        v-for="(type, key) in selectedCategoryData.types" 
                                        :key="key"
                                        class="type-card"
                                        :class="{ active: selectedType === key }"
                                        @click="selectType(key, type)"
                                    >
                                        <div class="type-info">
                                            <h4>{{ type.name }}</h4>
                                            <p>{{ type.description }}</p>
                                            <div class="type-meta">
                                                <span class="rule-type">{{ type.ruleType }}</span>
                                                <span class="field-count">{{ (type.fields || []).length }} 个配置项</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 下一步按钮 -->
                            <div class="step-actions">
                                <el-button type="primary" @click="nextStep" :disabled="!selectedType">
                                    下一步：配置参数
                                    <el-icon><ArrowRight /></el-icon>
                                </el-button>
                            </div>
                        </div>

                        <!-- 步骤2: 配置参数 -->
                        <div v-if="currentStep === 2" class="step-panel">
                            <div class="step-title">
                                <h2>配置促销参数</h2>
                                <p>设置 {{ selectedTypeData?.name }} 的具体参数</p>
                            </div>

                            <!-- 动态表单 -->
                            <DynamicFormRenderer
                                v-if="selectedTypeData"
                                v-model="formData"
                                :promotion-types="promotionTypes"
                                :selected-category="selectedCategory"
                                :selected-type="selectedType"
                                mode="create"
                                @submit="onFormSubmit"
                                @cancel="onFormCancel"
                            />

                            <!-- 步骤导航 -->
                            <div class="step-actions">
                                <el-button @click="prevStep">
                                    <el-icon><ArrowLeft /></el-icon>
                                    上一步
                                </el-button>
                                <el-button type="primary" @click="nextStep" :disabled="!isValidConfig">
                                    下一步：预览确认
                                    <el-icon><ArrowRight /></el-icon>
                                </el-button>
                            </div>
                        </div>

                        <!-- 步骤3: 预览确认 -->
                        <div v-if="currentStep === 3" class="step-panel">
                            <div class="step-title">
                                <h2>预览确认</h2>
                                <p>确认配置信息并提交</p>
                            </div>

                            <!-- 配置预览 -->
                            <div class="config-preview">
                                <div class="preview-section">
                                    <h3>基本信息</h3>
                                    <el-descriptions :column="2" border>
                                        <el-descriptions-item label="规则ID">{{ formData.ruleId }}</el-descriptions-item>
                                        <el-descriptions-item label="规则名称">{{ formData.ruleName }}</el-descriptions-item>
                                        <el-descriptions-item label="规则类型">{{ formData.ruleType }}</el-descriptions-item>
                                        <el-descriptions-item label="促销分类">{{ selectedCategoryData?.name }}</el-descriptions-item>
                                        <el-descriptions-item label="促销类型">{{ selectedTypeData?.name }}</el-descriptions-item>
                                        <el-descriptions-item label="状态">
                                            <el-tag :type="formData.isEnabled ? 'success' : 'info'">
                                                {{ formData.isEnabled ? '启用' : '禁用' }}
                                            </el-tag>
                                        </el-descriptions-item>
                                    </el-descriptions>
                                </div>

                                <div class="preview-section">
                                    <h3>配置详情</h3>
                                    <el-card>
                                        <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
                                    </el-card>
                                </div>
                            </div>

                            <!-- 最终操作 -->
                            <div class="step-actions">
                                <el-button @click="prevStep">
                                    <el-icon><ArrowLeft /></el-icon>
                                    上一步
                                </el-button>
                                <el-button @click="resetForm">
                                    <el-icon><Refresh /></el-icon>
                                    重新配置
                                </el-button>
                                <el-button type="success" @click="saveToBackend" :loading="saving">
                                    <el-icon><Check /></el-icon>
                                    确认创建
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 规则管理器对话框 -->
                <el-dialog 
                    v-model="showRuleManager" 
                    title="促销规则管理" 
                    width="80%" 
                    :before-close="() => showRuleManager = false"
                >
                    <RuleManager @close="showRuleManager = false" />
                </el-dialog>
            </div>
        </div>
    `
};

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PromotionConfigMainComponent;
} else if (typeof window !== 'undefined') {
    window.PromotionConfigMainComponent = PromotionConfigMainComponent;
}
