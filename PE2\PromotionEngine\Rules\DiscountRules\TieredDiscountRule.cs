using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.DiscountRules;

/// <summary>
/// 多种折扣规则 [OK]
/// 针对某一类商品，一件X折，两件Y折，三件及以上Z折
/// 场景案例：购买A商品（吊牌、零售、应收价格均为1000），当满足数量大于等于1件
///， A商品享受应收金额的0.8折，大于等于3件A商品享受应收金额0.5折；则购买1件A商品时
///执行结果为1000*0.8=800，购买2件A商品执行结果为1000*2*0.8=1600，购买3件执行
///结果为1000*3*0.5=1500 
/// </summary>
public class TieredDiscountRule : BaseDiscountRule
{
    public override string RuleType => "TieredDiscount";

    /// <summary>
    /// 适用的商品ID列表
    /// </summary>
    public List<string> ApplicableProductIds { get; set; } = [];

    /// <summary>
    /// 折扣梯度列表（按数量从低到高排序）
    /// </summary>
    public List<DiscountTier> DiscountTiers { get; set; } = [];

    /// <summary>
    /// 是否按金额计算（如果为true，则使用金额条件；否则使用数量条件）
    /// </summary>
    public bool IsByAmount => DiscountTiers.All(d => d.MinAmount > 0);

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (ApplicableProductIds.Count == 0 || DiscountTiers.Count == 0)
            return false;

        // 验证商品是否在购物车中
        if (!ValidateDiscountProductsInCart(cart, ApplicableProductIds))
            return false;

        // 检查是否满足最低梯度条件
        return GetApplicableTier(cart) != null;
    }

    /// <summary>
    /// 获取适用的折扣梯度
    /// </summary>
    private DiscountTier? GetApplicableTier(ShoppingCart cart)
    {
        if (IsByAmount)
        {
            var totalAmount = CalculateTotalAmount(cart, ApplicableProductIds);
            return DiscountTiers
                .Where(t => totalAmount >= t.MinAmount)
                .OrderByDescending(t => t.MinAmount)
                .FirstOrDefault();
        }
        else
        {
            var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);
            return DiscountTiers
                .Where(t => totalQuantity >= t.MinQuantity)
                .OrderByDescending(t => t.MinQuantity)
                .FirstOrDefault();
        }
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        // 多种折扣通常只应用一次，因为会选择最优梯度
        return 1;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyTieredDiscount(cart);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用多种折扣促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用多种折扣促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyTieredDiscount(ShoppingCart cart)
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var discountRecords = new List<GiftItem>(); // 折扣记录

        var applicableTier = GetApplicableTier(cart);
        if (applicableTier == null)
            return (0m, consumedItems, discountRecords);

        // 获取所有适用的商品项
        var applicableItems = cart
            .Items.Where(x => ApplicableProductIds.Contains(x.Product.Id) && x.Quantity > 0)
            .ToList();

        if (!applicableItems.Any())
            return (0m, consumedItems, discountRecords);

        // 根据策略排序商品
        var sortedItems = SortItemsByStrategy(applicableItems);

        var promotion = new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType
        };

        // 对所有适用商品应用相同的折扣
        foreach (var item in sortedItems)
        {
            var originalPrice = item.UnitPrice;
            var discountedPrice = originalPrice * applicableTier.DiscountRate;
            var discountAmount = (originalPrice - discountedPrice) * item.Quantity;

            if (discountAmount > 0)
            {
                totalDiscountAmount += discountAmount;

                var strategyDescription =
                    DiscountSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                        ? "客户利益最大化"
                        : "商家利益最大化";

                var description =
                    $"多种折扣：{applicableTier.Description}，{applicableTier.DiscountRate:P1}折，节省{discountAmount:C}（{strategyDescription}）";

                // 应用折扣到商品项
                ApplyDiscountToCartItem(item, applicableTier.DiscountRate, promotion, description);

                // 记录折扣
                discountRecords.Add(
                    CreateDiscountRecord(
                        item.Product.Id,
                        item.Product.Name,
                        item.Quantity,
                        discountAmount,
                        description
                    )
                );

                // 记录消耗的商品
                var existingConsumed = consumedItems.FirstOrDefault(x =>
                    x.ProductId == item.Product.Id
                );
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += item.Quantity;
                }
                else
                {
                    consumedItems.Add(
                        new ConsumedItem
                        {
                            ProductId = item.Product.Id,
                            ProductName = item.Product.Name,
                            Quantity = item.Quantity,
                            UnitPrice = originalPrice
                        }
                    );
                }
            }
        }

        return (totalDiscountAmount, consumedItems, discountRecords);
    }
}

/// <summary>
/// 折扣梯度
/// </summary>
public class DiscountTier
{
    /// <summary>
    /// 最小数量要求
    /// </summary>
    public int MinQuantity { get; set; }

    /// <summary>
    /// 最小金额要求
    /// </summary>
    public decimal MinAmount { get; set; }

    /// <summary>
    /// 折扣率（0.8表示8折）
    /// </summary>
    public decimal DiscountRate { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}
