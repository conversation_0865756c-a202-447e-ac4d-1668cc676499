using PE2.PromotionEngine.Rules;
using PE2.PromotionEngine.Rules.BuyFreeRules;
using PE2.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace PE2.Tests.Rules.BuyFreeRules;

/// <summary>
/// 组合买免规则测试类
/// 测试 CombinationBuyFreeRule 的组合买免计算和应用逻辑
/// </summary>
public class CombinationBuyFreeRuleTests(ITestOutputHelper output) : TestBase(output)
{
    #region 基础组合买免功能测试

    [Fact]
    [Trait("Category", "CombinationBuyFree")]
    [Trait("Priority", "High")]
    public void Apply_ValidCombinationBuyFreeScenario_ShouldApplyCorrectly()
    {
        // Arrange - 买A+B免最低价：买2件A、1件B，免费送其中价格最低的1件商品（不翻倍）
        var rule = new CombinationBuyFreeRule
        {
            Id = "COMBINATION_BUY_FREE_001",
            Name = "组合买免测试 - 买A+B免最低价（不翻倍）",
            Description = "购买A商品2件、B商品1件，免费送其中价格最低的1件商品",
            Priority = 80,
            IsEnabled = true,
            IsRepeatable = false,
            MaxApplications = 1,
            FreeItemSelectionStrategy = BenefitSelectionStrategy.MerchantBenefit,
            FreeQuantity = 1,
            FreeFromCombinationOnly = true,
            CombinationConditions = new List<CombinationBuyCondition>
            {
                new()
                {
                    ProductId = ["A"],
                    RequiredQuantity = 2
                },
                new()
                {
                    ProductId = ["B"],
                    RequiredQuantity = 1
                }
            }
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_001",
            (TestDataGenerator.CreateProductA(), 2), // 2件A，满足条件
            (TestDataGenerator.CreateProductB(), 1)  // 1件B，满足条件
        );

        ValidateTestData(cart, [rule]);
        LogCartDetails(cart, "组合买免测试购物车");

        // 应该免费B商品（价格更低：30元 vs 50元）
        var expectedFreeAmount = TestDataGenerator.CreateProductB().Price;

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "组合买免");
        LogPromotionResultDetails(result);

        // 验证规则被正确应用
        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证总优惠金额
        AssertAmountEqual(expectedFreeAmount, result.TotalDiscount, "应免费1件B商品价值30元");

        // 验证购物车中有免费商品
        //var cartAfter = result.ProcessedCart;
        var freeItems = result.AppliedPromotions[0].GiftItems;//cartAfter.Items.Where(i => i.ActualUnitPrice == 0).ToList();
        Assert.NotEmpty(freeItems);

        var totalFreeQuantity = freeItems.Sum(i => i.Quantity);
        Assert.Equal(1, totalFreeQuantity);

        // 验证免费的是B商品
        var freeBItem = freeItems.FirstOrDefault(i => i.ProductId == "B");
        Assert.NotNull(freeBItem);
        Assert.Equal(1, freeBItem.Quantity);
    }

    [Fact]
    [Trait("Category", "CombinationBuyFree")]
    [Trait("Priority", "High")]
    public void Apply_InsufficientCombinationProducts_ShouldNotApply()
    {
        // Arrange - 组合商品不足：只有1件A，需要2件A+1件B
        var rule = new CombinationBuyFreeRule
        {
            Id = "COMBINATION_BUY_FREE_001",
            Name = "组合买免测试",
            Priority = 80,
            IsEnabled = true,
            FreeQuantity = 1,
            CombinationConditions = new List<CombinationBuyCondition>
            {
                new()
                {
                    ProductId = ["A"],
                    RequiredQuantity = 2
                },
                new()
                {
                    ProductId = ["B"],
                    RequiredQuantity = 1
                }
            }
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_002",
            (TestDataGenerator.CreateProductA(), 1), // 只有1件A，不满足2件的条件
            (TestDataGenerator.CreateProductB(), 1)  // 1件B，满足条件
        );

        LogCartDetails(cart, "组合商品不足的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "组合商品不足场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "组合商品不足时应无优惠");
    }

    [Fact]
    [Trait("Category", "CombinationBuyFree")]
    [Trait("Priority", "High")]
    public void Apply_MissingCombinationProduct_ShouldNotApply()
    {
        // Arrange - 缺少组合商品：有A但没有B
        var rule = new CombinationBuyFreeRule
        {
            Id = "COMBINATION_BUY_FREE_001",
            Name = "组合买免测试",
            Priority = 80,
            IsEnabled = true,
            FreeQuantity = 1,
            CombinationConditions = new List<CombinationBuyCondition>
            {
                new()
                {
                    ProductId = ["A"],
                    RequiredQuantity = 2
                },
                new()
                {
                    ProductId = ["B"],
                    RequiredQuantity = 1
                }
            }
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_003",
            (TestDataGenerator.CreateProductA(), 3) // 3件A，满足条件，但没有B商品
        );

        LogCartDetails(cart, "缺少组合商品的购物车");

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "缺少组合商品场景");
        LogPromotionResultDetails(result);

        // 验证规则未被应用
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");

        // 验证无优惠
        AssertAmountEqual(0m, result.TotalDiscount, "缺少组合商品时应无优惠");
    }

    #endregion

    #region 多次应用测试

    [Fact]
    [Trait("Category", "CombinationBuyFree")]
    [Trait("Priority", "High")]
    public void Apply_MultipleApplications_ShouldApplyMultipleTimes()
    {
        // Arrange - 多次应用：买A+B免最低价，支持翻倍
        var rule = new CombinationBuyFreeRule
        {
            Id = "COMBINATION_BUY_FREE_002",
            Name = "组合买免测试 - 买A+B免最低价（翻2倍）",
            Priority = 85,
            IsEnabled = true,
            IsRepeatable = true,
            MaxApplications = 2,
            FreeItemSelectionStrategy = BenefitSelectionStrategy.MerchantBenefit,
            FreeQuantity = 1,
            FreeFromCombinationOnly = true,
            CombinationConditions = new List<CombinationBuyCondition>
            {
                new()
                {
                    ProductId = ["A"],
                    RequiredQuantity = 3
                },
                new()
                {
                    ProductId = ["B"],
                    RequiredQuantity = 1
                }
            }
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_004",
            (TestDataGenerator.CreateProductA(), 6), // 6件A，可以应用2次
            (TestDataGenerator.CreateProductB(), 2)  // 2件B，可以应用2次
        );

        LogCartDetails(cart, "多次应用测试购物车");

        var expectedFreeQuantity = 2; // 应免费2件
        var expectedFreeAmount = expectedFreeQuantity * TestDataGenerator.CreateProductB().Price; // 免费B商品

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "多次应用");
        LogPromotionResultDetails(result);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证总优惠金额
        AssertAmountEqual(expectedFreeAmount, result.TotalDiscount, $"应免费{expectedFreeQuantity}件B商品");

        // 验证免费商品数量
        //var cartAfter = result.ProcessedCart;
        var freeItems = result.AppliedPromotions[0].GiftItems;//cartAfter.Items.Where(i => i.ActualUnitPrice == 0).ToList();
        var totalFreeQuantity = freeItems.Sum(i => i.Quantity);
        Assert.Equal(expectedFreeQuantity, totalFreeQuantity);
    }

    #endregion

    #region 客户利益最大化测试

    [Fact]
    [Trait("Category", "CombinationBuyFree")]
    [Trait("Priority", "Medium")]
    public void Apply_CustomerBenefitStrategy_ShouldFreeHighestPriceItems()
    {
        // Arrange - 客户利益最大化：优先免费高价商品
        var rule = new CombinationBuyFreeRule
        {
            Id = "COMBINATION_BUY_FREE_003",
            Name = "组合买免测试 - 客户利益最大化",
            Priority = 75,
            IsEnabled = true,
            FreeItemSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit,
            FreeQuantity = 1,
            FreeFromCombinationOnly = true,
            CombinationConditions = new List<CombinationBuyCondition>
            {
                new()
                {
                    ProductId = ["A"],
                    RequiredQuantity = 1
                },
                new()
                {
                    ProductId = ["B"],
                    RequiredQuantity = 1
                },
                new()
                {
                    ProductId = ["C"],
                    RequiredQuantity = 1
                }
            }
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_005",
            (TestDataGenerator.CreateProductA(), 1), // A商品50元
            (TestDataGenerator.CreateProductB(), 1), // B商品30元
            (TestDataGenerator.CreateProductC(), 1)  // C商品20元
        );

        LogCartDetails(cart, "客户利益最大化测试购物车");

        // 应该免费A商品（价格最高）
        var expectedFreeAmount = TestDataGenerator.CreateProductA().Price;

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "客户利益最大化");
        LogPromotionResultDetails(result);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        // 验证免费了高价商品A
        AssertAmountEqual(expectedFreeAmount, result.TotalDiscount, "应免费高价商品A");

        var cartAfter = result.ProcessedCart;
        var freeItemA = cartAfter.Items.FirstOrDefault(i => i.Product.Id == "A" && i.ActualUnitPrice == 0);
        Assert.NotNull(freeItemA);
        Assert.Equal(1, freeItemA.Quantity);
    }

    #endregion

    #region 复杂组合条件测试

    [Fact]
    [Trait("Category", "CombinationBuyFree")]
    [Trait("Priority", "Medium")]
    public void Apply_ComplexCombinationConditions_ShouldApplyCorrectly()
    {
        // Arrange - 复杂组合条件：买A+B+C各1件，免费送其中价格最高的1件商品
        var rule = new CombinationBuyFreeRule
        {
            Id = "COMBINATION_BUY_FREE_003",
            Name = "组合买免测试 - 客户利益最大化",
            Priority = 75,
            IsEnabled = true,
            FreeItemSelectionStrategy = BenefitSelectionStrategy.CustomerBenefit,
            FreeQuantity = 1,
            FreeFromCombinationOnly = true,
            CombinationConditions = new List<CombinationBuyCondition>
            {
                new()
                {
                    ProductId = ["A"],
                    RequiredQuantity = 1
                },
                new()
                {
                    ProductId = ["B"],
                    RequiredQuantity = 1
                },
                new()
                {
                    ProductId = ["C"],
                    RequiredQuantity = 1
                }
            }
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_006",
            (TestDataGenerator.CreateProductA(), 2), // 2件A
            (TestDataGenerator.CreateProductB(), 2), // 2件B
            (TestDataGenerator.CreateProductC(), 2)  // 2件C
        );

        LogCartDetails(cart, "复杂组合条件测试购物车");

        var expectedFreeAmount = TestDataGenerator.CreateProductA().Price; // 免费A商品（价格最高）

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "复杂组合条件");
        LogPromotionResultDetails(result);

        Assert.Single(result.AppliedPromotions);
        AssertRuleApplication(result.AppliedPromotions[0], rule.Id);

        AssertAmountEqual(expectedFreeAmount, result.TotalDiscount, "应免费高价商品A");

        //var cartAfter = result.ProcessedCart;
        //var freeItems = cartAfter.Items.Where(i => i.ActualUnitPrice == 0).ToList();
        var totalFreeQuantity = result.AppliedPromotions[0].GiftItems[0].Quantity; //freeItems.Sum(i => i.Quantity);
        Assert.Equal(1, totalFreeQuantity);
    }

    #endregion

    #region 边界条件测试

    [Fact]
    [Trait("Category", "CombinationBuyFree")]
    [Trait("Priority", "Low")]
    public void Apply_EmptyCart_ShouldNotApply()
    {
        // Arrange
        var rule = new CombinationBuyFreeRule
        {
            Id = "COMBINATION_BUY_FREE_001",
            Name = "组合买免测试",
            Priority = 80,
            IsEnabled = true,
            FreeQuantity = 1,
            CombinationConditions = new List<CombinationBuyCondition>
            {
                new()
                {
                    ProductId = ["A"],
                    RequiredQuantity = 2
                },
                new()
                {
                    ProductId = ["B"],
                    RequiredQuantity = 1
                }
            }
        };

        var cart = TestDataGenerator.CreateEmptyCart();

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "空购物车");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertAmountEqual(0m, result.TotalDiscount, "空购物车应无优惠");
    }

    [Fact]
    [Trait("Category", "CombinationBuyFree")]
    [Trait("Priority", "Low")]
    public void Apply_DisabledRule_ShouldNotApply()
    {
        // Arrange
        var rule = new CombinationBuyFreeRule
        {
            Id = "COMBINATION_BUY_FREE_001",
            Name = "组合买免测试",
            Priority = 80,
            IsEnabled = false, // 禁用规则
            FreeQuantity = 1,
            CombinationConditions = new List<CombinationBuyCondition>
            {
                new()
                {
                    ProductId = ["A"],
                    RequiredQuantity = 2
                },
                new()
                {
                    ProductId = ["B"],
                    RequiredQuantity = 1
                }
            }
        };

        var cart = TestDataGenerator.CreateCustomCart(
            "TEST_CART",
            "CUSTOMER_007",
            (TestDataGenerator.CreateProductA(), 2),
            (TestDataGenerator.CreateProductB(), 1)
        );

        TestPromotionRuleService.Rules = [rule];
        // Act
        var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;

        // Assert
        AssertPromotionResult(result, "禁用规则");
        Assert.Empty(result.AppliedPromotions);
        Assert.Single(result.IgnoredPromotions);
        AssertIgnoredRule(result.IgnoredPromotions[0], "促销条件不满足");
        AssertAmountEqual(0m, result.TotalDiscount, "禁用规则应无优惠");
    }

    #endregion

    #region 性能测试

    [Fact]
    [Trait("Category", "CombinationBuyFree")]
    [Trait("Priority", "Low")]
    public void Apply_LargeCartWithCombinationBuyFree_ShouldPerformWell()
    {
        // Arrange - 大型购物车 + 复杂组合买免条件
        var rule = new CombinationBuyFreeRule
        {
            Id = "COMBINATION_BUY_FREE_PERFORMANCE",
            Name = "性能测试组合买免规则",
            Priority = 80,
            IsEnabled = true,
            IsRepeatable = true,
            FreeQuantity = 1,
            CombinationConditions = new List<CombinationBuyCondition>
            {
                new()
                {
                    ProductId = ["A"],
                    RequiredQuantity = 2
                },
                new()
                {
                    ProductId = ["B"],
                    RequiredQuantity = 1
                }
            }
        };

        var cart = TestDataGenerator.CreateLargeTestCart(500); // 500个商品项

        Output.WriteLine($"复杂组合买免性能测试: {cart.Items.Count} 个商品项, {rule.CombinationConditions.Count} 个组合条件");

        TestPromotionRuleService.Rules = [rule];
        // Act & Assert
        var executionTime = MeasureExecutionTime(() =>
        {
            var result = PromotionEngine.CalculateOptimalPromotionsAsync(cart).Result;
            AssertPromotionResult(result, "复杂组合买免性能测试");
        });

        // 性能断言：复杂组合买免计算应在合理时间内完成
        AssertPerformance(executionTime, TimeSpan.FromMilliseconds(600), "复杂组合买免规则计算");
    }

    #endregion

    #region 规则配置验证测试

    [Fact]
    [Trait("Category", "CombinationBuyFree")]
    [Trait("Priority", "Medium")]
    public void ValidateRule_ValidConfiguration_ShouldPass()
    {
        // Arrange
        var rule = new CombinationBuyFreeRule
        {
            Id = "COMBINATION_BUY_FREE_001",
            Name = "组合买免测试",
            Priority = 80,
            IsEnabled = true,
            FreeQuantity = 1,
            CombinationConditions = new List<CombinationBuyCondition>
            {
                new()
                {
                    ProductId = ["A"],
                    RequiredQuantity = 2
                },
                new()
                {
                    ProductId = ["B"],
                    RequiredQuantity = 1
                }
            }
        };

        // Act & Assert
        ValidateRuleBasicProperties(rule);

        // 验证组合买免规则特有属性
        Assert.NotNull(rule.CombinationConditions);
        Assert.NotEmpty(rule.CombinationConditions);
        Assert.True(rule.FreeQuantity > 0, "免费数量必须大于0");

        foreach (var condition in rule.CombinationConditions)
        {
            Assert.NotNull(condition.ProductId);
            Assert.NotEmpty(condition.ProductId);
            Assert.True(condition.RequiredQuantity > 0, "组合条件必须有效");

            foreach (var productId in condition.ProductId)
            {
                Assert.False(string.IsNullOrEmpty(productId), "组合条件商品ID不能为空");
            }
        }

        Output.WriteLine("组合买免规则配置验证通过 ✓");
    }

    #endregion 
}