using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Rules.DiscountRules;

/// <summary>
/// 递增折扣规则 [OK]
/// 针对某一类商品，第一件X折，第二件Y折，第三件及以上Z折
/// 场景案例：购买A商品（吊牌，零售，应收价格均为1000元），第一件打0.8折，第二件打0.7折，第三件及以上打0.5折。
///购买1件A商品：1000*0.8=800元
///购买2件A商品：1000*0.8+1000*0.7=1500元
///购买3件A商品：1000*0.8+1000*0.7+1000*0.5=2000元
///购买4件A商品：1000*0.8+1000*0.7+1000*0.5+1000*0.5=2500元
///备注：若优惠方式为客户利益最大化，则优先取最大金额的商品进行最大优惠的折扣；若优惠方式为商户利益最大化，则优先取最小金额的商品进行最大优惠。
/// </summary>
public class ProgressiveDiscountRule : BaseDiscountRule
{
    public override string RuleType => "ProgressiveDiscount";

    /// <summary>
    /// 适用的商品ID列表
    /// </summary>
    public List<string> ApplicableProductIds { get; set; } = new();

    /// <summary>
    /// 递增折扣列表（按顺序应用）
    /// </summary>
    public List<ProgressiveDiscountTier> ProgressiveTiers { get; set; } = [];

    protected override bool CheckConditions(ShoppingCart cart)
    {
        if (!ApplicableProductIds.Any() || !ProgressiveTiers.Any())
            return false;

        // 验证商品是否在购物车中
        if (!ValidateDiscountProductsInCart(cart, ApplicableProductIds))
            return false;

        // 检查是否有足够的商品数量
        var totalQuantity = CalculateTotalQuantity(cart, ApplicableProductIds);
        return totalQuantity >= 1;
    }

    public override int CalculateMaxApplications(ShoppingCart cart)
    {
        if (!CheckConditions(cart))
            return 0;

        // 递增折扣通常只应用一次，因为会对所有商品按顺序应用不同折扣
        return 1;
    }

    public override PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount)
    {
        var application = new PromotionApplication
        {
            RuleId = Id,
            RuleName = Name,
            ApplicationCount = applicationCount
        };

        try
        {
            var result = ApplyProgressiveDiscount(cart);
            application.DiscountAmount = result.Item1;
            application.ConsumedItems = result.Item2;
            application.GiftItems = result.Item3;
            application.IsSuccessful = result.Item1 > 0;

            if (!application.IsSuccessful)
            {
                application.ErrorMessage = "没有符合条件的商品可以应用递增折扣促销";
            }
        }
        catch (Exception ex)
        {
            application.IsSuccessful = false;
            application.ErrorMessage = ex.Message;
        }

        return application;
    }

    /// <summary>
    /// 应用递增折扣促销
    /// </summary>
    private (decimal, List<ConsumedItem>, List<GiftItem>) ApplyProgressiveDiscount(
        ShoppingCart cart
    )
    {
        var totalDiscountAmount = 0m;
        var consumedItems = new List<ConsumedItem>();
        var discountRecords = new List<GiftItem>(); // 折扣记录

        // 获取所有适用的商品项
        var applicableItems = cart
            .Items.Where(x => ApplicableProductIds.Contains(x.Product.Id) && x.Quantity > 0)
            .ToList();

        if (!applicableItems.Any())
            return (0m, consumedItems, discountRecords);

        // 根据策略排序商品（客户利益最大化时，高价商品优先享受高折扣）
        var sortedItems = SortItemsByStrategy(applicableItems);

        var promotion = new AppliedPromotion
        {
            RuleId = Id,
            RuleName = Name,
            PromotionType = RuleType
        };

        // 展开所有商品为单个商品项进行递增折扣计算
        var expandedItems = new List<(CartItem Item, decimal UnitPrice)>();
        foreach (var item in sortedItems)
        {
            for (int i = 0; i < item.Quantity; i++)
            {
                expandedItems.Add((item, item.UnitPrice));
            }
        }

        // 按递增折扣规则应用折扣
        var itemIndex = 0;
        var processedItems =
            new Dictionary<
                CartItem,
                (int ProcessedQuantity, decimal TotalDiscount, List<decimal> DiscountRates)
            >();

        foreach (var (item, unitPrice) in expandedItems)
        {
            // 确定当前商品应用的折扣梯度
            var tierIndex = Math.Min(itemIndex, ProgressiveTiers.Count - 1);
            var tier = ProgressiveTiers[tierIndex];

            var discountedPrice = unitPrice * tier.DiscountRate;
            var discountAmount = unitPrice - discountedPrice;

            if (discountAmount > 0)
            {
                totalDiscountAmount += discountAmount;

                // 记录处理的商品信息
                if (
                    !processedItems.TryGetValue(
                        item,
                        out (
                            int ProcessedQuantity,
                            decimal TotalDiscount,
                            List<decimal> DiscountRates
                        ) processed
                    )
                )
                {
                    processed = (0, 0m, new List<decimal>());
                    processedItems[item] = processed;
                }

                processed.ProcessedQuantity++;
                processed.TotalDiscount += discountAmount;
                processed.DiscountRates.Add(tier.DiscountRate);
                processedItems[item] = processed;
            }

            itemIndex++;
        }

        // 应用折扣到购物车项并记录详情
        foreach (var kvp in processedItems)
        {
            var item = kvp.Key;
            var (processedQuantity, totalDiscount, discountRates) = kvp.Value;

            if (processedQuantity > 0 && totalDiscount > 0)
            {
                // 计算平均折扣率（用于显示）
                var avgDiscountRate = discountRates.Average();

                // 计算新的实际单价
                var newActualUnitPrice = item.UnitPrice - (totalDiscount / processedQuantity);
                item.ActualUnitPrice = Math.Max(0, newActualUnitPrice);

                var strategyDescription =
                    DiscountSelectionStrategy == BenefitSelectionStrategy.CustomerBenefit
                        ? "客户利益最大化"
                        : "商家利益最大化";

                var description =
                    $"递增折扣：平均{avgDiscountRate:P1}折，节省{totalDiscount:C}（{strategyDescription}）";

                // 记录促销详情
                var promotionDetail = new ItemPromotionDetail
                {
                    RuleId = promotion.RuleId,
                    RuleName = promotion.RuleName,
                    PromotionType = promotion.PromotionType,
                    DiscountAmount = totalDiscount,
                    Description = description,
                    IsGiftRelated = false
                };

                item.AddPromotionDetail(promotionDetail);

                // 记录折扣
                discountRecords.Add(
                    CreateDiscountRecord(
                        item.Product.Id,
                        item.Product.Name,
                        processedQuantity,
                        totalDiscount,
                        description
                    )
                );

                // 记录消耗的商品
                var existingConsumed = consumedItems.FirstOrDefault(x =>
                    x.ProductId == item.Product.Id
                );
                if (existingConsumed != null)
                {
                    existingConsumed.Quantity += processedQuantity;
                }
                else
                {
                    consumedItems.Add(
                        new ConsumedItem
                        {
                            ProductId = item.Product.Id,
                            ProductName = item.Product.Name,
                            Quantity = processedQuantity,
                            UnitPrice = item.UnitPrice
                        }
                    );
                }
            }
        }

        return (totalDiscountAmount, consumedItems, discountRecords);
    }
}

/// <summary>
/// 递增折扣梯度
/// </summary>
public class ProgressiveDiscountTier
{
    /// <summary>
    /// 折扣率（0.8表示8折）
    /// </summary>
    public decimal DiscountRate { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}
