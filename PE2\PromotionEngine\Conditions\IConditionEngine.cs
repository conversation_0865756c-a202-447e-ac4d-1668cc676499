using PE2.Models;
using PE2.PromotionEngine.Models;

namespace PE2.PromotionEngine.Conditions;

/// <summary>
/// 条件引擎接口 - 统一化条件验证框架
/// </summary>
public interface IConditionEngine
{
    /// <summary>
    /// 验证条件是否满足
    /// </summary>
    Task<ConditionResult> ValidateAsync(ConditionExpression expression, ShoppingCart cart);

    /// <summary>
    /// 批量验证多个条件
    /// </summary>
    Task<Dictionary<string, ConditionResult>> ValidateBatchAsync(
        Dictionary<string, ConditionExpression> expressions, 
        ShoppingCart cart);

    /// <summary>
    /// 获取条件满足度分析
    /// </summary>
    Task<ConditionAnalysis> AnalyzeAsync(ConditionExpression expression, ShoppingCart cart);

    /// <summary>
    /// 预编译条件表达式（用于性能优化）
    /// </summary>
    CompiledCondition CompileCondition(ConditionExpression expression);
}

/// <summary>
/// 条件表达式 - 支持复杂条件组合
/// </summary>
public class ConditionExpression
{
    /// <summary>
    /// 条件ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 条件类型
    /// </summary>
    public ConditionType Type { get; set; }

    /// <summary>
    /// 逻辑操作符（AND/OR/NOT）
    /// </summary>
    public LogicalOperator Operator { get; set; } = LogicalOperator.And;

    /// <summary>
    /// 子条件列表
    /// </summary>
    public List<ConditionExpression> SubConditions { get; set; } = new();

    /// <summary>
    /// 条件参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// 条件权重（用于优化排序）
    /// </summary>
    public int Weight { get; set; } = 1;

    /// <summary>
    /// 是否可缓存
    /// </summary>
    public bool IsCacheable { get; set; } = true;
}

/// <summary>
/// 条件类型枚举
/// </summary>
public enum ConditionType
{
    /// <summary>商品数量条件</summary>
    ProductQuantity,
    /// <summary>商品金额条件</summary>
    ProductAmount,
    /// <summary>分类数量条件</summary>
    CategoryQuantity,
    /// <summary>分类金额条件</summary>
    CategoryAmount,
    /// <summary>总数量条件</summary>
    TotalQuantity,
    /// <summary>总金额条件</summary>
    TotalAmount,
    /// <summary>会员等级条件</summary>
    MemberLevel,
    /// <summary>时间条件</summary>
    TimeRange,
    /// <summary>组合条件</summary>
    Combination,
    /// <summary>自定义条件</summary>
    Custom
}

/// <summary>
/// 逻辑操作符
/// </summary>
public enum LogicalOperator
{
    And,
    Or,
    Not
}

/// <summary>
/// 条件验证结果
/// </summary>
public class ConditionResult
{
    /// <summary>
    /// 是否满足条件
    /// </summary>
    public bool IsSatisfied { get; set; }

    /// <summary>
    /// 满足度百分比（0-100）
    /// </summary>
    public decimal SatisfactionPercentage { get; set; }

    /// <summary>
    /// 缺口描述
    /// </summary>
    public string Gap { get; set; } = string.Empty;

    /// <summary>
    /// 详细信息
    /// </summary>
    public Dictionary<string, object> Details { get; set; } = new();

    /// <summary>
    /// 验证耗时（毫秒）
    /// </summary>
    public long ValidationTimeMs { get; set; }

    /// <summary>
    /// 子条件结果
    /// </summary>
    public Dictionary<string, ConditionResult> SubResults { get; set; } = new();
}

/// <summary>
/// 条件分析结果
/// </summary>
public class ConditionAnalysis
{
    /// <summary>
    /// 条件复杂度评分
    /// </summary>
    public int ComplexityScore { get; set; }

    /// <summary>
    /// 预估验证耗时
    /// </summary>
    public long EstimatedValidationTimeMs { get; set; }

    /// <summary>
    /// 优化建议
    /// </summary>
    public List<string> OptimizationSuggestions { get; set; } = new();

    /// <summary>
    /// 依赖的数据字段
    /// </summary>
    public List<string> RequiredDataFields { get; set; } = new();
}

/// <summary>
/// 编译后的条件（用于性能优化）
/// </summary>
public class CompiledCondition
{
    /// <summary>
    /// 条件ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 编译后的验证函数
    /// </summary>
    public Func<ShoppingCart, ConditionResult> ValidationFunction { get; set; } = null!;

    /// <summary>
    /// 编译时间
    /// </summary>
    public DateTime CompiledAt { get; set; }

    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; } = true;
}
