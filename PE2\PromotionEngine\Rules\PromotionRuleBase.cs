using System.Text.Json.Serialization;
using PE2.Models;
using PE2.PromotionEngine.Models;
using PE2.PromotionEngine.Rules.ExchangeRules;
using PE2.PromotionEngine.Rules.BuyFreeRules;
using PE2.PromotionEngine.Rules.CashDiscountRules;
using PE2.PromotionEngine.Rules.DiscountRules;
using PE2.PromotionEngine.Rules.SpecialPriceRules;
using PE2.PromotionEngine.Rules.BuyGiftRules;

namespace PE2.PromotionEngine.Rules;

/// <summary>
/// 促销规则基类
/// </summary>
[JsonDerivedType(typeof(PercentageDiscountRule), "PercentageDiscount")]
[JsonDerivedType(typeof(FixedAmountDiscountRule), "FixedAmountDiscount")]
[JsonDerivedType(typeof(BuyXGetYRule), "BuyXGetY")]
[JsonDerivedType(typeof(BundleOfferRule), "BundleOffer")]
[JsonDerivedType(typeof(CategoryDiscountRule), "CategoryDiscount")]
[JsonDerivedType(typeof(TotalAmountDiscountRule), "TotalAmountDiscount")]
[JsonDerivedType(typeof(GradientGiftRule), "GradientGift")]
[JsonDerivedType(typeof(UnifiedGiftRule), "UnifiedGift")]
[JsonDerivedType(typeof(CombinationGiftRule), "CombinationGift")]
// 换购规则类型
[JsonDerivedType(typeof(UnifiedSpecialPriceExchangeRule), "UnifiedSpecialPriceExchange")]
[JsonDerivedType(typeof(UnifiedDiscountExchangeRule), "UnifiedDiscountExchange")]
[JsonDerivedType(typeof(UnifiedDiscountAmountExchangeRule), "UnifiedDiscountAmountExchange")]
[JsonDerivedType(typeof(CombinationSpecialPriceExchangeRule), "CombinationSpecialPriceExchange")]
[JsonDerivedType(typeof(CombinationDiscountExchangeRule), "CombinationDiscountExchange")]
[JsonDerivedType(typeof(CombinationDiscountAmountExchangeRule), "CombinationDiscountAmountExchange")]
// 买免规则类型
[JsonDerivedType(typeof(ProductBuyFreeRule), "ProductBuyFree")]
[JsonDerivedType(typeof(CombinationBuyFreeRule), "CombinationBuyFree")]
// 减现规则类型
[JsonDerivedType(typeof(UnifiedCashDiscountRule), "UnifiedCashDiscount")]
[JsonDerivedType(typeof(GradientCashDiscountRule), "GradientCashDiscount")]
[JsonDerivedType(typeof(CombinationCashDiscountRule), "CombinationCashDiscount")]
// 打折规则类型
[JsonDerivedType(typeof(UnifiedDiscountRule), "UnifiedDiscount")]
[JsonDerivedType(typeof(TieredDiscountRule), "TieredDiscount")]
[JsonDerivedType(typeof(ProgressiveDiscountRule), "ProgressiveDiscount")]
[JsonDerivedType(typeof(CombinationDiscountRule), "CombinationDiscount")]
[JsonDerivedType(typeof(FreeFormDiscountRule), "FreeFormDiscount")]
[JsonDerivedType(typeof(CyclicDiscountRule), "CyclicDiscount")]
// 特价规则类型
[JsonDerivedType(typeof(UnifiedSpecialPriceRule), "UnifiedSpecialPrice")]
[JsonDerivedType(typeof(TieredSpecialPriceRule), "TieredSpecialPrice")]
[JsonDerivedType(typeof(CombinationSpecialPriceRule), "CombinationSpecialPrice")]
[JsonDerivedType(typeof(IndividualSpecialPriceRule), "IndividualSpecialPrice")]
// 买赠规则类型
//[JsonDerivedType(typeof(UnifiedGiftRule), "UnifiedGift")]
[JsonDerivedType(typeof(TieredGiftRule), "TieredGift")]
//[JsonDerivedType(typeof(CombinationGiftRule), "CombinationGift")]
public abstract class PromotionRuleBase
{
    /// <summary>
    /// 规则ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 规则名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 规则描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 规则类型
    /// </summary>
    public abstract string RuleType { get; }

    /// <summary>
    /// 优先级（数字越大优先级越高）
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 生效时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 失效时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 是否可重复应用
    /// </summary>
    public bool IsRepeatable { get; set; } = true;

    /// <summary>
    /// 最大应用次数（0表示无限制）
    /// </summary>
    public int MaxApplications { get; set; } = 0;

    /// <summary>
    /// 适用的客户类型
    /// </summary>
    public List<string> ApplicableCustomerTypes { get; set; } = [];

    /// <summary>
    /// 排斥的规则ID列表（互斥规则）
    /// </summary>
    public List<string> ExclusiveRuleIds { get; set; } = new();

    /// <summary>
    /// 是否可与其他促销叠加（false表示参与此促销的商品不能同时参与其他促销）
    /// </summary>
    public bool CanStackWithOthers { get; set; } = true;

    /// <summary>
    /// 商品互斥级别（用于更细粒度的控制）
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public ProductExclusivityLevel ProductExclusivity { get; set; } = ProductExclusivityLevel.None;

    /// <summary>
    /// 检查规则是否在有效期内
    /// </summary>
    public virtual bool IsInValidPeriod(DateTime checkTime)
    {
        if (StartTime.HasValue && checkTime < StartTime.Value)
            return false;

        if (EndTime.HasValue && checkTime > EndTime.Value)
            return false;

        return true;
    }

    /// <summary>
    /// 检查规则是否适用于指定购物车
    /// </summary>
    public virtual bool IsApplicable(ShoppingCart cart, DateTime checkTime)
    {
        if (!IsEnabled)
            return false;

        if (!IsInValidPeriod(checkTime))
            return false;

        return CheckConditions(cart);
    }

    /// <summary>
    /// 检查促销条件是否满足
    /// </summary>
    protected abstract bool CheckConditions(ShoppingCart cart);

    /// <summary>
    /// 计算可应用的最大次数
    /// </summary>
    public abstract int CalculateMaxApplications(ShoppingCart cart);

    /// <summary>
    /// 应用促销规则
    /// </summary>
    public abstract PromotionApplication ApplyPromotion(ShoppingCart cart, int applicationCount = 1);

    /// <summary>
    /// 获取促销预览信息（不实际修改购物车）
    /// </summary>
    public virtual PromotionPreview GetPromotionPreview(ShoppingCart cart)
    {
        if (!IsApplicable(cart, DateTime.Now))
        {
            return new PromotionPreview
            {
                RuleId = Id,
                RuleName = Name,
                IsApplicable = false,
                Reason = "促销条件不满足"
            };
        }

        var maxApplications = CalculateMaxApplications(cart);
        if (maxApplications <= 0)
        {
            return new PromotionPreview
            {
                RuleId = Id,
                RuleName = Name,
                IsApplicable = false,
                Reason = "商品数量不足"
            };
        }

        var tempCart = cart.Clone();
        var application = ApplyPromotion(tempCart, maxApplications);

        return new PromotionPreview
        {
            RuleId = Id,
            RuleName = Name,
            IsApplicable = true,
            MaxApplications = maxApplications,
            EstimatedDiscount = application.DiscountAmount,
            Description = $"预计优惠: {application.DiscountAmount:C}"
        };
    }
}

/// <summary>
/// 促销应用结果
/// </summary>
public class PromotionApplication
{
    /// <summary>
    /// 规则ID
    /// </summary>
    public string RuleId { get; set; } = string.Empty;

    /// <summary>
    /// 规则名称
    /// </summary>
    public string RuleName { get; set; } = string.Empty;

    /// <summary>
    /// 优惠金额
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// 应用次数
    /// </summary>
    public int ApplicationCount { get; set; }

    /// <summary>
    /// 消耗的商品
    /// </summary>
    public List<ConsumedItem> ConsumedItems { get; set; } = new();

    /// <summary>
    /// 赠送的商品
    /// </summary>
    public List<GiftItem> GiftItems { get; set; } = new();

    /// <summary>
    /// 是否成功应用
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;
}

/// <summary>
/// 促销预览
/// </summary>
public class PromotionPreview
{
    /// <summary>
    /// 规则ID
    /// </summary>
    public string RuleId { get; set; } = string.Empty;

    /// <summary>
    /// 规则名称
    /// </summary>
    public string RuleName { get; set; } = string.Empty;

    /// <summary>
    /// 是否适用
    /// </summary>
    public bool IsApplicable { get; set; }

    /// <summary>
    /// 不适用原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 最大应用次数
    /// </summary>
    public int MaxApplications { get; set; }

    /// <summary>
    /// 预计优惠金额
    /// </summary>
    public decimal EstimatedDiscount { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 商品互斥级别
/// </summary>
public enum ProductExclusivityLevel
{
    /// <summary>
    /// 无互斥限制，可以与任何促销叠加
    /// </summary>
    None = 0,

    /// <summary>
    /// 部分互斥，只能与同级别或更低级别的促销叠加
    /// </summary>
    Partial = 1,

    /// <summary>
    /// 完全互斥，参与此促销的商品不能参与任何其他促销
    /// </summary>
    Exclusive = 2
}
