# POSPE2 促销引擎系统

## 项目概述

POSPE2 是一个基于 .NET Core 的智能促销引擎系统，专为服装零售POS系统设计。系统采用先进的组合优化算法，能够在多种促销活动中自动选择最优组合，确保客户获得最大优惠。

## 核心特性

### 🎯 智能促销计算
- **多种促销类型支持**：百分比折扣、固定金额折扣、买赠促销、组合套餐、分类折扣、订单总额折扣
- **最优组合算法**：使用回溯+剪枝算法寻找最优促销组合
- **实时计算**：毫秒级响应，适合POS系统实时需求

### 🔍 详细溯源分析
- **计算过程追踪**：记录每个计算步骤和决策原因
- **促销忽略分析**：详细说明为什么某些促销被忽略
- **性能监控**：提供计算耗时和算法效率分析

### 🛠️ 灵活配置管理
- **JSON配置**：使用 System.Text.Json 的多态反序列化
- **热重载**：支持运行时重新加载促销规则
- **规则验证**：自动验证规则文件格式和逻辑

### 🏗️ 优秀架构设计
- **分层架构**：清晰的模型、服务、控制器分层
- **设计模式**：策略模式、工厂模式、责任链模式
- **RESTful API**：完整的API接口，支持Swagger文档

## 项目结构

```
PE2/
├── Models/                     # 核心数据模型
│   ├── Product.cs             # 商品模型
│   ├── CartItem.cs            # 购物车商品项
│   └── ShoppingCart.cs        # 购物车模型
├── PromotionEngine/           # 促销引擎核心
│   ├── Models/                # 促销相关模型
│   │   └── PromotionResult.cs # 促销计算结果
│   ├── Rules/                 # 促销规则实现
│   │   ├── PromotionRuleBase.cs      # 规则基类
│   │   ├── PercentageDiscountRule.cs # 百分比折扣
│   │   ├── FixedAmountDiscountRule.cs # 固定金额折扣
│   │   ├── BuyXGetYRule.cs           # 买赠规则
│   │   ├── BundleOfferRule.cs        # 组合套餐
│   │   ├── CategoryDiscountRule.cs   # 分类折扣
│   │   └── TotalAmountDiscountRule.cs # 订单总额折扣
│   └── Calculators/           # 计算器
│       ├── PromotionCalculator.cs    # 主计算器
│       └── PromotionTracker.cs       # 过程追踪器
├── Services/                  # 业务服务层
│   ├── PromotionRuleService.cs       # 规则管理服务
│   └── PromotionEngineService.cs     # 促销引擎服务
├── Controllers/               # API控制器
│   ├── PromotionController.cs        # 促销计算API
│   ├── PromotionRuleController.cs    # 规则管理API
│   └── DemoController.cs             # 演示API
└── Configuration/             # 配置文件
    └── promotion-rules.json          # 促销规则配置
```

## 快速开始

### 1. 启动应用
```bash
cd PE2
dotnet run
```

应用将启动在 `http://localhost:5213`，Swagger UI 可在根路径访问。

### 2. 基本使用示例

#### 创建示例购物车
```http
GET /api/demo/sample-cart
```

#### 计算最优促销
```http
POST /api/promotion/calculate
Content-Type: application/json

{
  "id": "CART_001",
  "customerId": "CUSTOMER_001",
  "items": [
    {
      "product": {
        "id": "A",
        "name": "商品A",
        "price": 50.00,
        "category": "电子产品"
      },
      "quantity": 1,
      "unitPrice": 50.00
    },
    {
      "product": {
        "id": "B",
        "name": "商品B",
        "price": 30.00,
        "category": "服装"
      },
      "quantity": 2,
      "unitPrice": 30.00
    },
    {
      "product": {
        "id": "C",
        "name": "商品C",
        "price": 20.00,
        "category": "家居"
      },
      "quantity": 5,
      "unitPrice": 20.00
    }
  ]
}
```

## 促销规则配置

系统支持以下促销规则类型：

### 1. 百分比折扣 (PercentageDiscount)
```json
{
  "$type": "PercentageDiscount",
  "id": "RULE001",
  "name": "商品B满2件8折",
  "applicableProductIds": ["B"],
  "discountPercentage": 0.8,
  "minQuantity": 2
}
```

### 2. 买赠促销 (BuyXGetY)
```json
{
  "$type": "BuyXGetY",
  "id": "RULE002",
  "name": "买1件A+1件B半价",
  "buyConditions": [
    {"productIds": ["A"], "requiredQuantity": 1},
    {"productIds": ["B"], "requiredQuantity": 1}
  ],
  "giftConditions": [
    {"productIds": ["B"], "giftQuantity": 1}
  ],
  "giftSameProduct": true
}
```

### 3. 组合套餐 (BundleOffer)
```json
{
  "$type": "BundleOffer",
  "id": "RULE004",
  "name": "A+B+C组合套餐",
  "bundleItems": [
    {"productId": "A", "requiredQuantity": 1},
    {"productId": "B", "requiredQuantity": 1},
    {"productId": "C", "requiredQuantity": 1}
  ],
  "bundlePrice": 99.00
}
```

## API 接口说明

### 促销计算 API
- `POST /api/promotion/calculate` - 计算最优促销组合
- `POST /api/promotion/preview` - 获取促销预览
- `POST /api/promotion/apply/{ruleId}` - 应用指定促销规则
- `POST /api/promotion/validate` - 验证促销组合
- `GET /api/promotion/statistics` - 获取促销统计信息

### 规则管理 API
- `GET /api/promotionrule` - 获取所有促销规则
- `GET /api/promotionrule/{id}` - 获取指定促销规则
- `POST /api/promotionrule` - 添加促销规则
- `PUT /api/promotionrule/{id}` - 更新促销规则
- `DELETE /api/promotionrule/{id}` - 删除促销规则
- `POST /api/promotionrule/reload` - 重新加载规则文件

### 演示 API
- `GET /api/demo/sample-cart` - 创建示例购物车
- `POST /api/demo/full-demo` - 完整功能演示
- `GET /api/demo/complex-cart` - 创建复杂场景购物车

## 算法说明

### 回溯优化算法
系统使用带剪枝的回溯算法来寻找最优促销组合：

1. **状态表示**：购物车剩余商品状态
2. **决策树搜索**：对每个促销规则进行应用/不应用的决策
3. **剪枝优化**：当前路径的潜在最大优惠低于已知最优解时停止搜索
4. **记忆化**：缓存重复状态的计算结果

### 性能特性
- **时间复杂度**：O(2^n) 最坏情况，实际通过剪枝大幅优化
- **空间复杂度**：O(n) 递归栈空间
- **响应时间**：通常在10-100毫秒内完成计算

## 扩展开发

### 添加新的促销规则类型
1. 继承 `PromotionRuleBase` 基类
2. 实现抽象方法：`CheckConditions`、`CalculateMaxApplications`、`ApplyPromotion`
3. 在 `PromotionRuleBase` 中添加 `JsonDerivedType` 属性
4. 更新配置文件示例

### 自定义计算策略
可以通过实现 `IPromotionCalculator` 接口来自定义计算策略，支持不同的优化算法。

